#!/bin/sh

rm -rf hknew
mkdir hknew && mkdir -p hknew/common && mkdir -p hknew/conf && mkdir -p hknew/const && mkdir -p hknew/core && mkdir -p hknew/dao && mkdir -p hknew/ds && mkdir -p hknew/service && mkdir -p hknew/util 
mkdir -p hknew/dao/news
mkdir -p hknew/dao/misuser

######
#待定的几个也先保留
mv phplib/service/QRCode.php hknew/service
mv phplib/util/Hash.php hknew/util
mv phplib/util/Mail.php hknew/util
mv phplib/util/Redirect.php hknew/util
mv phplib/util/String.php hknew/util
######

## common
mv phplib/common/* hknew/common
## conf
mv phplib/conf/antispam.conf hknew/conf
mv phplib/conf/bos.conf hknew/conf
mv phplib/conf/gps.conf hknew/conf
mv phplib/conf/idalloc.conf hknew/conf
mv phplib/conf/memcached.conf hknew/conf
mv phplib/conf/nmq.conf hknew/conf
mv phplib/conf/redis.conf hknew/conf
mv phplib/conf/rpc.conf hknew/conf
mv phplib/conf/video.conf hknew/conf
## const
mv phplib/const/ActionNo.php hknew/const
cp phplib/const/AppProtocol.php hknew/const
mv phplib/const/Command.php hknew/const
cp phplib/const/Img.php hknew/const
## core
mv phplib/core/* hknew/core
## dao
mv phplib/dao/friend hknew/dao
mv phplib/dao/order hknew/dao
mv phplib/dao/tiku hknew/dao
mv phplib/dao/user hknew/dao
mv phplib/dao/video hknew/dao
mv phplib/dao/news/SysMsg.php hknew/dao/news
mv phplib/dao/misuser/PublishTask.php hknew/dao/misuser
mv phplib/dao/misuser/SwitchStrategy.php hknew/dao/misuser
## ds
mv phplib/ds/friend hknew/ds
mv phplib/ds/misstrategy hknew/ds
mv phplib/ds/news hknew/ds
mv phplib/ds/publish hknew/ds
mv phplib/ds/spam hknew/ds
mv phplib/ds/tiku hknew/ds
mv phplib/ds/user hknew/ds
mv phplib/ds/video hknew/ds
## service
mv phplib/service/message hknew/service
mv phplib/service/sms hknew/service
mv phplib/service/ActsCtrl.php hknew/service
mv phplib/service/Adapt.php hknew/service
mv phplib/service/Bos.php hknew/service
mv phplib/service/CUGMap.php hknew/service
mv phplib/service/Db.php hknew/service
mv phplib/service/IdAlloc.php hknew/service
mv phplib/service/IpAddress.php hknew/service
mv phplib/service/Lcs.php hknew/service
mv phplib/service/Memcached.php hknew/service
mv phplib/service/Message.php hknew/service
mv phplib/service/MsgStore.php hknew/service
mv phplib/service/Nmq.php hknew/service
mv phplib/service/Oauth.php hknew/service
mv phplib/service/Pay.php hknew/service
mv phplib/service/Push.php hknew/service
mv phplib/service/Redis.php hknew/service
mv phplib/service/RedisLock.php hknew/service
mv phplib/service/Rpc.php hknew/service
mv phplib/service/SmsCommon.php hknew/service
mv phplib/service/SwStrategy.php hknew/service
mv phplib/service/SysMessage.php hknew/service
mv phplib/service/TokenCoin.php hknew/service
mv phplib/service/Ucloud.php hknew/service
mv phplib/service/Uuap.php hknew/service
mv phplib/service/Yun.php hknew/service
cp phplib/service/Coupon.php hknew/service
mv phplib/service/CourseCommand.php hknew/service
mv phplib/service/RecommandStrategy.php hknew/service
## util
mv phplib/util/Category.php hknew/util
mv phplib/util/Client.php hknew/util
mv phplib/util/Exception.php hknew/util
mv phplib/util/ExceptionCodes.php hknew/util
mv phplib/util/Filter.php hknew/util
mv phplib/util/Host.php hknew/util
mv phplib/util/HttpMulti.php hknew/util
mv phplib/util/IdCrypt.php hknew/util
mv phplib/util/Image.php hknew/util
mv phplib/util/Ip.php hknew/util
mv phplib/util/Level.php hknew/util
mv phplib/util/Log.php hknew/util
mv phplib/util/Rc4.php hknew/util
mv phplib/util/Shencelog.php hknew/util
mv phplib/util/StrCrypt.php hknew/util
mv phplib/util/Tools.php hknew/util
mv phplib/util/TransIdMutex.php hknew/util
