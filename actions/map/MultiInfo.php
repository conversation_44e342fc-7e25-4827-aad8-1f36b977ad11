<?php
/**
 * @deprecated
 * @befie   获取字典。web和海鸥都用了一个，已经让前端修改成 /lxjxcrm/schoolcustomer/getconfig
 * @file    actions/area/List.php
 * <AUTHOR>
 * @version 1.0
 * @date    2020-05-21
 */
class Action_MultiInfo extends Lxjxcrm_Common_BaseAction
{

    public function invoke()
    {
        $arrInput = [];
        $validator = new Lxjxlib_Util_Validator($this->_requestParam);
        $arrInput['name'] = $validator->required()->validate('name');

        if ($validator->hasErrors()) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::PARAM_ERROR, '', [], Lxjxlib_Const_Exception::NOTICE);
        }

        // web和海鸥都用了一个，已经让前端修改成 /lxjxcrm/schoolcustomer/getconfig
        /*
        https://lxjxmis-lxjx-cc.suanshubang.cc/lxjxcrm/map/multiinfo?appId=pccrm&os=pc&name=[%22school_level%22]&type=3
        {
          "errNo": 0,
          "errstr": "success",
          "data": {
            "school_level": {
              "id": 2,
              "name": "school_level",
              "content": "[{\"id\": 1,\"name\": \"市\"},{\"id\": 3,\"name\": \"县\"},{\"id\": 5,\"name\": \"乡\"}]",
              "description": "学校等级",
              "type": 3,
              "operator": "liangdongfang",
              "status": 1,
              "createTime": 1598518121,
              "updateTime": 1598518121
            }
          }
        }
         */

        Hk_Util_Log::start('lxjxcrm_map_multiinfo');
        $objPage = new Service_Page_Map_MultiInfo();
        $ret = $objPage->execute($arrInput);
        Hk_Util_Log::stop('lxjxcrm_map_multiinfo');

        return $ret;
    }
}
