<?php

/**
 * @befie   获取登录人的人员数据
 * @file    actions/user/UserInfo.php
 * <AUTHOR>
 * @version 1.0
 * @date    2020-03-05
 */
class Action_UserInfo extends Lxjxcrm_Common_BaseAction
{
    protected $_isCheckLogin    = true;
    protected $_checkMenuAuth   = true;
    protected $_checkDataAuth   = true;

    public function invoke()
    {
        $arrInput['act'] = 'info';
        Hk_Util_Log::start('lxjxcrm_user_userinfo');
        $objPs = new Service_Page_User_User();
        $arrOutPut = $objPs->execute($arrInput, $this->_userInfo);

        // 钉钉前端页面必须要有该字段，先兼容保留着，等前端有时间了再删除
        if ($arrOutPut && 0 < $arrOutPut['uid']) {
            $arrOutPut['eduStage'] = [];
        }

        $arrOutPut['menulist'] = $this->_menuList;
        $arrOutPut['datalist'] = $this->_authData;
        Hk_Util_Log::stop('lxjxcrm_user_userinfo');

        return $arrOutPut;
    }
}
