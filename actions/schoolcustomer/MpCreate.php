<?php

/**************************************************************************
 *
 * Copyright (c) 2019 Zuoyebang.com, Inc. All Rights Reserved
 *
 * @file    Create.php
 * @date    2020-03-09
 *
 **************************************************************************/
class Action_MpCreate extends Lxjxcrm_Common_BaseAction
{
    /**
     * 时间日志key
     */
    const LOG_KEY_TIME = 'lxjxcrm_schoolcustomer_mpcreate';

    /**
     * @var     $_retModel
     * @desc    接口模型
     */
    protected $_retModel = array(
        'res'      => 1, // 原来的API设计成了1为成功
        'msg'      => 'success',
        'schoolId' => 0,
    );

    protected $_isCheckLogin    = true;
    protected $_checkDataAuth   = true;
    protected $_checkApiAuth    = true;
    protected $_isRecordUserLog = true;

    protected function _getCreateFields(Lxjxlib_Util_Validator $validator)
    {
        $inputs = array();

        // 学校名称
        $inputs['schoolName'] = strval(trim($validator->required()->notEmpty()->validate('name', '')));
        if (0 == strlen($inputs['schoolName'])) {
            return 'name';
        }

        // 学校别名
        $inputs['simpleName'] = strval(trim($validator->maxMblength(25)->validate('simpleName', '')));

        // 学校性质
        $inputs['schoolNature'] = intval($validator->required()->integer()->validate('nature', 0));
        if (0 == $inputs['schoolNature']) {
            return 'nature';
        }

        // 学部
        $inputs['eduPhase'] = intval($validator->required()->integer()->validate('phase', 0));
        if (0 == $inputs['eduPhase']) {
            return 'phase';
        }

        // 省份ID
        $inputs['provinceId'] = intval($validator->required()->integer()->validate('provinceId', 0));
        if (0 == $inputs['provinceId']) {
            return 'provinceId';
        }

        // 省份名称
        $inputs['provinceName'] = strval(trim($validator->required()->notEmpty()->validate('provinceName', '')));
        if (0 == strlen($inputs['provinceName'])) {
            return 'provinceName';
        }

        // 市ID
        $inputs['cityId'] = intval($validator->required()->integer()->validate('cityId', 0));
        if (0 == $inputs['cityId']) {
            return 'cityId';
        }

        // 市名称
        $inputs['cityName'] = strval(trim($validator->required()->notEmpty()->validate('cityName', '')));
        if (0 == strlen($inputs['cityName'])) {
            return 'cityName';
        }

        // 县ID
        $inputs['countyId'] = intval($validator->required()->integer()->validate('countyId', 0));
        if (0 == $inputs['countyId']) {
            return 'countyId';
        }

        // 县名称
        $inputs['countyName'] = strval(trim($validator->required()->notEmpty()->validate('countyName', '')));
        if (0 == strlen($inputs['countyName'])) {
            return 'countyName';
        }

        // 详细地址
        $inputs['address'] = strval(trim($validator->required()->notEmpty()->validate('address', '')));
        if (0 == strlen($inputs['address'])) {
            return 'address';
        }

        // 经度
        $inputs['longitude'] = floatval($validator->required()->notEmpty()->validate('longitude', 0));
        if (0.1 > $inputs['longitude']) {
            return 'longitude';
        }

        // 纬度
        $inputs['latitude'] = floatval($validator->required()->notEmpty()->validate('latitude', ''));
        if ((0.1 > $inputs['latitude'])) {
            return 'latitude';
        }

        // 学校等级
        $inputs['schoolLevel'] = intval($validator->required()->integer()->validate('level', ''));
        if (0 == $inputs['schoolLevel']) {
            return 'level';
        }

        // 学校规模
        $inputs['schoolScale'] = intval($validator->required()->integer()->validate('scale', 0));
        if (0 == $inputs['schoolScale']) {
            return 'scale';
        }

        // 住宿情况
        $inputs['dormitoryRatio'] = intval(trim($validator->required()->notEmpty()->validate('dormitoryRatio', '0'), '%'));

        // 是否来自转介绍
        $inputs['isIntroduce'] = intval($validator->required()->validate('isIntroduce'));

        // 考试时间
        $inputs['examDates'] = strval($validator->notEmpty()->validate('examDates', ''));

        $inputs['namePya'] = Lxjxlib_Util_PinYin::abbr($inputs['schoolName']);

        return $inputs;
    }

    protected function invoke()
    {
        // 冗余的角色权限控制。应该已经使用_checkApiAuth来控制了。暂时不抽象到BaseAction了。
        $bHasAuth = Lxjxlib_Const_Crm_User::chkHasAuthSchoolCustomerManagement($this->_userInfo['role']);
        if (!$bHasAuth) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::CRM_USER_AUTH_LIMIT, '请确认账号角色', ['roleName' => $this->_userInfo['role']], Lxjxlib_Const_Exception::WARNING);
        }

        $validator = new Lxjxlib_Util_Validator($this->_requestParam);

        $createFields = $this->_getCreateFields($validator);

        if (!is_array($createFields)) {
            $this->_retModel['res'] = Lxjxcrm_ExceptionCodes::REQUEST_PARAM_LACK;
            $this->_retModel['msg'] = Lxjxlib_Const_ExceptionCodes::getErrMsg($this->_retModel['res']) . $createFields;
            return $this->_retModel;
        }

        $schoolScope = $validator->integer()->validate('scope', Lxjxlib_Const_Crm_School::SCHOOL_SCOPE_PUBLIC);

        Hk_Util_Log::start(self::LOG_KEY_TIME);

        $customerService = new Service_Page_School_Customer();
        $resPs           = $customerService->mpCreateSchool($createFields, $this->_userInfo, $schoolScope);
        if (is_int($resPs)) {
            $resPsRes = $resPs;
        } elseif (is_array($resPs)) {
            if (isset($resPs['errno'])) {
                $resPsRes = $resPs['errno'];
            }
            if (isset($resPs['schoolId'])) {
                $this->_retModel['schoolId'] = $resPs['schoolId'];
            }
            if (isset($resPs['schoolList'])) {
                $this->_retModel['schoolList'] = $resPs['schoolList'];
            }
        }
        $this->_retModel['res'] = Lxjxcrm_ExceptionCodes::isSuccess($resPsRes)? 1 : $resPsRes;
        $this->_retModel['msg'] = Lxjxlib_Const_ExceptionCodes::getErrMsg($resPsRes);

        Hk_Util_Log::stop(self::LOG_KEY_TIME);

        return $this->_retModel;
    }

}
