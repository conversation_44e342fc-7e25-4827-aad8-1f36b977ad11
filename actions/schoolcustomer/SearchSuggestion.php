<?php
/**
 * @befie “学校”输入框-获取学校可选列表
 */
class Action_SearchSuggestion extends Lxjxcrm_Common_BaseAction
{
    // 时间日志key
    const LOG_KEY_TIME      = 'lxjxcrm_schoolcustomer_searchsuggestion';

    // 返回结构体
    protected $_retModel    = array(
        'list'  => array(),
    );

    protected $_isCheckLogin  = true;

    public function invoke()
    {
        $validator = new Lxjxlib_Util_Validator($this->_requestParam);
        $searchName = strval($validator->maxMblength(20)->validate('searchName', ''));
        $pn = intval($validator->integer()->validate('pn', 0));
        $rn = intval($validator->integer()->validate('rn', 20));
        if ($validator->hasErrors()) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::PARAM_ERROR, $validator->getAllErrors(), [], Lxjxlib_Const_Exception::DEBUG);
        }

        $queryFields = [];
        if ($searchName) {
            $queryFields['searchName'] = $searchName;
        }
        $pn = (0 <= $pn)? $pn : 0;
        $rn = (0 < $rn && 100 >= $rn)? $rn : 20;

        Hk_Util_Log::start(self::LOG_KEY_TIME);

        $customerService = new Service_Page_School_Customer();

        $arrUser = [];
        // 获取学校数据
        $resList = $customerService->getSchoolSuggestionList($arrUser, $queryFields, $pn, $rn);
        if ($resList) {
            foreach ($resList as $valItem) {
                $this->_retModel['list'][] = [
                    'schoolId'   => $valItem['schoolId'],
                    'schoolName' => $valItem['schoolName'],
                ];
            }
        }

        Hk_Util_Log::stop(self::LOG_KEY_TIME);
        return $this->_retModel;
    }

}
