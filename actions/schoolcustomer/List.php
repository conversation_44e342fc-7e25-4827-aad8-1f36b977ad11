<?php
/**************************************************************************
 *
 * Copyright (c) 2019 Zuoyebang.com, Inc. All Rights Reserved
 *
 * @file    List.php
 * @date    2020-03-07
 *
 **************************************************************************/

class Action_List extends Lxjxcrm_Common_BaseAction
{
    /**
     * 时间日志key
     */
    const LOG_KEY_TIME      = 'lxjxcrm_schoolcustomer_list';

    /**
     * @var     $_retModel
     * @desc    接口模型
     */
    protected $_retModel    = array(
        'total'   => 0,
        'list'    => array(),
        'buttons' => array( // 控制页面上的按钮展现,1为展现
            'create'   => 0, // 创建
            'modify'   => 0, // 修改
            'delete'   => 0, // 删除
            'allocate' => 0, // 分配(含批量)
            'transfer' => 0, // 转移(含批量)
        ),
    );

    protected $_isCheckLogin  = true;
    protected $_checkDataAuth = true;

    public function invoke()
    {
        ini_set('memory_limit', '1536M');

        $validator = new Lxjxlib_Util_Validator($this->_requestParam);

        $queryFields = [];

        // 学校id
        $schoolId = intval($validator->notEmpty()->validate('schoolId', 0));
        if (0 < $schoolId) {
            $queryFields['schoolId'] = $schoolId;
        }
        // 检索学校名称
        $schoolName = strval($validator->notEmpty()->validate('searchName', ''));
        if (0 < strlen($schoolName)) {
            $queryFields['schoolName'] = $schoolName;
        }
        // 学校别名
        $simpleName = strval($validator->notEmpty()->validate('simpleName', ''));
        if (0 < strlen($simpleName)) {
            $queryFields['simpleName'] = $simpleName;
        }
        // 认领人姓名
        $claimantUid = intval($validator->notEmpty()->validate('claimantUid', 0));
        if (0 < $claimantUid) {
            $queryFields['claimantUid'] = $claimantUid;
        }
        // 认领人姓名
        $claimantName = strval($validator->notEmpty()->validate('claimantName', ''));
        if (0 < strlen($claimantName)) {
            $queryFields['claimantName'] = $claimantName;
        }
        // 住宿情况，有查询0的需要，比如传入 0 或 0%，不需要检索该字段时应传入空字符串
        $dormitoryRatio = $validator->notEmpty()->validate('dormitory', '');
        if (0 < strlen($dormitoryRatio)) {
            $queryFields['dormitoryRatio'] = intval($dormitoryRatio);
        }
        // 地区筛选-省，支持多省份
        $strProvinceId = strval($validator->integer()->validate('provinceId', 0));
        if (!empty($strProvinceId)) {
            $queryFields['provinceId'] = array();
            $strProvinceId = explode(',', $strProvinceId);
            foreach ($strProvinceId as $provIndex => $provId) {
                $queryFields['provinceId'][$provIndex] = intval($provId);
            }
        }
        // 地区筛选-市，支持多城市
        $strCityId = strval($validator->integer()->validate('cityId', 0));
        if (!empty($strCityId)) {
            $queryFields['cityId'] = array();
            $strCityId = explode(',', $strCityId);
            foreach ($strCityId as $cityIndex => $cityId) {
                $queryFields['cityId'][$cityIndex] = intval($cityId);
            }
        }
        // 地区筛选-县，支持多区县
        $strCountyId = strval($validator->integer()->validate('countyId', 0));
        if (!empty($strCountyId)) {
            $queryFields['countyId'] = array();
            $strCountyId = explode(',', $strCountyId);
            foreach ($strCountyId as $countyIndex => $countyId) {
                $queryFields['countyId'][ $countyIndex ] = intval($countyId);
            }
        }
        // 学校性质筛选
        $schoolNature = intval($validator->integer()->validate('nature', 0));
        if (0 < $schoolNature) {
            $queryFields['schoolNature'] = $schoolNature;
        }
        // 认领状态筛选
        $claimStatus = intval($validator->integer()->validate('isClaimed', 0));
        if (0 < $claimStatus) {
            $queryFields['claimStatus'] = $claimStatus;
        }
        // 教育阶段筛选
        $eduPhase = intval($validator->integer()->validate('eduPhase', 0));
        if (0 < $eduPhase) {
            $queryFields['eduPhase'] = $eduPhase;
        }

        // 导出文件标示
        $isExport = intval($validator->integer()->validate('export', 0));
        // 查询范围--公海/私海
        $schoolScope = intval($validator->integer()->validate('scope', 1));
        // 翻页
        $pn = intval($validator->integer()->validate('pn', 0));
        $rn = intval($validator->integer()->validate('rn', 10));

        Hk_Util_Log::start(self::LOG_KEY_TIME);

        $customerService = new Service_Page_School_Customer();

        if (0 != $isExport) {
            $customerService->exportSchoolList($queryFields, $this->_userInfo, $schoolScope);
        }

        // 控制页面上的按钮展现
        $this->_retModel['buttons'] = Lxjxcrm_Auth::getBtnDisplayAuthForSchoolCustomerListPage($this->_userInfo['role']);

        // 获取学校数量
        $this->_retModel['total'] = $customerService->getSchoolAmount($queryFields, $this->_userInfo, $schoolScope);
        if (0 == $this->_retModel['total'] || $pn * $rn >= $this->_retModel['total']) {
            return $this->_retModel;
        }

        // 获取学校数据
        $this->_retModel['list'] = $customerService->getSchoolsList($queryFields, $this->_userInfo, $schoolScope, $pn, $rn);

        Hk_Util_Log::stop(self::LOG_KEY_TIME);
        return $this->_retModel;
    }

}
