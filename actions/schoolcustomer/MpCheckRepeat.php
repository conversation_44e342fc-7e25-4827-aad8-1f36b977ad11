c<?php

/**
 * @desc    移动端学校验重
 * @file    MpCheckRepeat.php
 * <AUTHOR>
 * @date    2021-04-28 16:34
 */
class Action_MpCheckRepeat extends Lxjxcrm_Common_BaseAction
{
    /**
     * 时间日志key
     */
    const LOG_KEY_TIME = 'lxjxcrm_schoolcustomer_mpcheckrepeat';

    /**
     * @var     $_retModel
     * @desc    接口模型
     */
    protected $_retModel = array(
        'res' => 1, // 原来的API设计成了1为成功
        'msg' => '',
    );

    protected $_isCheckLogin  = true;
    protected $_checkDataAuth = true;
    protected $_checkApiAuth  = true;

    protected function invoke()
    {
        // 冗余的角色权限控制。应该已经使用_checkApiAuth来控制了。暂时不抽象到BaseAction了。
        $bHasAuth = Lxjxlib_Const_Crm_User::chkHasAuthSchoolCustomerManagement($this->_userInfo['role']);
        if (!$bHasAuth) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::CRM_USER_AUTH_LIMIT, '请确认账号角色', ['roleName' => $this->_userInfo['role']], Lxjxlib_Const_Exception::WARNING);
        }

        $inputs = array();

        $validator = new Lxjxlib_Util_Validator($this->_requestParam);

        // 学校名称
        if (!empty($validator->required()->notEmpty()->validate('name', ''))) {
            $inputs['schoolName'] = strval($validator->notEmpty()->validate('name', ''));
        }
        if (!$inputs['schoolName']) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::PARAM_ERROR, "学校名不能为空", [], Lxjxlib_Const_Exception::DEBUG);
        }
        // 学校别名
        if (!empty($validator->maxMblength(25)->validate('simpleName', ''))) {
            $inputs['simpleName'] = strval(trim($validator->maxMblength(25)->validate('simpleName', '')));
        }
        // 学部
        if (!empty($validator->required()->integer()->validate('phase', 0))) {
            $inputs['eduPhase'] = intval($validator->integer()->validate('phase', 0));
        }
        // 省
        if (!empty($validator->required()->integer()->validate('provinceId', 0))) {
            $inputs['provinceId'] = intval($validator->integer()->validate('provinceId', 0));
        }
        // 市
        if (!empty($validator->required()->integer()->validate('cityId', 0))) {
            $inputs['cityId'] = intval($validator->integer()->validate('cityId', 0));
        }
        // 县
        if (!empty($validator->required()->integer()->validate('countyId', 0))) {
            $inputs['countyId'] = intval($validator->integer()->validate('countyId', 0));
        }
        if (0 >= $inputs['countyId']) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::PARAM_ERROR, "区域ID不能为空", [], Lxjxlib_Const_Exception::DEBUG);
        }
        if ($validator->hasErrors()) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::PARAM_ERROR, $validator->getAllErrors(), [], Lxjxlib_Const_Exception::NOTICE);
        }

        Hk_Util_Log::start(self::LOG_KEY_TIME);

        $customerService        = new Service_Page_School_Customer();
        $ret                    = $customerService->mpCheckRepeat($inputs, $this->_userInfo);
        $this->_retModel['res'] = Lxjxcrm_ExceptionCodes::isSuccess($ret['errno'])? 1 : $ret['errno'];
        $this->_retModel['msg'] = Lxjxlib_Const_ExceptionCodes::getErrMsg($ret['errno']);

        if (is_array($ret) && $ret['schoolList']) {
            $this->_retModel['schoolList'] = $ret['schoolList'];
        }

        Hk_Util_Log::stop(self::LOG_KEY_TIME);
        return $this->_retModel;
    }

}