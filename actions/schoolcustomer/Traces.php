<?php
/**************************************************************************
 *
 * Copyright (c) 2019 Zuoyebang.com, Inc. All Rights Reserved
 *
 * @file    Traces.php
 * @date    2020-03-09
 *
 **************************************************************************/
class Action_Traces extends Lxjxcrm_Common_BaseAction
{
    /**
     * 时间日志key
     */
    const LOG_KEY_TIME = 'lxjxcrm_schoolcustomer_traces';

    /**
     * @var     $_retModel
     * @desc    接口模型
     */
    protected $_retModel = array(
        'total' => 0,
        'list'  => array(),
    );

    protected $_isCheckLogin    = true;
    protected $_checkDataAuth   = true;

    protected function invoke()
    {
        $validator = new Lxjxlib_Util_Validator($this->_requestParam);
        $schoolId = $validator->integer()->validate('schoolId', 0);
        $pn = $validator->integer()->validate('pn', 0);
        $rn = $validator->integer()->validate('rn', 10);

        Hk_Util_Log::start(self::LOG_KEY_TIME);
        $customerService = new Service_Page_School_Customer();
        $this->_retModel['total'] = $customerService->getSchoolTraceAmount($schoolId);
        if (0 === $this->_retModel['total']) {
            Hk_Util_Log::stop(self::LOG_KEY_TIME);
            return $this->_retModel;
        }
        $this->_retModel['list'] = $customerService->getSchoolTraceList($schoolId, $pn, $rn);
        Hk_Util_Log::stop(self::LOG_KEY_TIME);
        return $this->_retModel;
    }

}
