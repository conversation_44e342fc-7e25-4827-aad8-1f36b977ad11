<?php
/**
 * @befie   创建学校-获取可选配置项
 */
class Action_GetConfig extends Lxjxcrm_Common_BaseAction
{
    // 时间日志key
    const LOG_KEY_TIME = 'lxjxcrm_schoolcustomer_getconfig';

    // 返回结构体
    protected $_retModel = array(
        // 学校等级列表
        'school_level' => [
            'name'        => 'school_level',
            'content'     => "[{\"id\":1,\"name\":\"市\"},{\"id\":3,\"name\":\"县\"},{\"id\":5,\"name\":\"乡\"}]", // 原接口就返回这个值
            'description' => '学校等级',

        ],
    );

    protected $_isCheckLogin  = true;
    protected $_checkDataAuth = true;

    public function invoke()
    {
        $arrInput = [];
        $validator = new Lxjxlib_Util_Validator($this->_requestParam);
        if ($validator->hasErrors()) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::PARAM_ERROR, '', [], Lxjxlib_Const_Exception::NOTICE);
        }

        $arrSchoolLevel = [
            [
                'id'   => Lxjxcrm_Const_School::LEVEL_CITY_EXEMPLARY,
                'name' => Lxjxcrm_Const_School::getLevelName(Lxjxcrm_Const_School::LEVEL_CITY_EXEMPLARY),
            ],
            [
                'id'   => Lxjxcrm_Const_School::LEVEL_COUNTY_EXEMPLARY,
                'name' => Lxjxcrm_Const_School::getLevelName(Lxjxcrm_Const_School::LEVEL_COUNTY_EXEMPLARY),
            ],
            [
                'id'   => Lxjxcrm_Const_School::LEVEL_TOWN,
                'name' => Lxjxcrm_Const_School::getLevelName(Lxjxcrm_Const_School::LEVEL_TOWN),
            ],
        ];

        /* 暂时不需要ps
        Hk_Util_Log::start(self::LOG_KEY_TIME);
        Hk_Util_Log::stop(self::LOG_KEY_TIME);
        */

        $this->_retModel['school_level']['content'] = json_encode($arrSchoolLevel);

        return $this->_retModel;
    }
}
