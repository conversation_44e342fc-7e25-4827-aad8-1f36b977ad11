<?php
/**************************************************************************
 *
 * Copyright (c) 2019 Zuoyebang.com, Inc. All Rights Reserved
 *
 * @file    Transfer.php
 * @date    2020-08-31
 *
 **************************************************************************/

/**
 * Class        Action_Transfer
 * @date        2020-09-01
 * @desc        转移学校
 */
class Action_Transfer extends Lxjxcrm_Common_BaseAction
{
    /**
     * 时间日志key
     */
    const LOG_KEY_TIME = 'lxjxcrm_schoolcustomer_transfer';

    /**
     * @var     $_retModel
     * @desc    接口模型
     */
    protected $_retModel = array(
        'amount' => 0,
    );

    protected $_isCheckLogin    = true;
    protected $_checkDataAuth   = true;
    protected $_checkApiAuth    = true;
    protected $_isRecordUserLog = true;

    protected function invoke()
    {
        // 冗余的角色权限控制。应该已经使用_checkApiAuth来控制了。暂时不抽象到BaseAction了。
        $bHasAuth = Lxjxlib_Const_Crm_User::chkHasAuthSchoolCustomerManagement($this->_userInfo['role']);
        if (!$bHasAuth) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::CRM_USER_AUTH_LIMIT, '请确认账号角色', ['roleName' => $this->_userInfo['role']], Lxjxlib_Const_Exception::WARNING);
        }

        $validator = new Lxjxlib_Util_Validator($this->_requestParam);

        // 检查转移方式：1表示按条件转移，0或不传表示按schoolIds转移
        $transferByCondition = intval($validator->integer()->validate('transferByCondition', 0));
        $toUid = (int)$validator->required()->validate('toUid');

        if (!$toUid) {
            throw new Hk_Util_Exception(Lxjxcrm_ExceptionCodes::REQUEST_PARAM_VALUE, 'toUid参数不能为空');
        }

        Hk_Util_Log::start(self::LOG_KEY_TIME);
        $customerService = new Service_Page_School_Customer();

        if ($transferByCondition) {
            // 按条件转移全部学校
            $queryFields = $this->_buildQueryFields($validator);
            $schoolScope = intval($validator->integer()->validate('scope', 1));
            $this->_retModel['amount'] = $customerService->transferSchoolByCondition($queryFields, $toUid, $this->_userInfo, $schoolScope);
        } else {
            // 按schoolIds转移（原有逻辑）
            $schoolIds = trim($validator->required()->validate('schoolId'));
            if ($validator->hasErrors()) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, $validator->getAllErrors());
            }

            $schoolIds = !empty($schoolIds) ? explode(',', $schoolIds) : array();
            if (!is_array($schoolIds) || empty($schoolIds)) {
                throw new Hk_Util_Exception(Lxjxcrm_ExceptionCodes::REQUEST_PARAM_VALUE, 'schoolId参数不能为空');
            }

            $this->_retModel['amount'] = $customerService->transferSchool($schoolIds, $toUid);
        }

        Hk_Util_Log::stop(self::LOG_KEY_TIME);
        return $this->_retModel;
    }

    /**
     * 构建查询条件（复用list接口的查询条件逻辑）
     * @param Lxjxlib_Util_Validator $validator
     * @return array
     */
    private function _buildQueryFields($validator)
    {
        $queryFields = [];

        // 学校id
        $schoolId = intval($validator->notEmpty()->validate('schoolId', 0));
        if (0 < $schoolId) {
            $queryFields['schoolId'] = $schoolId;
        }
        // 检索学校名称
        $schoolName = strval($validator->notEmpty()->validate('searchName', ''));
        if (0 < strlen($schoolName)) {
            $queryFields['schoolName'] = $schoolName;
        }
        // 学校别名
        $simpleName = strval($validator->notEmpty()->validate('simpleName', ''));
        if (0 < strlen($simpleName)) {
            $queryFields['simpleName'] = $simpleName;
        }
        // 认领人姓名
        $claimantUid = intval($validator->notEmpty()->validate('claimantUid', 0));
        if (0 < $claimantUid) {
            $queryFields['claimantUid'] = $claimantUid;
        }
        // 认领人姓名
        $claimantName = strval($validator->notEmpty()->validate('claimantName', ''));
        if (0 < strlen($claimantName)) {
            $queryFields['claimantName'] = $claimantName;
        }
        // 住宿情况，有查询0的需要，比如传入 0 或 0%，不需要检索该字段时应传入空字符串
        $dormitoryRatio = $validator->notEmpty()->validate('dormitory', '');
        if (0 < strlen($dormitoryRatio)) {
            $queryFields['dormitoryRatio'] = intval($dormitoryRatio);
        }
        // 地区筛选-省，支持多省份
        $strProvinceId = strval($validator->integer()->validate('provinceId', 0));
        if (!empty($strProvinceId)) {
            $queryFields['provinceId'] = array();
            $strProvinceId = explode(',', $strProvinceId);
            foreach ($strProvinceId as $provIndex => $provId) {
                $queryFields['provinceId'][$provIndex] = intval($provId);
            }
        } else {
            throw new Hk_Util_Exception(Lxjxcrm_ExceptionCodes::REQUEST_PARAM_VALUE, 'provinceId参数不能为空');
        }
        // 地区筛选-市，支持多城市
        $strCityId = strval($validator->integer()->validate('cityId', 0));
        if (!empty($strCityId)) {
            $queryFields['cityId'] = array();
            $strCityId = explode(',', $strCityId);
            foreach ($strCityId as $cityIndex => $cityId) {
                $queryFields['cityId'][$cityIndex] = intval($cityId);
            }
        }
        // 地区筛选-县，支持多区县
        $strCountyId = strval($validator->integer()->validate('countyId', 0));
        if (!empty($strCountyId)) {
            $queryFields['countyId'] = array();
            $strCountyId = explode(',', $strCountyId);
            foreach ($strCountyId as $countyIndex => $countyId) {
                $queryFields['countyId'][ $countyIndex ] = intval($countyId);
            }
        }
        // 学校性质筛选
        $schoolNature = intval($validator->integer()->validate('nature', 0));
        if (0 < $schoolNature) {
            $queryFields['schoolNature'] = $schoolNature;
        }
        // 认领状态筛选
        $claimStatus = intval($validator->integer()->validate('isClaimed', 0));
        if (0 < $claimStatus) {
            $queryFields['claimStatus'] = $claimStatus;
        }
        // 教育阶段筛选
        $eduPhase = intval($validator->integer()->validate('eduPhase', 0));
        if (0 < $eduPhase) {
            $queryFields['eduPhase'] = $eduPhase;
        }

        return $queryFields;
    }

}
