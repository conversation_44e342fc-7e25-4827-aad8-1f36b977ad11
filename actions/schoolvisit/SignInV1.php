<?php

/**
 * @desc    签到
 * @file    SignInV1.php
 * <AUTHOR>
 * @date    2021-06-25 19:09
 */
class Action_SignInV1 extends Lxjxcrm_Common_BaseAction
{
    const LOG_KEY_TIME = 'lxjxcrm_schoolvisit_signinv1';

    // 接口模型
    protected $_retModel = array(
        'res' => Lxjxcrm_Const_Errors::ERROR_SUCCESS,
        'msg' => 'success',
    );

    protected $_isCheckLogin    = true;
    protected $_checkDataAuth   = true;
    protected $_isRecordUserLog = true;

    protected function invoke()
    {
        $validator = new Lxjxlib_Util_Validator($this->_requestParam);

        $signData = [
            'signinLongitude' => floatval($validator->notEmpty()->validate('longitude', 0)),
            'signinLatitude'  => floatval($validator->notEmpty()->validate('latitude', 0)),
            'signinPic'       => strval($validator->notEmpty()->validate('picUrl', '')),
            'signinAddress'   => strval($validator->notEmpty()->validate('address', '')),
        ];

        $schoolId = intval($validator->integer()->validate('schoolId', 0));
        $schemeId = intval($validator->integer()->validate('schemeId', 0));
        if (empty($schemeId) && empty($schoolId)) {
            throw new Lxjxlib_Const_Exception(Lxjxlib_Const_ExceptionCodes::PARAM_ERROR, 'schemeId,schoolId至少传一个', [], Lxjxlib_Const_Exception::NOTICE);
        }

        Hk_Util_Log::start(self::LOG_KEY_TIME);

        $visitService = new Service_Page_School_VisitV1();

        $this->_retModel = $visitService->signIn($schoolId, $schemeId, $signData, $this->_userInfo);

        $this->_retModel['msg'] = Lxjxcrm_Const_Errors::getErrorMsg($this->_retModel['res']);
        Hk_Util_Log::stop(self::LOG_KEY_TIME);
        return $this->_retModel;
    }
}