<?php

/**
 * @desc    导出拜访报告V1-小程序
 * @file    ExportMailV1.php
 * <AUTHOR>
 * @date    2021-06-30 11:13
 */
class Action_ExportMailV1 extends Lxjxcrm_Common_BaseAction
{
    // 时间日志key
    const LOG_KEY_TIME = 'lxjxcrm_schoolvisit_exportmailv1';
    // 接口模型
    protected $_retModel = [
        'res'  => Lxjxcrm_Const_Errors::ERROR_SUCCESS,
        'msg'  => 'success',
        'list' => [],
    ];
    // 验证登录
    protected $_isCheckLogin    = true;
    // 数据权限
    protected $_checkDataAuth   = true;

    protected function invoke()
    {
        $validator = new Lxjxlib_Util_Validator($this->_requestParam);
        // 部门ID
        $departId = intval($validator->required()->integer()->validate('departId', 1));
        // 日期
        $startDate = strval($validator->required()->integer()->validate('startDate', date('Ymd')));
        $endDate   = strval($validator->required()->integer()->validate('endDate', date('Ymd')));
        if ($validator->hasErrors()) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::PARAM_ERROR, $validator->getAllErrors(), [], Lxjxlib_Const_Exception::NOTICE);
        }
        # 时间闭区间
        $startTimestamp = strtotime($startDate);
        $endTimestamp   = strtotime($endDate . ' +1 day') - 1;
        if ($startTimestamp > $endTimestamp) {
            return $this->_retModel;
        }

        # 导出时间间隔最大7天
        if (($endTimestamp - $startTimestamp) > 3600 * 24 * 7) {
            $this->_retModel['res'] = Lxjxcrm_Const_Errors::ERROR_VISIT_EXPORT_TIME_LIMIT;
            $this->_retModel['msg'] = Lxjxcrm_Const_Errors::getErrorMsg($this->_retModel['res']);
            return $this->_retModel;
        }

        Hk_Util_Log::start(self::LOG_KEY_TIME);

        $statService = new Service_Page_School_VisitV1();
        $params      = [
            'creatorDepartId' => $departId,
            'signTimeStart'   => $startTimestamp,
            'signTimeEnd'     => $endTimestamp,
        ];

        $dataList = $statService->exportVisitSchemeListForMail($params, $this->_userInfo);

        Hk_Util_Log::stop(self::LOG_KEY_TIME);

        return $dataList;
    }
}