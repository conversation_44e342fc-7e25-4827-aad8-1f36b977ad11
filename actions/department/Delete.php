<?php

/**
 * @befie   crm组织架构相关
 * @file    actions/department/Delete.php
 * <AUTHOR>
 * @version 1.0
 * @date    2020-03-05
 */
class Action_Delete extends Lxjxcrm_Common_BaseAction
{
    protected $_isCheckLogin    = true;
    protected $_checkApiAuth    = true;
    protected $_isRecordUserLog = true;

    public function invoke()
    {
        $validator = new Lxjxlib_Util_Validator($this->_requestParam);
        $arrInput['departId'] = $validator->required()->digits()->validate('departId');
        if ($validator->hasErrors()) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::PARAM_ERROR, $validator->getAllErrors(), [], Lxjxlib_Const_Exception::NOTICE);
        }

        $arrInput['opId'] = $this->_userInfo['uid'];
        $arrInput['act'] = 'del';
        Hk_Util_Log::start('lxjxcrm_department_delete');
        $objPs = new Service_Page_Department_Department();
        $arrOutPut = $objPs->execute($arrInput);
        Hk_Util_Log::stop('lxjxcrm_department_delete');

        return $arrOutPut;
    }
}
