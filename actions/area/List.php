<?php

/**
 * @deprecated 应该之前是用于给蜂鸟页面的省市区学校筛选，先标记弃用, by 2024-08-19
 * @befie   获取省份城市map
 * @file    actions/area/List.php
 * <AUTHOR>
 * @version 1.0
 * @date    2020-05-21
 */
class Action_List extends Lxjxcrm_Common_BaseAction
{
    public function invoke()
    {
        $type = [
            Lxjxlib_Const_Crm_School::TYPE_PROVINCE,
            Lxjxlib_Const_Crm_School::TYPE_CITY,
            Lxjxlib_Const_Crm_School::TYPE_COUNTY,
            Lxjxlib_Const_Crm_School::TYPE_SCHOOL,
        ];
        $validator = new Lxjxlib_Util_Validator($this->_requestParam);
        $arrInput['type'] = $validator->required()->oneOf($type)->validate('type');
        $arrInput['pid'] = $validator->required()->validate('pid', 0);
        $arrInput['gradeId'] = $validator->validate('gradeId', 0);
        $arrInput['school'] = $validator->validate('school', '');

        $arrInput['eduStage'] = Hk_Util_Category::getLearningStageIdByGradeId($arrInput['gradeId']);
        if ($validator->hasErrors()) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::PARAM_ERROR, $validator->getAllErrors(), [], Lxjxlib_Const_Exception::NOTICE);
        }
        Hk_Util_Log::start('lxjxcrm_area_list');
        $objPs = new Service_Page_Area_List();
        $arrOutPut = $objPs->execute($arrInput);
        Hk_Util_Log::stop('lxjxcrm_area_list');

        return $arrOutPut;
    }
}
