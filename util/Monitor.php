<?php
/**
 * @file   Monitor.php
 * <AUTHOR>
 * @date   2020/07/28 19:00
 * @brief  普罗米修斯监控 - 拉取指标数据后不会删除数据
 *         注意同一个集群，使用apcu存储数据时，拉取指标数据是否要删除数据要保持一致
 **/
class Lxjxlib_Util_Monitor
{
    protected static $namespace = MAIN_APP; // 默认namespaces是模块名
    protected static $adapter  = null;
    protected static $registry = null;

    /**
     * 自增counter类型的指标
     *
     * @date   2020/1/6 17:53
     * @param        $name
     * @param        $labels (指标label的key不可修改；示例：['subject' => 1, 'grade' => 5])
     * @param        $value
     * @param string $help
     * @return bool
     */
    public static function incCounter($name, $labels, $value, $help = '')
    {
        $namespace = self::$namespace;
        $logData   = compact('namespace', 'name', 'labels', 'value', 'help');
        try {
            $labelKeys   = array_keys($labels);
            $labelKeys   = array_map('strval', $labelKeys);
            $labelValues = array_values($labels);
            $labelValues = array_map('strval', $labelValues);

            $registry = self::getRegistry();
            if ($registry) {
                $counter = $registry->registerCounter(self::$namespace, $name, $help, $labelKeys);
                $counter->incBy($value, $labelValues);

                Bd_Log::addNotice("incCounterOk", json_encode($logData, JSON_UNESCAPED_UNICODE));
                return true;
            }
            Bd_Log::addNotice("incCounterErr", json_encode($logData, JSON_UNESCAPED_UNICODE));
            return false;
        } catch (Exception $e) {
            Oplib_Util_Log::alarm('lxjx', __CLASS__, __METHOD__, 'incCounter err,errMsg:' . $e->getMessage(), $logData);
            return false;
        }
    }

    /**
     * 暴露指标数据(请限制内网访问)
     *
     * @return string
     */
    public static function metrics($outputHeader = true)
    {
        $registry = self::getRegistry();
        try {
            $oRender = new Infra_Prometheus_Util_RenderTextFormat();
            $result  = $oRender->render($registry->getMetricFamilySamples());
            Bd_Log::addNotice("get_metrics_success", 1);
            $outputHeader && header('Content-type: ' . Infra_Prometheus_Util_RenderTextFormat::MIME_TYPE);
            return $result;
        } catch (\Exception $e) {
            Bd_Log::addNotice("get_metrics_fail", 1);
            Bd_Log::warning('metrics exception'.$e->getMessage());
            $outputHeader && header('Content-type: ' . Infra_Prometheus_Util_RenderTextFormat::MIME_TYPE);
            return '';
        }
    }

    public static function flushAPC()
    {
        $adapter = self::getAdapter();
        if ($adapter && method_exists($adapter, 'flushAPC')) {
            $adapter->flushAPC();
        }
    }
    ///////////////////////////////////////////////////////////////////////////////////
    private static function setApcuAdapter()
    {
        //扩展安装检查
        if (!extension_loaded('apcu')) {
            Lxjxlib_Util_Log::fatal(self::$namespace, __CLASS__, __METHOD__, '未安装apcu扩展', '');
            return false;
        }
        try {
            self::$adapter = new Infra_Prometheus_Storage_APCU();
        } catch (Exception $e) {
            Lxjxlib_Util_Log::fatal(self::$namespace, __CLASS__, __METHOD__, '实例化Infra_Prometheus_Storage_APCU失败', '');
            return false;
        }
        self::$registry = null;// 需要重新注册

        return true;
    }

    private static function getAdapter()
    {
        if (self::$adapter) {
            return self::$adapter;
        }
        self::setApcuAdapter();

        return self::$adapter;
    }

    protected static function getRegistry()
    {
        if (self::$registry) {
            return self::$registry;
        }
        $adapter = self::getAdapter();
        if (!$adapter) {
            return false;
        }
        try {
            self::$registry = new Infra_Prometheus_Metric_CollectorRegistry($adapter);
        } catch (Exception $e) {
            Lxjxlib_Util_Log::fatal(self::$namespace, __CLASS__, __METHOD__, '实例化Infra_Prometheus_Metric_CollectorRegistry失败', '');
            return false;
        }

        return self::$registry;
    }
}