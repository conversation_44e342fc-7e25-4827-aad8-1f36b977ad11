<?php

/**
 * <AUTHOR>
 * @date    2019/7/22 下午8:27
 * @brief   公共函数类
 */
class Lxjxlib_Util_Tools
{
    /**
     * 获取当前环境
     * @return string
     */
    public static function getEnv()
    {
        return (Hk_Util_Env::getRunEnv() == Hk_Util_Env::RunEnvTest) ? 'test' : 'prod';
    }

    /**
     * 判断当前环境是否测试环境
     * @return bool
     */
    public static function isTestEnv()
    {
        return Hk_Util_Env::getRunEnv() == Hk_Util_Env::RunEnvTest;
    }

    /**
     * 根据概率配置获取命中flag结果
     * $conf配置如下：
     * array(
     *     "flag" => chance(int),
     * );
     * @param array $randConf
     * @return mixed
     */
    public static function itemRand($randConf)
    {
        $hit = '';
        $cSum = array_sum($randConf);
        foreach ($randConf as $item => $chance) {
            $rand = mt_rand(1, $cSum);
            if ($rand <= $chance) {
                $hit = $item;
                break;
            } else {
                $cSum = max(0, $cSum - $chance);
            }
        }
        return $hit;
    }

    /**
     * 获取当前使用平均内存值
     * @return string
     */
    public static function getUsageMem()
    {
        return bcdiv(memory_get_usage(), 1048576, 2) . 'MB';
    }

    /**
     * 获取当前使用最高峰内存值
     * @return string
     */
    public static function getPeakMem()
    {
        return bcdiv(memory_get_peak_usage(), 1048576, 2) . 'MB';
    }

    /**
     * 检验isbn
     * @param $code
     * @return bool
     */
    public static function checkIsbn($code)
    {
        // 长度校验，必须为13位
        if (strlen($code) != 13) {
            return false;
        }
        // 构造检查，13位数字
        if (preg_match('/^[0-9]{13}$/', $code, $out) !== 1) {
            return false;
        }
        // 前缀有效性检查
        $prefix = array(
            '9787',     //中国大陆
            '978962',   //中国香港
            '97899937', //中国澳门
            '97899965', //中国澳门
            '978957',   //中国台湾
            '978986'    //中国台湾
        );
        $pos = -1;
        foreach ($prefix as $key) {
            $pos = strpos($code, $key);
            if ($pos === 0) {
                break;
            }
        }
        if ($pos !== 0) {
            return false;
        }
        // 校验位检查
        $total = 0;
        for ($i = 0; $i < 12; $i++) {
            $intNum = intval($code[$i]);
            if ($i % 2 == 0) {
                $total += $intNum;
            } else {
                $total += $intNum * 3;
            }
        }
        $mod = $total % 10;
        $check = strval((10 - $mod) % 10);
        if ($check != $code[12]) {
            return false;
        }
        return true;
    }

    /**
     * 手机号中间四位屏蔽
     * @param $phone
     * @return string|string[]|null
     */
    public static function formatPhone($phone)
    {
        // 不是手机号直接返回手机号
        if (!Hk_Util_Tools::checkPhoneFormat($phone)) {
            return $phone;
        }

        // 符合手机号规则, 遮挡中间四位
        $pattern = '/(\d{1,3})(\d{4,8})(\d{4,8})$/';
        $replacement = '${1}****${3}';
        $number = preg_replace($pattern, $replacement, $phone);
        if (empty($number)) {
            return $phone;
        }
        return $number;
    }

    /**
     * 身份证号屏蔽
     * @param $idNumber
     * @return string
     */
    public static function formatIdNumber($idNumber)
    {
        $replacement = '********';
        $start = substr($idNumber, 0, 6);
        $end = substr($idNumber, -4, 4);
        return $start . $replacement . $end;
    }

    /**
     * 检测php脚本是否在执行（仅限单台机器）
     * @param $command
     * @return bool
     */
    public static function isPeerRunning($command)
    {
        $shellCmd = "ps aux | grep 'php {$command}' | grep -v 'grep' | wc -l";

        $shellRes = shell_exec("$shellCmd");
        if (!empty($shellRes)) {
            $shellRes = (int)rtrim($shellRes, "\r\n");

            // 检查是否达到最大进程数量
            if ($shellRes > 1) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取服务器地址
     * @return string
     */
    public static function getDomainUrl()
    {
        $protocal = 'http';
        if (strstr($_SERVER['SCRIPT_URI'], 'https') !== false) {
            $protocal = 'https';
        }
        $url = $protocal . '://' . $_SERVER['HTTP_HOST'];
        return $url;
    }


    //驼峰命名转下划线命名
    public static function toUnderScore($str)
    {
        $dstr = preg_replace_callback('/([A-Z]+)/', function ($matchs) {
            return '_' . strtolower($matchs[0]);
        }, $str);
        return trim(preg_replace('/_{2,}/', '_', $dstr), '_');
    }

    //下划线命名到驼峰命名
    public static function toCamelCase($data)
    {
        foreach ($data as &$v) {
            $array = explode('_', $v);
            $result = $array[0];
            $len = count($array);
            if ($len > 1) {
                for ($i = 1; $i < $len; $i++) {
                    $result .= ucfirst($array[$i]);
                }
            }
            $v = $result;
        }
        return $data;
    }


    /**
     * 将键值数组中的键值用数据库表的列名做映射转换
     * @param mixed $arrRow 程序中使用的键值数组，格式为array('key' => value, ....)，
     *                              若该参数为字符串则直接返回
     * @param array $arrFieldsMap 字段和数据库表列名的映射数组
     * @return array 转换之后的数组
     */
    public static function mapRow($arrRow, $arrFieldsMap)
    {
        //参数的校验
        if (empty($arrRow) || !is_array($arrRow) || empty($arrFieldsMap)) {
            return $arrRow;
        }

        //将rows中的名称转换成映射表中对应的名称
        $ret = array();
        foreach ($arrRow as $field => $value) {
            if (isset($arrFieldsMap[$field]) && $field != $arrFieldsMap[$field]) {
                $ret[$arrFieldsMap[$field]] = $value;
                unset($arrRow[$field]);
            } else {
                $ret[$field] = $value;
            }
        }
        return $ret;
    }


    /**
     * 导出csv文件
     * @param $fileName string 文件名称
     * @param $title    array  导出csv文件的title
     * @param $list     array  导出数据
     * @param $keyMap   array  list中需要写入csv字段的key
     */
    public static function export($fileName, $title, $list, $keyMap)
    {
        ob_clean();
        header('Content-type: text/csv; charset=UTF-8');
        header('Content-Disposition: attachment; filename=' . $fileName); //指定下载文件的描述
        header("Content-Type: application/force-download");
        $bom = pack("H*", 'EFBBBF');
        $fp = fopen("php://output", "w");
        fwrite($fp, $bom);
        $row = $title;
        fputcsv($fp, $row);

        foreach ($list as $item) {
            $row = [];
            foreach ($keyMap as $k) {
                $row[] = $item[$k];
            }
            fputcsv($fp, $row);
        }
        fclose($fp);
        flush();
        exit;
    }

    /**
     * 将下划线命名转换为驼峰式命名
     * @param $data
     * @return array
     */
    public static function convertUnderline($data)
    {
        if (is_array($data)) {
            $ret = [];
            foreach ($data as $k => $v) {
                $str = str_replace(' ', '', lcfirst(ucwords(str_replace('_', ' ', $k))));
                $ret[$str] = $v;
            }

            return $ret;
        } else {
            $str = str_replace(' ', '', lcfirst(ucwords(str_replace('_', ' ', $data))));
            return $str;
        }
    }

    /**
     * 获得随机字符串
     * @param $len     int        需要的长度
     * @param bool $special 是否需要特殊符号
     * @return string          返回随机字符串
     */
    public static function getRandomStr($len, $special = false)
    {
        $chars = [
            "a",
            "b",
            "c",
            "d",
            "e",
            "f",
            "g",
            "h",
            "i",
            "j",
            "k",
            "l",
            "m",
            "n",
            "o",
            "p",
            "q",
            "r",
            "s",
            "t",
            "u",
            "v",
            "w",
            "x",
            "y",
            "z",
            "0",
            "1",
            "2",
            "3",
            "4",
            "5",
            "6",
            "7",
            "8",
            "9"
        ];

        if ($special) {
            $chars = array_merge($chars, [
                "!",
                "@",
                "#",
                "$",
                "?",
                "|",
                "{",
                "/",
                ":",
                ";",
                "%",
                "^",
                "&",
                "*",
                "(",
                ")",
                "-",
                "_",
                "[",
                "]",
                "}",
                "<",
                ">",
                "~",
                "+",
                "=",
                ",",
                "."
            ]);
        }

        $charsLen = count($chars) - 1;
        //打乱数组顺序
        shuffle($chars);
        $str = '';
        for ($i = 0; $i < $len; $i++) {
            //随机取出一位
            $str .= $chars[mt_rand(0, $charsLen)];
        }
        return $str;
    }

    public static function getPidUrl($pid, $domain = Lxjxlib_Const_Common::COS_DOMAIN_LXJX)
    {
        if ($pid[0] !== '/') {
            $pid = '/' . $pid;
        }

        return 'https://' . $domain . $pid;
    }

    /**
     * 拼接url参数
     * @param $url
     * @param $params
     * @return string
     */
    public static function joinUrlParams($url, $params)
    {
        $queryParams = http_build_query($params);
        if (strpos($url, '?') === false) {
            $queryUrl = $url . '?' . $queryParams;
        } else {
            $queryUrl = $url . '&' . $queryParams;
        }
        return $queryUrl;
    }

    /**
     * 获取请求URI
     * @return bool|string
     * @example /project/controller/action
     */
    public static function getRequestUri()
    {
        // 检测是否有对应的接口访问权限
        if (strpos($_SERVER['REQUEST_URI'], '?') === false) {
            $length = strlen($_SERVER['REQUEST_URI']);
        } else {
            $length = strpos($_SERVER['REQUEST_URI'], '?');
        }
        // 访问接口对应的地址
        $requestUri = substr($_SERVER['REQUEST_URI'], 0, $length);
        return $requestUri;
    }

    /**
     * 对课件filePath的课件名称部分进行rawurlencode编码（如果对整个filePath编码的话，下载时课件名称会是整个filePath）
     * @param $filePath
     * @return string
     */
    public static function getCwRawurlencodeFilePath($filePath)
    {
        $prevWordCnt = self::isTestEnv() ? 5 : 11;
        $needEncodeWords = substr($filePath, $prevWordCnt);
        $newPath = self::isTestEnv() ? Lxjxlib_Const_Common::COS_PREFIX_TEST : Lxjxlib_Const_Common::COS_PREFIX_DEFAULT;
        $newPath .= '/' . rawurlencode($needEncodeWords);

        return $newPath;
    }

    public static function getOffsetStart($pn, $rn)
    {
        return $pn * $rn;
    }

    /**
     * 通过Saf获取账号必要的请求上下文参数
     * @return array
     */
    public static function getReqCtx()
    {
        $req = Saf_SmartMain::getCgi()['request_param'];
        $appId = $req['appId'];
        $os = isset($req['os']) ? strval($req['os']) : 'pcweb';
        $ctx = array(
            'os'     => $os,
            'app_id' => $appId,
            'cuid'   => '',
            'vcname' => '',
            'device' => '',
        );
        if ('ios' === $os || 'android' === $os) {
            $ctx['cuid'] = strval($req['cuid']);
            $ctx['vcname'] = strval($req['vcname']);
            $ctx['device'] = strval($req['device']);
        }
        return $ctx;
    }

    /**
     * 生成lxjxuss
     *
     * @param int $uid 用户ID
     * @return string lxjxuss
     */
    public static function genUidLxjxuss($uid)
    {
        $uid = intval($uid);
        $os = isset($_REQUEST['os']) ? $_REQUEST['os'] : '';

        $osType = isset(Lxjxlib_Const_Lxjxuser::$OS_TYPE_MAP[$os]) ? Lxjxlib_Const_Lxjxuser::$OS_TYPE_MAP[$os] : 'unknown';
        $lxjxuss = pack('a16vvVVV', $uid, 0x029B, $osType, $uid & 0xFFFFFFFF, $uid >> 32, time());
        for ($i = 0; $i < 16; $i++) {
            // padding 48 byte
            $lxjxuss .= chr(48 + rand(0, 74));
        }

        $key = md5(Lxjxlib_Const_Lxjxuser::RC4_KEY_LXJXUSS);
        $lxjxuss = Hk_Util_Rc4::rc4($key, $lxjxuss);
        $lxjxuss = base64_encode($lxjxuss);

        // URL-safe Base64
        $lxjxuss = str_replace('+', '-', $lxjxuss);
        $lxjxuss = str_replace('/', '_', $lxjxuss);
        return 'lxjxuss_' . $lxjxuss;
    }

    /**
     * 驼峰转下划线
     * @param $value
     * @param string $delimiter
     * @return string
     */
    public static function snake($value, $delimiter = '_')
    {
        if (!ctype_lower($value)) {
            $value = preg_replace('/\s+/', '', $value);

            $value = strtolower(preg_replace('/(.)(?=[A-Z])/', '$1' . $delimiter, $value));
        }

        return $value;
    }


    /**
     * 来源是否是企业微信
     * @param $userAgent
     * @return bool
     */
    public static function isQw($userAgent)
    {
        if (strpos($userAgent, 'wxwork') !== false) {
            return true;
        }
        return false;
    }

    /**
     * 特殊字符串encode
     * 小程序个别特殊字符无法识别
     * 导致图片无法读取,不能整体urlencode会导致资源无法识别
     * 视频封面地址
     * 例如：[']
     * @param string
     * @return string
     */
    public static function coverEncode($path)
    {
        return rawurlencode($path);
    }

    /*
     * 编码文件名
     */
    public static function getRawurlencodeFilePath($filePath, $type = 'hotquestion')
    {
        $newPath = self::isTestEnv() ? Lxjxlib_Const_Common::COS_PREFIX_TEST . '/' . $type : $type;
        $newPath .= '/' . rawurlencode($filePath);

        return $newPath;
    }

    /**
     * 根据参数classify或sourceType获取来源business
     * @param $classify
     * @return false|int
     */
    public static function getBusiness($params)
    {
        if (!empty($params['classify'])) {
            $classify = intval($params['classify']);
        } elseif (!empty($params['sourceType'])) {
            $classify = intval($params['sourceType']);
        } else {
            throw new Lxjxlib_Const_Exception(Lxjxlib_Const_ExceptionCodes::PARAM_ERROR);
        }

        $business = intval($params['business']);

        //试卷类型 必传来源
        if ($classify == Lxjxlib_Const_Common::SOURCE_TYPE_EXAM && empty($business)) {
            throw new Lxjxlib_Const_Exception(Lxjxlib_Const_ExceptionCodes::PARAM_ERROR);
        }

        if ($classify == Lxjxlib_Const_Common::SOURCE_TYPE_PPT) {
            $business = Lxjxlib_Const_Common::BUSINESS_HKB;
        }

        if ($classify == Lxjxlib_Const_Common::SOURCE_TYPE_TID) {
            $business = Lxjxlib_Const_Common::BUSINESS_JTX;
        }

        if ($classify == Lxjxlib_Const_Common::SOURCE_TYPE_VLXJXUME) {
            $business = Lxjxlib_Const_Common::BUSINESS_JTX;
        }

        if ($classify == Lxjxlib_Const_Common::SOURCE_TYPE_VIDEO) {
            $business = Lxjxlib_Const_Common::BUSINESS_HKB;
        }

        if ($classify == Lxjxlib_Const_Common::SOURCE_TYPE_HOT_QUESTION) {
            $business = Lxjxlib_Const_Common::BUSINESS_LXJX;
        }
        return $business;
    }
    public static function saveCos($keyName, $strResult, $dir, $cosBucket = Lxjxlib_Const_Common::COS_CONFIG_NAME_LXJX, $domainUrl = Lxjxlib_Const_Common::COS_DOMAIN_LXJX)
    {
        if (empty($keyName) || empty($strResult) || empty($dir)) {
            return false;
        }
        $service = new Lxjxlib_Service_Tcos($cosBucket);

        if (Hk_Util_Env::RunEnvTest === Hk_Util_Env::getRunEnv()) {
            $key = "test/" . $dir . "/";
        } else {
            $key = $dir . '/';
        }

        $ret = $service->putObject($strResult, $key . $keyName);
        if (false === $ret) {
            throw new Lxjxlib_Const_Exception(Lxjxlib_Const_ExceptionCodes::UPLOAD_FILE_ERR);
        }

        $url = Lxjxlib_Util_Tools::getPidUrl($key . $keyName, $domainUrl);
        return $url;
    }

    /**
     * 判断是否存在Emoji表情符号
     * @param $str
     */
    public static function hasEmoji($str)
    {
        return preg_match(
            '/[\x{1F600}-\x{1F64F}\x{1F300}-\x{1F5FF}\x{1F680}-\x{1F6FF}\x{2600}-\x{26FF}\x{2700}-\x{27BF}]/u',
            $str
        ) > 0;
    }

    /**
     * 字符串转译
     * @param $str
     * @return array|int|mixed
     */
    public static function strTransfer($str)
    {
        if (is_scalar($str)) {
            $str = addslashes($str);
        }
        if (is_array($str)) {
            foreach ($str as &$val) {
                $val = self::strTransfer($val);
            }
        }

        return $str;
    }

    /**
     * 整数转译
     * @param $int
     * @return array|int|mixed
     */
    public static function intTransfer($int)
    {
        if (is_scalar($int)) {
            $int = intval($int);
        }
        if (is_array($int)) {
            foreach ($int as &$val) {
                $val = self::intTransfer($val);
            }
        }
        return $int;
    }
    public function sendMail($cosUrl, $filePath,$name)
    {
        if(empty($cosUrl) || empty($filePath) || empty($name)){
            return false;
        }
        //发送邮件附件
        $mailNum      = mt_rand(0, 10);
        $fromConfList = Lxjxlib_Const_Common::MAIL_JOB_FROM_ARR;
        $mailClass    = new Lxjxlib_Service_Mail($fromConfList[$mailNum]);
        $content      = sprintf('<p><a href="%s">%s</a></p>', $cosUrl,$name);
        $size         = $this->kbChangeMB(filesize($filePath));
        $tmpFilePath  = $size<=50?$filePath:'';
       // $tmpFilePath  = $size <= 50 ? $filePath : '';

        $ret = $mailClass->sendMail(Lxjxlib_Const_PoetryWh_CommonMap::$arrMail, $name, [
            [
                'type'    => 'text',
                'title'   => '',
                'content' => $content,
            ],
        ], $tmpFilePath);

        if (!$ret) {
            Bd_Log:: warning('send_test_mail_fail' . json_encode($ret));
        }

    }
    /**
     * 字节转换成MB
     * @param $byte
     * @return int
     */
    public function kbChangeMB($byte)
    {
        $mb = pow(1024, 2);
        $mb = round($byte / $mb, 2);
        return $mb;
    }
    //写入CSV
    public function rewriteCsv($title, $items, $fileName)
    {
        $fp = fopen($fileName, "w");
        fwrite($fp, "\xEF\xBB\xBF");
        fputcsv($fp, $title);
        foreach ($items as $item) {
            fputcsv($fp, $item);
        }
        fclose($fp);
    }

    /**
     * 加密手机号
     * @param string $plaintext 明文
     * @return string
     */
    public static function encodePhone($plaintext)
    {
        if (empty($plaintext)) {
            return "";
        }
        return base64_encode(Hk_Util_Rc4::rc4(Lxjxlib_Const_Common::RC4_KEY_PHONE, $plaintext));
    }

    /**
     * 解密手机号
     *
     * @param string $ciphertext 密文
     * @return string
     */
    public static function decodePhone($ciphertext)
    {
        if (empty($ciphertext)) {
            return "";
        }
        return Hk_Util_Rc4::decode(Lxjxlib_Const_Common::RC4_KEY_PHONE, base64_decode($ciphertext));
    }

}
