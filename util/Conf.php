<?php
/**
 * @befie   配置文件
 * @file    util/Conf.php
 * <AUTHOR>
 * @version 1.0
 * @date    2019-09-19
 */
class Lxjxlib_Util_Conf
{
    private static $arrEnvConf = [];
    private static $arrAppEnvConf = [];

    /**
     * 按环境获取配置文件
     * @param $item
     * @return mixed
     */
    public static function getEnvConf($item)
    {

        if (isset(self::$arrEnvConf[$item])) {
            return self::$arrEnvConf[$item];
        }

        self::$arrEnvConf[$item] = Bd_Conf::getConf('/lxjxlib/' . Lxjxlib_Util_Tools::getEnv() . '/' . $item);
        Bd_Log::addNotice("1111", '/lxjxlib/' . Lxjxlib_Util_Tools::getEnv() . '/' . $item);
        Bd_Log::addNotice("22222", json_encode(self::$arrEnvConf));
        return self::$arrEnvConf[$item];
    }

    public static function getCommonParams()
    {
        return self::getEnvConf('common/params');
    }

    /**
     * 获取当前环境服务域名
     * @return string
     */
    public static function getAppHost()
    {
        $commonParams = self::getCommonParams();
        return $commonParams['host'];
    }

    /**
     * 按环境获取app配置文件
     * @param $item
     * @return mixed
     */
    public static function getAppEnvConf($item)
    {

        if (isset(self::$arrAppEnvConf[$item])) {
            return self::$arrAppEnvConf[$item];
        }

        self::$arrAppEnvConf[$item] = Bd_Conf::getAppConf(Lxjxlib_Util_Tools::getEnv() . '/' . $item);
        return self::$arrAppEnvConf[$item];
    }

}
