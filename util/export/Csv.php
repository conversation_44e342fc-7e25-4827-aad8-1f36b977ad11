<?php

/**
 * @desc    导出csv
 * @file    Csv.php
 * <AUTHOR>
 * @date    2021-02-19 10:54
 */
class Lxjxlib_Util_Export_Csv
{
    private $tmpPath = '/tmp/export';
    private $tmpFileName;
    # 使用 字段名=>表头名称
    # ['name'=>'名称']
    protected $titleMap = [];

    public function __construct($titleMap, $fileName = null)
    {
        $this->titleMap = $titleMap;
        # 临时文件目录
        if (!is_dir($this->tmpPath)) {
            mkdir($this->tmpPath, 0777, true);
        }
        # 临时文件名称
        if ($fileName) {
            $this->tmpFileName = $this->tmpPath . '/' . $fileName;
        } else {
            $this->tmpFileName = $this->tmpPath . '/' . Lxjxlib_Util_Tools::getRandomStr(32);
        }
        # 写入表头
        $this->writeTitle();
    }

    /**
     * 追加分页数据
     * @param $pageData
     */
    public function addData($pageData)
    {
        if (empty($pageData) || !is_array($pageData)) {
            return;
        }
        # 将分页数据追加临时文件
        $fp = fopen($this->tmpFileName, "a");
        foreach ($pageData as $row) {
            $tmpRow = [];
            foreach ($this->titleMap as $field => $name) {
                $tmpCell = '';
                if (isset($row[$field])) {
                    $tmpCell = $row[$field];
                }
                $tmpRow[] = $tmpCell;
            }
            # 一行数据
            fputcsv($fp, $tmpRow);
        }
        fclose($fp);
    }

    /**
     * 导出
     * @param      $fileName
     * @param bool $isDownload
     * @return string
     */
    public function export($fileName, $isDownload = true)
    {
        if ($isDownload) {
            $this->setDownload($fileName);
            $fp = fopen($this->tmpFileName, 'r');
            echo fread($fp, filesize($this->tmpFileName));
            fclose($fp);
            $this->afterExport();
            die;
        } else {
            return $this->tmpFileName;
        }
    }

    /**
     * 写入表头
     */
    protected function writeTitle()
    {
        if (!file_exists($this->tmpFileName)) {
            $bom = pack("H*", 'EFBBBF');
            $fp  = fopen($this->tmpFileName, "w");
            fwrite($fp, $bom);
            # 输入表头
            fputcsv($fp, array_values($this->titleMap));
        }
        fclose($fp);
    }

    /**
     * 导出文件后的操作
     */
    protected function afterExport()
    {
        # 删除临时文件
        unlink($this->tmpFileName);
    }

    /**
     * 设置下载header
     * @param $filename
     */
    private function setDownload($filename)
    {
        header('Expires: 0');
        header('Cache-Control:must-revalidate, post-check=0, pre-check=0');
        header('Content-Type: text/csv; charset=UTF-8');
        header('Content-Type: application/force-download');
        header("Content-Disposition: attachment; filename=" . $filename . ".csv");
        header('Content-Transfer-Encoding: binary');
    }
}