<?php
/**
 *User:Printemps <zhang<PERSON><EMAIL>>
 *Date:2019/12/12 15:37
 *Desc:
 */

class Zb_Util_ZbuiDebugTool
{
    /**
     * @var integer 使用调试模式的ra的作业帮uid
     */
    protected static $rdUid;

    /**
     * @var array skr调用记录的容器 服务维度聚合
     */
    protected static $skrData = [];

    /**
     * @var array ral调用记录的容器 服务维度聚合
     */
    private static $ralData = [];

    /**
     * @var array 手动触发记录的容器 函数维度聚合
     */
    protected static $manualData = [];

    /**
     * @var array 下游接口返回的调试数据容器
     */
    protected static $downstreamData = [];

    /**
     * @var array 记录warning或其他异常情况 错误码维度聚合
     */
    protected static $warningData = [];

    /**
     * @var array skr自动注入调试标识的下游服务名
     */
    public static $needFlagService = [
        'billing','zbui'
    ];

    /**
     * @var int 1:线上 0：线下 线下不校验白名单和黑名单
     */
    private static $env = 1;

    /**
     * @var array 研发的uid白名单
     */
    private static $whiteList = [
        //offline
        2000086986,//张昊岚
        2000085332,//丙志
        2000013499,//王颖

        //online
        2408899798,//丙志
        2217601153,//张昊岚
        2307162553,//袁泉
        2344029783,//肖洋
        2275178354,//和平
        2310604555,//方源
        2325028749,//李密
        2341361971,//文华
        2334377716,//智超
        2328375934,//香宏
        2276258206,//海军
        2309451497,//秀珠
        2423374584,//向伟
        **********,//志伟
        **********,//王颖

        **********,//长帅
        **********,//雪阳
        **********,//定君
        **********,//晓佩
        **********,//雄文
        **********,//雄文
    ];

    /**
     * @var bool 调试模式开关
     */
    private static $debugSwitch;

    /**
     * @var array 调试模式下不执行的接口
     */
    private static $blackMethod = [
        'zybpay' => ['preContract','payContract','deleteContract','submitPayment','updateAccountInfo','submitTransfer'],
        'actplat' => ['tuanpurchase','createPintuan'],
        'zbtrade' => ['purchase','manualPayOrder','closeOrder','payorder','addAddress','transferClass','transferChain','mixedRefund'],
        'zbofc' => ['updateaddress'],
        'zbui'  => ['delCollect'],
        'zbcore' => ['reservationCancel']
    ];


    /**
     * 手动触发记录的方法
     *
     * @param $key
     * @param null $data
     */
    public static function addLog($key,$data = null)
    {
        if(!$_REQUEST['zbuiDebugSwitch']) return;
        $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS,2);
        $caller = $backtrace[1]['class'] . '::' . $backtrace[1]['function'];
        if($data == null) {
            self::$manualData[$caller] []= $key;
        } else {
            self::$manualData[$caller] []= [$key => $data];
        }
    }

    public static function addWarning($errNo = 0,$data = '')
    {
        if(!$_REQUEST['zbuiDebugSwitch']) return;
        $backtrace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS,2);
        $caller = $backtrace[1]['class'] . '::' . $backtrace[1]['function'] . '()  line' . ' -- ' . $backtrace[1]['line'];
        self::$warningData[$errNo][] = [$caller => $data];
    }

    /**
     * 记录SKR调用时的请求和返回
     *
     * @param Skr_Api_Model_Request $request
     * @param String $output (response的json)
     */
    public static function addSkrData($request,$output)
    {
        if(!$_REQUEST['zbuiDebugSwitch']) return;
        $key = $request->getKey();
        $resp = json_decode($output,1);
        $downstreamData = $resp['debugData'] ?? [];
        unset($resp['debugData']);
        self::$skrData[$key] = [
            'url' => $request->getUrl(),
            'params' => $request->getParams(),
            'response' => $resp,
            'cost' => $request->getElapsed(),
        ];
        if (in_array($request->service,self::$needFlagService)) {
            self::addDownstreamData($request,$downstreamData);
            unset($downstreamData);
        }
    }

    /**
     * 记录ral请求的日志,根据服务聚合
     *
     * @param $service
     * @param $data
     */
    public static function addRalData($service,$data)
    {
        if(!$_REQUEST['zbuiDebugSwitch']) return;
        self::$ralData[$service] []= $data;
    }

    /**
     * 记录特定模块的打印数据
     *
     * @param Skr_Api_Model_Request $request
     * @param array $downstreamData
     */
    public static function addDownstreamData($request,$downstreamData)
    {
        //第三层数据（billing调下游）根据服务聚合，保留debugTrace
        $skrData = $downstreamData['skrData'] ?? null;
        $ralData = $downstreamData['ralData'] ?? null;
        $manualData = $downstreamData['manualData'] ?? null;

        $dwData = [
            'skrData' => $skrData,
            'ral' => $ralData,
        ];
        foreach ($dwData as $key => $value) {
            if($value == null) unset($dwData[$key]);
        }
        //如果只有一个的话，就直接取出来减少一层
        if(count($dwData) == 1) $dwData = current($dwData);

        self::$downstreamData[$request->service] []= [
            'url' => $request->getUrl(),
            'params' => $request->getParams(),
            'debugData' => [
                //中台和billing都没有skr，所以都先直接取ral数据
                'downstream' => $dwData,
                'manual' => $downstreamData['manualData'],
                'warning' => $downstreamData['warningData']
            ],
        ];
    }

    /**
     * 输出DebugData，可以接收一个匿名函数做过滤器
     *
     * @param callable|null $formatter
     * @return array
     */
    public static function getDebugData(callable $formatter = null) :array
    {
        $resp = [
            'skrData' => self::$skrData,
            'ralData' => self::$ralData,
            'manualData' => self::$manualData,
            'warningData' => self::$warningData,
            'downstreamData' => self::$downstreamData,
        ];
        if(isset($formatter) && is_callable($formatter)) {
            $resp = $formatter($resp);
            if(is_array($resp)) $resp = [];
        }
        //unset掉没有值的键
        foreach ($resp as $type => $data) {
            if ($data == []) unset($resp[$type]);
        }
        //如果输出了数据就记录，接入noah报警
        Bd_Log::addNotice('phplib_zbuiDebugTool_rdUid',self::$rdUid);
        return $resp;
    }

    /**
     * 检验是否开启调试模式
     * @param $rdUid
     * @return bool
     * @notic 请确保在单次请求的生命周期中第一次调用这个方法传了rdUid
     */
    public static function getSwitch($rdUid = 0) :bool
    {
        if (isset(self::$debugSwitch)) {
            return self::$debugSwitch;
        }
        if(!Hk_Util_Ip::isInnerIp() || !in_array($rdUid,self::$whiteList)  || !$_REQUEST['zbuiDebugSwitch']) {
            self::$debugSwitch = false;
        } else {
            //记录操作者id，接入noah
            self::$rdUid = $rdUid;
            self::$debugSwitch = true;
        }
        //线下模式，直接检验参数里是否带了开关，带了开关就不校验白名单
        $domain = Hk_Util_Host::getReqDomain();
        if(Hk_Util_Ip::isInnerIp() && $domain == 'suanshubang.com' && $_REQUEST['zbuiDebugSwitch']) {
            self::$rdUid = $rdUid;
            self::$env = 0;
            self::$debugSwitch = true;
        }
        return self::$debugSwitch ?? false;
    }

    /**
     * 判断高危接口,线上的调试模式下不执行
     * 12.26 线下调试模式下也可以执行黑名单里方法
     * 现版本只支持到skr调用！！！！
     *
     * @param Skr_Api_Model_Request $request
     * @return bool
     */
    public static function isBlackMethod($request) :bool
    {
        // 2020/1/8当不模拟用户操作时不禁用任何接口,模拟假数据下个版本统一收口
        if($_REQUEST['fakeUid'] == null) return false;
        if(self::$debugSwitch === false || self::$env === 0) return false;
        $service = $request->service;
        $method = $request->method;
        if(isset(self::$blackMethod[$service]) && in_array($method,self::$blackMethod[$service])) return true;
        return false;
    }

}

//TODO checkList
// auto SKR collect
// manual collect
// formatter
// format downstream data
// blacklist service & function
// rd's uid whitelist
// cookie & switch