<?php

/**
 * @file   SensitiveWordDetection.php
 * <AUTHOR>
 * @date   2018/11/21
 * @brief
 */

class Hkzb_Util_SensitiveWordDetection {

    protected $words = array();

    public function __construct($words)
    {
        if (is_array($words)) {
            foreach ($words as $word) {
                if (is_string($word)) {
                    $this->words[] = $word;
                }
            }
        }
    }

    /**
     * 检测数组中敏感词
     * @param $list array
     * @return array|bool
     */
    public function detectList($list) {
        if (empty($this->words)) {
            return false;
        }

        if (empty($list) || !is_array($list)) {
            return false;
        }

        $content = '';
        foreach ($list as $item) {
            if (is_string($item)) {
                $content = $content . $item . "\n";
            }
        }

        $ret = $this->detectString($content);
        if (empty($ret)) {
            return array();
        }
        return $ret;
    }

    /**
     * 检测字符串中的敏感词
     * @param $string
     * @return array|bool
     */
    public function detectString($string) {
        if (empty($this->words) || empty($string)) {
            return false;
        }

        $sensitiveWords = array();
        foreach ($this->words as $word) {

            if (stripos($word, '*')) {
                $pattern = str_ireplace('*', '.{0,18}', "/{$word}/");
                $ret = preg_match($pattern, $string);
                if ($ret === 1) {
                    $sensitiveWords[] = $word;
                }
            }else {
                $ret = strpos($string, $word);
                if ($ret !== false) {
                    $sensitiveWords[] = $word;
                }
            }

        }

        return $sensitiveWords;
    }

}
