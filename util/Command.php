<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file: Command.php
 * @author: l<PERSON><PERSON><PERSON><PERSON> <liji<PERSON><PERSON><PERSON>@zuoyebang.com>
 * @date: 2017/12/28 下午12:49
 * @brief: NMQ命令发送
 */
class Hkzb_Util_Command
{
    /**
     * 批量通知模式 （默认关闭）
     * @var boolean
     */
    private static $batchNotifyMode = false;

    /**
     * 批量通知队列
     *
     * @var array
     */
    private static $batchNotifyQueue = array();

    /**
     * 开启批量通知模式
     */
    public static function enableBatchNotifyMode()
    {
        self::$batchNotifyMode = true;
    }

    /**
     * 关闭批量通知模式
     */
    public static function disableBatchNotifyMode()
    {
        self::$batchNotifyMode = false;
    }

    /**
     * 发送NMQ
     *
     * @param integer $cmdNo NMQ命令号
     * @param array $info 发送的数据
     * @throws Hk_Util_Exception
     */
    public static function notify($cmdNo, $info)
    {
        //批量发送模式下，记录待发送的命令集
        if (self::$batchNotifyMode) {
            self::$batchNotifyQueue[] = array(
                'cmdno' => $cmdNo,
                'info' => $info
            );
            return;
        }

        //直接发送命令
        if (!Zb_Service_Nmq::sendCommand($cmdNo, $info)) {
            Bd_Log::warning("Error:[ds error], Detail:[NMQ Command sent failed]");
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::NETWORK_ERROR, '通知其它系统失败');
        }
        Bd_Log::notice("cmd:$cmdNo cmdInfo:" . json_encode($info));
    }

    /**
     * 【批量发送模式下需要显示调用】批量发送NMQ
     */
    public static function notifyAll()
    {
        Bd_Log::warning("======productNotifyAll===[  ". json_encode(self::$batchNotifyQueue) ."   ]");
        foreach (self::$batchNotifyQueue as $notifyTaskInfo) {
            //发送命令, 不抛出异常
            if (!Zb_Service_Nmq::sendCommand($notifyTaskInfo['cmdno'], $notifyTaskInfo['info'])) {
                Bd_Log::warning("Error:[ds error], Detail:[NMQ Command sent failed] NmqInfo: [" . json_encode($notifyTaskInfo) . "]");
            }
            usleep(2000);
        }
        //发送后清空队列
        self::$batchNotifyQueue = array();
    }


}