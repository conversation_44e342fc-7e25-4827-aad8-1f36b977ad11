<?php
/**
 * Created by phplib.
 * Auther: <EMAIL>
 * Date: 2021/9/27 2:27 下午
 */

/**
 * zbcore dar 支持moat鉴权
 *
 * @link https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=302176977
 */
class Zb_Util_Moatauth_ZbcoreDar extends Zb_Util_Moatauth_MoatAuth implements Zb_Util_Moatauth_AuthTpl
{
    /** @var string  用户配置目录 */
    const AUTH_CONF_PATH = 'ral/services/zbcore_dar';

    /** @var string 兜底配置 <b>切换完成后下线</b> */
    const AUTH_DEFAULT_CONF_PATH = 'zb/moatAuth/default';

    /** @var array|false|null 最终鉴权配置 */
    private static $AUTH_CONF = null;

    /** @var array|false|null zbcoredar-ral配置，用于区分是否为内部用户 */
    private static $RAL_CONF = null;

    /**
     * <p>Zb_Util_Moatauth_ZbcoreDar constructor.</p>
     * <p>初始化配置信息:
     * <ul><li>加载ral配置信息: RAL_CONF</li>
     * <li>加载鉴权配置信息: AUTH_CONF</li></ul>
     * </p>
     */
    public function __construct()
    {
        self::$RAL_CONF = self::getUserAuthConf();
        if (!self::checkConf(self::$RAL_CONF)) {
            self::$AUTH_CONF = self::getDefaultAuthConf();
        } else {
            self::$AUTH_CONF = self::$RAL_CONF;
        }
    }

    /**
     * <p>前置检查是否需要加签</p>
     *
     * @return bool <p>根据返回结果判断是否跳过</p>
     * <ul>
     * <li><b>false</b> : 跳过
     * <li><b>true </b> : 不跳过
     * </ul>
     */
    public function isNeedSign()
    {
        //判断内部用户， 内部用户不需要鉴权
        if (!isset(self::$RAL_CONF['domain'])) {
            return false;
        }
        if (self::isInnerApp(self::$RAL_CONF['domain'])) {
            return false;
        }
        //非内部用户，检查鉴权配置信息是否正确
        if (!self::checkConf(self::$RAL_CONF)) {
            Bd_Log::warning(
                sprintf(
                    'get moat-sign user config fail, path: %s, conf: %s',
                    self::AUTH_CONF_PATH,
                    json_encode(self::$RAL_CONF, 256)
                )
            );
        }
        if (!self::checkConf(self::$AUTH_CONF)) {
            return false;
        }

        return true;
    }

    /**
     * moat sign
     *
     * @param array $arrParams 请求moat参数
     *
     * @return bool
     */
    public function Sign(&$arrParams, $query)
    {
        $ak = self::$AUTH_CONF["moatAppKey"];
        $sk = self::$AUTH_CONF["moatAppSecret"];
        return self::moatSign($ak, $sk, $arrParams, $query);
    }

    /**
     * defalut config
     *
     * @return array|false
     */
    private static function getDefaultAuthConf()
    {
        return Bd_Conf::getConf(self::AUTH_DEFAULT_CONF_PATH);
    }

    /**
     * item config
     *
     * @return array|false
     */
    private static function getUserAuthConf()
    {
        return Bd_Conf::getConf(self::AUTH_CONF_PATH);
    }

    /**
     * check config
     *
     * @param $conf
     *
     * @return bool
     */
    private static function checkConf($conf)
    {
        if (empty($conf['moatAppKey']) || empty($conf['moatAppSecret'])) {
            return false;
        }
        return true;
    }

    /**
     * <p>判断是否为售卖内部用户</p>
     * <p>内部用户不通过网关进行调用，所以内部用户请求的配置文件中
     * domain一定不是moat，
     * 这里暂且使用moat关键词判断是否是moat网关地址</p>
     *
     * @param $domain
     *
     * @return bool
     */
    private static function isInnerApp($domain)
    {
        return false === strpos($domain, 'moat');
    }
}
