<?php

/**
 * @befie   进程工具
 * @file    util/Process.php
 * <AUTHOR>
 * @version 1.0
 * @since
 * @date    2020-03-04
 */
class Lxjxlib_Util_Process
{
    const MAX_PROC_NUM = 10; // 最大进程数

    /**
     * 多进程并发执行任务
     * @param $taskParams 分片好的执行数据，每个分片使用一个子进程
     * @param $callFunc 调用方子进程处理回调函数[__CLASS__, 'funcName']
     * @param int $maxProcNum 最大进程数
     * @return array
     */
    public static function runMultiProcessTask($taskParams, $callFunc, $maxProcNum = self::MAX_PROC_NUM)
    {
        $running = 0;
        $pids    = [];

        $stat = [
            'totalNum' => 0,
            'procNum'  => 0,
        ];

        foreach ($taskParams as $cursor => $subParams) {
            $stat['procNum']++;
            $pids[$cursor] = pcntl_fork();
            if (0 === $pids[$cursor]) {
                call_user_func_array($callFunc, [$subParams]);
                exit(0);
            } elseif (-1 === $pids[$cursor]) {
                Bd_Log::warning('runMultiProcess_fork_failed, peakMem=' . Lxjxlib_Util_Tools::getPeakMem());
                unset($subParams[$cursor]);
                unset($pids[$cursor]);
                sleep(1);
            } else {
                // 父进程执行，并控制并发数量
                $running++;
                if ($running >= $maxProcNum) {
                    pcntl_wait($status);
                    $running--;
                }

                unset($taskParams[$cursor]);
            }
        }

        foreach ($pids as $pid) {
            pcntl_waitpid($pid, $status);
        }

        return $stat;
    }
}