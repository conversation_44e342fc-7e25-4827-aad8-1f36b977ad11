<?php
class Hkzb_Util_Fudao_VideoBufferCache {
    const LESSON_VIDEO_BUFFER_PRE     = 'inclass_video_buffer_lesson_';
    const LESSON_VIDEO_BUFFER_HASH_KEY= 'inclass_video_buffer_hash_key';
    const STU_LESSON_VIDEO_BUFFER_PRE = 'inclass_video_buffer_stu_lesson_';
    const CACHE_EXPIRE_TIME           = 864000;
    const DEFAULT_VIDEO_BUFFER_TIME   = 5;
    const VIDEO_BUFFER_UPPER_LIMIT    = 12;
    const VIDEO_BUFFER_LOWER_LIMIT    = 3;
    const VIDEO_BUFFER_NOT_USE        = -1;

    const RedisBns = 'zbjx';


    public static function checkBufferValid($intBufferTime){
        return ($intBufferTime == self::VIDEO_BUFFER_NOT_USE) || ($intBufferTime <= self::VIDEO_BUFFER_UPPER_LIMIT && $intBufferTime >= self::VIDEO_BUFFER_LOWER_LIMIT);
    }

    public static function getVideoBufferCache($studentUid , $lessonId){
        if(empty($studentUid) || empty($lessonId)){
            Bd_Log::warning('param error , return default');
            return self::DEFAULT_VIDEO_BUFFER_TIME;
        }

        //MVP课走低延迟配置
        if(Hkzb_Const_XengCourseConf::isMVPCourse($lessonId)){
            return self::VIDEO_BUFFER_LOWER_LIMIT;
        }

        $objCache  = Hk_Service_RedisClient::getInstance(self::RedisBns);

        $stuLessonRet = $objCache->get(self::STU_LESSON_VIDEO_BUFFER_PRE.$lessonId.'_'.$studentUid); 
        if($stuLessonRet !== false && self::checkBufferValid($stuLessonRet)){
            Bd_Log::addNotice('hit_video_buffer_lid_'.$lessonId.'_stuId_'.$studentUid , $stuLessonRet);
            return intval($stuLessonRet);
        } 
        
        $lessonRet = self::getVideoBufferCacheByLesson($lessonId);
        if($lessonRet !== false && self::checkBufferValid($lessonRet)){
            Bd_Log::addNotice('hit_video_buffer_lid_'.$lessonId , $lessonRet);
            return intval($lessonRet);
        } 

        Bd_Log::addNotice('video_buffer_no_hit' , 1); 
        return self::DEFAULT_VIDEO_BUFFER_TIME;
    }
    /**
     * @param $studentUid
     * @param $lessonId
     * @param array $mvpInfo acls从mvp测数据库获取到的信息，如果不为空则为mvp课程
     * @return int
     * 获取直播buffer时长
     */
    public static function getVideoBufferCacheForEnter($studentUid , $lessonId, $mvpInfo){
        if(empty($studentUid) || empty($lessonId)){
            Bd_Log::warning('param error , return default');
            return self::DEFAULT_VIDEO_BUFFER_TIME;
        }

        //MVP课走低延迟配置
        if(!empty($mvpInfo)){
            return self::VIDEO_BUFFER_LOWER_LIMIT;
        }

        $objCache  = Hk_Service_RedisClient::getInstance(self::RedisBns);

        $stuLessonRet = $objCache->get(self::STU_LESSON_VIDEO_BUFFER_PRE.$lessonId.'_'.$studentUid);
        if($stuLessonRet !== false && self::checkBufferValid($stuLessonRet)){
            Bd_Log::addNotice('hit_video_buffer_lid_'.$lessonId.'_stuId_'.$studentUid , $stuLessonRet);
            return intval($stuLessonRet);
        }

        $lessonRet = self::getVideoBufferCacheByLesson($lessonId);
        if($lessonRet !== false && self::checkBufferValid($lessonRet)){
            Bd_Log::addNotice('hit_video_buffer_lid_'.$lessonId , $lessonRet);
            return intval($lessonRet);
        }

        Bd_Log::addNotice('video_buffer_no_hit' , 1);
        return self::DEFAULT_VIDEO_BUFFER_TIME;
    }

    public static function getVideoBufferCacheByLesson($lessonId){
        if(empty($lessonId)){
            Bd_Log::warning('param error , return default');
            return self::DEFAULT_VIDEO_BUFFER_TIME;
        }
        $objCache  = Hk_Service_RedisClient::getInstance(self::RedisBns);
        $retBuffTime = $objCache->hget(self::LESSON_VIDEO_BUFFER_HASH_KEY , $lessonId);  
        if($retBuffTime !== false){
            Bd_Log::addNotice('hash_get_succ_'.$lessonId , $retBuffTime);
            return $retBuffTime;
        }
        Bd_Log::addNotice('hash_get_fail_'.$lessonId , 1);
        return self::DEFAULT_VIDEO_BUFFER_TIME;
    }


    public static function setVideoBufferCacheByLessonArr($lessonArr){
        if(empty($lessonArr)){
            return false;
        } 
        $cacheSetArr = array();
        foreach($lessonArr as $lid => $bufferTime){
            if(self::checkBufferValid($bufferTime)){
                $cacheSetArr[$lid] = $bufferTime;
            }
        }
        
        if(!empty($cacheSetArr)){
            $objCache  = Hk_Service_RedisClient::getInstance(self::RedisBns);
            if($objCache->exists(self::LESSON_VIDEO_BUFFER_HASH_KEY) == true){
                $delRet = $objCache->del(self::LESSON_VIDEO_BUFFER_HASH_KEY); 
                if(is_null($delRet)){
                    Bd_Log::warning('del_hash_key_failed');
                    return false;
                }
                Bd_Log::addNotice('delAll_hash_key_succ' , 1); 
            }

            //同步写入achilles
            $v3list = [];
            foreach($cacheSetArr as $lessonId => $bufferTime) {
                $v3list[strval($lessonId)] = strval($bufferTime);
            }
            Hkzb_Service_Achilles::v3call("/achilles/v3/origin/aclsphp/acls/lessonbuffer/set", "", $v3list, [], "");

            $hashMsetRet = $objCache->hmset(self::LESSON_VIDEO_BUFFER_HASH_KEY , $cacheSetArr);
            if($hashMsetRet != false){
                Bd_Log::addNotice('hash_mset_succ' , json_encode($cacheSetArr));
                return true;
            }else{
                Bd_Log::warning('hash_mset_fail');
                return false;
            }
        }
        
        Bd_Log::warning('no hash for set');
        return true;
    }


    public static function setVideoBufferCacheByStuLesson($lessonId , $studentUid , $intTime){
        if(empty($lessonId) || empty($studentUid) || !self::checkBufferValid($intTime)){
            Bd_Log::warning('param error');        
            return false;
        }

        $objCache  = Hk_Service_RedisClient::getInstance(self::RedisBns);
        //同步写入achilles
        $stuLessonBuffer = [strval($studentUid).'_'.strval($lessonId) => strval($intTime)];
        Hkzb_Service_Achilles::v3call("/achilles/v3/origin/aclsphp/acls/studentlessonbuffer/set", "", $stuLessonBuffer, [], "");
        return $objCache->setex(self::STU_LESSON_VIDEO_BUFFER_PRE.$lessonId.'_'.$studentUid , self::CACHE_EXPIRE_TIME , $intTime);
    }

    public static function getVideoBufferByAcls($buffer, $mvpInfo)
    {
        if (empty($buffer)) {
            return self::DEFAULT_VIDEO_BUFFER_TIME;
        }

        //MVP课走低延迟配置
        if (!empty($mvpInfo)) {
            return self::VIDEO_BUFFER_LOWER_LIMIT;
        }

        //优先读取学生配置值
        if (!empty($buffer['student']) && self::checkBufferValid($buffer['student'])) {
            return intval($buffer['student']);
        }

        //然后读取章节配置值
        if (!empty($buffer['lesson']) && self::checkBufferValid($buffer['lesson'])) {
            return intval($buffer['student']);
        }

        //都没有则返回默认值
        return self::DEFAULT_VIDEO_BUFFER_TIME;
    }

}

