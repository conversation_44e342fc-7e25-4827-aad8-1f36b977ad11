<?php
/**
 * @file    IMVoice.php
 * <AUTHOR>
 * @date    2017-12-22
 * @brief   IM语音上传服务
 */

class Hkzb_Util_Fudao_IMVoice {
    const FROM_UNKNOWN = 0; //未知
    const FROM_GROUP_CHAT = 1;//群聊天
    const FROM_GROUP_EXERCISE = 2;//群作业
    /**
     * 上传语音工具
     * @param string $bucket  bucket名称
     * @param string $voiceContent 语音内容
     * @param int $voiceLen 语音长度
     * @param int $voiceSize 语音大小
     * @param string $extension 扩展名
     * @param bool $isPublic 是否公共可读
     * @param int $from 来源 0 未知; 1 群聊天; 2 群作业
     * @param string $contentType 文件类型
     * @return array|bool 返回值
     */
    public static function sendVoice($bucket, $voiceContent, $voiceLen, $voiceSize, $extension = 'amr', $isPublic = true, $from = self::FROM_UNKNOWN, $contentType='audio/amr' ) {
        $prefix = ral_get_idc() == "test" ? "qa" : "zyb";             # 语音的prefix
        $pid    = sprintf("%s_%s_%s", $prefix, md5(md5($voiceContent) . strlen($voiceContent)), $from);
        $meta   = array(         # added 2016-05-26，提供meta存储
            "voiceLen"  => $voiceLen,
            "voiceSize" => $voiceSize,
        );

        // 上传语音到BOS，存储到$bucket
        $objBos     = new Hkzb_Service_IMBos($bucket, $isPublic, 'zyb-student');
        $ret        = $objBos->putObjectByString($voiceContent, $pid ."." . $extension, $meta, $contentType);
        if (false === $ret) {
            $arg    = ["pid" => $pid];
            Bd_Log::warning("Image, Upload picture to bos failed", Hk_Util_ExceptionCodes::ERRNO_VOICE_UPLOAD, $arg);
            return false;
        }
        $arrRes = array(
            'voiceId'   => $pid,
            'pid'        => $pid,
            "voiceLen"  => $voiceLen,
            "voiceSize" => $voiceSize,
        );
        return $arrRes;
    }

    /**
     * 获取语音的链接
     * @param string $bucket bucket命名空间
     * @param string $pid 语音pid
     * @param string $extension 扩展名
     * @param bool $isPublic 是否公共可读
     * @return string 语音的url
     */
    public static function getVoiceUrl($bucket, $pid, $extension="amr", $isPublic = true) {

        $url = '';
        $objBos = new Hkzb_Service_IMBos($bucket, $isPublic, 'zyb-student');
        $url = $objBos->getObject($pid . "." . $extension);

        return $url;
    }
}