<?php
/**
 * Created by PhpStorm.
 * User: s<PERSON><PERSON><PERSON>@zuoyebang.com
 * Time: 2017/11/16 20:29
 */
class Hkzb_Util_Fudao_Course_AdjustClass
{
    public static $AdjustClass = array (
        // 小学
        36457 =>
            array (
                0 => 36458,
                1 => 36465,
                2 => 36474,
            ),
        35458 =>
            array (
                0 => 36457,
                1 => 36459,
                2 => 36475,
                3 => 47796,
            ),
        36459 =>
            array (
                0 => 36458,
                1 => 36465,
                2 => 36474,
            ),
        36465 =>
            array (
                0 => 36457,
                1 => 36459,
                2 => 36475,
                3 => 47796,
            ),
        36474 =>
            array (
                0 => 36457,
                1 => 36459,
                2 => 36475,
                3 => 47796,
            ),
        36475 =>
            array (
                0 => 36458,
                1 => 36465,
                2 => 36474,
            ),
        36502 =>
            array (
                0 => 36531,
                1 => 36555,
            ),
        36529 =>
            array (
                0 => 36531,
                1 => 36555,
            ),
        36531 =>
            array (
                0 => 36502,
                1 => 36529,
                2 => 36554,
                3 => 36577,
            ),
        36536 =>
            array (
                0 => 36562,
                1 => 36563,
                2 => 42014,
            ),
        36548 =>
            array (
                0 => 36549,
                1 => 36561,
                2 => 41000,
            ),
        36549 =>
            array (
                0 => 36548,
                1 => 36550,
                2 => 40995,
            ),
        36550 =>
            array (
                0 => 36549,
                1 => 36561,
                2 => 41000,
            ),
        36554 =>
            array (
                0 => 36531,
                1 => 36555,
            ),
        36555 =>
            array (
                0 => 36502,
                1 => 36529,
                2 => 36554,
                3 => 36577,
            ),
        36561 =>
            array (
                0 => 36548,
                1 => 36550,
                2 => 40995,
            ),
        36562 =>
            array (
                0 => 36536,
            ),
        36563 =>
            array (
                0 => 36536,
            ),
        36577 =>
            array (
                0 => 36531,
                1 => 36555,
            ),
        36470 =>
            array (
                0 => 36471,
                1 => 36569,
            ),
        36471 =>
            array (
                0 => 36470,
                1 => 36473,
                2 => 47050,
            ),
        36473 =>
            array (
                0 => 36471,
                1 => 36569,
            ),
        36503 =>
            array (
                0 => 36520,
                1 => 36525,
                2 => 36540,
            ),
        36504 =>
            array (
                0 => 36516,
                1 => 36517,
                2 => 36533,
                3 => 39202,
            ),
        36505 =>
            array (
                0 => 36516,
                1 => 36517,
                2 => 36533,
                3 => 39202,
            ),
        36512 =>
            array (
                0 => 36513,
                1 => 36574,
            ),
        36513 =>
            array (
                0 => 36512,
                1 => 36564,
                2 => 36575,
            ),
        36516 =>
            array (
                0 => 36504,
                1 => 36505,
            ),
        36517 =>
            array (
                0 => 36504,
                1 => 36505,
            ),
        36519 =>
            array (
                0 => 36520,
                1 => 36525,
                2 => 36540,
            ),
        36520 =>
            array (
                0 => 36503,
                1 => 36519,
                2 => 36556,
            ),
        36525 =>
            array (
                0 => 36503,
                1 => 36519,
                2 => 36556,
            ),
        36533 =>
            array (
                0 => 36504,
                1 => 36505,
            ),
        36540 =>
            array (
                0 => 36503,
                1 => 36519,
                2 => 36556,
            ),
        36556 =>
            array (
                0 => 36520,
                1 => 36525,
                2 => 36540,
            ),
        36564 =>
            array (
                0 => 36513,
                1 => 36574,
            ),
        36569 =>
            array (
                0 => 36470,
                1 => 36473,
                2 => 47050,
            ),
        36574 =>
            array (
                0 => 36512,
                1 => 36564,
                2 => 36575,
            ),
        36575 =>
            array (
                0 => 36513,
                1 => 36574,
            ),
        36464 =>
            array (
                0 => 36466,
                1 => 36565,
                2 => 36566,
                3 => 36570,
            ),
        36466 =>
            array (
                0 => 36464,
                1 => 36467,
                2 => 36477,
            ),
        36467 =>
            array (
                0 => 36466,
                1 => 36565,
                2 => 36566,
                3 => 36570,
            ),
        36477 =>
            array (
                0 => 36466,
                1 => 36565,
                2 => 36566,
                3 => 36570,
            ),
        36481 =>
            array (
                0 => 36496,
                1 => 36508,
                2 => 36514,
                3 => 42094,
                4 => 42095,
                5 => 46560,
            ),
        36496 =>
            array (
                0 => 36481,
                1 => 36506,
                2 => 36552,
                3 => 39196,
                4 => 46562,
            ),
        36506 =>
            array (
                0 => 36496,
                1 => 36508,
                2 => 36514,
                3 => 42094,
                4 => 42095,
                5 => 46560,
            ),
        36508 =>
            array (
                0 => 36481,
                1 => 36506,
                2 => 36552,
                3 => 39196,
                4 => 46562,
            ),
        36509 =>
            array (
                0 => 36511,
                1 => 36535,
            ),
        36511 =>
            array (
                0 => 36509,
                1 => 36534,
                2 => 36538,
                3 => 36551,
            ),
        36514 =>
            array (
                0 => 36481,
                1 => 36506,
                2 => 36552,
                3 => 39196,
                4 => 46562,
            ),
        36524 =>
            array (
                0 => 36544,
                1 => 36557,
            ),
        36534 =>
            array (
                0 => 36511,
                1 => 36535,
            ),
        35535 =>
            array (
                0 => 36509,
                1 => 36534,
                2 => 36538,
                3 => 36551,
            ),
        36538 =>
            array (
                0 => 36511,
                1 => 36535,
            ),
        36542 =>
            array (
                0 => 36544,
                1 => 36557,
            ),
        36544 =>
            array (
                0 => 36524,
                1 => 36542,
                2 => 36545,
                3 => 36558,
            ),
        36545 =>
            array (
                0 => 36544,
                1 => 36557,
            ),
        36551 =>
            array (
                0 => 36511,
                1 => 36535,
            ),
        36552 =>
            array (
                0 => 36496,
                1 => 36508,
                2 => 36514,
                3 => 42094,
                4 => 42095,
                5 => 46560,
            ),
        36557 =>
            array (
                0 => 36524,
                1 => 36542,
                2 => 36545,
                3 => 36558,
            ),
        36558 =>
            array (
                0 => 36544,
                1 => 36557,
            ),
        36565 =>
            array (
                0 => 36464,
                1 => 36467,
                2 => 36477,
            ),
        36566 =>
            array (
                0 => 36464,
                1 => 36467,
                2 => 36477,
            ),
        36570 =>
            array (
                0 => 36464,
                1 => 36467,
                2 => 36477,
            ),
        36461 =>
            array (
                0 => 36462,
                1 => 36463,
                2 => 36469,
                3 => 36472,
                4 => 36567,
                5 => 36568,
                6 => 46451,
            ),
        36462 =>
            array (
                0 => 36461,
                1 => 36468,
                2 => 36476,
            ),
        36463 =>
            array (
                0 => 36461,
                1 => 36468,
                2 => 36476,
            ),
        36468 =>
            array (
                0 => 36462,
                1 => 36463,
                2 => 36469,
                3 => 36472,
                4 => 36567,
                5 => 36568,
                6 => 46451,
            ),
        36469 =>
            array (
                0 => 36461,
                1 => 36468,
                2 => 36476,
            ),
        36472 =>
            array (
                0 => 36461,
                1 => 36468,
                2 => 36476,
            ),
        36476 =>
            array (
                0 => 36462,
                1 => 36463,
                2 => 36469,
                3 => 36472,
                4 => 36567,
                5 => 36568,
                6 => 46451,
            ),
        36478 =>
            array (
                0 => 36491,
                1 => 36493,
                2 => 36494,
                3 => 36497,
                4 => 36510,
                5 => 40837,
                6 => 45949,
            ),
        36479 =>
            array (
                0 => 36491,
                1 => 36493,
                2 => 36494,
                3 => 36497,
                4 => 36510,
                5 => 40837,
                6 => 45949,
            ),
        36480 =>
            array (
                0 => 36491,
                1 => 36493,
                2 => 36494,
                3 => 36497,
                4 => 36510,
                5 => 40837,
                6 => 45949,
            ),
        36491 =>
            array (
                0 => 36478,
                1 => 36479,
                2 => 36480,
                3 => 36498,
                4 => 40835,
                5 => 45941,
            ),
        36492 =>
            array (
                0 => 36571,
                1 => 36573,
                2 => 38504,
            ),
        36493 =>
            array (
                0 => 36478,
                1 => 36479,
                2 => 36480,
                3 => 36498,
                4 => 40835,
                5 => 45941,
            ),
        36494 =>
            array (
                0 => 36478,
                1 => 36479,
                2 => 36480,
                3 => 36498,
                4 => 40835,
                5 => 45941,
            ),
        36495 =>
            array (
                0 => 36571,
                1 => 36573,
                2 => 38504,
            ),
        36497 =>
            array (
                0 => 36478,
                1 => 36479,
                2 => 36480,
                3 => 36498,
                4 => 40835,
                5 => 45941,
            ),
        36498 =>
            array (
                0 => 36491,
                1 => 36493,
                2 => 36494,
                3 => 36497,
                4 => 36510,
                5 => 40837,
                6 => 45949,
            ),
        36510 =>
            array (
                0 => 36478,
                1 => 36479,
                2 => 36480,
                3 => 36498,
                4 => 40835,
                5 => 45941,
            ),
        36522 =>
            array (
                0 => 36528,
                1 => 36543,
                2 => 36559,
            ),
        36526 =>
            array (
                0 => 36528,
                1 => 36543,
                2 => 36559,
            ),
        36527 =>
            array (
                0 => 36528,
                1 => 36543,
                2 => 36559,
            ),
        36528 =>
            array (
                0 => 36522,
                1 => 36526,
                2 => 36527,
                3 => 36539,
            ),
        36537 =>
            array (
                0 => 36571,
                1 => 36573,
                2 => 38504,
            ),
        36539 =>
            array (
                0 => 36528,
                1 => 36543,
                2 => 36559,
            ),
        36543 =>
            array (
                0 => 36522,
                1 => 36526,
                2 => 36527,
                3 => 36539,
            ),
        36559 =>
            array (
                0 => 36522,
                1 => 36526,
                2 => 36527,
                3 => 36539,
            ),
        36567 =>
            array (
                0 => 36461,
                1 => 36468,
                2 => 36476,
            ),
        36568 =>
            array (
                0 => 36461,
                1 => 36468,
                2 => 36476,
            ),
        36571 =>
            array (
                0 => 36492,
                1 => 36495,
                2 => 36537,
                3 => 36572,
            ),
        36572 =>
            array (
                0 => 36571,
                1 => 36573,
                2 => 38504,
            ),
        36573 =>
            array (
                0 => 36492,
                1 => 36495,
                2 => 36537,
                3 => 36572,
            ),
        36336 =>
            array (
                0 => 36337,
                1 => 36344,
                2 => 36354,
            ),
        36337 =>
            array (
                0 => 36336,
                1 => 36338,
                2 => 36353,
            ),
        36338 =>
            array (
                0 => 36337,
                1 => 36344,
                2 => 36354,
            ),
        36344 =>
            array (
                0 => 36336,
                1 => 36338,
                2 => 36353,
            ),
        36353 =>
            array (
                0 => 36337,
                1 => 36344,
                2 => 36354,
            ),
        36354 =>
            array (
                0 => 36336,
                1 => 36338,
                2 => 36353,
            ),
        36379 =>
            array (
                0 => 36410,
                1 => 36432,
            ),
        36408 =>
            array (
                0 => 36410,
                1 => 36432,
            ),
        36410 =>
            array (
                0 => 36379,
                1 => 36408,
                2 => 36433,
                3 => 36455,
            ),
        36417 =>
            array (
                0 => 36441,
                1 => 36442,
                2 => 42013,
            ),
        36426 =>
            array (
                0 => 36427,
                1 => 36429,
                2 => 40975,
            ),
        36427 =>
            array (
                0 => 36426,
                1 => 36440,
                2 => 40991,
            ),
        36429 =>
            array (
                0 => 36426,
                1 => 36440,
                2 => 40991,
            ),
        36432 =>
            array (
                0 => 36379,
                1 => 36408,
                2 => 36433,
                3 => 36455,
            ),
        36433 =>
            array (
                0 => 36410,
                1 => 36432,
            ),
        36440 =>
            array (
                0 => 36427,
                1 => 36429,
                2 => 40975,
            ),
        36441 =>
            array (
                0 => 36417,
            ),
        36442 =>
            array (
                0 => 36417,
            ),
        36455 =>
            array (
                0 => 36410,
                1 => 36432,
            ),
        36349 =>
            array (
                0 => 36350,
                1 => 36449,
            ),
        36350 =>
            array (
                0 => 36349,
                1 => 36352,
            ),
        36352 =>
            array (
                0 => 36350,
                1 => 36449,
            ),
        36381 =>
            array (
                0 => 36398,
                1 => 36404,
                2 => 36419,
            ),
        36383 =>
            array (
                36395, 36396, 36412,39202
            ),
        36384 =>
            array (
                36395, 36396, 36412,39202
            ),
        39202 =>
            array(
                36504,36505
            ),
        36391 =>
            array (
                0 => 36392,
                1 => 36443,
                2 => 36454,
            ),
        36392 =>
            array (
                0 => 36391,
                1 => 36453,
            ),
        36395 =>
            array (
                0 => 36383,
                1 => 36384,
                2 => 42090,
            ),
        36396 =>
            array (
                0 => 36383,
                1 => 36384,
                2 => 42090,
            ),
        36398 =>
            array (
                0 => 36381,
                1 => 36399,
                2 => 36434,
            ),
        36399 =>
            array (
                0 => 36398,
                1 => 36404,
                2 => 36419,
            ),
        36404 =>
            array (
                0 => 36381,
                1 => 36399,
                2 => 36434,
            ),
        36412 =>
            array (
                0 => 36383,
                1 => 36384,
            ),
        36419 =>
            array (
                0 => 36381,
                1 => 36399,
                2 => 36434,
            ),
        36434 =>
            array (
                0 => 36398,
                1 => 36404,
                2 => 36419,
            ),
        36443 =>
            array (
                0 => 36391,
                1 => 36453,
            ),
        36449 =>
            array (
                0 => 36349,
                1 => 36352,
            ),
        36453 =>
            array (
                0 => 36392,
                1 => 36443,
                2 => 36454,
            ),
        36454 =>
            array (
                0 => 36391,
                1 => 36453,
            ),
        36341 =>
            array (
                0 => 36345,
                1 => 36444,
                2 => 36445,
                3 => 36448,
            ),
        36345 =>
            array (
                0 => 36341,
                1 => 36346,
                2 => 36356,
            ),
        36346 =>
            array (
                0 => 36345,
                1 => 36444,
                2 => 36445,
                3 => 36448,
            ),
        36356 =>
            array (
                0 => 36345,
                1 => 36444,
                2 => 36445,
                3 => 36448,
            ),
        36360 =>
            array (
                0 => 36378,
                1 => 36387,
                2 => 36393,
                3 => 42093,
            ),
        36378 =>
            array (
                0 => 36360,
                1 => 36385,
                2 => 36431,
                3 => 39193,
            ),
        36385 =>
            array (
                0 => 36378,
                1 => 36387,
                2 => 36393,
                3 => 42093,
            ),
        36387 =>
            array (
                0 => 36360,
                1 => 36385,
                2 => 36431,
                3 => 39193,
            ),
        36389 =>
            array (
                0 => 36390,
                1 => 36414,
            ),
        36390 =>
            array (
                0 => 36389,
                1 => 36413,
                2 => 36416,
                3 => 36430,
            ),
        36393 =>
            array (
                0 => 36360,
                1 => 36385,
                2 => 36431,
                3 => 39193,
            ),
        36403 =>
            array (
                0 => 36423,
                1 => 36438,
            ),
        36413 =>
            array (
                0 => 36390,
                1 => 36414,
            ),
        36414 =>
            array (
                0 => 36389,
                1 => 36413,
                2 => 36416,
                3 => 36430,
            ),
        36416 =>
            array (
                0 => 36390,
                1 => 36414,
            ),
        36418 =>
            array (
                0 => 36423,
                1 => 36438,
            ),
        36422 =>
            array (
                0 => 36423,
                1 => 36438,
            ),
        36423 =>
            array (
                0 => 36403,
                1 => 36418,
                2 => 36422,
                3 => 36437,
            ),
        36430 =>
            array (
                0 => 36390,
                1 => 36414,
            ),
        36431 =>
            array (
                0 => 36378,
                1 => 36387,
                2 => 36393,
                3 => 42093,
            ),
        36437 =>
            array (
                0 => 36423,
                1 => 36438,
            ),
        36438 =>
            array (
                0 => 36403,
                1 => 36418,
                2 => 36422,
                3 => 36437,
            ),
        36444 =>
            array (
                0 => 36341,
                1 => 36346,
                2 => 36356,
            ),
        36445 =>
            array (
                0 => 36341,
                1 => 36346,
                2 => 36356,
            ),
        36448 =>
            array (
                0 => 36341,
                1 => 36346,
                2 => 36356,
            ),
        36340 =>
            array (
                0 => 36342,
                1 => 36343,
                2 => 36347,
                3 => 36351,
                4 => 36446,
                5 => 36447,
            ),
        36342 =>
            array (
                0 => 36340,
                1 => 36348,
                2 => 36355,
            ),
        36343 =>
            array (
                0 => 36340,
                1 => 36348,
                2 => 36355,
            ),
        36347 =>
            array (
                0 => 36340,
                1 => 36348,
                2 => 36355,
            ),
        36348 =>
            array (
                0 => 36342,
                1 => 36343,
                2 => 36347,
                3 => 36351,
                4 => 36446,
                5 => 36447,
            ),
        36351 =>
            array (
                0 => 36340,
                1 => 36348,
                2 => 36355,
            ),
        36355 =>
            array (
                0 => 36342,
                1 => 36343,
                2 => 36347,
                3 => 36351,
                4 => 36446,
                5 => 36447,
            ),
        36357 =>
            array (
                0 => 36370,
                1 => 36371,
                2 => 36372,
                3 => 36376,
                4 => 36388,
                5 => 40834,
                6 => 45947,
            ),
        36358 =>
            array (
                0 => 36370,
                1 => 36371,
                2 => 36372,
                3 => 36376,
                4 => 36388,
                5 => 40834,
                6 => 45947,
            ),
        36359 =>
            array (
                0 => 36370,
                1 => 36371,
                2 => 36372,
                3 => 36376,
                4 => 36388,
                5 => 40834,
                6 => 45947,
            ),
        36370 =>
            array (
                0 => 36357,
                1 => 36358,
                2 => 36359,
                3 => 36375,
                4 => 40833,
                5 => 45937,
            ),
        36371 =>
            array (
                0 => 36357,
                1 => 36358,
                2 => 36359,
                3 => 36375,
                4 => 40833,
                5 => 45937,
            ),
        36372 =>
            array (
                0 => 36357,
                1 => 36358,
                2 => 36359,
                3 => 36375,
                4 => 40833,
                5 => 45937,
            ),
        36373 =>
            array (
                0 => 36450,
                1 => 36451,
                2 => 38496,
            ),
        36374 =>
            array (
                0 => 36450,
                1 => 36451,
                2 => 38496,
            ),
        36375 =>
            array (
                0 => 36370,
                1 => 36371,
                2 => 36372,
                3 => 36376,
                4 => 36388,
                5 => 40834,
                6 => 45947,
            ),
        36376 =>
            array (
                0 => 36357,
                1 => 36358,
                2 => 36359,
                3 => 36375,
                4 => 40833,
                5 => 45937,
            ),
        36388 =>
            array (
                0 => 36357,
                1 => 36358,
                2 => 36359,
                3 => 36375,
                4 => 40833,
                5 => 45937,
            ),
        36401 =>
            array (
                0 => 36406,
                1 => 36424,
                2 => 36439,
            ),
        36405 =>
            array (
                0 => 36406,
                1 => 36424,
                2 => 36439,
            ),
        36406 =>
            array (
                0 => 36401,
                1 => 36405,
                2 => 36407,
                3 => 36421,
            ),
        36407 =>
            array (
                0 => 36406,
                1 => 36424,
                2 => 36439,
            ),
        36415 =>
            array (
                0 => 36450,
                1 => 36451,
                2 => 38496,
            ),
        36421 =>
            array (
                0 => 36406,
                1 => 36424,
                2 => 36439,
            ),
        36424 =>
            array (
                0 => 36401,
                1 => 36405,
                2 => 36407,
                3 => 36421,
            ),
        36439 =>
            array (
                0 => 36401,
                1 => 36405,
                2 => 36407,
                3 => 36421,
            ),
        36446 =>
            array (
                0 => 36340,
                1 => 36348,
                2 => 36355,
            ),
        36447 =>
            array (
                0 => 36340,
                1 => 36348,
                2 => 36355,
            ),
        36450 =>
            array (
                0 => 36373,
                1 => 36374,
                2 => 36415,
                3 => 36452,
            ),
        36451 =>
            array (
                0 => 36373,
                1 => 36374,
                2 => 36415,
                3 => 36452,
            ),
        36452 =>
            array (
                0 => 36450,
                1 => 38496,
            ),
        36578 =>
            array (
                0 => 36579,
                1 => 36586,
                2 => 36595,
            ),
        36579 =>
            array (
                0 => 36578,
                1 => 36580,
                2 => 36596,
                3 => 47799,
            ),
        36580 =>
            array (
                0 => 36579,
                1 => 36586,
                2 => 36595,
            ),
        36586 =>
            array (
                0 => 36578,
                1 => 36580,
                2 => 36596,
                3 => 47799,
            ),
        36595 =>
            array (
                0 => 36578,
                1 => 36580,
                2 => 36596,
            ),
        36596 =>
            array (
                0 => 36579,
                1 => 36586,
                2 => 36595,
                3 => 47799,
            ),
        36623 =>
            array (
                0 => 36652,
                1 => 36676,
            ),
        36650 =>
            array (
                0 => 36652,
                1 => 36676,
            ),
        36652 =>
            array (
                0 => 36623,
                1 => 36650,
                2 => 36675,
                3 => 36698,
            ),
        36657 =>
            array (
                0 => 36683,
                1 => 36684,
                2 => 42034,
            ),
        36669 =>
            array (
                0 => 36670,
                1 => 36682,
                2 => 41001,
            ),
        36670 =>
            array (
                0 => 36671,
                1 => 40998,
            ),
        36671 =>
            array (
                0 => 36670,
                1 => 36682,
                2 => 41001,
            ),
        36675 =>
            array (
                0 => 36652,
                1 => 36676,
            ),
        36676 =>
            array (
                0 => 36623,
                1 => 36650,
                2 => 36675,
                3 => 36698,
            ),
        36682 =>
            array (
                0 => 36669,
                1 => 36671,
                2 => 40998,
            ),
        36683 =>
            array (
                0 => 36657,
            ),
        36684 =>
            array (
                0 => 36657,
            ),
        36698 =>
            array (
                0 => 36652,
                1 => 36676,
            ),
        36591 =>
            array (
                0 => 36592,
                1 => 36690,
            ),
        36594 =>
            array (
                0 => 36592,
                1 => 36690,
            ),
        36624 =>
            array (
                0 => 36641,
                1 => 36646,
                2 => 36661,
            ),
        36625 =>
            array (
                0 => 36637,
                1 => 36638,
                2 => 36654,
                3 => 39203,
            ),
        36626 =>
            array (
                0 => 36637,
                1 => 36638,
                2 => 36654,
                3 => 39203,
            ),
        36633 =>
            array (
                0 => 36634,
                1 => 36695,
            ),
        36634 =>
            array (
                0 => 36633,
                1 => 36685,
                2 => 36696,
            ),
        36637 =>
            array (
                0 => 36625,
                1 => 36626,
                2 => 42092,
            ),
        36638 =>
            array (
                0 => 36625,
                1 => 36626,
                2 => 42092,
            ),
        36640 =>
            array (
                0 => 36641,
                1 => 36646,
                2 => 36661,
            ),
        36641 =>
            array (
                0 => 36624,
                1 => 36640,
                2 => 36677,
            ),
        36646 =>
            array (
                0 => 36624,
                1 => 36640,
                2 => 36677,
            ),
        36654 =>
            array (
                0 => 36625,
                1 => 36626,
                2 => 42092,
            ),
        36661 =>
            array (
                0 => 36624,
                1 => 36640,
                2 => 36677,
            ),
        36677 =>
            array (
                0 => 36641,
                1 => 36646,
                2 => 36661,
            ),
        36685 =>
            array (
                0 => 36634,
                1 => 36695,
            ),
        36695 =>
            array (
                0 => 36633,
                1 => 36685,
                2 => 36696,
            ),
        36696 =>
            array (
                0 => 36634,
                1 => 36695,
            ),
        36585 =>
            array (
                0 => 36587,
                1 => 36686,
                2 => 36687,
                3 => 36691,
            ),
        36587 =>
            array (
                0 => 36585,
                1 => 36588,
                2 => 36598,
            ),
        36588 =>
            array (
                0 => 36587,
                1 => 36686,
                2 => 36687,
                3 => 36691,
            ),
        36598 =>
            array (
                0 => 36587,
                1 => 36686,
                2 => 36687,
                3 => 36691,
            ),
        36602 =>
            array (
                0 => 36617,
                1 => 36629,
                2 => 36635,
                3 => 46561,
            ),
        36617 =>
            array (
                0 => 36602,
                1 => 36627,
                2 => 36673,
                3 => 39200,
                4 => 46563,
            ),
        36627 =>
            array (
                0 => 36617,
                1 => 36629,
                2 => 36635,
                3 => 46561,
            ),
        36629 =>
            array (
                0 => 36602,
                1 => 36627,
                2 => 36673,
                3 => 39200,
                4 => 46563,
            ),
        36630 =>
            array (
                0 => 36632,
                1 => 36656,
            ),
        36632 =>
            array (
                0 => 36630,
                1 => 36655,
                2 => 36659,
                3 => 36672,
            ),
        36635 =>
            array (
                0 => 36602,
                1 => 36627,
                2 => 36673,
                3 => 39200,
                4 => 46563,
            ),
        36645 =>
            array (
                0 => 36665,
                1 => 36678,
            ),
        36655 =>
            array (
                0 => 36632,
                1 => 36656,
            ),
        36656 =>
            array (
                0 => 36630,
                1 => 36655,
                2 => 36659,
                3 => 36672,
            ),
        36659 =>
            array (
                0 => 36632,
                1 => 36656,
            ),
        36663 =>
            array (
                0 => 36665,
                1 => 36678,
            ),
        36665 =>
            array (
                0 => 36645,
                1 => 36663,
                2 => 36666,
                3 => 36679,
            ),
        36666 =>
            array (
                0 => 36665,
                1 => 36678,
            ),
        36672 =>
            array (
                0 => 36632,
                1 => 36656,
            ),
        36673 =>
            array (
                0 => 36617,
                1 => 36629,
                2 => 36635,
                3 => 46561,
            ),
        36678 =>
            array (
                0 => 36645,
                1 => 36663,
                2 => 36666,
                3 => 36679,
            ),
        36679 =>
            array (
                0 => 36665,
                1 => 36678,
            ),
        36686 =>
            array (
                0 => 36585,
                1 => 36588,
                2 => 36598,
            ),
        36687 =>
            array (
                0 => 36585,
                1 => 36588,
                2 => 36598,
            ),
        36691 =>
            array (
                0 => 36585,
                1 => 36588,
                2 => 36598,
            ),
        36582 =>
            array (
                0 => 36583,
                1 => 36584,
                2 => 36590,
                3 => 36593,
                4 => 36688,
                5 => 36689,
                6 => 46452,
            ),
        36583 =>
            array (
                0 => 36582,
                1 => 36589,
                2 => 36597,
            ),
        36584 =>
            array (
                0 => 36582,
                1 => 36589,
                2 => 36597,
            ),
        36589 =>
            array (
                0 => 36583,
                1 => 36584,
                2 => 36590,
                3 => 36593,
                4 => 36688,
                5 => 36689,
                6 => 46452,
            ),
        36590 =>
            array (
                0 => 36582,
                1 => 36589,
                2 => 36597,
            ),
        36593 =>
            array (
                0 => 36582,
                1 => 36589,
                2 => 36597,
            ),
        36597 =>
            array (
                0 => 36583,
                1 => 36584,
                2 => 36590,
                3 => 36593,
                4 => 36688,
                5 => 36689,
                6 => 46452,
            ),
        36599 =>
            array (
                0 => 36612,
                1 => 36614,
                2 => 36615,
                3 => 36618,
                4 => 36631,
                5 => 40838,
                6 => 45950,
            ),
        36600 =>
            array (
                0 => 36612,
                1 => 36614,
                2 => 36615,
                3 => 36618,
                4 => 36631,
                5 => 40838,
                6 => 45950,
            ),
        36601 =>
            array (
                0 => 36612,
                1 => 36614,
                2 => 36615,
                3 => 36618,
                4 => 36631,
                5 => 40838,
                6 => 45950,
            ),
        36612 =>
            array (
                0 => 36599,
                1 => 36600,
                2 => 36601,
                3 => 36619,
                4 => 40836,
                5 => 45942,
            ),
        36613 =>
            array (
                0 => 36692,
                1 => 36694,
                2 => 38510,
            ),
        36614 =>
            array (
                0 => 36599,
                1 => 36600,
                2 => 36601,
                3 => 36619,
                4 => 40836,
                5 => 45942,
            ),
        36615 =>
            array (
                0 => 36599,
                1 => 36600,
                2 => 36601,
                3 => 36619,
                4 => 40836,
                5 => 45942,
            ),
        36616 =>
            array (
                0 => 36692,
                1 => 36694,
                2 => 38510,
            ),
        36618 =>
            array (
                0 => 36599,
                1 => 36600,
                2 => 36601,
                3 => 36619,
                4 => 40836,
                5 => 45942,
            ),
        36619 =>
            array (
                0 => 36612,
                1 => 36614,
                2 => 36615,
                3 => 36618,
                4 => 36631,
                5 => 40838,
                6 => 45950,
            ),
        36631 =>
            array (
                0 => 36599,
                1 => 36600,
                2 => 36601,
                3 => 36619,
                4 => 40836,
                5 => 45942,
            ),
        36643 =>
            array (
                0 => 36649,
                1 => 36664,
                2 => 36680,
            ),
        36647 =>
            array (
                0 => 36649,
                1 => 36664,
                2 => 36680,
            ),
        36648 =>
            array (
                0 => 36649,
                1 => 36664,
                2 => 36680,
            ),
        36649 =>
            array (
                0 => 36643,
                1 => 36647,
                2 => 36648,
                3 => 36660,
            ),
        36658 =>
            array (
                0 => 36692,
                1 => 36694,
                2 => 38510,
            ),
        36660 =>
            array (
                0 => 36649,
                1 => 36664,
                2 => 36680,
            ),
        36664 =>
            array (
                0 => 36643,
                1 => 36647,
                2 => 36648,
                3 => 36660,
            ),
        36680 =>
            array (
                0 => 36643,
                1 => 36647,
                2 => 36648,
                3 => 36660,
            ),
        36688 =>
            array (
                0 => 36582,
                1 => 36589,
                2 => 36597,
            ),
        36689 =>
            array (
                0 => 36582,
                1 => 36589,
                2 => 36597,
            ),
        36692 =>
            array (
                0 => 36613,
                1 => 36616,
                2 => 36658,
                3 => 36693,
            ),
        36693 =>
            array (
                0 => 36692,
                1 => 36694,
                2 => 38510,
            ),
        36694 =>
            array (
                0 => 36613,
                1 => 36616,
                2 => 36658,
                3 => 36693,
            ),
        38496 =>
            array (
                0 => 36373,
                1 => 36374,
                2 => 36415,
                3 => 36452,
            ),
        38504 =>
            array (
                0 => 36492,
                1 => 36495,
                2 => 36537,
                3 => 36572,
            ),
        38510 =>
            array (
                0 => 36613,
                1 => 36616,
                2 => 36658,
                3 => 36693,
            ),
        39203 =>
            array (
                0 => 36625,
                1 => 36626,
                2 => 42092,
                3 => 36625,
                4 => 36626,
            ),
        39200 =>
            array (
                0 => 36617,
                1 => 36629,
                2 => 36635,
                3 => 46561,
            ),
        40833 =>
            array (
                0 => 36370,
                1 => 36371,
                2 => 36372,
                3 => 36376,
                4 => 36388,
                5 => 40834,
            ),
        40834 =>
            array (
                0 => 36357,
                1 => 36358,
                2 => 36359,
                3 => 40833,
                4 => 36375,
            ),
        40835 =>
            array (
                0 => 36491,
                1 => 36493,
                2 => 36494,
                3 => 36497,
                4 => 36510,
            ),
        40837 =>
            array (
                0 => 36478,
                1 => 36479,
                2 => 36480,
                3 => 36498,
            ),
        40836 =>
            array (
                0 => 36612,
                1 => 36614,
                2 => 36615,
                3 => 36618,
                4 => 36631,
            ),
        40838 =>
            array (
                0 => 36599,
                1 => 36600,
                2 => 36601,
                3 => 36619,
            ),
        40975 =>
            array(
                36426,36440,40991,
            ),
        40991 =>
            array(
                36427,36429,40975,
            ),
        40995 =>
            array(
                36548,36550,40995,
            ),
        41000 =>
            array(
                36548,36550,40995,
            ),
        40998 =>
            array(
                36670,36682,41001,
            ),
        41001 =>
            array(
                40998,6669,36671,
            ),
        42093 =>
            array(
                39193,36431,36385,36360
            ),
        39193 =>
            array(
                42093,36378,36387,36393
            ),
        42090 =>
            array(
                39195,36396,36395
            ),
        39195 =>
            array(
                42090,36370,36383,36384
            ),

        42013 =>
            array(
                36417
            ),
        42094 =>
            array(
                39196,36552,36506,36481,46562
            ),
        39196 =>
            array(
                42094,42095,36496,36508,36514,46560,
            ),
        42014 =>
            array(
                36536
            ),
        42095 =>
            array(
                39200,36673,36627,36602,46563,
            ),
        42092 =>
            array(
                39203,36654,36638,36637
            ),
        42034 =>
            array(
                36657,
            ),
        45937 =>
            array(
                36426,36370,36371,36372,45947,36376,36388
            ),
        45947 =>
            array(
                45937,36357,36358,36359,36375
            ),
        45941 =>
            array(
                36491,36493,45949,36494,36497,36510,
            ),
        45949 =>
            array(
                36478,36479,45941,36480,36498,
            ),
        45942 =>
            array(
                36612,36614,36615,45950,36618,36631,
            ),
        45950 =>
            array(
                36599,36600,45942,36601,36619,
            ),
        46560 =>
            array(
                46562,39196,36552,36506,36481,
            ),
        46562 =>
            array(
                46560, 42094,36514,36508,36496
            ),
        46561 =>
            array(
                46563,39200,36673,36627,36602
            ),
        46563 =>
            array(
                46561,42095,36635,36629,36617,
            ),
        47796 =>
            array(
                35458,36465,36474,
            ),
        47050 =>
            array(
                36471,36569,
            ),
        47799 =>
            array(
                36579,36586,36595,
            ),
        36592 =>
            array(
                36594,47051,36591,
            ),
        36690 =>
            array(
                36594,47051,36591,
            ),
        47051 =>
            array(
                36592,36690,
            ),
        46451 =>
            array(
                36461,36468,36476
            ),
        46452 =>
            array(
                36582,36589,36597
            ),
        // 初中
        35746 =>
            array (

            ),
        35744 =>
            array (
                0 => 35751,
            ),

        35752 =>
            array (
                0 => 35751,
            ),
        35761 =>
            array (
                0 => 35751,
            ),
        35756 =>
            array (
                0 => 35751,
            ),
        35762 =>
            array (
                0 => 35751,
            ),
        35764 =>
            array (
                0 => 35751,
            ),
        35751 =>
            array (
                0 => 35746,
                1 => 35744,
                2 => 35752,
                3 => 35761,
                4 => 35756,
                5 => 35762,
                6 => 35764,
                7 => 35766,
                8 => 35745,
            ),
        35754 =>
            array (
                0 => 35750,
                1 => 35757,
                2 => 35749,
                3 => 35765,
                4 => 35758,
                5 => 35759,
                6 => 38167,
            ),
        35750 =>
            array (
                0 => 35754,
            ),
        35757 =>
            array (
                0 => 35754,
            ),
        35749 =>
            array (
                0 => 35754,
            ),
        35765 =>
            array (
                0 => 35754,
            ),
        35758 =>
            array (
                0 => 35754,
            ),
        35759 =>
            array (
                0 => 35754,
            ),
        35766 =>
            array (
                0 => 35751,
            ),
        35745 =>
            array (
                0 => 35751,
            ),
        35791 =>
            array (
                0 => 35792,
                1 => 35795,
                2 => 35793,
                3 => 35796,
            ),
        35793 =>
            array (
                0 => 35791,
                1 => 35794,
                2 => 35797,
                3 => 35798,
                4 => 38170,
            ),
        35794 =>
            array (
                0 => 35793,
                1 => 35796,
            ),
        35796 =>
            array (
                0 => 35791,
                1 => 38170,
                2 => 35794,
                3 => 35797,
                4 => 35798,
            ),
        35797 =>
            array (
                0 => 35793,
                1 => 35796,
            ),
        35798 =>
            array (
                0 => 35793,
                1 => 35796,
            ),
        35799 =>
            array (
                0 => 35800,
                1 => 35802,
                2 => 35805,
                3 => 35806,
            ),
        35800 =>
            array (
                0 => 35799,
                1 => 35801,
                2 => 35804,
            ),
        35801 =>
            array (
                0 => 35800,
                1 => 35802,
                2 => 35805,
                3 => 35806,
            ),
        35802 =>
            array (
                0 => 35799,
                1 => 35801,
                2 => 35804,
            ),
        35804 =>
            array (
                0 => 35800,
                1 => 35802,
                2 => 35805,
                3 => 35806,
            ),
        35805 =>
            array (
                0 => 35799,
                1 => 35801,
                2 => 35804,
            ),
        35806 =>
            array (
                0 => 35799,
                1 => 35801,
                2 => 35804,
            ),
        35807 =>
            array (
                0 => 35808,
                1 => 35810,
                2 => 35811,
                3 => 35812,
                4 => 35813,
                5 => 35814,
                6 => 35815,
            ),
        35808 =>
            array (
                0 => 35807,
                1 => 35809,
                2 => 39482,
            ),
        35809 =>
            array (
                0 => 35808,
                1 => 35810,
                2 => 35811,
                3 => 35812,
                4 => 35813,
                5 => 35814,
                6 => 35815,
            ),
        35810 =>
            array (
                0 => 35807,
                1 => 35809,
                2 => 39482,
            ),
        35811 =>
            array (
                0 => 35807,
                1 => 35809,
                2 => 39482,
            ),
        35812 =>
            array (
                0 => 35807,
                1 => 35809,
                2 => 39482,
            ),
        35813 =>
            array (
                0 => 35807,
                1 => 35809,
                2 => 39482,
            ),
        35814 =>
            array (
                0 => 35807,
                1 => 35809,
                2 => 39482,
            ),
        35815 =>
            array (
                0 => 35807,
                1 => 35809,
                2 => 39482,
            ),
        35911 =>
            array (
                0 => 35910,
                1 => 35915,
                2 => 35916,
            ),
        35912 =>
            array (
                0 => 35910,
                1 => 35915,
                2 => 35916,
            ),
        35913 =>
            array (
                0 => 35910,
                1 => 35915,
                2 => 35916,
            ),
        35914 =>
            array (
                0 => 35910,
                1 => 35915,
                2 => 35916,
            ),
        35910 =>
            array (
                0 => 35911,
                1 => 35912,
                2 => 35913,
                3 => 35914,
            ),
        35915 =>
            array (
                0 => 35911,
                1 => 35912,
                2 => 35913,
                3 => 35914,
            ),
        35916 =>
            array (
                0 => 35911,
                1 => 35912,
                2 => 35913,
                3 => 35914,
            ),
        35858 =>
            array (
                0 => 35859,
                1 => 35863,
                2 => 42221
            ),
        35860 =>
            array (
                0 => 35859,
                1 => 35863,
                2 => 35859,
                3 => 35863,
                4 => 42221
            ),
        35864 =>
            array (
                0 => 35859,
                1 => 35863,
                2 => 42221
            ),
        35863 =>
            array (
                0 => 35858,
                1 => 35860,
                2 => 35864,
                3 => 35861,
            ),
        35861 =>
            array(
                0 => 42221,
            ),
        42221 => array(
            35858,35860,35864,35861
        ),
        35866 =>
            array (
                0 => 35865,
                1 => 35869,
            ),
        35868 =>
            array (
                0 => 35865,
                1 => 35869,
                2 => 42220
            ),
        35865 =>
            array (
                0 => 35866,
                1 => 35868,
                2 => 38118,
                3 => 42220
            ),
        35869 =>
            array (
                0 => 35866,
                1 => 35868,
                2 => 38118,
            ),
        35871 =>
            array (
                0 => 35873,
                1 => 35874,
                2 => 35870,
                3 => 35875,
                4 => 35876,
            ),
        35873 =>
            array (
                0 => 35871,
                1 => 39019
            ),
        35874 =>
            array (
                0 => 35871,
                1 => 39019
            ),
        35870 =>
            array (
                0 => 35871,
                1 => 39019,
            ),
        35875 =>
            array (
                0 => 35871,
                1 => 39019
            ),
        35876 =>
            array (
                0 => 35871,
                1 => 39019
            ),
        35789 =>
            array (
                0 => 35769,
            ),
        35771 =>
            array (
                0 => 35769,
            ),
        35781 =>
            array (
                0 => 35769,
            ),
        35777 =>
            array (
                0 => 35769,
            ),
        35775 =>
            array (
                0 => 35769,
            ),
        35779 =>
            array (
                0 => 35769,
            ),
        35767 =>
            array (
                0 => 35769,
            ),
        35769 =>
            array (
                0 => 35789,
                1 => 35771,
                2 => 35781,
                3 => 35777,
                4 => 35775,
                5 => 35779,
                6 => 35767,
                7 => 35787,
                8 => 35772,
            ),
        35760 =>
            array (
                0 => 35763,
            ),
        35763 =>
            array (
                0 => 35760,
            ),

        35733 =>
            array (
                0 => 35728,
                1 => 35730,
                2 => 35732,
                3 => 35729,
                4 => 35734,
                5 => 35735,
                6 => 35731,
                7 => 37378,
            ),
        35731 =>
            array (
                0 => 35733,
            ),
        37378 =>
            array (
                0 => 35733,
            ),
        35728 =>
            array (
                0 => 35733,
            ),
        35730 =>
            array (
                0 => 35733,
            ),
        35732 =>
            array (
                0 => 35733,
            ),
        35729 =>
            array (
                0 => 35733,
            ),
        35734 =>
            array (
                0 => 35733,
            ),
        35735 =>
            array (
                0 => 35733,
            ),
        35787 =>
            array (
                0 => 35769,
            ),
        35772 =>
            array (
                0 => 35769,
                1 => 35784,
            ),
        35816 =>
            array (
                0 => 35818,
                1 => 35819,
                2 => 35820,
                3 => 35821,
            ),
        35817 =>
            array (
                0 => 35818,
                1 => 35819,
                2 => 35820,
                3 => 35821,
            ),
        35818 =>
            array (
                0 => 35816,
                1 => 35817,
            ),
        35819 =>
            array (
                0 => 35816,
                1 => 35817,
            ),
        35820 =>
            array (
                0 => 35816,
                1 => 35817,
            ),
        35821 =>
            array (
                0 => 35816,
                1 => 35817,
            ),
        35824 =>
            array (
                0 => 35827,
                1 => 35828,
                2 => 35829,
                3 => 35830,
            ),
        35825 =>
            array (
                0 => 35827,
                1 => 35828,
                2 => 35829,
                3 => 35830,
            ),
        35826 =>
            array (
                0 => 35827,
                1 => 35828,
                2 => 35829,
                3 => 35830,
            ),
        35827 =>
            array (
                0 => 35824,
                1 => 35825,
                2 => 35826,
                48225
            ),
        35828 =>
            array (
                0 => 35824,
                1 => 35825,
                2 => 35826,
                48225
            ),
        35829 =>
            array (
                0 => 35824,
                1 => 35825,
                2 => 35826,
                48225
            ),
        35830 =>
            array (
                0 => 35824,
                1 => 35825,
                2 => 35826,
                48225
            ),
        35833 =>
            array (
                0 => 35836,
                1 => 35837,
            ),
        35834 =>
            array (
                0 => 35836,
                1 => 35837,
            ),
        35835 =>
            array (
                0 => 35836,
                1 => 35837,
            ),
        35836 =>
            array (
                0 => 35833,
                1 => 35834,
                2 => 35835,
            ),
        35837 =>
            array (
                0 => 35833,
                1 => 35834,
                2 => 35835,
            ),
        35917 =>
            array (
                0 => 35920,
                1 => 35921,
                48517
            ),
        35918 =>
            array (
                0 => 35920,
                1 => 35921,
                48517
            ),
        35919 =>
            array (
                0 => 35920,
                1 => 35921,
                48517
            ),
        35920 =>
            array (
                0 => 35917,
                1 => 35918,
                2 => 35919,
            ),
        35921 =>
            array (
                0 => 35917,
                1 => 35918,
                2 => 35919,
            ),
        35884 =>
            array (
                0 => 35885,
                1 => 35886,
                2 => 43437,
                47079
            ),
        35887 =>
            array (
                0 => 35885,
                1 => 35886,
                2 => 43437,
                47079
            ),
        35885 =>
            array (
                0 => 35884,
                1 => 35887,
                2 => 38121,
                47069
            ),
        35886 =>
            array (
                0 => 35884,
                1 => 35887,
                2 => 38121,
                3 => 43462
            ),
        35880 =>
            array (
                0 => 35878,
            ),
        35881 =>
            array (
                0 => 35878,
                1 => 43399
            ),
        35877 =>
            array (
                0 => 35878,
                1 => 43399
            ),
        35879 =>
            array (
                0 => 35878,
                1 => 43437,
                2 => 43399
            ),
        35878 =>
            array(
                35888,35881
            ),
        35891 =>
            array (
                0 => 35890,
                1 => 35893,
                2 => 35889,
                3 => 35888,
                47074
            ),
        35890 =>
            array (
                0 => 35891,
                1 => 39030
            ),
        35893 =>
            array (
                0 => 35891,
                1 => 39030,
                47069
            ),
        35889 =>
            array (
                0 => 35891,
                1 => 39030
            ),
        35888 =>
            array (
                0 => 35891,
                1 => 39019,
                2 => 39030
            ),
        35790 =>
            array (
                0 => 35770,
            ),
        35773 =>
            array (
                0 => 35770,
            ),
        35782 =>
            array (
                0 => 35770,
            ),
        35778 =>
            array (
                0 => 35770,
            ),
        35776 =>
            array (
                0 => 35770,
            ),
        35780 =>
            array (
                0 => 35770,
            ),
        35768 =>
            array (
                0 => 35770,
            ),
        35770 =>
            array (
                0 => 35790,
                1 => 35773,
                2 => 35782,
                3 => 35778,
                4 => 35776,
                5 => 35780,
                6 => 35768,
                7 => 35788,
            ),
        35788 =>
            array (
                0 => 35770,
            ),
        35838 =>
            array (
                0 => 35840,
                1 => 35841,
                2 => 35842,
                3 => 35843,
            ),
        35839 =>
            array (
                0 => 35840,
                1 => 35841,
                2 => 35842,
                3 => 35843,
            ),
        35840 =>
            array (
                0 => 35838,
                1 => 35839,
            ),
        35841 =>
            array (
                0 => 35838,
                1 => 35839,
            ),
        35842 =>
            array (
                0 => 35838,
                1 => 35839,
            ),
        35843 =>
            array (
                0 => 35838,
                1 => 35839,
            ),
        35846 =>
            array (
                0 => 35849,
                1 => 35850,
                2 => 35851,
                3 => 35852,
            ),
        35847 =>
            array (
                0 => 35849,
                1 => 35850,
                2 => 35851,
                3 => 35852,
            ),
        35848 =>
            array (
                0 => 35849,
                1 => 35850,
                2 => 35851,
                3 => 35852,
            ),
        35849 =>
            array (
                0 => 35846,
                1 => 35847,
                2 => 35848,
                48226
            ),
        43472 =>
            array(
                35902,35903,43386,47080
            ),
        35850 =>
            array (
                0 => 35846,
                1 => 35847,
                2 => 35848,
                48226
            ),
        35851 =>
            array (
                0 => 35846,
                1 => 35847,
                2 => 35848,
                48226
            ),
        35852 =>
            array (
                0 => 35846,
                1 => 35847,
                2 => 35848,
                48226
            ),
        35922 =>
            array (
                0 => 35925,
                1 => 35926,
                48519
            ),
        35923 =>
            array (
                0 => 35925,
                1 => 35926,
                48519
            ),
        35924 =>
            array (
                0 => 35925,
                1 => 35926,
                48519
            ),
        35925 =>
            array (
                0 => 35922,
                1 => 35923,
                2 => 35924,
            ),
        35926 =>
            array (
                0 => 35922,
                1 => 35923,
                2 => 35924,
            ),
        35901 =>
            array (
                0 => 35902,
                1 => 35903,
                2 => 47080
            ),
        35904 =>
            array (
                0 => 35902,
                1 => 35903,
                47080
            ),
        35902 =>
            array (
                0 => 35901,
                1 => 35904,
                2 => 38125,
                3 => 43472,
                47071
            ),
        35903 =>
            array (
                0 => 35901,
                1 => 35904,
                2 => 38125,
                3 => 43472,
                4 => 38125,
                47071
            ),
        35895 =>
            array (
                0 => 35897,
                1 => 35894,
                2 => 35898,
                3 => 35896,
            ),
        35897 =>
            array (
                0 => 35895,
                1 => 43449
            ),
        35894 =>
            array (
                0 => 35895,
                1 => 43449
            ),
//        35898 =>
//            array (
//                0 => 35895,
//                1 => 43449
//            ),
        35898 =>
            array(
                35858,35860,35864,35861
            ),
        35896 =>
            array (
                0 => 35895,
                1 => 43449
            ),
        39019 =>
            array (
                0 => 35873,
                1 => 35874,
                2 => 35870,
                3 => 35875,
                4 => 35876,
            ),
        39030 =>
            array (
                0 => 35890,
                1 => 35893,
                2 => 35889,
                3 => 35888,
                4 => 47074
            ),
        39482 =>
            array (
                0 => 35808,
                1 => 35810,
                2 => 35811,
                3 => 35812,
                4 => 35813,
                5 => 35814,
                6 => 35815,
            ),
        39485 =>
            array (
                0 => 35834,
                1 => 35835,
            ),

        // 高中
        35110 =>
            array (
                0 => 35115,
                1 => 35119,
            ),
        35112 =>
            array (
                0 => 35115,
                1 => 35119,
            ),
        35113 =>
            array (
                0 => 35115,
                1 => 35119,
            ),
        35114 =>
            array (
                0 => 35115,
                1 => 35119,
            ),
        35115 =>
            array (
                0 => 35113,
                1 => 35118,
            ),
        35117 =>
            array (
                0 => 35115,
                1 => 35119,
            ),
        35118 =>
            array (
                0 => 35115,
                1 => 35119,
            ),
        35119 =>
            array (
                0 => 35113,
                1 => 35118,
            ),
        35120 =>
            array (
                0 => 35122,
                1 => 35124,
            ),
        35122 =>
            array (
                0 => 35120,
                1 => 35125,
            ),
        35124 =>
            array (
                0 => 35120,
                1 => 35125,
            ),
        35125 =>
            array (
                0 => 35122,
                1 => 35124,
            ),
        35128 =>
            array(
                35127,35131
            ),
        35129 =>
            array(
                35130,35126
            ),
        35130 =>
            array(
                35129
            ),
        35131 =>
            array(
                35128
            ),
        35236 =>
            array (
                0 => 35238,
            ),
        35237 =>
            array (
                0 => 35238,
                1 => 35239,
            ),
        35238 =>
            array (
                0 => 35236,
                1 => 43921
            ),
        35239 =>
            array (
                0 => 35236,
                1 => 35237,
            ),
        35240 =>
            array (
                0 => 35261,
            ),
        35261 =>
            array (
                0 => 35240,
                1 => 43473,
            ),
        35243 =>
            array (
                0 => 35245,
            ),
        35244 =>
            array (
                0 => 35245,
            ),
        35245 =>
            array (
                0 => 35243,
                1 => 35244,
            ),
        35306 =>
            array (
                0 => 35310,
                1 => 35320,
            ),
        35310 =>
            array (
                0 => 35322,
            ),
        35311 =>
            array (
                0 => 35321,
                1 => 35310,
            ),
        35320 =>
            array (
                0 => 35306,
                1 => 35311,
                2 => 35322,
            ),
        35322 =>
            array (
                0 => 35310,
            ),
        35326 =>
            array (
                0 => 35312,
            ),
        35327 =>
            array(
                0 => 35316
            ),
        35312 =>
            array (
                0 => 35326,
            ),
        35313 =>
            array (
                0 => 35312,
                1 => 35324,
            ),
        35324 =>
            array (
                0 => 35373,
                1 => 35313,
                2 => 35326,
            ),
        35373 =>
            array (
                0 => 35312,
                1 => 35324,
            ),
        35316 =>
            array (
                0 => 35319,
                1 => 35330,
                2 => 35327
            ),
        35319 =>
            array (
                0 => 35316,
            ),
        35330 =>
            array (
                0 => 35316,
            ),
        35284 =>
            array (
                0 => 35287,
            ),
        35281 =>
            array (
                0 => 35287,
                1 => 35290,
            ),
        35287 =>
            array (
                0 => 35284,
                1 => 44818
            ),
        35290 =>
            array (
                0 => 35281,
                1 => 35284,
                2 => 40596,
                3 => 39425,
            ),
        35271 =>
            array (
                0 => 35277,
            ),
        35277 =>
            array (
                0 => 35278,
            ),
        35272 =>
            array (
                0 => 35277,
            ),
        35278 =>
            array (
                0 => 35277,
            ),
        37735 =>
            array(
                0 => 35277,
            ),
        35299 =>
            array (
                0 => 35305,
            ),
        35302 =>
            array (
                0 => 35305,
            ),
        35296 =>
            array (
                0 => 35305,
            ),
        35305 =>
            array (
                0 => 35302,
            ),
        35146 =>
            array (
                0 => 35149,
                1 => 35150,
            ),
        35147 =>
            array (
                0 => 35149,
            ),
        35148 =>
            array (
                0 => 35149,
                1 => 35150,
            ),
        35149 =>
            array (
                0 => 35147,
            ),
        35150 =>
            array (
                0 => 35146,
                1 => 35147,
                2 => 35148,
            ),
        35152 =>
            array (
                0 => 35154,
            ),
        35153 =>
            array (
                0 => 35152,
            ),
        35154 =>
            array (
                0 => 35152,
            ),
        35156 =>
            array (
                0 => 35157,
            ),
        35157 =>
            array (
                0 => 35156,
            ),
        35158 =>
            array (
                0 => 35159,
            ),
        35159 =>
            array (
                0 => 35158,
            ),
        35192 =>
            array (
                0 => 35196,
                1 => 35198,
            ),
        35194 =>
            array (
                0 => 35198,
            ),
        35196 =>
            array (
                0 => 35192,
                1 => 35194,
            ),
        35198 =>
            array (
                0 => 35194,
            ),
        35202 =>
            array (
                0 => 35262,
            ),
        35262 =>
            array (
                0 => 35202,
            ),
        35206 =>
            array (
                0 => 35202,
            ),
        35208 =>
            array (
                0 => 35210,
            ),
        35210 =>
            array (
                0 => 35206,
                1 => 35208,
            ),
        35337 =>
            array(
                35343
            ),
        35358 =>
            array (
                0 => 35356,
                1 => 35357,
            ),
        35359 =>
            array (
                0 => 35356,
                1 => 35357,
            ),
        35356 =>
            array (
                0 => 35360,
            ),
        35360 =>
            array (
                0 => 35356,
            ),
        35357 =>
            array (
                0 => 35358,
                1 => 35359,
                2 => 35360,
            ),
        35323 =>
            array (
                0 => 35379,
                1 => 35381,
                2 => 35375,
            ),
        35372 =>
            array (
                0 => 35372,
            ),
        35375 =>
            array (
                0 => 35372,
            ),
        35379 =>
            array (
                0 => 35372,
                1 => 35323,
            ),
        35381 =>
            array (
                0 => 35372,
                1 => 35323,
            ),
        35343 =>
            array (
                0 => 35341,
                1 => 35342,
                2 => 35337
            ),
        35341 =>
            array (
                0 => 35343,
            ),
        35342 =>
            array (
                0 => 35343,
            ),
        35288 =>
            array (
                0 => 35282,
                1 => 35279,
                2 => 40597,
                3 => 39626,
            ),
        35282 =>
            array (
                0 => 35285,
            ),
        35285 =>
            array (
                0 => 35282,
            ),
        35279 =>
            array (
                0 => 35285,
                1 => 35288,
            ),
        35269 =>
            array (
                0 => 35275,
            ),
        35270 =>
            array (
                0 => 35275,
            ),
        35275 =>
            array (
                0 => 35276,
            ),
        35276 =>
            array (
                0 => 35275,
            ),
        35294 =>
            array (
                0 => 35303,
            ),
        35127 =>
            array(
                0 => 35128
            ),
        35297 =>
            array (
                0 => 35303,
            ),
        35300 =>
            array (
                0 => 35303,
            ),
        35303 =>
            array (
                0 => 35300,
            ),
        35160 =>
            array (
                0 => 35163,
                1 => 35164,
            ),
        35161 =>
            array (
                0 => 35163,
            ),
        35162 =>
            array (
                0 => 35163,
                1 => 35164,
            ),
        35163 =>
            array (
                0 => 35161,
            ),
        35164 =>
            array (
                0 => 35160,
                1 => 35161,
                2 => 35162,
            ),
        35166 =>
            array (
                0 => 35168,
            ),
        35167 =>
            array (
                0 => 35166,
            ),
        35168 =>
            array (
                0 => 35166,
            ),
        35170 =>
            array (
                0 => 35170,
            ),
        35171 =>
            array (
                0 => 35170,
            ),
        35172 =>
            array (
                0 => 35173,
            ),
        35173 =>
            array (
                0 => 35172,
            ),
        35193 =>
            array (
                0 => 35197,
                1 => 35199,
            ),
        35195 =>
            array (
                0 => 35199,
            ),
        35197 =>
            array (
                0 => 35193,
                1 => 35195,
            ),
        35199 =>
            array (
                0 => 35195,
            ),
        35203 =>
            array (
                0 => 35263,
            ),
        35263 =>
            array (
                0 => 35203,
            ),
        35207 =>
            array (
                0 => 35211,
            ),
        35209 =>
            array (
                0 => 35211,
            ),
        35211 =>
            array (
                0 => 35207,
                1 => 35209,
            ),
        35364 =>
            array (
                0 => 35362,
                1 => 35363,
            ),
        35365 =>
            array (
                0 => 35362,
                1 => 35363,
            ),
        35362 =>
            array (
                0 => 35366,
            ),
        35366 =>
            array (
                0 => 35362,
            ),
        35363 =>
            array (
                0 => 35366,
                1 => 35364,
                2 => 35365,
            ),
        35307 =>
            array (
                0 => 35374,
                1 => 35378,
                2 => 35380,
            ),
        35371 =>
            array (
                0 => 35374,
            ),
        35374 =>
            array (
                0 => 35371,
            ),
        35378 =>
            array (
                0 => 35307,
                1 => 35371,
            ),
        35380 =>
            array (
                0 => 35307,
                1 => 35371,
            ),
        35355 =>
            array (
                0 => 35350,
            ),
        35350 =>
            array (
                0 => 35355,
            ),
        35289 =>
            array (
                0 => 35283,
                1 => 35280,
                2 => 40599,
                3 => 39628,
            ),
        35283 =>
            array (
                0 => 35286,
            ),
        35286 =>
            array (
                0 => 35283,
            ),
        35280 =>
            array (
                0 => 35289,
                1 => 35286,
            ),
        35126 =>
            array(
                0 => 35129
            ),
        35267 =>
            array (
                0 => 35273,
            ),
        35268 =>
            array (
                0 => 35273,
            ),
        35273 =>
            array (
                0 => 35274,
            ),
        35274 =>
            array (
                0 => 35273,
            ),
        35295 =>
            array (
                0 => 35304,
            ),
        35298 =>
            array (
                0 => 35304,
            ),
        35301 =>
            array (
                0 => 35304,
            ),
        35304 =>
            array (
                0 => 35301,
            ),
        39424 =>
            array (
                0 => 35305,
            ),
        39425 =>
            array (
                0 => 35287,
                1 => 35290,
            ),
        39610 =>
            array (
                0 => 35303,
            ),
        39626 =>
            array (
                0 => 35285,
                1 => 35288,
            ),
        39613 =>
            array (
                0 => 35304,
            ),
        39628 =>
            array (
                0 => 35286,
                1 => 35289,
            ),
        40596 =>
            array (
                0 => 35287,
                1 => 35290,
            ),
        40597 =>
            array (
                0 => 35285,
                1 => 35288,
            ),
        40599 =>
            array (
                0 => 35286,
                1 => 35289,
            ),
        38118 =>
            array(
                0 => 35865,
                1 => 42220
            ),
        38170 =>
            array(
                0 => 35793,
                1 => 35796,
            ),
        38167 =>
            array(
                0 => 35754,
            ),
        38121 =>
            array(
                0 => 35885,
                1 => 35886,
                47079
            ),
        38125 =>
            array(
                0 => 35902,
                1 => 35903,
                47080
            ),
        43921 =>
            array(
                0 => 35238
            ),
        43473 =>
            array(
                35261
            ),
        44818 =>
            array(
                35287
            ),
        43223 =>
            array(
                35865,35869
            ),
        42220 =>
            array(
                35866,35868,38118,47068
            ),
        43399 =>
            array(
                35888,35881,35877,35879
            ),
        43386 =>
            array(
                35884,35887,38121,43462,47069
            ),
        47068 =>
            array(
                35865,35869,42220
            ),
        47073 =>
            array(
                35871,39019
            ),
        47074 =>
            array(
                35891,39030
            ),
        43462 =>
            array(
                47079
            ),
        47069 =>
            array(
                35885,35886,43437,47079
            ),
        47079 =>
            array(
                35884,35887,38121,43462,47069
            ),
        47071 =>
            array(
            35902,35903,43386,47080
            ),
        47080 =>
            array(
                35901,35904,38125,43472,47071
            ),
        43437 =>
            array(
                35901,35904,38125,43472,47071
            ),
        48225 =>
            array(
            35827,35828,35829,35830
            ),
        48226 =>
            array(
                35849,35850,35851,35852
            ),
        43227 =>
            array(
                35920,35921,48517
            ),
        48517 =>
            array(
                35917,35918,35919,43277
            ),
        43278 =>
            array(
                35925,35926,48519
            ),
        48519 =>
            array(
                35922,35923,35924,43278
            )






    );

    /**
     * @var array
     * type  1降 2升
     * scoreLine 分数线
     */
    public static $AdjustType = array (
        //小学
        36457 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35458 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36459 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36465 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36474 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36475 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36502 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36529 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36531 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36536 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36548 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36549 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36550 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36554 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36555 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36561 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36562 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36563 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36577 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36470 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36471 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36473 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36503 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36504 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36505 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36512 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36513 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36516 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36517 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36519 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36520 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36525 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36533 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36540 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36556 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36564 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36569 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36574 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36575 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36464 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36466 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36467 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36477 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36481 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36496 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36506 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36508 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36509 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36511 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36514 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36524 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36534 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35535 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36538 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36542 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36544 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36545 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36551 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36552 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36557 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36558 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36565 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36566 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36570 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36461 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36462 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36463 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36468 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36469 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36472 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36476 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36478 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36479 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36480 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36491 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36492 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36493 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36494 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36495 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36497 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36498 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36510 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36522 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36526 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36527 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36528 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36537 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36539 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36543 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36559 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36567 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36568 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36571 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36572 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36573 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36336 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36337 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36338 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36344 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36353 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36354 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36379 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36408 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36410 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36417 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36426 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36427 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36429 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36432 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36433 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36440 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36441 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36442 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36455 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36349 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36350 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36352 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36381 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36383 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36384 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        39202 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36391 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36392 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36395 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36396 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36398 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36399 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36404 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36412 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36419 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36434 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36443 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36449 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36453 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36454 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36341 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36345 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36346 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36356 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36360 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36378 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36385 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36387 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36389 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36390 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36393 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36403 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36413 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36414 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36416 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36418 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36422 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36423 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36430 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36431 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36437 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36438 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36444 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36445 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36448 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36340 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36342 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36343 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36347 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36348 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36351 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36355 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36357 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36358 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36359 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36370 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36371 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36372 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36373 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36374 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36375 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36376 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36388 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36401 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36405 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36406 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36407 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36415 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36421 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36424 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36439 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36446 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36447 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36450 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36451 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36452 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36578 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36579 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36580 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36586 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36595 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36596 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36623 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36650 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36652 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36657 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36669 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36670 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36671 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36675 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36676 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36682 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36683 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36684 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36698 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36591 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36594 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36624 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36625 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36626 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36633 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36634 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36637 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36638 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36640 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36641 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36646 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36654 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36661 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36677 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36685 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36695 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36696 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36585 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36587 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36588 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36598 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36602 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36617 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36627 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36629 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36630 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36632 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36635 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36645 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36655 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36656 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36659 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36663 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36665 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36666 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36672 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36673 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36678 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36679 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36686 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36687 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36691 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36582 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36583 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36584 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36589 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36590 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36593 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36597 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36599 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36600 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36601 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36612 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36613 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36614 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36615 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36616 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36618 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36619 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36631 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36643 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36647 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36648 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36649 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36658 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36660 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36664 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36680 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36688 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36689 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36692 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        36693 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        36694 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        38496 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        38504 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        38510 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        39203 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        39200 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        40833 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        40834 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        40835 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        40837 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        40836 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        40838 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        40975 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        40991 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        40995 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        41000 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        40998 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        41001 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        42093 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        39193 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        42090 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        39195 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        42013 =>
            array(
                'type' => 2,
                'scoreLine' => 60,
            ),
        42094 =>
            array(
                'type' => 2,
                'scoreLine' => 60,
            ),
        39196 =>
            array(
                'type' => 1,
                'scoreLine' => 60,
            ),
        42014 =>
            array(
                'type' => 2,
                'scoreLine' => 60,
            ),
        42095 =>
            array(
                'type' => 2,
                'scoreLine' => 60,
            ),
        42092 =>
            array(
                'type' => 2,
                'scoreLine' => 60,
            ),
        42034 =>
            array(
                'type' => 2,
                'scoreLine' => 60,
            ),
        45937 =>
            array(
                'type' => 2,
                'scoreLine' => 60,
            ),
        45947 =>
            array(
                'type' => 1,
                'scoreLine' => 60,
            ),
        45941 =>
            array(
                'type' => 2,
                'scoreLine' => 60,
            ),
        45949 =>
            array(
                'type' => 1,
                'scoreLine' => 60,
            ),
        45942 =>
            array(
                'type' => 2,
                'scoreLine' => 60,
            ),
        45950 =>
            array(
                'type' => 1,
                'scoreLine' => 60,
            ),
        46560 =>
            array(
                'type' => 2,
                'scoreLine' => 60,
            ),
        46562 =>
            array(
                'type' => 1,
                'scoreLine' => 60,
            ),
        46561 =>
            array(
                'type' => 2,
                'scoreLine' => 60,
            ),
        46563 =>
            array(
                'type' => 1,
                'scoreLine' => 60,
            ),
        47796 =>
            array(
                'type' => 1,
                'scoreLine' => 60,
            ),
        47050 =>
            array(
                'type' => 2,
                'scoreLine' => 60,
            ),
        47799 =>
            array(
                'type' => 1,
                'scoreLine' => 60,
            ),
        36592 =>
            array(
                'type' => 1,
                'scoreLine' => 60,
            ),
        36690 =>
            array(
                'type' => 1,
                'scoreLine' => 60,
            ),
        47051 =>
            array(
                'type' => 2,
                'scoreLine' => 60,
            ),
        46451 =>
            array(
                'type' => 1,
                'scoreLine' => 60,
            ),
        46452 =>
            array(
                'type' => 1,
                'scoreLine' => 60,
            ),
        // 初中
        35746 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35744 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35752 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35761 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35756 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35762 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35764 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35751 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35754 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35750 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35757 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35749 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35765 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35758 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35759 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35766 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35745 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35791 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35793 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35794 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35796 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35797 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35798 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35799 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35800 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35801 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35802 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35804 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35805 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35806 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35807 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35808 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35809 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35810 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35811 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35812 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35813 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35814 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35815 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35911 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35912 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35913 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35914 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35910 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35915 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35916 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35858 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35860 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35864 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35863 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35866 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35868 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35865 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35869 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35871 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35873 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35874 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35870 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35875 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35876 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35789 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35771 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35781 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35777 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35775 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35779 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35767 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35769 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35760 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35763 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35733 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35731 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        37378 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35728 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35730 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35732 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35729 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35734 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35735 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35787 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35772 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35816 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35817 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35818 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35819 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35820 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35821 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35824 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35825 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35826 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35827 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35828 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35829 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35830 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35833 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35834 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35835 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35836 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35837 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35917 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35918 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35919 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35920 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35921 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35884 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35887 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35885 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35886 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35880 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35881 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35877 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35879 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35891 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35890 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35893 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35889 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35888 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35790 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35773 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35782 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35778 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35776 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35780 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35768 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35770 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35788 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35838 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35839 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35840 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35841 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35842 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35843 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35846 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35847 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35848 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35849 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35850 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35851 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35852 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35922 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35923 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35924 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35925 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35926 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35901 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35904 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35902 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35903 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35895 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35897 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35894 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35898 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35896 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        39019 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        39030 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        39482 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        39485 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),

        // 高中
        35110 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35112 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35113 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35114 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35115 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35117 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35118 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35119 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35120 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35122 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35124 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35125 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35236 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35237 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35238 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35239 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35240 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35261 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35243 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35244 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35245 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35306 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35310 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35311 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35320 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35322 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35326 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35312 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35313 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35324 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35373 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35316 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35319 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35330 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35284 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35281 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35287 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35290 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35271 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35277 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35272 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35278 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        37735 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35299 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35302 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35296 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35305 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35146 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35147 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35148 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35149 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35150 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35152 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35153 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35154 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35156 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35157 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35158 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35159 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35192 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35194 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35196 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35198 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35202 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35262 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35206 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35208 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35210 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35358 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35359 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35356 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35360 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35357 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35323 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35372 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35375 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35379 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35381 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35343 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35341 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35342 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35288 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35282 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35285 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35279 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35269 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35270 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35275 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35276 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35294 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35297 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35300 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35303 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35160 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35161 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35162 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35163 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35164 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35166 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35167 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35168 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35170 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35171 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35172 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35173 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35193 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35195 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35197 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35199 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35203 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35263 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35207 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35209 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35211 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35364 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35365 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35362 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35366 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35363 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35307 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35371 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35374 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35378 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35380 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35355 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35350 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35289 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35283 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35286 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35280 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35267 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35268 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35273 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        35274 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35295 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35298 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35301 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        35304 =>
            array (
                'type' => 2,
                'scoreLine' => 60,
            ),
        39424 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        39425 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        39610 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        39626 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        39613 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        39628 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        40596 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        40597 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        40599 =>
            array (
                'type' => 1,
                'scoreLine' => 60,
            ),
        38118 =>
            array(
                'type' => 1,
                'scoreLine' => 60
            ),
        38121 =>
            array(
                'type' => 1,
                'scoreLine' => 60,
            ),
        38125 =>
            array(
                'type' => 1,
                'scoreLine' => 60,
            ),
        38170 =>
            array(
                'type' => 1,
                'scoreLine' => 60,
            ),
        38167 =>
            array(
                'type' => 2,
                'scoreLine' => 50,
            ),
        43921 =>
            array(
                'type' => 1,
                'scoreLine' => 60,
            ),
        43473 =>
            array(
                'type' => 1,
                'scoreLine' => 60
            ),
        35126 =>
            array(
                'type' => 1,
                'scoreLine' => 60,
            ),
        35127 =>
            array(
                'type' => 2,
                'scoreLine' => 60,
            ),
        35128 =>
            array(
                'type' => 1,
                'scoreLine' => 60,
            ),
        35129 =>
            array(
                'type' => 2,
                'scoreLine' => 60,
            ),
        35130 =>
            array(
                'type' => 1,
                'scoreLine' => 60,
            ),
        35131 =>
            array(
                'type' => 2,
                'scoreLine' => 60,
            ),
        35327 =>
            array(
                'type' => 1,
                'scoreLine' => 60,
            ),
        35337 =>
            array(
                'type' => 1,
                'scoreLine' => 60,
            ),
        35861 =>
            array(
                'type' => 1,
                'scoreLine' => 60
            ),
        44818 =>
            array(
                'type' => 1,
                'scoreLine' => 60
            ),
        43223 =>
            array(
                'type' => 1,
                'scoreLine' => 60
            ),
        42221 =>
            array(
                'type' => 2,
                'scoreLine' => 60
            ),
        42220 =>
            array(
                'type' => 2,
                'scoreLine' => 60
            ),
        43399 =>
            array(
                'type' => 2,
                'scoreLine' => 60
            ),
        35878 =>
            array(
                'type' => 2,
                'scoreLine' => 60
            ),
        43472 =>
            array(
                'type' => 1,
                'scoreLine' => 60
            ),
        43386 =>
            array(
                'type' => 2,
                'score' => 60
            ),
        47068 =>
            array(
                'type' => 1,
                'score' => 60
            ),
        47069 =>
            array(
                'type' => 1,
                'score' => 60
            ),
        47073 =>
            array(
                'type' => 1,
                'score' => 60
            ),
        47074 =>
            array(
                'type' => 1,
                'score' => 60
            ),
        47462 =>
            array(
                'type' => 1,
                'score' => 60
            ),
        47079 =>
            array(
                'type' => 2,
                'score' => 60
            ),
        47071 =>
            array(
                'type' => 1,
                'score' => 60
            ),
        47080 =>
            array(
                'type' => 2,
                'score' => 60
            ),
        47437 =>
            array(
                'type' => 2,
                'score' => 60
            ),
        48225 =>
            array(
                'type' => 2,
                'score' => 60
            ),
        48226 =>
            array(
                'type' => 2,
                'score' => 60
            ),
        43227 =>
            array(
                'type' => 1,
                'score' => 60
            ),
        43278 =>
            array(
                'type' => 1,
                'score' => 60
            ),
        48517 =>
            array(
                'type' => 2,
                'score' => 60
            ),
        48519 =>
            array(
                'type' => 2,
                'score' => 60
            )



    );
}
