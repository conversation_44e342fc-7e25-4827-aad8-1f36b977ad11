<?php
/**
 * Copyright (c) 2017 zuoyebang.com, Inc. All Rights Reserved
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @file: Gift.php
 * @date: 2017/11/23
 * @time: 14:23
 * @desc:
 */

class Hkzb_Util_Fudao_Gift
{
    //赠品信息
    const COURSE_GIFT_PENCIL_CASE_ID = 100;//笔袋
    const COURSE_GIFT_NOTEBOOK_ID    = 101;//笔记本
    const COURSE_GIFT_SCHOOL_BAG_ID  = 102;//书包
    //赠品价格信息（单位：分）
    static public $COURSE_GIFT_PRICE = array(
        self::COURSE_GIFT_PENCIL_CASE_ID => 2900,
        self::COURSE_GIFT_NOTEBOOK_ID    => 3000,
        self::COURSE_GIFT_SCHOOL_BAG_ID  => 29900,
    );
    //赠品名称
    static public $COURSE_GIFT_NAME = array(
        self::COURSE_GIFT_PENCIL_CASE_ID => 'pencilCase',
        self::COURSE_GIFT_NOTEBOOK_ID    => 'notebook',
        self::COURSE_GIFT_SCHOOL_BAG_ID  => 'schoolbag',
    );
    //赠品名称
    static public $GIFT_NAME = array(
        self::COURSE_GIFT_PENCIL_CASE_ID => '笔袋',
        self::COURSE_GIFT_NOTEBOOK_ID    => '笔记本',
        self::COURSE_GIFT_SCHOOL_BAG_ID  => '书包',
    );

    //退款类型标识
    const SIGN_OLD         = 0; //老退款逻辑，包含课程（和教材）
    const SIGN_COURSE      = 1; //课程退款（可能暂扣教材）
    const SIGN_MATERIAL    = 2; //教材退款
    const SIGN_PENCIL_CASE = 3;//笔袋
    const SIGN_NOTEBOOK    = 4;//笔记本
    const SIGN_SCHOOLBAG   = 5;//书包
    const SIGN_ENTITY      = 6;//实物
}