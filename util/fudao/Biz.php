<?php
/**
 * Copyright (c) 2017 zuoyebang.com, Inc. All Rights Reserved
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @file: Biz.php
 * @date: 2018/1/29
 * @time: 15:05
 * @desc:
 */

class Hkzb_Util_Fudao_Biz
{
    /**
     * 获取单门课程策略（主要面向列表页、详情页）
     * @param $arrSkuIdList
     * @param $intStudentUid
     * @param $strAppId
     * @param $strOS
     * @param $strPageType
     * @return array|bool
     */
    public static function getSingleSkuBiz($arrSkuIdList, $intStudentUid, $strAppId, $strOS, $strPageType)
    {
        if (empty($arrSkuIdList)) {
            return false;
        }
        //biz接口入参
        $intNowTimestamp = Hkzb_Util_FuDao::getCurrentTimeStamp();
        $res             = Zb_Service_Zbbiz::getSignalHitBiz($arrSkuIdList, $intStudentUid, $intNowTimestamp, $strAppId, $strOS, $strPageType);
        if ($res === false) {
            Bd_Log::warning("getsinglehitbiz fail, skuIdList:" . json_encode($arrSkuIdList) . " studentUid:$intStudentUid nowTimestamp:$intNowTimestamp appId:$strAppId os:$strOS pageType:$strPageType");

            return false;
        }

        return $res;
    }

    /**
     * 获取商品group策略（主要面向选课单、详情页-推荐组合）
     * @param $arrSkuIdList
     * @param $intStudentUid
     * @param $strAppId
     * @param $strOS
     * @param $strPageType
     * @return array|bool
     */
    public static function getGroupSkuBiz($arrSkuIdList, $intStudentUid, $strAppId, $strOS, $strPageType, $needLog=0)
    {
        if (empty($arrSkuIdList)) {
            return false;
        }
        //biz接口入参
        $intNowTimestamp = Hkzb_Util_FuDao::getCurrentTimeStamp();
        $res             = Zb_Service_Zbbiz::getHitBizList($arrSkuIdList, $intStudentUid, $intNowTimestamp, $strAppId, $strOS, $strPageType, $needLog);
        if ($res === false) {
            Bd_Log::warning("getHitBizList  fail, skuIdList:" . json_encode($arrSkuIdList) . " studentUid:$intStudentUid nowTimestamp:$intNowTimestamp appId:$strAppId os:$strOS pageType:$strPageType");

            return false;
        }

        return $res;
    }

    /**
     * 获取商品组的推荐策略信息
     * @param $arrSkuIdGroup
     * @param $intStudentUid
     * @param $strAppId
     * @param $strOS
     * @param $strPageType
     * @return array|bool
     */
    public static function getBizRecommendInfo($arrSkuIdGroup, $intStudentUid, $strAppId, $strOS, $strPageType)
    {
        if (empty($arrSkuIdGroup)) {
            return false;
        }
        $intNowTimestamp = Hkzb_Util_FuDao::getCurrentTimeStamp();
        $res             = Zb_Service_Zbbiz::getRecommendInfo($arrSkuIdGroup, $intStudentUid, $intNowTimestamp, $strAppId, $strOS, $strPageType);
        if ($res === false) {
            Bd_Log::warning("getRecommendInfo  fail, skuIdList:" . json_encode($arrSkuIdGroup) . " studentUid:$intStudentUid nowTimestamp:$intNowTimestamp appId:$strAppId os:$strOS pageType:$strPageType");

            return false;
        }

        return $res;
    }
}