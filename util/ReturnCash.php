<?php
/**
 * Created by PhpStorm.
 * User: <EMAIL>
 * Date: 2018/6/15
 * Time: 10:01
 */
class Hkzb_Util_ReturnCash
{
    /**
     * 获取用户信息
     * @param $itemId
     * @param $source
     * @param $secret
     * @return bool|mix
     */
    public static function getAccount($uid){
        if (empty($uid)) {
            Bd_Log::warning("Error:[param error], Detail[uid:$uid ]");
            return false;
        }
        $arrHeader = array(
            'pathinfo' => 'pay/transfer/getaccountinfo',
        );
        $arrParams = array(
            'uids' =>$uid,
        );
        $ret = self::requestCoupon($arrHeader, $arrParams);
        return $ret;
    }
    /**
     * 充值
     * @param $code
     * @param $uid
     * @param $cuid
     * @return bool|mix
     */
    public static function recharge($uid,$userName,$keyId,$activityId,$comment,$amount, $rechargeLimit = 0) {
        if (intval($uid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail[uid:$uid ]");
            return false;
        }
        $arrHeader = array(
            'pathinfo' => 'pay/transfer/recharge',
        );
        $arrParams = array(
            'uid'          => intval($uid),
            'userName'     => strval($userName),
            'keyId'        => intval($keyId),
            'comment'      => strval($comment),
            'activityId'   => intval($activityId),
            'amount'       => intval($amount),
            'rechargeLimit' => intval($rechargeLimit),

        );
        $ret = self::requestCoupon($arrHeader, $arrParams);
        return $ret;
    }


    /**
     * 更新用户身份信息
     * @param $code
     * @param $uid
     * @param $cuid
     * @return bool|mix
     */
    public static function updateAccount($uid,$account,$name,$uname,$phone){
        if (intval($uid) <= 0 || strlen($account) <= 0 || strlen($name)<=0 ||strlen($uname)<=0 ||strlen($phone)<=0){
            Bd_Log::warning("Error:[param error], Detail[uid:$uid account:$account name:$name uname:$uname phone:$phone] " );
            return false;
        }

        $arrHeader = array(
            'pathinfo' => 'pay/transfer/updateaccountinfo',
        );
        $arrPara = array(
            'uid'      => $uid,
            'account'  => $account,
            'realname' => $name,
            'idNumber' => 2,   //身份证信息临时下掉，给一个固定值到pay
            'uname'    => $uname,
            'phone'    => $phone,
        );

        $ret = self::requestCoupon($arrHeader, $arrPara);
        return $ret;
    }

    /**
     * 获取充值记录
     * @param $code
     * @param $uid
     * @param $cuid
     * @return bool|mix
     */
    public static function getRechargeLog($uid){
        if (empty($uid)) {
            Bd_Log::warning("Error:[param error], Detail[uid:$uid]");
            return false;
        }
        $arrHeader = array(
            'pathinfo' => 'pay/transfer/getrechargelog',
        );
        $arrPara = array(
            'uids'  => $uid,
        );

        $ret = self::requestCoupon($arrHeader, $arrPara);
        return $ret;
    }
    /**
     * 获取提现记录
     * @param $code
     * @param $uid
     * @param $cuid
     * @return bool|mix
     */
    public static function getTransferLog($uid){
        if (empty($uid)) {
            Bd_Log::warning("Error:[param error], Detail[uid:$uid]");
            return false;
        }

        $arrHeader = array(
            'pathinfo' => 'pay/transfer/gettransferlog',
            'cookie' => $_COOKIE,
        );
        $arrPara = array(
            'uids' =>$uid,
        );

        $ret = self::requestCoupon($arrHeader, $arrPara);
        return $ret;
    }


    /**
     * 发起提现接口
     * @param $code
     * @param $uid
     * @param $cuid
     * @return bool|mix
     */
    public static function submitTransfer($arrPara){
        $arrHeader = array(
            'pathinfo' => 'pay/transfer/submittransfer'
        );
        $uid = $arrPara['uid'];
        $amount = $arrPara['amount'];
        $arrPara = array(
           'uid' => $uid,
           'amount' => $amount,
        );
        $ret = self::requestCoupon($arrHeader, $arrPara);
        return $ret;
    }


    /**
     * 请求用户提现情况
     *
     * @param  mix $arrHeader
     * @param  mix $arrParams
     *
     * @return mix
     */
    private static function requestCoupon($arrHeader, $arrParams) {
        $ret = ral('zybpay', 'POST', $arrParams, 123, $arrHeader);
        if(false === $ret) {
            $errno           = ral_get_errno();
            $errmsg          = ral_get_error();
            $protocol_status = ral_get_protocol_code();
            Bd_Log::warning("Error:[service zybpay connect error], Detail:[errno:$errno errmsg:$errmsg protocol_status:$protocol_status]");
            return false;
        }


        $ret = json_decode($ret, true);
        $errno = intval($ret['errNo']);
        $errmsg = strval($ret['errstr']);
        if($errno > 0) {
            Bd_Log::warning("Error:[service zybpay process error], Detail:[errno:$errno errmsg:$errmsg]");
            return false;
        }
        return $ret['data'];
    }

}
