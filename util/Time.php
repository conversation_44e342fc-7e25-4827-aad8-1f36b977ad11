<?php

class Zb_Util_Time
{
    static $TIME = 0;

    public static function uTime($format = 'u', $uTimeStamp = null)
    {
        if (is_null($uTimeStamp))
            $uTimeStamp = microtime(true);

        $timestamp = floor($uTimeStamp);
        $milliseconds = round(($uTimeStamp - $timestamp) * 1000000);

        return date(preg_replace('`(?<!\\\\)u`', $milliseconds, $format), $timestamp);
    }

    /**
     * 毫秒级sleep
     * @param $elapse
     */
    public static function sleepMs($elapse)
    {
        $elapse = intval($elapse);
        if ($elapse <= 0) return;
        usleep($elapse * 1000);
    }

    /**
     * 获取毫秒级时间戳
     * @return int
     */
    public static function mTime()
    {
        list($f, $i) = explode(" ", microtime());
        return $i * 1000 + intval($f * 1000);
    }

    /**
     * 获取毫秒时间戳
     * @return float|int
     */
    public static function uTimeS() {
        list($f, $i) = explode(" ", microtime());
        return $i * 1000000 + intval($f * 1000000);
    }

    public static function now()
    {
        if (self::$TIME > 0) {
            return self::$TIME;
        }
        return Hkzb_Util_FuDao::getCurrentTimeStamp();
    }

    const FORMAT_DEFAULT = "Y-m-d H:i:s";

    /**
     * 获取格式化字符串
     * @param $time
     * @param string $formatter
     * @return string
     */
    public static function format($time=null, $formatter=self::FORMAT_DEFAULT) {
        if (is_null($time)) {
            $time = time();
        }
        return date($formatter, $time);
    }
}
