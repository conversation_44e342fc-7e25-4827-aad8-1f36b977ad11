<?php

class Lxjxlib_Util_Dingtalk
{
    const KEYWORD = '[LXJX监控报警]';

    const TOKEN_LXJXRD_ALARM = '162326eb265dda4c1cc87c257ff48b3456eaa17864103ece24c00279d30f5533'; // LXJX研发告警群

    const TOKEN_FENGNIAO_CONF_ALARM = 'ee55c6bd97593c9cf39b6f55a6cb38eebd32c21ddaad322907c238c881df8c99'; // 蜂鸟活动配置告警群

    const TOKEN_SIGN_UP_ALARM = '410b2feffa681eb81b5f725bfde063dbe71827f28cd3947e24eb7c065f8f1f2d'; // 报名预警告警群

    // 活动平台日志报警群
    const TOKEN_ACTPLAT_RDER = 'a079014917d675266ed3c9136399d5ec161f6480930d39a54c6f876e7a29bd38';

    // 测试群
    const TOKEN_TEST = '151d158472d46e88badddb5d50df066ea8a7dbc53a35b15e64df40122e818d55';

    // 钉钉群消息关键字--活动平台日志报警群
    const TOKEN_KEYWORD_ACTPLAT = 'Fatal报警';

    const ROBOT_URL = 'https://oapi.dingtalk.com/robot/send';

    private static $_keywords = [
        self::TOKEN_LXJXRD_ALARM          => self::KEYWORD,
        self::TOKEN_ACTPLAT_RDER        => self::TOKEN_KEYWORD_ACTPLAT,
        self::TOKEN_TEST                => self::TOKEN_KEYWORD_ACTPLAT,
        self::TOKEN_FENGNIAO_CONF_ALARM => self::KEYWORD,
    ];

    /**
     * @param        $content  发送钉钉消息内容
     * @param bool   $isAtAll @所有人时：true，否则为：false
     * @param array  $atMobiles 被@人的手机号(在content里添加@人的手机号)
     * @param string $accessToken 报警群token，没有使用默认token
     * @return bool|mixed
     */
    public static function sendDingtalkRobot($content, $isAtAll = true, $atMobiles = [], $accessToken = '')
    {
        if (empty($content)) {
            return false;
        }
        $idc = Hk_Util_Env::getRunEnv();
        if ($idc == Hk_Util_Env::RunEnvTest) {
            $content = sprintf("忽略IDC环境=%s\n%s", $idc, $content);
        }

        $content = self::KEYWORD . $content;

        // 组织发送消息参数
        $data = [
            'msgtype' => 'text',
            'text'    => ['content' => $content],
            'at'      => [
                'isAtAll'   => $isAtAll ? true : false,
                'atMobiles' => $isAtAll === true ? [] : $atMobiles,
            ],
        ];
        $url  = self::getUrl($accessToken);
        $ret  = self::send($url, $data);
        return $ret;
    }

    /**
     * @function        sendDingtalkMarkDown
     * @access          public
     * @date            2020-08-24
     * @desc            发送钉钉消息
     * @param           array  $markdown 消息内容
     * @param           string $accessToken 钉钉群token
     * @param           array  $atMobiles 需要@的钉钉账号绑定的手机号
     * @return          bool|string
     */
    public static function sendDingtalkMarkDown($markdown, $accessToken, $atMobiles = [])
    {
        if (empty($markdown) || empty($markdown['title']) || empty($markdown['text'])) {
            return false;
        }

        $idcName = Hk_Util_Env::getRunEnv();
        if (Hk_Util_Env::RunEnvTest == $idcName) {
            $markdown['text'] = sprintf("忽略IDC环境=%s\n%s", $idcName, $markdown['text']);
        }

        //组织发送消息参数
        $data = [
            'msgtype'  => 'markdown',
            'markdown' => $markdown,
            'at'       => [
                'isAtAll'   => empty($atMobiles) ? true : false,
                'atMobiles' => $atMobiles,
            ],
        ];

        $data = json_encode($data, JSON_UNESCAPED_UNICODE);
        $url  = self::getUrl($accessToken);
        $ret  = self::sendMarkdown($url, $data);
        return $ret;
    }

    private static function getUrl($accessToken)
    {
        if (empty($accessToken)) {
            $accessToken = self::TOKEN_LXJXRD_ALARM;
        }
        $url = self::ROBOT_URL . '?access_token=' . $accessToken;
        return $url;
    }

    private static function send($url, $data)
    {
        $ret = Lxjxlib_Util_Curl::callRequest($url, $data, 'POST', 5,
            'JSON', [], false, false, false);
        return $ret;
    }

    /**
     * @function        sendMarkdown
     * @access          private
     * @date            2020-08-24
     * @desc            发送markdown消息
     * @param           $url
     * @param           $data
     * @return          bool|string
     */
    private static function sendMarkdown($url, $data)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 1);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json;charset=utf-8']);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        // 线下环境不用开启curl证书验证, 未调通情况可尝试添加该代码
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        $data = curl_exec($ch);
        curl_close($ch);
        return $data;
    }

    public static function getKeywordByToken($token)
    {
        empty($token) && $token = self::TOKEN_LXJXRD_ALARM;
        if (isset(self::$_keywords[$token])) {
            return self::$_keywords[$token];
        }

        return '';
    }
}