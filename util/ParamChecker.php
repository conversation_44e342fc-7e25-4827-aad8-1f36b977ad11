<?php
/**
 * Copyright (c) 2018 zuoyebang.com, Inc. All Rights Reserved
 * @author: liu<PERSON><PERSON>@zuoyebang.com
 * @file: phplib/util/ParamChecker.php
 * @date: 2019/02/18
 * @file: 9:16 PM
 * @desc: 提供对公共参数的检查有效性检查方法
 */


/**
 * 提供对公共参数的检查有效性检查
 * Class Zb_Util_ParamChecker
 */
class Zb_Util_ParamChecker {
    const RULE_NUMERIC = "Numeric";
    const RULE_POSITIVE = "Positive";
    const RULE_NOT_NEGATIVE = "NotNegative";
    const RULE_RANGE = "Range";
    const RULE_DATE_VALID = "DateValid";
    const RULE_ENUM = "Enum";
    const RULE_DEFAULT = "Default";
    const RULE_IS_ARRAY = "IsArray";
    const RULE_IS_STRING = "IsString";
    const RULE_NOT_EMPTY = "NotEmpty";
    const RULE_LENGTH_LIMIT = "LengthLimit";
    const RULE_MB_LENGTH_LIMIT = "MBLengthLimit";
    const RULE_MB_LENGTH_FLOOR = "MBLengthFloor";
    const RULE_EMPTY_CASE = "EmptyCase";

    /** @var array parameter should be numeric and larger than zero */
    public static $numericPositive = [
        self::RULE_NUMERIC,
        self::RULE_POSITIVE,
    ];

    /** @var array parameter should be numeric and not negative */
    public static $numericNotNegative = [
        self::RULE_NUMERIC,
        self::RULE_NOT_NEGATIVE,
    ];

    /** @var array parameter should be a not empty string */
    public static $notEmptyString = [
        self::RULE_IS_STRING,
        self::RULE_NOT_EMPTY,
    ];

    /** @var array parameter should be numeric and not negative. If not pass, will be init to `0` */
    public static $default0NotNegative = [
        Zb_Util_ParamChecker::RULE_DEFAULT => 0,
        Zb_Util_ParamChecker::RULE_EMPTY_CASE => 0,
        Zb_Util_ParamChecker::RULE_NUMERIC,
        Zb_Util_ParamChecker::RULE_NOT_NEGATIVE,
    ];

    /** @var array parameter should be a default empty string */
    public static $defaultEmptyString = [
        self::RULE_DEFAULT => '',
        self::RULE_EMPTY_CASE => '',
        self::RULE_IS_STRING,
    ];

    public static function getLengthLimitRule($maxLength, $allowEmpty=false) {
        $rule = [self::RULE_IS_STRING];
        if($allowEmpty){
            $rule[self::RULE_DEFAULT] = '';
            $rule[self::RULE_EMPTY_CASE] = '';
        }else{
            $rule[] = self::RULE_NOT_EMPTY;
        }
        $rule[self::RULE_LENGTH_LIMIT] = $maxLength;
        return $rule;
    }

    /**
     * @param $params
     * @param array $ruleArr
     * @return array
     * @throws Hk_Util_Exception
     */
    public static function check($params, array $ruleArr) {
        $ret = [];
        foreach ($ruleArr as $field => $rules) {
            if(!isset($params[$field]) && !in_array(self::RULE_DEFAULT, array_keys($rules), true)) {
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "Has no param `{$field}`");
            }
            if(!isset($params[$field]) && in_array(self::RULE_DEFAULT, array_keys($rules), true)) {
                $ret[$field] = $rules[self::RULE_DEFAULT];
                continue;
            }
            if (in_array(self::RULE_EMPTY_CASE, array_keys($rules), true) &&
                isset($params[$field]) && self::checkIsEmpty($params[$field])) {
                $ret[$field] = $rules[self::RULE_EMPTY_CASE];
                continue;
            }
            unset($rules[self::RULE_DEFAULT]);
            unset($rules[self::RULE_EMPTY_CASE]);
            $value = $params[$field];
            if(!empty($rules)) {
                foreach ($rules as $rule => $rulePara) {
                    if (is_int($rule)) {
                        $rule = $rulePara;
                        $rulePara = null;
                    }
                    $method = "rule" . $rule;
                    if(!method_exists("Zb_Util_ParamChecker", $method)) {
                        throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "check rule {$method} not exists");
                    }
                    self::$method($field, $value, $rulePara);
                }
            }
            $ret[$field] = $value;
        }
        return $ret;
    }

    private static function rulePositive($field, $value) {
        if($value <= 0) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "param `{$field}` should be positive, value is '{$value}'");
        }
    }

    private static function ruleNumeric($field, &$value) {
        if(!is_numeric($value)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "param `{$field}` should be numeric, value is '{$value}'");
        }
        $value = intval($value);
    }

    private static function ruleNotNegative($field, $value) {
        if($value < 0) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "param `{$field}` should be not negative, value is '{$value}'");
        }
    }

    private static function ruleRange($field, $value, $rulePara=null) {
        if (empty($rulePara) || !isset($rulePara['min']) || !isset($rulePara['max'])) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "Invalid rule params, function:" . __FUNCTION__);
        }

        $min = intval($rulePara['min']);
        $max = intval($rulePara['max']);
        if($value < $min || $value > $max) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "param `{$field}` out of range {$min} ~ {$max}, value is '{$value}'");
        }
    }

    private static function ruleDateValid($field, &$value) {
        $time = strtotime($value);
        if(empty($time) || $time < 946656000 || $time > 4102416000) { // 有效时间范围 2000-01-01 00:00:00 ~ 2100-01-01 00:00:00
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "param `{$field}` is a not valid date, value is '{$value}'");
        }
        $value = date("Y-m-d H:i:s", $time);
    }

    private static function ruleEnum($field, $value, $rulePara=null) {
        if(empty($rulePara) || !is_array($rulePara)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "Invalid rule params, function:" . __FUNCTION__);
        }
        if (!in_array($value, $rulePara)) {
            $enums = implode(",", $rulePara);
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "param `{$field}` not one of enum value, " .
                "the value should be one of [{$enums}], but given is '{$value}'");
        }
    }

    private static function ruleIsString($field, &$value) {
        if(!is_string($value)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "param `{$field}` should be string, value is {$value}");
        }
        $value = strval($value);
    }

    private static function ruleNotEmpty($field, $value) {
        if(empty($value)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "param `{$field}` should be not Empty, value is {$value}");
        }
    }

    private static function ruleLengthLimit($field, $value, $rulePara=null) {
        $limit = $rulePara;
        if (!is_int($limit) || intval($limit <= 0)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "Invalid rule params, function:"
                . __FUNCTION__  .", the rule param of " . __FUNCTION__  ." should be positive integer, the given is {$rulePara}");
        }

        if(strlen($value) > $rulePara) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "the length of param `{$field}` should be less than {$rulePara}");
        }
    }

    private static function ruleMBLengthLimit($field, $value, $rulePara=null) {
        $limit = $rulePara;
        if (!is_int($limit) || intval($limit <= 0)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "Invalid rule params, function:"
                . __FUNCTION__  .", the rule param of " . __FUNCTION__  ." should be positive integer, the given is {$rulePara}");
        }
        if(mb_strlen($value, 'utf8') > $rulePara) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "the length of param `{$field}` should be less than {$rulePara}");
        }
    }

    private static function ruleMBLengthFloor($field, $value, $rulePara=null) {
        $limit = $rulePara;
        if (!is_int($limit) || intval($limit <= 0)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "Invalid rule params, function:"
                . __FUNCTION__  .", the rule param of " . __FUNCTION__  ." should be positive integer, the given is {$rulePara}");
        }
        if(mb_strlen($value, 'utf8') < $rulePara) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "the length of param `{$field}` should be bigger than {$rulePara}");
        }
    }

    private static function ruleIsArray($field, &$value, $rulePara) {
        // 针对是json字符串的情况
        if(is_string($value)) {
            $value=json_decode($value, true);
        }
        if(!is_array($value)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "param `{$field}` should be array or json array");
        }

        if(empty($rulePara)) return;
        foreach ($value as $one) {
            foreach ($rulePara as $rule => $ruleParas) {
                if (is_int($rule)) {
                    $rule = $ruleParas;
                    $ruleParas = null;
                }
                $method = "rule" . $rule;
                if(!method_exists("Zb_Util_ParamChecker", $method)) {
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "check rule {$method} not exists");
                }
                try {
                    self::$method($field, $one, $ruleParas);
                }catch (Hk_Util_Exception $e) {
                    $pieces = explode("--", $e->getMessage());
                    $usefulMsg = $pieces[count($pieces) - 1];
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "the value '{$one}' in array param `{$field}` not pass $method check. {$usefulMsg}");
                }
            }
        }
    }

    private static function checkIsEmpty($value) {
        if (is_int($value)) {
            return false;
        }
        if(is_string($value)) {
            if (strlen($value) == 0) {
                return true;
            }
            $decode = json_decode($value, true);
            if(is_array($decode) && empty($decode)) {
                return true;
            }else {
                return false;
            }
        }
        if (is_array($value)) {
            if (empty($value)) {
                return true;
            }else {
                return false;
            }
        }
        return false;
    }
}


define("ZB_UTIL_PARAM_CHECKER_TEST", false);
if (defined("ZB_UTIL_PARAM_CHECKER_TEST") && ZB_UTIL_PARAM_CHECKER_TEST) {

    if (!class_exists("Hk_Util_Exception_Test")) {
        class Hk_Util_Exception_Test extends Exception {
            public function __construct($code = 0, $message = "", Throwable $previous = null) {
                parent::__construct($message, $code, $previous);
            }
        }
        class_alias("Hk_Util_Exception_Test", "Hk_Util_Exception");
    }

    if (!class_exists("Hk_Util_ExceptionCodes_Test")) {
        class Hk_Util_ExceptionCodes_Test {
            const PARAM_ERROR = 1;
        }
        class_alias("Hk_Util_ExceptionCodes_Test", "Hk_Util_ExceptionCodes");
    }

    $params = [
        "table" => 12,
        "type" => 1,
        "cds" => 12,
        "sdf" => "[1,2,3]",
        "ccc" => 1,
        "lengthLimit" => "sdf",
        "date" =>  "Mon Mar  4 16:52:29 CST 2009",
        "name" => "ndfai",
        "empty" => [],
    ];

    $rules = [
        "table" => [
            Zb_Util_ParamChecker::RULE_RANGE => ["min" => 0, "max" => 100],
        ],
        "type" => [
            Zb_Util_ParamChecker::RULE_ENUM => [1,2,3],
        ],
        "cds" => [
            Zb_Util_ParamChecker::RULE_DEFAULT => 131,
            Zb_Util_ParamChecker::RULE_POSITIVE,
            Zb_Util_ParamChecker::RULE_NUMERIC,
        ],
        "sdf" => [
            Zb_Util_ParamChecker::RULE_IS_ARRAY => [
                Zb_Util_ParamChecker::RULE_POSITIVE,
                Zb_Util_ParamChecker::RULE_NUMERIC,
            ],
        ],
        "ccc" => [
            Zb_Util_ParamChecker::RULE_NOT_NEGATIVE,
        ],
        "lengthLimit" => [
            Zb_Util_ParamChecker::RULE_IS_STRING,
            Zb_Util_ParamChecker::RULE_LENGTH_LIMIT => 10,
        ],
        "date" => [
            Zb_Util_ParamChecker::RULE_DATE_VALID,
        ],
        "name" => [
            Zb_Util_ParamChecker::RULE_MB_LENGTH_FLOOR => 3,
        ],
        "empty" => [
            Zb_Util_ParamChecker::RULE_DEFAULT => "sdfa",
            Zb_Util_ParamChecker::RULE_EMPTY_CASE => "sdfa",
        ],
    ];
    try {
        var_dump(Zb_Util_ParamChecker::check($params, $rules));
    }catch (Hk_Util_Exception $e) {
        echo $e->getCode() . "   " . $e->getMessage().PHP_EOL;
    }
}