<?php


/**
 * 参数校验模块，负责进行参数校验以及按照参数列表生成调用顺序<br>
 * 配置方式：x:x:x<br>
 * require|options : 参数是否必须<br>
 * array|string|int|float : 参数类型<br>
 * default|not_null : 是否可以为空<br>
 *

 *
 * @filesource hk/rpc/util/Valid.php
 * <AUTHOR>
 * @version 1.0
 * @date    2018-11-01
 */
class Hk_Rpc_Util_Valid {

    /**
     * 对参数进行输入校验<br>
     * 同时自动按照配置文件指定的参数顺序排序参数列表<br>
     * 参数校验失败会抛出异常
     *
     * @throws Hk_Rpc_Error
     *
     * @param array       $pDesc        参数定义
     * @param array       $input        输入参数
     * @return array
     */
    public static function inputValid($paramConf, $input) {
        $params = array();
        if (empty($paramConf) || !is_array($paramConf)) {
            return $params;
        }

        foreach ($paramConf as $pName => $pDesc) {
            $pValue     = NULL;
            if (isset($input[$pName])) {         # 获取输入参数值
                $pValue = isset($input[$pName]) ? $input[$pName] : $pValue;
            }

            # 解析配置文件，获取参数配置
            $inputDesc  = self::paramDesc($pDesc);
            if (false === $inputDesc) {
                throw new Hk_Rpc_Error(Hk_Rpc_ErrorCodes::CONF_INVALID);
            }
            list($require, $type, $notNull) = $inputDesc;

            # 校验参数对应配置
            self::checkRequire($pName, $pValue, $require, $type);
            self::checkNotNull($pName, $pValue, $notNull);
            self::checkType($pName, $pValue, $type);
            $params[$pName] = $pValue;
        }
        return $params;
    }

    /**
     * 根据约定格式解析参数配置<br>
     * 参数配置具体实例：<br>
     * eg. require:array:not_null<br>
     * 必须，数组，并且数组不能为空<br>
     * eg. options:string:default<br>
     * 可选参数，字符串类型，默认为空
     *
     * @param string       $desc
     * @return boolean
     */
    private static function paramDesc($desc) {
        $conf = explode(':', $desc);
        if (empty($conf) || count($conf) < 2) {
            return false;
        }
        $require = false;
        $type    = "";
        $notNull = false;
        if ('require' === $conf[0]) {       # 参数是否必须
            $require = true;
        }
        $type    = strval($conf[1]);        # 参数类型
        if (isset($conf[2]) && 'not_null' === $conf[2]) {       # 参数是否可为空，为空会根据类型给默认值
            $notNull = true;
        }
        return array(
            $require,
            $type,
            $notNull,
        );
    }

    /**
     * 根据条件校验参数是否必须传递<br>
     * 如果非必须并未赋值，会根据类型赋值默认值。
     *
     * @param string      $pName
     * @param mixed       $pValue
     * @param boolean     $require
     * @param string      $type
     */
    private static function checkRequire($pName, &$pValue, $require, $type) {
        if (NULL !== $pValue) {     # 参数已传递，不用再校验此项
            return;
        }
        # 参数为空，判断是否必须，如果必须报错，非必须对$pValue初始化
        if ($require) {
            throw new Hk_Rpc_Error(Hk_Rpc_ErrorCodes::PARAM_ERROR, ["field" => $pName], "{$pName} not found");
        }
        # 对非必须参数进行默认值赋值
        switch ($type) {
            case 'array':
                $pValue = array();
                break;
            case 'string':
                $pValue = '';
                break;
            case 'int':
                $pValue = 0;
                break;
            case 'float':
                $pValue = 0.0;
                break;
            case 'boolean':
                $pValue = false;
                break;
            default:
                break;
        }
    }

    /**
     * 根据条件校验传入值的参数类型是否符合设定。
     * 如果校验失败会抛出参数错误异常
     *
     * @param string      $pName
     * @param mixed       $pValue
     * @param string      $type
     */
    private static function checkType($pName, &$pValue, $type) {
        $pass = false;
        switch ($type) {
            case 'array':
                $pass = is_array($pValue) ? true : false;
                break;
            case 'string':
                $pass = is_string($pValue) ? true : false;
                break;
            case 'int':
                if (is_numeric($pValue)) {
                    $pass   = true;
                    $pValue = intval($pValue);
                }
                break;
            case 'float':
                if (is_numeric($pValue)) {
                    $pass   = true;
                    $pValue = floatval($pValue);
                }
                break;
            case 'boolean':
                $pass = is_bool($pValue) ? true : false;
                break;
            case 'mixed':           # 未限制类型，谨慎使用
                $pass = true;
                break;
        }
        if (false === $pass) {
            throw new Hk_Rpc_Error(Hk_Rpc_ErrorCodes::PARAM_ERROR, ["field" => $pName], "{$pName} not {$type}");
        }
    }

    /**
     * 根据条件校验一个参数是否为空
     *
     * @param string      $pName
     * @param mixed       $pValue
     * @param boolean     $notNull
     */
    private static function checkNotNull($pName, $pValue, $notNull) {
        if ($notNull && NULL === $pValue) {
            throw new Hk_Rpc_Error(Hk_Rpc_ErrorCodes::PARAM_ERROR, ["field" => $pName], "{$pName} null");
        }
    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=0: */
