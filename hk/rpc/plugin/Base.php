<?php


/**
 * 通用插件基类
 *
 * @filesource hk/rpc/plugin/Base.php
 * <AUTHOR>
 * @version 1.0
 * @date    2018-11-08
 */
abstract class Hk_Rpc_Plugin_Base {

    const DICT_READ  = 1;
    const DICT_WRITE = 3;

    const CONTEXT = 'context';
    const FILTER  = 'filter';

    protected $app   = MAIN_APP;
    protected $pluginDict;

    # 上下文数据
    protected static $ctxDict = array(
        self::FILTER  => array('data' => array(), 'status' => self::DICT_READ),  # filter
        self::CONTEXT => array('data' => array(), 'status' => self::DICT_READ),  # 请求ctx
    );

    public function init() {}

    /**
     * 设置上下文数据
     *
     * @param string      $type
     * @param array       $value
     * @return boolean
     */
    protected static function setDict($type, array $value) {
        if (self::DICT_WRITE === (self::$ctxDict[$type]['status'] & self::DICT_WRITE)) {
            self::arrSuperMerge(self::$ctxDict[$type]['data'], $value);
            return true;
        }
        return false;
    }

    /**
     * 获取上下文数据
     *
     * @param string      $type
     * @return mixed:array|boolean
     */
    protected static function getDict($type) {
        if (self::DICT_READ === (self::$ctxDict[$type]['status'] & self::DICT_READ)) {
            return self::$ctxDict[$type]['data'];
        }
        return false;
    }

    /**
     * 开启字典写状态
     *
     * @param string      $type
     */
    protected static function openDict($type) {
        if (empty($type)) {
            return;
        }
        if (isset(self::$ctxDict[$type])) {
            self::$ctxDict[$type]['status'] = self::DICT_WRITE;
        }
    }

    /**
     * 关闭字典写状态
     *
     * @param string      $type
     */
    protected static function closeDict($type) {
        if (empty($type)) {
            return;
        }
        if (isset(self::$ctxDict[$type])) {
            self::$ctxDict[$type]['status'] = self::DICT_READ;
        }
    }

    /**
     * 增强多维数组合并，将&$arrHistoryLog数组中array类型的key全部展开成K-V形式
     * 并平行储存于&$arrHistoryLog对应数组中
     *
     * @param array &$arrHistory
     * @param array &$arrInputValue
     */
    protected static function arrSuperMerge(&$arrHistoryLog, &$arrValue) {
        if (!is_array($arrHistoryLog)) {
            $arrHistoryLog = array();
        }
        foreach ($arrValue as $k => &$v) {
            if (!is_array($v)) {
                $arrHistoryLog[$k] = $v;
            } else {
                self::arrSuperMerge($arrHistoryLog[$k], $v);
            }
        }
    }

    public function startupInit() {
        self::openDict($this->pluginDict);
    }

    public function startupFinish() {
        self::closeDict($this->pluginDict);
    }

    public function shutdownInit() {
    }

    public function shutdownFinish() {
    }

    /**
     * 插件启动主逻辑，需要实现
     *
     * @param Ap_Request_Abstract  $request
     * @param Ap_Response_Abstract $response
     */
    abstract public function startup(Ap_Request_Abstract $request, Ap_Response_Abstract $response);

    /**
     * 插件关闭主逻辑，需要实现
     *
     * @param Ap_Request_Abstract  $request
     * @param Ap_Response_Abstract $response
     */
    abstract public function shutdown(Ap_Request_Abstract $request, Ap_Response_Abstract $response);
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
