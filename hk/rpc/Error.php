<?php


/**
 * rpc请求错误异常类
 *
 * @filesource hk/rpc/Error.php
 * <AUTHOR>
 * @version 1.0
 */
class Hk_Rpc_Error extends Exception {


    private $errno;        # 错误码
    private $errmsg;       # 错误码对应说明
    private $errDetail;    # 详细出错信息, 会记录在日志便于快速定位问题
    private $errData;      # 返回调用方的数据

    public function __construct($errno, $errData = array(), $errDetails = '', $depth = 0) {
        $appErrClass = ucfirst(Bd_AppEnv::getCurrApp()) . "_ErrorCodes";
        if (isset(Hk_Rpc_ErrorCodes::$codes[$errno])) {             # 全局错误信息，定义在Hk_Rpc_ErrorCodes
            $baseMsg = Hk_Rpc_ErrorCodes::$codes[$errno];
        } elseif (class_exists($appErrClass) && isset($appErrClass::$appCodes[$errno])) {             # 获取app错误定义：library/app/ErrorCodes.php
            $baseMsg = $appErrClass::$appCodes[$errno];
        } else {
            $baseMsg = 'unknown error';
        }
        $this->errno     = $errno;
        $this->errmsg    = $baseMsg;
        $this->errData   = $errData;
        $this->errDetail = !empty($errDetails) ? "{$baseMsg}, {$errDetails}" : $baseMsg;

        $logArg = array(
            'errno'  => $this->errno,
            'errmsg' => $this->errmsg,
        );
        if (is_array($errData) && !empty($errData)) {
            $logArg = array_merge($logArg, $errData);
        }

        $strace   = $this->getTrace();
        $class    = @$strace[0]['class'];
        $type     = @$strace[0]['type'];
        $function = @$strace[0]['function'];
        if (NULL !== $class) {
            $function = "{$class}{$type}{$function}";
        }
        Bd_Log::warning("{$function} throw Exception, " . $this->errDetail, $errno, $logArg, $depth + 1);
        parent::__construct($this->errmsg, $errno);
    }

    public function getErrno() {
        return $this->errno;
    }

    public function getErrmsg() {
        return $this->errmsg;
    }

    public function getErrDetail() {
        return $this->errDetail;
    }

    public function getErrData() {
        return $this->errData;
    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=0: */
