<?php

class Hk_Rmq_Producer {
    const NoDelay = 0;
    const Second = 1;
    const Seconds5 = 2;
    const Seconds10 = 3;
    const Seconds30 = 4;
    const Minute1 = 5;
    const Minutes2 = 6;
    const Minutes3 = 7;
    const Minutes4 = 8;
    const Minutes5 = 9;
    const Minutes6 = 10;
    const Minutes7 = 11;
    const Minutes8 = 12;
    const Minutes9 = 13;
    const Minutes10 = 14;
    const Minutes20 = 15;
    const Minutes30 = 16;
    const Hour1 = 17;
    const Hours2 = 18;

    const defaultHost = "unix:/usr/local/var/run/rmq.sock";
    const defaultURL = "http://localhost/msg/send";
    const cmdURL = "http://localhost/cmd/send";

    const cmdServicePrefix = "cmd-";
    const headerPrefix = "X-Zyb-Mq-";

    private static $rocketType = "mq";

    private $service;
    private $conf;

    public function __construct($service = "") {
        $this->service = $service;
        if(!empty($this->service)) {
            $this->service = $service;
            $this->conf = Bd_Conf::getConf("/ral/services/".$this->service);
        }

        if(!isset($this->conf["domain"]) || empty($this->conf["domain"])) {
            $this->conf["domain"] = self::defaultHost;
        }

        if(!isset($this->conf["timeout"]) || empty($this->conf["timeout"])) {
            $this->conf["timeout"] = 2; //second
        }

        if(!isset($this->conf["connectTimeOut"]) || empty($this->conf["connectTimeOut"])) {
            $this->conf["connectTimeOut"] = 0.5; //second
        }

        if(!isset($this->conf["retry"]) || empty($this->conf["retry"])) {
            $this->conf["retry"] = 1;
        }
    }

    /*
     * input:
     * $key: 每个消息在业务层面的唯一标识码要设置到key字段，方便将来定位消息丢失问题。
     *       服务器会为每个消息创建索引（哈希索引），应用可以通过topic、key来查询这条消息内容，
     *       以及消息被谁消费。由于是哈希索引，请务必保证key尽可能唯一，这样可以避免潜在的哈希冲突。
     *
     *
     * $tag: 单条消息最多只能指定一个 Tag
     * $header: 自定义消息头 //为兼容php、go等不同语言的header格式规范，统一标准：X-Zyb-Mq-Keyname
     *
     * output:
     *  {
     *     "msgID": "AC1DF08D000022CF000001E5FB2DDCF1"
     *  }
     *  当且仅当errNo=0时表示发送成功，且返回msgID。
     */

    public function send($body, $tag = "", $shard = "", $key = "", $xHeaders = [], $delay = self::NoDelay) {
        $queryParam = [
            "service" => $this->service,
            "tag"     => $tag,
            "key"     => $key,
            "delay"   => $delay,
            "shard"   => $shard,
        ];
        $header = [
            "Content-Type" => "application/octet-stream",
        ];

        foreach($xHeaders as $k => $v) {
            $key = self::canonicalMIMEHeaderKey(self::headerPrefix.$k);
            $header[$key] = $v;
        }

        $ret = $this->request(self::defaultURL, $header, $body, $queryParam);
        if($ret != false){
            return $ret;
        }else{
            return false;
        }
    }

    /*
     * input:
     * $commandNo 命令号
     * $data array 消息体
     * $shard string 分片参数, 如果消息有顺序要求则需要指定分片参数, 如用户ID,订单ID的值
     *
     * return: 成功返回transID, 失败返回false
     * {
     *    "_transid": "134567987978934324",
     *    "_error_no": 0,
     *    "_error_msg": "OK"
     * }
    */
    public function sendCmd($commandNo, $data, $shard = "") {
        $data['_cmd']     = strval($commandNo);
        $data['command_no'] = $commandNo;

        // if (Hk_Util_Tools::isTestRequest()) {
        //     $data['skip'] = Hk_Util_Tools::TEST_TAG;
        // }
        if (defined('LOG_ID')) {
            $data['_log_id'] = intval(LOG_ID);
        }
        if ($data["_caller_uri"] == "") {
            $data["_caller_uri"] = "\x00";
        }

        $data['_provider']       = "RAL";
        $data['_cluster']        = "RMQ";
        $data['_idc']            = "cn";
        $data['_client_ip']      = Bd_Ip::getLocalIp();
        $data['_commit_time']    = time();
        $data['_commit_time_us'] = intval((microtime(true) - time())*1000000);
        $body = mc_pack_array2pack($data);

        $service = self::cmdServicePrefix . $data['_cmd'];
        $queryParam = [
            "service" => $service,
            "tag"     => strval($commandNo),
            "delay"   => self::NoDelay,
            "shard"   => $shard,
        ];
        $header = [
            "Content-Type" => "application/octet-stream",
        ];
        $ret = $this->request(self::cmdURL, $header, $body, $queryParam);
        if ($ret != false) {
            return $ret;
        }else{
            return false;
        }
    }

    protected function request($url, $header, $body, $queryParam) {
        // 超时时间追加到header中
        $header["timeout"] = $this->conf["timeout"];
        $header["connectTimeOut"] = $this->conf["connectTimeOut"];

        $req = [
            "header"   => $header,
            "convert"  => "string",
            "host"     => $this->conf["domain"],
            "url"      => $url."?".http_build_query($queryParam),
            "method"   => Ext_Ral_HttpCurl::METHOD_POST,
            "input"    => $body,
            "retry"    => $this->conf["retry"],
            "reqProxy" => 0,
        ];
        $ret = Ext_Ral_HttpCurl::instance()->curl($req);

        $ralArg = [
            "idc"         => Ext_Ral_Ral::ralGetIdc(),
            "local_ip"    => Bd_Ip::getLocalIp(),
            "prot"        => self::$rocketType,
            "service"     => $this->service,
            "method"      => __METHOD__,
            "querystring" => http_build_query($queryParam)
        ];

        if($ret == false) {
            $ralArg['errno'] = -1;
            $ralArg['errmsg'] = false;
            ral_write_log(LOG_TYPE_WARN, "RAL", $ralArg);
            return false;
        }

        $resp = json_decode($ret["data"], true);
        if(isset($resp["errNo"]) && $resp["errNo"] != 0) {
            $ralArg['errno'] = -1;
            $ralArg['errmsg'] = json_encode($ret["data"]);
            ral_write_log(LOG_TYPE_WARN, "RAL", $ralArg);
            return false;
        }

        $ralArg['errno'] = 0;
        $ralArg['data'] = json_encode($resp['data']);
        ral_write_log(RAL_LOG_SUM_SUCC, "RAL", $ralArg);

        return $resp['data'];
    } 

    //按标准处理header格式, eg: "zyb_mq-key" => "Zyb-Mq-Key"
    public static function canonicalMIMEHeaderKey($strKey) {
        if(strlen($strKey) < 1) {
            return "";
        }

        //下划转中划
        $strKey = str_replace("_", "-", trim($strKey));

        $upper = true;

        for($i = 0; $i < strlen($strKey); $i++) {
            $isChar = preg_match('/[a-zA-Z]/', $strKey[$i], $matchs);

            if($upper && $isChar) {
                $strKey[$i] = strtoupper($strKey[$i]);
            } elseif(!$upper && $isChar) {
                $strKey[$i] = strtolower($strKey[$i]);
            }

            // for next time
            $upper = $strKey[$i] == "-";
        }

        return $strKey;
    }
}