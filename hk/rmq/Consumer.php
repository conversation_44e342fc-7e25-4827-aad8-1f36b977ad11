<?php

abstract class Hk_Rmq_Consumer extends Ap_Action_Abstract {

    // 子类特有逻辑，强制子类必须实现
    abstract protected function invoke($body, $msgID, $topic, $tag, $extParams);

    // 注意：php获取到的http header会默认转大写，并中划线转下划线
    private static $headerPrefix = "HTTP_X_ZYB_MQ_";

    // response
    protected $_tplData = array(
        'errNo'  => 0,
        'errMsg' => 'success',
        'data'   => array(),
    );

    public function execute() {
        try {
            $body = file_get_contents('php://input');
            $extParams = [
                "key"    => $_GET["key"],
                "shard"  => $_GET["shard"],
                "header" => $this->getHeader(),
            ];


            Hk_Util_Log::start('ts_execute');
            // 业务回调
            $out = $this->invoke($body, $_GET["msgID"], $_GET["topic"], $_GET["tag"], $extParams);
            Hk_Util_Log::stop('ts_execute');

        } catch(Exception $e) {
            $errMsg = "Caught exception: "."file: ".$e->getFile()." Line".$e->getLine()." Message:".$e->getMessage();
            Bd_Log::warning($errMsg);
            return $this->renderOut($e->getCode(), $errMsg);
        }

        if($out === false) {
            // fail
            return $this->renderOut(-1, "fail");
        }
        // success
        return $this->renderOut(0, "success");
    }

    protected function getHeader() {
        $headers = array();
        foreach($_SERVER as $key => $value) {
            if(self::$headerPrefix == substr($key, 0, 14)) {
                $key = Hk_Rmq_Producer::canonicalMIMEHeaderKey(substr($key, 14));
                $headers[$key] = $value;
            }
        }
        return $headers;
    }

    protected function renderOut($errNo = 0, $errMsg = "success") {
        Bd_Log::addNotice("ProcessResult", $errMsg);

        $this->_tplData = [
            "errNo"  => $errNo,
            "errMsg" => $errMsg,
            "data"   => [],
        ];

        $json = json_encode($this->_tplData, JSON_FORCE_OBJECT);
        if($json === false) {
            Bd_Log::warning("json_encode failed");
        }

        header('Content-type:application/json; charset=UTF-8');

        // upsHeader 设定
        $upsErrNo = Hk_Util_UpsErrorHeader::getUpsErrNo();
        if($upsErrNo != -1) {
            header('X_BD_UPS_ERR_NO: '.$upsErrNo);
        }
        $upsErrMsg = Hk_Util_UpsErrorHeader::getUpsErrMsg();
        if($upsErrMsg != "") {
            header('X_BD_UPS_ERR_MSG: '.$upsErrMsg);
        }

        echo $json;
        return true;
    }
}