<?php
/***************************************************************************
 *
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/



/**
 * @since 2018-12-17 清空代码
 *
 * @deprecated
 *
 * @file SysMsg.php
 * <AUTHOR>
 * @date 2017-06-12
 * @brief 系统消息
 **/
class Hk_Ds_News_SysMsg {

    public function getSysMsgInfo($intMid) {
        return array();
    }

    public function getSysMsgList($intLimit = 200, $intOffset = 0) {
        return array();
    }

    public function setSendNum($intMid, $intNum) {
        return false;
    }

    public function addSysMsg($arrInput) {
        return array();
    }


    public function getSysMsgListByConds($arrFields, $arrConds, $arrAppends = null) {
        return array();
    }


    public function getSysMsgCount($arrFields, $arrConds, $arrAppends = null) {
        return array();
    }


    public function updateSysMsg($arrFields, $arrConds, $arrAppends = null) {
        return array();
    }

    public function getInsertID() {
        return array();
    }
}
