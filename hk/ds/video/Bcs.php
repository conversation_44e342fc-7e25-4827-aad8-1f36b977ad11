<?php
class Hk_Ds_Video_Bcs {
    protected $arrConf;
    const FLAG = 'MBOT';
    const URL_FORMAT = 'http://%s/%s%s?sign=%s';
    const METHOD_GET = 'GET';
    const METHOD_POST = 'POST';
    const METHOD_PUT = 'PUT';
    public function __construct() {
        //$this->arrConf = Bd_Conf::getConf ( 'cms/napi/video' );
        $this->arrConf = array(
            'bcs'=>array (
            	'ak'     => 'HjAjDNzjUQDwq5ApviPqKjEauXoKXF',
                'sk'     => 'fo4FrH5RbKUO1PfAyvr9VvZGJEQhJCxDHfL',
                'domain' => 'bs.baidu.com',
            )
        );
        /*
            $this->arrConf = array (
            	'ak'     => 'vyUr2cMa9OsUyEMcSLkoXk',
                'sk'     => 'IFW13xGRj6lQwEcNCq6Xf0j9Sui7',
                'domain' => 'bcs-sandbox.baidu.com',
            );

           if (empty($this->arrConf)) { throw new Napi_Exception(Napi_ExceptionCodes::OTHER_ERROR); }
         */
    }
    public function getUrl($strMethod, $strBucket, $strObject, $intTime = null) {
        if ($intTime == null) {
            $intTime = time () + 86400;
        }
        $strSign = self::FLAG . ':' . $this->arrConf ['bcs'] ['ak'] . ':' . $this->getSign ( $this->getContent ( $strMethod, $strBucket, $strObject, $intTime ) );
        return sprintf ( self::URL_FORMAT, $this->arrConf ['bcs'] ['domain'], $strBucket, $strObject, $strSign ) . '&time=' . $intTime;
    }
    protected function getSign($strContent) {
        return urlencode ( base64_encode ( hash_hmac ( 'sha1', $strContent, $this->arrConf ['bcs'] ['sk'], true ) ) );
    }
    protected function getContent($strMethod, $strBucket, $strObject, $intTime) {
        return self::FLAG . "\n" . "Method=$strMethod" . "\n" . "Bucket=$strBucket" . "\n" . "Object=$strObject" . "\n" . "Time=$intTime" . "\n";
    }
}
