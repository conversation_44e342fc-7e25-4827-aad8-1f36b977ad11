<?php


/**
 * 发布系统任务常量定义
 *
 * @filesource hk/ds/publish/Const.php
 * <AUTHOR>
 * @version 1.2
 */
//class Hk_Ds_Publish_Const {
//
//
//    # 策略连接 || &&
//    const STR_COND_AND = 1;
//    const STR_COND_OR  = 2;
//
//    # app产品线标示
//    const APP_HOMEWORK    = Hk_Const_AppId::APP_HOMEWORK;
//    const APP_AIRCLASS    = Hk_Const_AppId::APP_AIRCLASS;
//    const APP_AIRTEACHER  = Hk_Const_AppId::APP_YKTEACHER;
//    const APP_DAYITEACHER = Hk_Const_AppId::APP_DYTEACHER;
//    const APP_PARENT      = Hk_Const_AppId::APP_PARENT;
//    const APP_HXENGLISH   = Hk_Const_AppId::APP_HXENGLISH;
//    const APP_KOUSUAN     = Hk_Const_AppId::APP_KOUSUAN;
//    const APP_STUWIN      = "stuwin";
//    const APP_YAYAENG     = Hk_Const_AppId::APP_YAYAENG;
//    const APP_YAYAXIEZI   = Hk_Const_AppId::APP_YAYAXIEZI;
//    //后续常量定义不再依赖hk
//    const APP_COLLEGE     = 'college';
//    const APP_JINLI       = 'jinli';
//    const APP_YAYAYUWEN = "yayayuwen";          # 鸭鸭语文
//
//    public static $appIdSupport = array(       # 当前支持的产品线
//        self::APP_HOMEWORK    => "作业帮homework",
//        self::APP_AIRCLASS    => "一课airclass",
//        self::APP_AIRTEACHER  => "直播老师teacher",
//        self::APP_DAYITEACHER => "答疑老师teacher",
//        self::APP_PARENT      => "家长端parent",
//        self::APP_STUWIN      => "一课（windows）stuwin",
//        self::APP_HXENGLISH   => "浣熊学堂",
//        self::APP_KOUSUAN     => "作业帮口算",
//        self::APP_YAYAENG     => '鸭鸭英语',//2020-03-26
//        self::APP_YAYAXIEZI   => '鸭鸭写字',//2020-04-21
//        self::APP_COLLEGE     => '大学搜题college',//2020-06-11
//        self::APP_JINLI       => '锦鲤',//2020-06-15
//        self::APP_YAYAYUWEN   => '鸭鸭语文',//2020-07-28
//    );
//
//    # app操作系统
//    const APP_OS_ANDROID = "android";
//    const APP_OS_IOS     = "ios";
//    const APP_OS_WINDOWS = "windows";
//    public static $osTypes  = array(         # 当前支持的操作系统
//        self::APP_OS_ANDROID => "android",
//        self::APP_OS_IOS     => "iOS",
//        self::APP_OS_WINDOWS => "windows-pc",
//    );
//
//    # 发布任务类型
//    const PUB_TYPE_NORMAL = 1;      # 普通发布
//    const PUB_TYPE_SHADOW = 2;      # 灰度发布
//    const PUB_TYPE_ABTEST = 3;      # 多包对比发布
//    public static $publishType = array(     # 当前支持的发布类型
//        self::PUB_TYPE_NORMAL => "普通升级",
//        self::PUB_TYPE_SHADOW => "灰度升级",
//        self::PUB_TYPE_ABTEST => "多包灰度升级",
//    );
//
//    # 发布状态标示
//    const PUB_WAIT  = 0;            # 等待发布
//    const PUB_ING   = 1;            # 正在发布
//    const PUB_PAUSE = 2;            # 暂停发布
//    const PUB_FIN   = 3;            # 完成发布
//    public static $taskStatus = array(      # 发布任务状态标示
//        self::PUB_WAIT  => "等待",
//        self::PUB_ING   => "发布中",
//        self::PUB_PAUSE => "暂停",
//        self::PUB_FIN   => "完成",
//    );
//
//    # 策略条件标示
//    const CHK_UID         = 1;
//    const CHK_CUID        = 2;
//    const CHK_LOCATION    = 3;      # 所在地区
//    const CHK_UID_RATIO   = 4;      # uid比例
//    const CHK_CUID_RATIO  = 5;      # cuid比例
//    const CHK_APP_VERSION = 6;      # app版本
//    const CHK_OS_VERSION  = 7;      # android版本
//    const CHK_CHANNEL     = 8;      # 渠道号
//    const CHK_PHONETYPE   = 9;      # 手机型号
//    const CHK_WIN_VERSION = 10;     # windows版本
//    const CHK_IOS_VERSION = 11;     # ios版本
//    const CHK_USERGRADE   = 12;     # 用户年级
//
//    # 支持的元策略方法<br>
//    # 如果配置此项，则表示已支持，并需要在chkFuncMap中配置对应的方法
//    public static $supportType = array(
//        self::CHK_UID         => "UID列表",
//        self::CHK_CUID        => "CUID列表",
//        self::CHK_LOCATION    => "所在地域",
//        self::CHK_UID_RATIO   => "用户比例",
//        self::CHK_CUID_RATIO  => "客户端比例",
//        self::CHK_APP_VERSION => "应用版本",
//        self::CHK_OS_VERSION  => "手机操作系统版本",
//        self::CHK_CHANNEL     => "渠道号",
//        self::CHK_PHONETYPE   => "手机型号",
//        self::CHK_WIN_VERSION => "windows版本",
//        self::CHK_IOS_VERSION => "iOS版本",
//        self::CHK_USERGRADE   => "用户年级",
//    );
//
//    # 元策略对应Hk_Ds_Publish_Caller中的方法名字
//    public static $chkFuncMap  = array(
//        self::CHK_CUID        => "checkCuid",
//        self::CHK_UID         => "checkUid",
//        self::CHK_LOCATION    => "checkLocation",
//        self::CHK_UID_RATIO   => "checkUidRatio",
//        self::CHK_CUID_RATIO  => "checkCuidRatio",
//        self::CHK_APP_VERSION => "checkAppVersion",
//        self::CHK_OS_VERSION  => "checkOsVersion",
//        self::CHK_CHANNEL     => "checkChannel",
//        self::CHK_PHONETYPE   => "checkPhoneType",
//        self::CHK_WIN_VERSION => "checkWinVersion",
//        self::CHK_IOS_VERSION => "checkOsVersion",
//        self::CHK_USERGRADE   => "checkGrade",
//    );
//}
