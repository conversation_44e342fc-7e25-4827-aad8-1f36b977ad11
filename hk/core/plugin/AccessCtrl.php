<?php


/**
 * Rpc访问权限控制插件<br>
 * 1、只限制rpc接口，其他的一律不进行限制<br>
 * 2、rpc只能通过内网访问<br>
 * 3、服务白名单功能，配置在app/conf/rpc/access.conf中
 *
 * @deprecated 迁移到独立repo管理，如需使用请联系**********************
 *
 * @since 2.0 2018-10-30 增加权限访问控制到模块级别
 * @since 1.0 初始化
 *
 * @filesource hk/core/plugin/RequestIdFilter.php
 * <AUTHOR>
 * @version 2.0
 * @date    2018-10-30
 */
class Hk_Core_Plugin_AccessCtrl extends Ap_Plugin_Abstract {

    public function routerShutdown(Ap_Request_Abstract $request, Ap_Response_Abstract $response) {
        if ("Rpc" !== $request->controller) {           # 非rpc请求不执行此插件
            return;
        }
        # 内网控制
        if (false === Hk_Util_Ip::isInnerIp()) {
            throw new Hk_Core_ApiError(Hk_Core_ErrorCodes::ACCESS_DENIED, array(
                "denyIp" => CLIENT_IP,
            ));
        }
        # 模块权限
        $method     = $request->getParam('method');
        $reqModule  = isset($_SERVER["HTTP_X_BD_MODULE"]) ? $_SERVER["HTTP_X_BD_MODULE"] : "unknown-app";
        if (false === $this->accessCtrl($reqModule, $method)) {
            throw new Hk_Core_ApiError(Hk_Core_ErrorCodes::ACCESS_DENIED, array(
                "denyApp" => $reqModule,
            ));
        }
    }

    /**
     * 判断请求app是否有权限，服务权限使用global+service校验<br>
     * 1、判断访问模块是否有全局模块权限<br>
     * 2、如果用户无全局权限，判断是否配置单独服务权限<br>
     * 3、单独权限acc，all：全部放开；deny：全部拒绝；array：名单列表<br>
     * 4、如果未传递reqModule字段，默认为unknown-app，会被自动拒绝
     *
     * @param string       $reqModule
     * @param string       $name
     * @return boolean
     */
    private function accessCtrl($reqModule, $name) {
        $conf = Bd_Conf::getAppConf("rpc/access", MAIN_APP);
        if (false === $conf) {          # 未配置访问控制，跳过
            return true;
        }

        # global全局权限
        $gAcc = isset($conf["global"]["acc"]) ? $conf["global"]["acc"] : array();
        if (!empty($gAcc) && in_array($reqModule, $gAcc)) {      # 已配置全局权限，未配置则跳过
            return true;
        }
        # service单独服务权限，若无配置，代表所有人都能访问
        if (!isset($conf["service"][$name]["acc"])) {            # 无配置，直接放行
            return true;
        }
        # 根据配置判断是否放行
        $acc  = $conf["service"][$name]["acc"];
        if (is_string($acc)) {          # allow校验权限，只允许填all|deny，all: 允许所有；deny: 禁止所有
            if ("all" !== $acc && "deny" !== $acc) {
                return false;
            }
            return "all" === $acc ? true : false;
        }
        if (is_array($acc)) {           # 权限白名单
            return in_array($reqModule, $acc) ? true : false;
        }
        return false;
    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
