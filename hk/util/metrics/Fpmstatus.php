<?php

class Hk_Util_Metrics_Fpmstatus {

    private static $names = [
        //"pool",
        //"process manager",
        //"dynamic",
        //"start time",
        "start since",
        "accepted conn",
        "listen queue",
        "max listen queue",
        "listen queue len",
        "idle processes",
        "active processes",
        "total processes",
        "max active processes",
        "max children reached",
        "slow requests",
    ];

    const FPM_STAUTS_NAMESPACE = 'zyb_phpfpm';

    public static function runtimeMetrics() {
        self::render(self::getFpmStatus());
    }

    private static function getFpmStatus() {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "http://127.0.0.1:8099/fpmstatus?json");
        curl_setopt($ch, CURLOPT_HEADER, false);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        $result = curl_exec($ch);
        curl_close($ch);
        return $result;
    }

    /**
     * 传入取到的json格式的fpm-status内容,转化成prometheus metrics指标（eg:http://127.0.0.1:8099/fpmstatus?json）
     * @param string $fpmStatusContent
     * @return bool|string 失败返回false
     */
    public static function render($fpmStatusContent) {
        if(empty($fpmStatusContent)) {
            return false;
        }

        // 解析指定的json返回
        $arrStatusContent = json_decode($fpmStatusContent, true);
        if(empty($arrStatusContent) || !is_array($arrStatusContent)) {
            return false;
        }

        try {
            $registry = new Ext_Prometheus_CollectorRegistry(new FpmAdapter());

            foreach(self::$names as $name) {
                $desc = $name;
                $name = str_replace(" ", "_", $name);
                $labels = [self::FPM_STAUTS_NAMESPACE."_".$name];
                $gauge = $registry->getOrRegisterGauge(self::FPM_STAUTS_NAMESPACE, $name, $desc, $labels);
                $gauge->set($arrStatusContent[$desc], $labels);
            }

            $renderer = new Ext_Prometheus_RenderTextFormat();
            echo $renderer->render($registry->getMetricFamilySamples());

            return true;
        } catch(Exception $e) {
            Bd_Log::warning("get runtime-metrics exception:".$e->getMessage());
        }
        return true;
    }
}


class FpmAdapter implements Ext_Prometheus_Storage_Adapter {

    private $gauges = [];

    /**
     * @return Ext_Prometheus_MetricFamilySamples[]
     */
    public function collect() {
        $metrics = $this->gauges;
        $result = [];
        foreach($metrics as $metric) {
            $metaData = $metric['meta'];
            $data = [
                'name'       => $metaData['name'],
                'help'       => $metaData['help'],
                'type'       => $metaData['type'],
                'labelNames' => [],
            ];
            foreach($metric['samples'] as $key => $value) {
                $parts = explode(':', $key);
                $labelValues = $parts[2];
                $data['samples'][] = [
                    'name'        => $metaData['name'],
                    'labelNames'  => [],
                    'labelValues' => json_decode($labelValues),
                    'value'       => $value
                ];
            }
            $this->sortSamples($data['samples']);
            $result[] = new Ext_Prometheus_MetricFamilySamples($data);
        }
        return $result;
    }

    public function updateHistogram(array $data) {

    }

    public function updateGauge(array $data) {
        $metaKey = $this->metaKey($data);
        $valueKey = $this->valueKey($data);
        if(array_key_exists($metaKey, $this->gauges) === false) {
            $this->gauges[$metaKey] = [
                'meta'    => $this->metaData($data),
                'samples' => []
            ];
        }
        if(array_key_exists($valueKey, $this->gauges[$metaKey]['samples']) === false) {
            $this->gauges[$metaKey]['samples'][$valueKey] = 0;
        }

        $this->gauges[$metaKey]['samples'][$valueKey] = $data['value'];
    }


    public function updateCounter(array $data) {

    }


    /**
     * @param array $data
     * @return array
     */
    private function metaData(array $data) {
        $metricsMetaData = $data;
        unset($metricsMetaData['value']);
        unset($metricsMetaData['command']);
        unset($metricsMetaData['labelValues']);
        return $metricsMetaData;
    }

    /**
     * @param array $data
     * @return string
     */
    private function metaKey(array $data) {
        return implode(':', [$data['type'], $data['name'], 'meta']);
    }

    /**
     * @param array $data
     * @return string
     */
    private function valueKey(array $data) {
        return implode(':', [$data['type'], $data['name'], json_encode($data['labelValues']), 'value']);
    }

    private function sortSamples(array &$samples) {
        usort($samples, function($a, $b) {
            return strcmp(implode("", $a['labelValues']), implode("", $b['labelValues']));
        });
    }
}

