<?php


/**
 * 数据库处理基类，封装自Bd_DB库
 *
 * @since 2.1 2019-03-22 兼容无前缀自动搜寻表名
 * @since 2.0 2018-12-25 修改异常处理模式，从hk/core/base迁移
 * @since 1.8
 *
 * @filesource hk/common/Dao.php
 * <AUTHOR>
 * @version 2.1
 * @date    2019-03-22
 */
abstract class Hk_Common_Dao {

    protected $_dbConf   = "";
    protected $prefix    = '';
    protected $dbCluster = array();     # 数据库集群配置列表
    protected $tableName = '';          # 表名
    protected $fields    = array();     # 表字段名
    protected $key       = NULL;        # 负载均衡key
    protected $getNew    = false;       # getNew 是否重新连接

    private $dbClient = NULL;

    public final function __construct() {
        $this->initDbParams();
        $this->init();
    }

    /**
     * 初始化已定义好的数据库配置，如果有定义
     */
    private function initDbParams() {
        $dbConf = Bd_Conf::getAppConf('database/db', MAIN_APP);                    # 项目数据库配置database.conf
        if (false === $dbConf) {               # 无法读取配置，需要自定义初始化
            return;
        }

        # 初始化值
        $dbConfig  = array(
            "prefix"  => "",
        );
        $dbCluster = array(
            "default" => "",
        );
        $dbConfig  = array_merge($dbConfig,  isset($dbConf["config"])   ? $dbConf["config"]   : array());
        $dbCluster = array_merge($dbCluster, isset($dbConf["clusters"]) ? $dbConf["clusters"] : array());

        $this->prefix    = $dbConfig["prefix"];
        $this->_dbConf   = $dbCluster["default"];
        $this->dbCluster = $dbCluster;
        $this->tableName = $this->getTableName();
    }

    /**
     * 子类继承用于初始化各种参数：
     * $_dbConf
     * $tableName
     * $fields
     */
    public function init() {}

    /**
     * 获取DAO的字段列表
     *
     * @param string       $tableName
     * @return array
     */
    public function getFields($tableName) {
        $objDb = $this->getDBConn();
        $ret   = $objDb->query(sprintf("SHOW COLUMNS FROM %s", $tableName));
        if (false === $ret) {
            $arg  = array(
                'last_sql' => $objDb->getLastSQL(),
                'err_info' => 'db error: query failed, ' . $objDb->error(),
            );
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_QUERY_ERROR, "", $arg);
        }
        $fields = array();
        if (!empty($ret)) {
            foreach ($ret as $column) {
                $fields[] = $column["Field"];
            }
        } else {
            $fields = array("*");
        }
        return $fields;
    }

    /**
     * 根据Dao命名自动解析并填写默认数据库表名
     *
     * @return string
     */
    public function getTableName() {
        $params    = explode("_", get_class($this));
        $tableName = array_pop($params);
        if (!empty($this->prefix)) {
            return sprintf("%s%s", $this->prefix, $tableName);
        }
        return lcfirst($tableName);
    }

    /**
     * 连接丢失后强制重连
     */
    public function reconnect() {
        $this->dbClient = Bd_Db_ConnMgr::getConn($this->_dbConf, $this->key, true);
    }

    /**
     * 获取最后一次执行的SQL
     *
     * @return string
     */
    public function getLastSQL() {
        return $this->getDBConn()->getLastSQL();
	}

	/**
	 * 获取影响的行数
     *
     * @return int
	 */
	 public function getAffectedRows() {
		return $this->getDBConn()->getAffectedRows();
	 }

    /**
     * 插入记录<br>
     * 成功：影响行数<br>
     * 失败：throw Exception
     *
     * @param $row:      字段
     * @param $options:  选项
     * @param $onDup:    键冲突时的字段值列表
     * @return int
     **/
    public function insert($row, $options = NULL, $onDup = NULL) {
        $objDb = $this->getDBConn();
        $ret   = $objDb->insert($this->tableName, $row, $options, $onDup);
        if (false === $ret) {
            $arg  = array(
                'last_sql' => $objDb->getLastSQL(),
                'err_info' => 'db error: insert failed, ' . $objDb->error(),
            );
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_QUERY_ERROR, "", $arg);
        }
        return $ret;
    }

    /**
     * 批量插入数据<br>
     * 成功：影响行数<br>
     * 失败：throw Exception
     *
     * @param $fields:   字段名列表
     * @param $values:   对应字段的多行值映射
     * @param $options:  选项
     * @param $onDup:    键冲突时的字段值列表
     * @return mixed:int|boolean
     */
    public function batchInsert(array $fields, array $values, $options = NULL, $onDup = NULL) {
        $objDb = $this->getDBConn();
        $ret   = $objDb->multiInsert($this->tableName, $fields, $values, $options, $onDup);
        if (false === $ret) {
            $arg  = array(
                'last_sql' => $objDb->getLastSQL(),
                'err_info' => 'db error: insert failed, ' . $objDb->error(),
            );
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_QUERY_ERROR, "", $arg);
        }
        return $ret;
    }

    /**
     * 获取刚插入记录id<br>
     * 成功：新记录ID<br>
     * 失败：throw Exception
     *
     * @return int
     **/
    public function getInsertID() {
        $objDb = $this->getDBConn();
        $ret   = $objDb->getInsertID();
        if (false === $ret) {
            $arg  = array(
                'last_sql' => $objDb->getLastSQL(),
                'err_info' => 'db error: getInsertID failed, ' . $objDb->error(),
            );
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_QUERY_ERROR, "", $arg);
        }
        return $ret;
    }

    /**
     * 删除记录<br>
     * 成功：影响行数<br>
     * 失败：throw Exception
     *
     * @param $conds:     条件
     * @param $appends:   结尾操作
     * @param $options:   选项
     * @return int
     **/
    public function delete($conds = NULL, $appends = NULL, $options = NULL) {
        $objDb = $this->getDBConn();
        $ret   = $objDb->delete($this->tableName, $conds, $options, $appends);
        if (false === $ret) {
            $arg  = array(
                'last_sql' => $objDb->getLastSQL(),
                'err_info' => 'db error: delete failed, ' . $objDb->error(),
            );
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_QUERY_ERROR, "", $arg);
        }
        return $ret;
    }

    /**
     * 更新记录<br>
     * 成功：影响行数<br>
     * 失败：throw Exception
     *
     * @param $row:       字段
     * @param $conds:     条件
     * @param $appends:   结尾操作
     * @param $options:   选项
     * @return int
     */
    public function update($row, $conds = NULL, $appends = NULL, $options = NULL) {
        $objDb = $this->getDBConn();
        $ret   = $objDb->update($this->tableName, $row, $conds, $options, $appends);
        if (false === $ret) {
            $arg  = array(
                'last_sql' => $objDb->getLastSQL(),
                'err_info' => 'db error: update failed, ' . $objDb->error(),
            );
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_QUERY_ERROR, "", $arg);
        }
        return $ret;
    }

    /**
     * 根据条件查询记录<br>
     * 成功：查询结果<br>
     * 失败：throw Exception
     *
     * @param $fields:    字段名
     * @param $conds:     条件
     * @param $appends:   结尾操作
     * @param $options:   选项
     * @param $fetchType: 获取类型
     * @param $useResult: 是否使用MYSQL_USE_RESULT
     * @return array
     */
    public function select($fields = NULL, $conds = NULL, $appends = NULL, $options = NULL, $fetchType = Bd_DB::FETCH_ASSOC, $useResult = false) {
        $fields = empty($fields) ? $this->fields : $fields;
        $objDb  = $this->getDBConn();
        $ret    = $objDb->select($this->tableName, $fields, $conds, $options, $appends, $fetchType, $useResult);
        if (false === $ret) {
            $arg  = array(
                'last_sql' => $objDb->getLastSQL(),
                'err_info' => 'db error: select failed, ' . $objDb->error(),
            );
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_QUERY_ERROR, "", $arg);
        }
        return $ret;
    }

    /**
     * 单行查询<br>
     * 成功：查询结果<br>
     * 失败：throw Exception
     *
     * @param $fields:    字段名
     * @param $conds:     条件
     * @param $appends:   结尾操作
     * @param $options:   选项
     * @param $fetchType: 获取类型
     * @param $useResult: 是否使用MYSQL_USE_RESULT
     * @return array
     */
    public function find($fields = NULL, $conds = NULL, $appends = array(), $options = NULL, $fetchType = Bd_DB::FETCH_ASSOC, $useResult = false) {
        $fields    = empty($fields) ? $this->fields : $fields;
        $appends[] = "LIMIT 1";

        $objDb = $this->getDBConn();
        $ret   = $objDb->select($this->tableName, $fields, $conds, $options, $appends, $fetchType, $useResult);
        if (false === $ret) {
            $arg  = array(
                'last_sql' => $objDb->getLastSQL(),
                'err_info' => 'db error: find failed, ' . $objDb->error(),
            );
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_QUERY_ERROR, "", $arg);
        }
        return empty($ret) ? $ret : $ret[0];
    }

    /**
     * 查询记录总数<br>
     * 成功：记录总数<br>
     * 失败：throw Exception
     *
     * @param $conds:   条件
     * @param $appends: 结尾操作
     * @param $options: 选项
     * @return int
     */
    public function selectCount($conds = NULL, $appends = NULL, $options = NULL) {
        $objDb = $this->getDBConn();
        $ret   = $objDb->selectCount($this->tableName, $conds, $options, $appends);
        if (false === $ret) {
            $arg  = array(
                'last_sql' => $objDb->getLastSQL(),
                'err_info' => 'db error: selectCount failed, ' . $objDb->error(),
            );
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_QUERY_ERROR, "", $arg);
        }
        return $ret;
    }

    /**
     * 直接执行SQL语句<br>
     * 警告：会直接执行输入语句，不会对语句做安全检查，使用前请自行判断是否有SQL漏洞<br>
     *
     * 成功：记录总数<br>
     * 失败：throw Exception
     *
     * @param $sql:       sql语句
     * @param $fetchType: 获取类型
     * @param $useResult: 是否使用MYSQL_USE_RESULT
     * @return int
     */
    public function query($sql, $fetchType = Bd_DB::FETCH_ASSOC, $useResult = false) {
        $objDb = $this->getDBConn();
        $ret   = $objDb->query($sql, $fetchType, $useResult);
        if (false === $ret) {
            $arg  = array(
                'last_sql' => $objDb->getLastSQL(),
                'err_info' => 'db error: query failed, ' . $objDb->error(),
            );
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_QUERY_ERROR, "", $arg);
        }
        return $ret;
    }

    /**
     * 创建一个IN查询条件
     *
     * @param array      $itemList  数据列表
     * @param string     $fieldName 字段名称
     * @return string
     */
    public function createIN($itemList, $fieldName) {
        if (empty($itemList)) {
            $ret = array($fieldName, "IN", array(""));
        } else {
            $ret = array($fieldName, "IN", $itemList);
        }
        return $ret;
    }

    /**
     * 对insert批量插入的value, 按指定列排序
     *
     * @param array      $fields  字段名称
     * @param string     $row     字段
     */
    public function createInsertValue($fields, $row) {
        $insertValue = array();
        foreach($fields as $name) {         // 将value按fields排序
            if (!isset($row[$name])) {
                $arg = array(
                    'field_name' => $name,
                    'err_info' => 'db error: createInsertValue param error',
                );
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, "", $arg);
            }
            $insertValue[] = $row[$name];
        }
        return $insertValue;
    }

    /**
     * 开始事务
     *
     * @return boolean
     */
    public function startTransaction() {
        $objDb = $this->getDBConn();
        $ret   = $objDb->startTransaction();
        if (false === $ret) {
            $arg  = array(
                'last_sql' => $objDb->getLastSQL(),
                'err_info' => 'db error: start_transaction failed',
            );
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_QUERY_ERROR, "", $arg);
        }
        return $ret;
    }

    /**
     * 回滚事务
     *
     * @return boolean
     */
    public function rollback() {
        $objDb = $this->getDBConn();
        $ret   = $objDb->rollback();
        if (false === $ret) {
            $arg  = array(
                'last_sql' => $objDb->getLastSQL(),
                'err_info' => 'db error: rollback failed',
            );
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_QUERY_ERROR, "", $arg);
        }
        return $ret;
    }

    /**
     * 提交事务
     *
     * @return boolean
     */
    public function commit() {
        $objDb = $this->getDBConn();
        $ret   = $objDb->commit();
        if (false === $ret) {
            $arg  = array(
                'last_sql' => $objDb->getLastSQL(),
                "err_info" => 'db error: commit failed',
            );
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_QUERY_ERROR, "", $arg);
        }
        return $ret;
    }

    /**
     * 关闭连接
     *
     * @return boolean
     */
    public function closeDB() {
        if (!empty($this->dbClient)) {
            return $this->dbClient->close();
        }
        return true;
    }

    /**
     * 获取DB连接
     */
    private function getDBConn() {
        if (empty($this->_dbConf)) {                           # 获取数据库配置文件
            $arg = array(
                "err_info" => 'db error: db conf not exist',
            );
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_CONF_ERROR, "", $arg);
        }
        if (NULL === $this->dbClient) {
            $this->dbClient = Bd_Db_ConnMgr::getConn($this->_dbConf, $this->key, $this->getNew);
        }
        if (false === $this->dbClient || false === $this->dbClient->isConnected()) {       # 连接被失败或丢失，需强制重连
            $this->reconnect();
        }
        if (false === $this->dbClient) {
            $arg = array(
                'cluster'  => $this->cluster,
                'key'      => $this->key,
                'getNew'   => $this->getNew,
                'err_info' => 'db error: connect failed',
            );
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_CONNECT_ERROR, "", $arg);
        }
        return $this->dbClient;
    }
}

/* vim: set ft=php expandtab ts=4 sw=4 sts=4 tw=0: */
