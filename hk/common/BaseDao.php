<?php
/**
 * @category    library
 * @package     napi
 * <AUTHOR>
 * @version     2014-12-1 17:57:41
 * @copyright   Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 **/

/**
 * Dao的公共基类，封装了常见的数据库操作
 */
class Hk_Common_BaseDao {
    /**
     * 待连接数据库配置名称
     * @var string
     */
    protected $_dbName;

    /**
     * 数据库连接对象
     * @var resource
     */
    protected $_db;

    /**
     * mysql日志文件
     * @var string
     */
    protected $_logFile = NULL;

    //是否每次都获取新的连接，true是，false否，默认false
    protected $_new = false;

    protected $_autoRotate = true;

    /**
     * 待连接的数据表名称
     * @var string
     */
    protected $_table;

    /**
     * 程序中的字段名和数据表列名的映射数组
     * @var array
     */
    protected $arrFieldsMap;

    /**
     * 返回结果的字段类型映射数组
     * @var array
     */
    protected $arrTypesMap;

    /**
     * Dao基类的构造函数，子类需要写自己的构造函数覆盖父类构造函数.
     * 子类构造函数Demo:<br/>
     * <code>
     * public function __construct() {
     *     $this->_dbName = "question";
     *     $this->_db     = Hk_Service_Db::getDB($this->_dbName);
     *     $this->_table  = "tblQuestion";
     *     $this->arrFieldsMap = array(
     *         'field' => 'column',
     *     );
     * }
     * </code>
     */
    public function __construct() {
    }


    //数据隔离，表名统一加shadow
    protected function initShadow() {
        /* --- 压测流量读写走影子表 Start--- */
        $isPressure = Hk_Util_Navigator::isPressure();
        if($isPressure) {
            $this->_table = $this->_table.'Shadow';
        }

        /* --- 压测流量读写走影子表 End--- */
    }

    /**
     * 使用输入的SQL语句进行查询
     * @api
     * @param  string $sql SQL语句
     * @return array|false 返回查询结果集，失败为false
     */
    public function query($sql) {
        if(empty($this->_db)) {
            $this->_db = Hk_Service_Db::getDB($this->_dbName, $this->_new, $this->_logFile, $this->_autoRotate);
        }

        //初始化db失败
        if(!$this->_db) {
            return false;
        }

        $res = $this->_db->query($sql);
        return $res;
    }


    /**
     * 获取影响行数
     * @api
     * @param  null
     * @return array|false 返回查询结果集，失败为false
     */
    public function getAffectedRows() {
        if(empty($this->_db)) {
            $this->_db = Hk_Service_Db::getDB($this->_dbName, $this->_new, $this->_logFile, $this->_autoRotate);
        }

        //初始化db失败
        if(!$this->_db) {
            return false;
        }

        $res = $this->_db->getAffectedRows();
        return $res;
    }


    /**
     * Select查询，根据限制条件获取结果数组
     * @param  mixed $arrConds   限制条件，数组或者字符串形式均可, 示例:<br/>
     *                           <code>
     *                           array (
     *                           'field' => value,
     *                           'field' => array(value1, "<", value2, "?"),
     *                           'field < vale'
     *                           );
     *                           </code>
     *                           或者<br/>
     *                           <code>"field in (...,...)"</code>
     *                           <b>注意</b>：字符串格式不会自动做字段映射
     * @param  array $arrFields  需要查询的字段名数组，格式必须为数组
     * @param  array $arrOptions SQL前置选项，示例：<br/>
     *                           <code>
     *                           $option = array(
     *                           'DISTINCT',
     *                           'SQL_NO_CACHE'
     *                           );
     *                           </code>
     * @param  array $arrAppends SQL后置选项,示例：<br/>
     *                           <code>
     *                           $appends = array(
     *                           'ORDER BY b.id',
     *                           'LIMIT 5'
     *                           );
     *                           </code>
     * @param  array $strIndex   支持mysql的USE/IGNORE/FORCE Index的语法，指定
     *                           的索引名称，示例：<br/>
     *                           <code>$strIndex = "USE INDEX (index1, index2)";</code>
     *                           或者<br/>
     *                           <code>$strIndex = "FORCE INDEX (index1)";</code>
     * @return array|false 返回查询的结果列表，连接DB失败返回false
     * @see getRecordByConds() 获取单条记录
     */
    public function getListByConds($arrConds, $arrFields, $arrOptions = NULL, $arrAppends = NULL, $strIndex = NULL) {
        if(empty($this->_db)) {
            $this->_db = Hk_Service_Db::getDB($this->_dbName, $this->_new, $this->_logFile, $this->_autoRotate);
        }

        //初始化db失败
        if(!$this->_db) {
            return false;
        }

        //限制条件字段以及格式的转换
        $arrConds = Hk_Service_Db::mapRow($arrConds, $this->arrFieldsMap);
        $arrConds = Hk_Service_Db::getConds($arrConds);

        //查询字段的转换
        $arrFields = Hk_Service_Db::mapField($arrFields, $this->arrFieldsMap, true);
        //表名以及强制索引字段的添加
        $tableName = (empty($strIndex)) ? $this->_table : $this->_table." {$strIndex}";
        //查询
        $arrRes = $this->_db->select($tableName, $arrFields, $arrConds, $arrOptions, $arrAppends);
        if(is_array($arrRes) && is_array($arrRes[0])) {
            foreach($arrRes as &$row) {
                $row = Hk_Service_Db::mapFieldType($row, $this->arrTypesMap);
            }
            unset($row);
        }
        return $arrRes;
    }

    /**
     * Select查询，根据限制条件获取结果的单条记录
     * @param  mixed $arrConds   限制条件，数组或者字符串形式均可，示例见{@link getListByConds()}
     * @param  array $arrFields  需要查询的字段名数组，格式必须为数组
     * @param  array $arrOptions SQL前置选项
     * @param  array $arrAppends SQL后置选项
     * @param  array $strIndex   强制索引时，指定的索引名称
     * @return array|false 返回查询的结果单条记录，连接DB失败返回false
     * @see  getListByConds() 获取多条记录
     */
    public function getRecordByConds($arrConds, $arrFields, $arrOptions = NULL, $arrAppends = NULL, $strIndex = NULL) {
        if(empty($this->_db)) {
            $this->_db = Hk_Service_Db::getDB($this->_dbName, $this->_new, $this->_logFile, $this->_autoRotate);
        }

        //初始化db失败
        if(!$this->_db) {
            return false;
        }

        $arrRes = $this->getListByConds($arrConds, $arrFields, $arrOptions, $arrAppends, $strIndex);
        if(false === $arrRes) {
            return false;
        }
        return !empty($arrRes[0]) ? $arrRes[0] : array();
    }

    /**
     * Insert插入，不支持多行插入
     * @param  array $arrFields 需要插入的键值数组，示例：<br/>
     *                          <code>
     *                          array (
     *                          'name' => 'Robin Li',
     *                          )
     *                          </code>
     * @param  array $options   INSERT插入选项，支持"LOW_PRIORITY","DELAYED", "HIGH_PRIORITY", "IGNORE"
     * @param  array $onDup     主键冲突时更新的键值列表，格式参见{@link getListByConds()}的conds参数
     * @return bool 插入成功返回true，否则返回false
     */
    public function insertRecords($arrFields, $options = NULL, $onDup = NULL) {
        if(empty($this->_db)) {
            $this->_db = Hk_Service_Db::getDB($this->_dbName, $this->_new, $this->_logFile, $this->_autoRotate);
        }

        //初始化db失败
        if(!$this->_db) {
            return false;
        }

        $arrFields = Hk_Service_Db::mapRow($arrFields, $this->arrFieldsMap);
        $onDup = Hk_Service_Db::mapRow($onDup, $this->arrFieldsMap);
        $ret = $this->_db->insert($this->_table, $arrFields, $options, $onDup);
        return $ret === false ? false : true;
    }

    /**
     * 返回插入后的自增ID
     * @return int 最新的自增ID
     */
    public function getInsertId() {
        if(empty($this->_db)) {
            $this->_db = Hk_Service_Db::getDB($this->_dbName, $this->_new, $this->_logFile, $this->_autoRotate);
        }

        //初始化db失败
        if(!$this->_db) {
            return false;
        }

        return $this->_db->getInsertID();
    }

    /**
     * 返回上次执行的语句
     * @return string
     */
    public function getLastSQL() {
        if(empty($this->_db)) {
            $this->_db = Hk_Service_Db::getDB($this->_dbName, $this->_new, $this->_logFile, $this->_autoRotate);
        }

        //初始化db失败
        if(!$this->_db) {
            return false;
        }

        return $this->_db->getLastSQL();
    }

    /**
     * Update更新，根据限制条件更新对应的数据库记录
     * @param  mixed $arrConds   限制条件，数组或者字符串形式均可，示例见{@link getListByConds()}的conds参数
     * @param  array $arrFields  需要更新的字段名数组，格式必须为数组且包含主键
     * @param  array $arrOptions SQL前置选项，参见{@link getListByConds()}对应参数
     * @param  array $arrAppends SQL后置选项，参见{@link getListByConds()}对应参数
     * @return bool 成功返回true，失败返回false
     */
    public function updateByConds($arrConds, $arrFields, $arrOptions = NULL, $arrAppends = NULL) {
        if(empty($this->_db)) {
            $this->_db = Hk_Service_Db::getDB($this->_dbName, $this->_new, $this->_logFile, $this->_autoRotate);
        }

        //初始化db失败
        if(!$this->_db) {
            return false;
        }

        $arrConds = Hk_Service_Db::mapRow($arrConds, $this->arrFieldsMap);
        $arrConds = Hk_Service_Db::getConds($arrConds);
        $arrFields = Hk_Service_Db::mapRow($arrFields, $this->arrFieldsMap);
        $ret = $this->_db->update($this->_table, $arrFields, $arrConds, $arrOptions, $arrAppends);
        return $ret;
    }

    /**
     * Delete删除，根据限制条件删除对应的数据库记录
     * @param  mixed $arrConds 限制条件，数组或者字符串形式均可，示例见{@link getListByConds()}的conds参数
     * @return bool 成功返回true，失败返回false
     */
    public function deleteByConds($arrConds) {
        if(empty($this->_db)) {
            $this->_db = Hk_Service_Db::getDB($this->_dbName, $this->_new, $this->_logFile, $this->_autoRotate);
        }

        //初始化db失败
        if(!$this->_db) {
            return false;
        }

        $arrConds = Hk_Service_Db::mapRow($arrConds, $this->arrFieldsMap);
        $arrConds = Hk_Service_Db::getConds($arrConds);
        $ret = $this->_db->delete($this->_table, $arrConds);
        return $ret;
    }

    /**
     * Count符合条件的记录数
     * @param  mixed $arrConds 限制条件，数组或者字符串形式均可，示例见getListByConds
     * @return int|false 成功返回记录总数，失败返回false
     */
    public function getCntByConds($arrConds) {
        if(empty($this->_db)) {
            $this->_db = Hk_Service_Db::getDB($this->_dbName, $this->_new, $this->_logFile, $this->_autoRotate);
        }

        //初始化db失败
        if(!$this->_db) {
            return false;
        }

        $arrConds = Hk_Service_Db::mapRow($arrConds, $this->arrFieldsMap);
        $arrConds = Hk_Service_Db::getConds($arrConds);
        $ret = $this->_db->selectCount($this->_table, $arrConds);
        return $ret;
    }

    /**
     * 建立DAO对应的DB连接
     * @return mix 成功返回db实例，否则false
     */
    public function getDB($strDbName) {
        $db = Hk_Service_Db::getDB($strDbName, true);
        $this->_db = $db;
        return $db;
    }

    /**
     * 关闭DAO对应的DB连接，适用于部分场景需要迅速断开DB连接的情况
     * @return bool 成功返回true，否则false
     */
    public function closeDB() {
        if(!empty($this->_db)) {
            return $this->_db->close();
        }
        return true;
    }

    /**
     * 重新建立在先前断开的连接
     * @return bool 成功返回true，否则false
     */
    public function reconnect() {
        if(!empty($this->_db) && !$this->_db->isConnected()) {
            $this->_db = Hk_Service_Db::getDB($this->_dbName, true, $this->_logFile);
        }
        return $this->_db->isConnected();
    }

    /**
     * 设置或查询当前自动提交状态
     * @param  bool $bolAuto NULL返回当前状态，其它设置当前状态
     * @return bool 查询时，成功返回bool值，失败返回null值，设置时，返回bool值
     */
    public function autoCommit($bolAuto) {
        if(!empty($this->_db)) {
            return $this->_db->autoCommit($bolAuto);
        }
        return false;
    }

    /**
     * 开始一个事务
     * @return bool 成功返回true，否则false
     */
    public function startTransaction() {
        if(empty($this->_db)) {
            $this->_db = Hk_Service_Db::getDB($this->_dbName, $this->_new, $this->_logFile, $this->_autoRotate);
        }

        //初始化db失败
        if(!$this->_db) {
            return false;
        }

        if(!empty($this->_db)) {
            return $this->_db->startTransaction();
        }
        return false;
    }

    /**
     * 提交一个事务
     * @return bool 成功返回true，否则false
     */
    public function commit() {
        if(!empty($this->_db)) {
            return $this->_db->commit();
        }
        return false;
    }

    /**
     * 回滚当前事务
     * @return bool 成功返回true，否则false
     */
    public function rollback() {
        if(!empty($this->_db)) {
            return $this->_db->rollback();
        }
        return false;
    }
}
