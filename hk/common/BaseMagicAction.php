<?php
/***************************************************************************
 *
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file BaseMagicAction.php
 * <AUTHOR>
 * @date 2014-4-15
 * @version 1.0
 **/
abstract class Hk_Common_BaseMagicAction extends Hk_Common_BaseAction {

    public function execute() {
        Hk_Util_Log::start('ts_all');

        try{
            Hk_Util_Log::start('ts_init');
            $this->_init();
            Hk_Util_Log::stop('ts_init');

            //na端需要进行签名检查
            if ($this->_appType == 'android' || $this->_appType == 'ios' || $this->_appType == 'harmony') {
                Hk_Util_Log::start('ts_antispam');
                $this->_antispam();
                Hk_Util_Log::stop('ts_antispam');
            }

            Hk_Util_Log::start('ts_before');
            $ret = $this->_before();
            Hk_Util_Log::stop('ts_before');
            if ($ret === true) {
                Hk_Util_Log::start('ts_invoke');
                $res = $this->invoke();
                $this->_tplData['data'] = $res;
                Hk_Util_Log::stop('ts_invoke');

                Hk_Util_Log::start('ts_after');
                $ret = $this->_after();
                Hk_Util_Log::stop('ts_after');
            }
        } catch (Hk_Util_Exception $e) {
            $this->_tplData['errNo']  = $e->getErrNo();
            $this->_tplData['errstr'] = $e->getErrStr();
        }
        //输出
        Hk_Util_Log::start('ts_display');
        $this->_display();
        Hk_Util_Log::stop('ts_display');
        Hk_Util_Log::stop('ts_all');

        //打印日志
        $this->_processLog();
    }
}