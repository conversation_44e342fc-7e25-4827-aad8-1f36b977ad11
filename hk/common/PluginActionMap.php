<?php
/***************************************************************************
 *
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/



/**
 * @file ActionMap.php
 * <AUTHOR>
 * @date 2013/12/08 13:18:47
 * @brief 处理action转发的插件
 *
 **/

class Hk_Common_PluginActionMap extends Ap_Plugin_Abstract
{
    /**
     * @brief 分发循环开始之前
     *
     */
    public function dispatchLoopStartup(Ap_Request_Abstract $request, Ap_Response_Abstract $response)
    {
        //接口特例
        $controllerName = strtolower ($request->getControllerName());
        if ($controllerName == 'import' || $controllerName == 'mis' || $controllerName == 'sdk') {
            return;
        }

        if(isset($_REQUEST['token']) && strlen($_REQUEST['token']) > 0)
        {
            $arrVersion = Hk_Util_Client::getVersion();
            $intVersion = intval(Hk_Util_Client::$arrVersionMap[$arrVersion[Hk_Util_Client::APP_TOKEN]]);
            if($intVersion > 0)
            {
                $strControllerName = $request->getControllerName();
                $strActionName = $request->getActionName();
                $strPath = strtolower("interface/appv$intVersion/$strControllerName/$strActionName");
                $strActionMap = Bd_Conf::getAppConf($strPath);
                if(strlen($strActionMap) > 0)
                {
                    //切换到配置中指定的action
                    $request->setActionName($strActionMap);
                    if(preg_match('/(v\d+)$/',$strActionMap, $out))
                    {
                        $request->setParam('apiversion', $out[1]);
                    }
                }
            }
        }
        else
        {
            $intDebug = Bd_Conf::getAppConf('debug/switch/version');
            if(intval($intDebug) > 0)
            {
                //debug模式
                $strControllerName = $request->getControllerName();
                $strActionName = $request->getActionName();
                $strPath = strtolower("interface/appv$intDebug/$strControllerName/$strActionName");
                $strActionMap = Bd_Conf::getAppConf($strPath);
                if(strlen($strActionMap) > 0)
                {
                    //切换到配置中指定的action
                    $request->setActionName($strActionMap);
                    if(preg_match('/(v\d+)$/',$strActionMap, $out))
                    {
                        $request->setParam('apiversion', $out[1]);
                    }
                }
            }
        }
    }
}


/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
