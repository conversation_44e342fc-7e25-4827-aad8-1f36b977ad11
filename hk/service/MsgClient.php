<?php


/**
 * 系统通知消息客户端，系统通知需指定模板
 *
 * @filesource hk/service/MsgClient.php
 * <AUTHOR>
 * @version 1.0
 * @date    2018-07-07
 */
class Hk_Service_MsgClient {


    const MTYPE_GLOBAL  = 1;
    const MTYPE_LIKE    = 3;
    const MTYPE_COMMENT = 4;
    const MTYPE_TRANS   = 5;
    const MTYPE_ACT     = 6;

    private $appId;
    private static $accessApps = array(     # 已对接appId
        Hk_Const_AppId::APP_HOMEWORK,
        Hk_Const_AppId::APP_AIRCLASS,
        'kousuan', //作业帮老口算
        'knowledge', //作业帮家长版
        'haokezaixian', //好课在线
        'youketang', //优课堂
        'bfsports', //百分运动
    );

    public function __construct($appId) {
        if (!in_array($appId, self::$accessApps)) {
            Bd_Log::warning("{$appId} access denied");
            return false;
        }
        $this->appId = $appId;
    }

    /**
     * 获取指定用户未读消息数
     *
     * @param int         $uid
     * @param int         $mType
     * @return boolean
     */
    public function getUnreadNum($uid, $mType) {
        $input  = array(
            "appId" => $this->appId,
            "uid"   => intval($uid),
            "mType" => intval($mType),
        );
        $method = "getUnreadNum";
        $ret    = $this->callRpc($method, $input);
        if (false === $ret || $ret["errno"] > 0) {
            return false;
        }
        return $ret["data"];

    }

    /**
     * 发送系统通知，如果不指定用户将发送给所有用户<br>
     * 模板申请请联系平台
     *
     * @param int         $tplId        模版 ID
     * @param array       $contentCtx   内容数据
     * @param array       $urlCtx       跳转链接数据
     * @param array       $uids         接收人列表
     * @param string      $img          图片链接
     * @return boolean
     */
    public function sendNotice($tplId, array $uids, array $contentCtx = array(), array $urlCtx = array(), $img = '') {
        foreach ($uids as &$uid) {
            $uid = intval($uid);
        }
        $input  = array(
            "appId"      => $this->appId,
            "uids"       => $uids,
            "tplId"      => intval($tplId),
            "contentCtx" => $contentCtx,
            "urlCtx"     => $urlCtx,
            "img"        => strval($img),
        );
        $method = "sendNotice";
        $ret    = $this->callRpc($method, $input);
        if (false === $ret || $ret["errno"] > 0) {
            return false;
        }
        return true;
    }

    /**
     * 发送点赞和评论通知消息，即MTYPE_LIKE|MTYPE_COMMENT<br>
     * payload字段随类型不同进行校验，字段如下：<br>
     * <code>
     * array(<br>
     *     # 点赞通知<br>
     *     MTYPE_LIKE => array(<br>
     *         "url"   => "string",        # 必填<br>
     *         "refer" => array(           # 选填，二选一<br>
     *             "image"   => "string",<br>
     *             "content" => "string",<br>
     *         ),<br>
     *     ),<br>
     *     # 评论通知<br>
     *     MTYPE_COMMENT => array(<br>
     *         "url"   => "string",        # 必填<br>
     *         "reply" => "string",        # 必填<br>
     *         "refer" => array(           # 选填，二选一<br>
     *             "image"   => "string",<br>
     *             "content" => "string",<br>
     *             "replies" => array(),<br>
     *             "source"  => "string",  # 评论来源 <br>
     *         ),<br>
     *         "ext"   => array(),# 拓展字段，存储评论 qid, toRid, toFloor 信息
     *     ),<br>
     * );<br>
     * </code>
     * @param int         $suid
     * @param array       $uids
     * @param int         $mType
     * @param array       $payload
     * @param boolean     $isPush
     * @param string      $msgId push msgId 选传
     * @return boolean
     */
    public function sendMsg($suid, array $uids, $mType, array $payload, $isPush = false, $msgId = '') {
        foreach ($uids as &$uid) {
            $uid = intval($uid);
        }
        $input  = array(
            "appId"   => $this->appId,
            "suid"    => intval($suid),
            "uids"    => $uids,
            "mType"   => $mType,
            "payload" => $payload,
            "isPush"  => $isPush,
            "msgId"   => $msgId,
        );
        $method = "sendMsg";
        $ret    = $this->callRpc($method, $input);
        if (false === $ret || $ret["errno"] > 0) {
            return false;
        }
        return true;
    }

    /**
     * 获取指定名称徽标列表<br>
     * 如果指定徽标和用户相关，必须传递用户uid
     *
     * @param array       $names
     * @param int         $uid
     * @return mixed:array|boolean
     */
    public function getBadges(array $names, $uid = 0) {
        $input  = array(
            "appId" => $this->appId,
            "names" => $names,
            "uid"   => intval($uid),
        );
        $method = "getBadges";
        $ret    = $this->callRpc($method, $input);
        if (false === $ret || $ret["errno"] > 0) {
            return false;
        }
        return $ret["data"];
    }

    /**
     * 覆盖设置徽标值
     *
     * @param string      $name
     * @param int         $value
     * @param int         $uid
     * @return boolean
     */
    public function setBadge($name, $value, $uid = 0) {
        $input  = array(
            "appId" => $this->appId,
            "name"  => $name,
            "value" => $value,
            "uid"   => intval($uid),
        );
        $method = "setBadge";
        $ret    = $this->callRpc($method, $input);
        if (false === $ret || $ret["errno"] > 0) {
            return false;
        }
        return true;
    }

    /**
     * 递增/递减设置徽标值
     *
     * @param string      $name
     * @param int         $value
     * @param int         $uid
     * @return boolean
     */
    public function incrBadge($name, $value, $uid = 0) {
        $input  = array(
            "appId" => $this->appId,
            "name"  => $name,
            "value" => $value,
            "uid"   => intval($uid),
        );
        $method = "incrBadge";
        $ret    = $this->callRpc($method, $input);
        if (false === $ret || $ret["errno"] > 0) {
            return false;
        }
        return $ret["data"];
    }

    /**
     * 清空徽标以及对应的值
     *
     * @param array       $names
     * @param int         $uid
     * @return boolean
     */
    public function clearBadges(array $names, $uid) {
        $input  = array(
            "appId" => $this->appId,
            "names" => $names,
            "uid"   => intval($uid),
        );
        $method = "clearBadges";
        $ret    = $this->callRpc($method, $input);
        if (false === $ret || $ret["errno"] > 0) {
            return false;
        }
        return true;
    }

    private function callRpc($method, $input) {
        $srvName = "sysmessage";
        return Hk_Service_Rpc::call($srvName, $method, $input);
    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
