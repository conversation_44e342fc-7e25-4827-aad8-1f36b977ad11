<?php
/**
 * 对message RPC、查询调用的封装
 *
 * @deprecated
 *
 * @since 2018-12-13 将代码内所有的函数清空，永远返回true
 *
 * @fileName Msg.php
 * <AUTHOR>
 * @version 1.0
 */
class Hk_Service_Message {

    public static function sendMsg($cmdNo, $datas) {
        return false;
    }

    public static function delMsg($storeType, $uid, $key = "", $product = "napi") {
        return false;
    }

    public static function delMsgBatch($storeType, $uid, $keys, $product = "napi") {
        return false;
    }

    public static function markMsg($storeType, $uid, $key = "", $product = "napi") {
        return false;
    }

    public static function markMsgBatch($storeType, $uid, $keys, $product = "napi") {
        return false;
    }

    public static function markMsgQuestion($uid, $qid, $fid) {
        return false;
    }

    public static function markMsgByQid($uid, $qid) {
        return false;
    }

    public static function delMsgByQid($uid, $qid) {
        return false;
    }

    public static function delMsgByFid($uid, $fid) {
        return false;
    }

    public static function delMsgByQuestionType($uid, $type) {
        return false;
    }

    public static function markMsgByFid($uid, $fid) {
        return false;
    }

    public static function getUnreadCountByQid($uid, $qid) {
        return 0;
    }
    public static function getUserMsg($storeType, $uid, $min = false, $count = false, $offset = false, $product = "napi") {
        return false;
    }

    public static function getUserChatMsg($uid, $min = false, $count = false, $offset = false) {
        return false;
    }

    public static function getQuestionUnread($uid) {
        return false;
    }

    public static function getQuestionType($msgNo) {
        return 3;
    }

    public static function getUserUnread($storeType, $uid, $product = "napi") {
        return 0;
    }

    public static function getUserAskAnswerMsg($intUid, $intBaseTime = 0, $intType = 0, $intLimit = 20, $intOffset = 0) {
        return array(
            'list'        => array(),
            'total'       => 0,
            'askTotal'    => 0,
            'answerTotal' => 0,
            'inviteTotal' => 0,
        );
    }

    public static function getGlobalSysMsgInfoUnread($input, $product = "napi") {
        return 0;
    }

    public static function delGlobalSysMsg($uid, $os, $key = "", $product = "napi") {
        return false;
    }

    public static function markGlobalSysMsg($uid, $product = "napi") {
        return false;
    }

    public static function getGlobalSysMsg($input, $countOnly = false, $product = "napi") {
        return [];
    }

    public static function buildGlobalSysMsgDelMap($msgList, $deltm, $delmap) {
        return 0;
    }

    public static function getProductSwitchType($product, $cmdNo = null) {
        return array();
    }

    public static function getUserSwitch($uid, $product = "napi") {
		return array();
    }

    public static function setUserSwitch($uid, $type, $close = 1, $product = "napi") {
        return true;
    }

    public static function getInstance($cls, $args = []) {
        static $instances = [];
        if (!isset($instances[$cls])) {
            if (!class_exists($cls)) {
                $instances[$cls] = false;
            } else {
                try {
                    $reflectionCls = new ReflectionClass($cls);
                    $instances[$cls] = $reflectionCls->newInstanceArgs($args);
                } catch (Exception $e) {
                }
            }
        }
        return $instances[$cls];
    }
}
