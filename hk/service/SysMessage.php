<?php

/**
 * @deprecated
 *
 * @since 2018-12-13 代码全部置空，返回false
 *
 * @file SysMessage.php
 * <AUTHOR>
 * @date 2017-06-13
 * @brief 基于Hk_Service_Message封装的发送系统消息的服务
 * 可以指定不同的产品app，具体的product在Hk_Service_Message_Const中定义
 * 使用Hk_Service_Message_Const::CHARGE，而不是SYS_NOTICE，可以避免消息存储到db的过程
 * 1. 发送纯文本消息：sendTextSysMessage
 * 2. 发送带跳转链接的消息：sendUrlSysMessage
 **/
class Hk_Service_SysMessage {

    public static function sendTextSysMessage($uid, $title, $content, $product = "napi") {
        return false;
    }

    public static function sendUrlSysMessage($uid, $title, $content, $urlTitle, $urlLink, $product = "napi") {
        return false;
    }
}