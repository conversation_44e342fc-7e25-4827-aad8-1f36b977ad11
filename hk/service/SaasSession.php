<?php
/** 
 * @file SaasSession.php
 * @brief session服务下沉，代理session接口调用
 *        saas鉴权逻辑
 * <AUTHOR>
 * @date 2020-03-10
 */

class Hk_Service_SaasSession{

    //service name
    private $_serviceName = 'zybsession';

    private $saasOcs;

    /**
     * $tenantcode 由saas平台提供的租户id
     * $ak 由saas平台提供的ak
     * $sk 由saas平台提供的sk
     */
    public function __construct($tenantcode = '', $ak = '', $sk = '') {
        $this->saasOcs = new Hk_Service_SaasOcs($tenantcode, $ak, $sk);
    }

    /*
     * 请求session服务的代理，拼接ocs鉴权需要的数据
     * @param string $uri 例:/session/submit/login
     * @param array $requestParam 请求参数
     * @param array $header
     * 
     * */
    public function call($uri, $requestParam = [], $header = []) {
        //验证uri合法
        if(empty($uri)) {
            return false;
        }
        $arrUri = explode("?", $uri);
        $pathinfo = trim($arrUri[0]);
        if (substr($pathinfo, 0, 1) != "/") {
            $pathinfo = '/' . $pathinfo;
        }
        ral_set_pathinfo($pathinfo);

        $query = [];
        !empty($arrUri[1]) && $query[] = trim($arrUri[1],'&');
        !empty($header['querystring']) && $query[] = trim($header['querystring'],'&');

        //获取ocs参数
        if ($saasParam = $this->saasOcs->getOcsQuery($pathinfo, 'string')) {
            $query[] = $saasParam;
        }
        $query && $header['querystring'] = implode('&', $query);

        $ret = ral($this->_serviceName, 'POST', $requestParam, mt_rand(), $header);
        if (false === $ret) {
            $errno      = ral_get_errno();
            $errmsg     = ral_get_error();
            $protocolSt = ral_get_protocol_code();
            Bd_Log::warning("Error:[service ".$this->_serviceName."connect error], Detail:[errno:$errno errmsg:$errmsg protocol_status:$protocolSt]");
            return false;
        }
        return $ret;
    }
}
