<?php


/**
 * @brief 知识点详细内容
 */
class Hk_Dao_Tiku_SubjectPoint extends Hk_Common_BaseDao{
    public function __construct(){
        //$this->_dbName = "tikumis/tiku_offline";
        $this->_dbName = "homework/homework";
        //$this->_db = Hk_Service_Db::getDB($this->_dbName);
        $this->_db = null;
        $this->_table = "tblSubjectPoint";
        $this->arrFieldsMap = array(
            'id' => 'id',
            'tid' => 'tid',// 上线前修改
            'pointId' => 'pointId',
        );

        $this->arrTypesMap = array(
            'id' => Hk_Service_Db::TYPE_INT,
            'tid' => Hk_Service_Db::TYPE_INT,
            'pointId' => Hk_Service_Db::TYPE_INT,
        );
    }
}
