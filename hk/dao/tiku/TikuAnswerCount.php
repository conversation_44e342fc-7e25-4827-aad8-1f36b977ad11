<?php
/***************************************************************************
 *
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/



/**
 * @file TikuAnswerCount.php
 * <AUTHOR>
 * @date 2015-05-27
 * @brief 题库问题回答计数
 **/
class Hk_Dao_Tiku_TikuAnswerCount extends Hk_Common_BaseDao {
    //tid分表
    const TABLE_ROW_MAX = 10000000;

    /**
     * 待连接的数据表名称前缀
     * @var string
     */
    protected $_tablePrefix;

    public function __construct() {
        $this->_dbName      = "zybpractice/practice";
        //$this->_db          = Hk_Service_Db::getDB($this->_dbName);
        $this->_db          = null;
        $this->_table       = "tblTikuAnswerCount";
        $this->_tablePrefix = "tblTikuAnswerCount";
        $this->arrFieldsMap = array(
            'tid'      => 'tid',
            'ansCount' => 'ansCount',
            'ansRight' => 'ansRight',
            'ansWrong' => 'ansWrong',
            'mtime'    => 'mtime',
            'opt1'     => 'opt1',
            'opt2'     => 'opt2',
            'opt3'     => 'opt3',
            'opt4'     => 'opt4',
            'opt5'     => 'opt5',
            'opt6'     => 'opt6',
            'opt7'     => 'opt7',
            'opt8'     => 'opt8',
            'ext'      => 'ext',
        );

        $this->arrTypesMap = array(
            'tid'      => Hk_Service_Db::TYPE_INT,
            'ansCount' => Hk_Service_Db::TYPE_INT,
            'ansRight' => Hk_Service_Db::TYPE_INT,
            'ansWrong' => Hk_Service_Db::TYPE_INT,
            'mtime'    => Hk_Service_Db::TYPE_INT,
            'opt1'     => Hk_Service_Db::TYPE_INT,
            'opt2'     => Hk_Service_Db::TYPE_INT,
            'opt3'     => Hk_Service_Db::TYPE_INT,
            'opt4'     => Hk_Service_Db::TYPE_INT,
            'opt5'     => Hk_Service_Db::TYPE_INT,
            'opt6'     => Hk_Service_Db::TYPE_INT,
            'opt7'     => Hk_Service_Db::TYPE_INT,
            'opt8'     => Hk_Service_Db::TYPE_INT,
            'ext'      => Hk_Service_Db::TYPE_JSON,
        );
    }


    /**
     * 给题库tid增加练习计数
     *
     * @param  integer    $tid
     * @param  integer    $isRight
     * @param  integer    $choice
     */
    public function addPracticeCount($tid, $isRight, $choice) {
        //确定分表
        $result = $this->setPartionTable($tid);
        if (!$result) {
            return false;
        }

        $ansRight = (0 < $isRight)? 1 : 0;
        $ansWrong = (0 < $isRight)? 0 : 1;
        $choice   = intval($choice);
        $sql = sprintf("INSERT INTO %s(tid,ansCount,ansRight,ansWrong,mtime,opt1,opt2,opt3,opt4,opt5,opt6,opt7,opt8) ".
            "VALUES(%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d) ON DUPLICATE KEY UPDATE mtime=VALUES(mtime),ansCount=ansCount+VALUES(ansCount),".
            "ansRight=ansRight+VALUES(ansRight),ansWrong=ansWrong+VALUES(ansWrong),opt1=opt1+VALUES(opt1),opt2=opt2+VALUES(opt2),".
            "opt3=opt3+VALUES(opt3),opt4=opt4+VALUES(opt4),opt5=opt5+VALUES(opt5),opt6=opt6+VALUES(opt6),opt7=opt7+VALUES(opt7),opt8=opt8+VALUES(opt8)",
            $this->_table,
            intval($tid),
            1,
            $ansRight,
            $ansWrong,
            time(),
            ($choice >> 0) & 1,
            ($choice >> 1) & 1,
            ($choice >> 2) & 1,
            ($choice >> 3) & 1,
            ($choice >> 4) & 1,
            ($choice >> 5) & 1,
            ($choice >> 6) & 1,
            ($choice >> 7) & 1
        );
        $data = $this->query($sql);
        if (false === $data) {
            Bd_Log::warning("Error[dbError] Detail[db insert failed, sql:{$sql}");
            return false;
        }

        return $data;
    }


    /**
     * 获取题库tid练习计数
     *
     * @param  integer    $tid
     */
    public function getPracticeCount($tid) {
        //确定分表
        $result = $this->setPartionTable($tid);
        if (!$result) {
            return false;
        }

        $arrFields = array('tid', 'ansCount', 'ansRight', 'ansWrong', 'mtime', 'opt1', 'opt2', 'opt3', 'opt4', 'opt5', 'opt6', 'opt7', 'opt8', 'ext');
        //查询字段的转换
        $arrFields = Hk_Service_Db::mapField($arrFields, $this->arrFieldsMap, true);
        $strFields = implode(",", $arrFields);
        $sql = sprintf("SELECT %s FROM %s WHERE tid=%d LIMIT 10",
            $strFields,
            $this->_table,
            intval($tid)
        );
        $data = $this->query($sql);
        if (false === $data) {
            Bd_Log::warning("Error[dbError] Detail[db select failed, sql:{$sql}");
            return false;
        }

        return $data;
    }


    /**
     * 确定分表
     *
     * @param integer    $tid
     * @return bool 成功返回true，否则false
     */
    protected function setPartionTable($tid){
        $tid  = intval($tid);
        if(0 >= $tid){
            Bd_Log::warning("Error[paramError] Detail[tid:{$tid}]");
            return false;
        }

        $tableIndex = intval($tid/self::TABLE_ROW_MAX);
        $this->_table = $this->_tablePrefix. intval($tableIndex);

        Bd_Log::addNotice("tblTikuAnswerCount", $tableIndex);

        return true;
    }
}
