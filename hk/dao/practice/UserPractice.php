<?php
/***************************************************************************
 *
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/



/**
 * @file UserPractice.php
 * <AUTHOR>
 * @date 2015-05-15
 * @brief 用户-练习题记录
 * 
 * @deprecated  已废弃 2020/06/18
 *
 **/

class Hk_Dao_Practice_UserPractice extends Hk_Common_BaseDao {
    //用户分表
    const TABLE_NUM = 20;

    /**
     * 待连接的数据表名称前缀
     * @var string
     */
    protected $_tablePrefix;

    public function __construct() {
        $this->_dbName      = "practice/practice";
        $this->_table       = "tblUserPractice";
        $this->_tablePrefix = "tblUserPractice";
        $this->arrFieldsMap = array(
            'id'            => 'id',
            'uid'           => 'uid',
            'cuid'          => 'cuid',
            'tid'           => 'tid',
            'ctime'         => 'ctime',
            'choice'        => 'choice',
            'judge'         => 'judge',
            'bit'           => 'bit',
            'ext'           => 'ext',
        );

        $this->arrTypesMap = array(
            'id'         => Hk_Service_Db::TYPE_INT,    
            'uid'        => Hk_Service_Db::TYPE_INT,    
            'tid'        => Hk_Service_Db::TYPE_INT,     
            'ctime'      => Hk_Service_Db::TYPE_INT,    
            'choice'     => Hk_Service_Db::TYPE_INT,    
            'judge'      => Hk_Service_Db::TYPE_INT,    
            'bit'        => Hk_Service_Db::TYPE_INT,      
            'ext'        => Hk_Service_Db::TYPE_JSON,    
        );
    }


    /**
     * 增加记录
     * @param integer $uid
     * @param string  $cuid
     * @param array   $paras 
     * @return MIX
     */
    public function add($uid, $cuid, $paras) {

        //确定分表 
        $result = $this->setPartionTable($uid, $cuid);
        if (!$result) {
            return false;
        }
        //登录状态，cuid不需要，这里置空
        if ($uid > 0) {
            $cuid = "";
        }

        $sql = sprintf("INSERT INTO %s(uid,cuid,tid,ctime,choice,judge,bit,ext) VALUES(%d,unhex('%s'),%d,%d,%d,%d,%d,unhex('%s'))", 
            $this->_table,
            intval($uid),
            bin2hex($cuid),
            intval($paras['tid']),
            time(),
            intval($paras['choice']),
            intval($paras['judge']),
            intval($paras['bit']),
            isset($paras['ext'])? bin2hex($paras['ext']) : bin2hex("[]")
        );

        $data = $this->query($sql);
        if (false === $data) {
            Bd_Log::warning("Error[dbError] Detail[db insert failed, sql:{$sql}]");
            return false;
        }

        return $data;
    }


    /**
     * 增加记录（为单个用户导数据使用）
     * @param integer $uid
     * @param string  $cuid
     * @param array   $items 
     * @return MIX
     */
    public function addBatch($uid, $cuid, $items) {
        if ( !is_array($items) || empty($items)) {
            return false;
        }

        //确定分表 
        $result = $this->setPartionTable($uid, $cuid);
        if (!$result) {
            return false;
        }

        //登录状态，cuid不需要，这里置空
        if ($uid > 0) {
            $cuid = "";
        }
        $tpl    = "(%d,unhex('%s'),%d,%d,%d,%d,%d,unhex('%s'))";
        $values = array();
        foreach ($items as $key => $val) {
            $values[] = sprintf(
                $tpl,
                intval($uid), 
                bin2hex($cuid), 
                intval($val['tid']), 
                intval($val['ctime']), 
                intval($val['choice']), 
                intval($val['judge']), 
                intval($val['bit']), 
                isset($val['ext'])? bin2hex($val['ext']) : bin2hex("[]")
            );
        }

        $sql = sprintf("INSERT INTO %s(uid,cuid,tid,ctime,choice,judge,bit,ext) VALUES%s", 
            $this->_table,
            implode(",", $values)
        );

        $data = $this->query($sql);
        if (false === $data) {
            Bd_Log::warning("Error[dbError] Detail[db insert failed, sql:{$sql}]");
            return false;
        }

        return $data;
    }


    /**
     * 批量删除记录，按tids（为单个用户使用）
     *
     * @param  int    $uid
     * @param  string $cuid
     * @param  array  $tids   
     */
    public function delByUserTids($uid, $cuid, $tids) {
        if ( !is_array($tids) || empty($tids)) {
            return false;
        }

        //确定分表    
        $result = $this->setPartionTable($uid, $cuid);
        if (!$result) {
            return false;
        }

        $strTids = array();
        foreach ($tids as $key => $val) {
            $strTids[] = intval($val);
        }
        $strTids = implode(",", $strTids);

        //处理uid和cuid的关系
        if (0 < $uid) {
            $sql = sprintf("DELETE FROM %s WHERE uid=%d AND tid IN(%s) LIMIT 200", 
                $this->_table,
                intval($uid),
                $strTids
            );
        } else {
            $sql = sprintf("DELETE FROM %s WHERE uid=0 AND cuid=unhex('%s') AND tid IN(%s) LIMIT 200", 
                $this->_table,
                bin2hex($cuid),
                $strTids
            );
        }
        
        $data = $this->query($sql);
        if (false === $data) {
            Bd_Log::warning("Error[dbError] Detail[db select failed, sql:{$sql}]");
            return false;
        }
        return $data;
    }


    /**
     * Select查询，根据限制条件获取结果的单条记录
     *
     * @param  int    $uid
     * @param  string $cuid
     * @param  array  $tids   
     * @param  array  $arrFields  需要查询的字段名数组，格式必须为数组
     */
    public function getPracticeByTids($uid, $cuid, $tids, $arrFields=array()) {

        //确定分表    
        $result = $this->setPartionTable($uid, $cuid);
        if (!$result) {
            return false;
        }

        if(empty($arrFields)) {
            $arrFields = array('id', 'uid', 'cuid', 'tid', 'ctime', 'choice', 'judge', 'bit', 'ext');
        }
        //查询字段的转换
        $arrFields = Hk_Service_Db::mapField($arrFields, $this->arrFieldsMap, true);
        $strFields = implode(",", $arrFields);

        $strTids = array();
        foreach ($tids as $key => $val) {
            $strTids[] = intval($val);
        }
        $strTids = implode(",", $strTids);

        //处理uid和cuid的关系
        if (0 < $uid) {
            $sql = sprintf("SELECT %s FROM %s WHERE uid=%d AND tid IN(%s) LIMIT 200", 
                $strFields,
                $this->_table,
                intval($uid),
                $strTids
            );
        } else {
            $sql = sprintf("SELECT %s FROM %s WHERE uid=0 AND cuid=unhex('%s') AND tid IN(%s) LIMIT 200", 
                $strFields,
                $this->_table,
                bin2hex($cuid),
                $strTids
            );
        }
        
        $data = $this->query($sql);
        if (false === $data) {
            Bd_Log::warning("Error[dbError] Detail[db select failed, sql:{$sql}]");
            return false;
        }
        return $data;
    }


    /**
     * 获取用户答题计数
     * @param integer $uid
     * @param string  $cuid
     * @param integer $isRight 是否区分对错，不传则对错都统计
     * @return MIX integer/false
     */
    public function getCountByUser($uid, $cuid, $isRight=null) {

        //确定分表 
        $result = $this->setPartionTable($uid, $cuid);
        if (!$result) {
            return false;
        }

        $strAddons = (null === $isRight)? "" : "AND judge=".intval($isRight);

        //处理uid和cuid的关系
        if (0 < $uid) {
            $sql = sprintf("SELECT count(id) as cnt FROM %s WHERE uid=%d %s", 
                $this->_table,
                intval($uid),
                $strAddons
            );
        } else {
            $sql = sprintf("SELECT count(id) as cnt FROM %s WHERE uid=0 AND cuid=unhex('%s') %s", 
                $this->_table,
                bin2hex($cuid),
                $strAddons
            );
        }

        $data = $this->query($sql);
        if (false === $data) {
            Bd_Log::warning("Error[dbError] Detail[db select failed, sql:{$sql}]");
            return false;
        }

        return $data;
    }


    /**
     * Select查询，根据user获取列表
     *
     * @param  int    $uid
     * @param  string $cuid
     * @param  int    $pn
     * @param  int    $rn 
     * @param  array  $arrFields  需要查询的字段名数组，格式必须为数组
     */
    public function getListByUser($uid, $cuid, $pn=0, $rn=200, $arrFields=array()) {

        //确定分表    
        $result = $this->setPartionTable($uid, $cuid);
        if (!$result) {
            return false;
        }

        if(empty($arrFields)) {
            $arrFields = array('id', 'uid', 'cuid', 'tid', 'ctime', 'choice', 'judge', 'bit', 'ext');
        }
        //查询字段的转换
        $strFields = array();
        foreach ($arrFields as $key => $val) {
            if (isset($this->arrFieldsMap[$val])) {
                //!!!注意 id 字段的特殊处理!!!
                $rowName = ('id' == $val)? "b.".$val : $val;
                $strFields[] = $rowName;
            }
        }
        $strFields = implode(",", $strFields);

        //处理uid和cuid的关系
        if (0 < $uid) {
            $sql = sprintf("SELECT %s FROM (SELECT id FROM %s WHERE uid=%d LIMIT %d, %d) a, %s b WHERE a.id=b.id", 
                $strFields,
                $this->_table,
                intval($uid),
                intval($pn),
                intval($rn),
                $this->_table
            );
        } else {
            $sql = sprintf("SELECT %s FROM (SELECT id FROM %s WHERE uid=0 AND cuid=unhex('%s') LIMIT %d, %d) a, %s b WHERE a.id=b.id", 
                $strFields,
                $this->_table,
                bin2hex($cuid),
                intval($pn),
                intval($rn),
                $this->_table
            );
        }
        
        $data = $this->query($sql);
        if (false === $data) {
            Bd_Log::warning("Error[dbError] Detail[db select failed, sql:{$sql}]");
            return false;
        }
        return $data;
    }

    public function getPracticeListByOther($uid, $cuid, $arrConds, $pn=0, $rn=200, $arrFields = array()){

        //确定分表    
        $result = $this->setPartionTable($uid, $cuid);
        if (!$result) {
            return false;
        }

        if(empty($arrFields)) {
            $arrFields = array('id', 'uid', 'cuid', 'tid', 'ctime', 'choice', 'judge', 'bit', 'ext');
        }
        //查询字段的转换
        $strFields = array();
        foreach ($arrFields as $key => $val) {
            if (isset($this->arrFieldsMap[$val])) {
                //!!!注意 id 字段的特殊处理!!!
                $rowName = ('id' == $val)? "b.".$val : $val;
                $strFields[] = $rowName;
            }
        }
        $strFields = implode(",", $strFields);
        
        //处理字段
        $startTime = $arrConds['startTime'];
        $endTime   = $arrConds['endTime'];

        //处理uid和cuid的关系
        if (0 < $uid) {
            $sql = sprintf("SELECT %s FROM (SELECT id FROM %s WHERE uid=%d AND ctime>=%d AND ctime<= %d LIMIT %d, %d) a, %s b WHERE a.id=b.id", 
                $strFields,
                $this->_table,
                intval($uid),
                intval($startTime),
                intval($endTime),
                intval($pn),
                intval($rn),
                $this->_table
            );
        } else {
            $sql = sprintf("SELECT %s FROM (SELECT id FROM %s WHERE uid=0 AND ctime>=%d AND ctime<=%d AND cuid=unhex('%s') LIMIT %d, %d) a, %s b WHERE a.id=b.id", 
                $strFields,
                $this->_table,
                intval($startTime),
                intval($endTime),
                bin2hex($cuid),
                intval($pn),
                intval($rn),
                $this->_table
            );
        }
        
        $data = $this->query($sql);
        if (false === $data) {
            Bd_Log::warning("Error[dbError] Detail[db select failed, sql:{$sql}]");
            return false;
        }
        return $data;
    } 

    /**
     * 增加修改回答记录
     * @param integer $uid
     * @param string  $cuid
     * @param integer $tid
     * @param array   $paras 
     * @return MIX
     */
    public function modChoiceByUserTid($uid, $cuid, $tid, $paras) {

        $result = $this->setPartionTable($uid, $cuid);
        if (!$result) {
            return false;
        }

        //处理uid和cuid的关系
        if (0 < $uid) {
            // cuid置空
            $cuid = "";
            $sql = sprintf("UPDATE %s SET choice=%d, judge=%d, ctime=%d, cuid=unhex('%s') WHERE uid=%d AND tid=%d LIMIT 20", 
                $this->_table,
                intval($paras['choice']),
                intval($paras['judge']),
                time(),
                bin2hex($cuid),
                intval($uid),
                intval($tid)
            );
        } else {
            $sql = sprintf("UPDATE %s SET choice=%d, judge=%d, ctime=%d WHERE uid=0 AND cuid=unhex('%s') AND tid=%d LIMIT 20", 
                $this->_table,
                intval($paras['choice']),
                intval($paras['judge']),
                time(),
                bin2hex($cuid),
                intval($tid)
            );
        }

        $data = $this->query($sql);
        if (false === $data) {
            Bd_Log::warning("Error[dbError] Detail[db update failed, sql:{$sql}]");
            return false;
        }

        return $data;
    }


    /**
     * 确定分表
     *
     * @param int    uid
     * @param string cuid 
     * @return bool 成功返回true，否则false
     */
    protected function setPartionTable($uid = 0, $cuid = ''){
        $uid  = intval($uid);
        $cuid = trim(strval($cuid));
        if($uid <= 0 && strlen($cuid) <= 0){
            Bd_Log::warning("Error[paramError] Detail[uid:$uid cuid:$cuid]");
            return false;
        }

        if($uid > 0) {
            $tableIndex  = $uid % self::TABLE_NUM;
            $this->_table = $this->_tablePrefix. intval($tableIndex);
            $flag = "uid";
        }else{
            $tableIndex  = crc32($cuid) % self::TABLE_NUM;
            $this->_table = $this->_tablePrefix. intval($tableIndex);
            $flag = "cuid";
        }
        Bd_Log::addNotice("{$flag}_tblUserPractice", $tableIndex);
        return true;
    }
}
