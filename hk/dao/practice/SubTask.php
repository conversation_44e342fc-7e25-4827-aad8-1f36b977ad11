<?php

/**
 * @file SubTask.php
 * <AUTHOR>
 * @date 2017-06-06
 * @version $Revision$-
 * @brief 微课子任务
 * 
 * @deprecated  已废弃 2020/06/18
 *
 **/

class Hk_Dao_Practice_SubTask extends Hk_Common_BaseDao {

    public function __construct()
    {
        $this->_dbName = 'mobile/nativeapp';
        $this->_table = "tblWKSubTask";
    }
    /**
     * @param $data
     * @return bool|int
     * @desc 插入一条记录 成功返回插入的id 失败返回false
     */
    public function insert($data)
    {
        $ret = $this->insertRecords($data);
        if ($ret) {
            return $this->getInsertId();
        }
        return false;
    }

    /**
     * @param $task_id
     * @return bool
     */
    public function deleteByTaskId($task_id)
    {
        $conds = [
            'course_id' => $task_id,
        ];
        $list = $this->deleteByConds($conds);
        return $list;
    }

    public function updateByTaskId($data,$task_id) {
        $conds = [
            'task_id' => $task_id,
        ];
        $list = $this->updateByConds($conds,$data);
        return $list;
    }


    public function incrFieldsValue($subTaskId, $field, $incr=1){
        if($subTaskId <= 0){
            return false;
        }
        $sql = "update " . $this->_table . " set ". $field . "=" . $field . "+" . $incr . " where id = " . $subTaskId . ";";
        $ret = $this->query($sql);
        return $ret;
    }
}
