<?php
/**
 * GPS定位相关操作  请求lbs接口
 * <AUTHOR>
 * @date 2014-07-30 17:43:08
 */
class Hk_Dao_User_Gps {

    protected $arrConf = array();

    //const RAL_MODULE_NAME_SEARCH = 'placesearch';
    //const RAL_MODULE_NAME_UPDATE = 'mapapi';

    const PATH_CREATE_POI = '/geodata/v3/poi/create';
    const PATH_GET_POI    = '/geodata/v3/poi/detail';
    const PATH_UPDATE_POI = '/geodata/v3/poi/update';
    const PATH_DELETE_POI = '/geodata/v3/poi/delete';

    const PATH_SEARCH_NEARBY = '/geosearch/v3/nearby';  //周围搜索
    const PATH_SEARCH_LOCAL  = '/geosearch/v3/local';   //地区搜索

    const COORD_TYPE_BAIDU = 3;

    const SEARCH_SORT_BY = 'distance:1';

    public function __construct() {
        $this->arrConf = Bd_Conf::getConf('hk/gps');
        if (empty($this->arrConf)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::OTHER_ERROR);
        }
    }

    /**
     * @brief poi详情检索
     * @param int $intTableId
     * @param int $intLbsId
     * @return json
     * @throws Napi_Exception
     */
    public function detailPoi($intTableId, $intLbsId){

        $arrParam = array(
			'id' => $intLbsId,
            'geotable_id' => $intTableId,
            'coord_type' => self::COORD_TYPE_BAIDU
        );

        Hk_Util_Log::start('dao_geodata_detail');
        $strRet = $this->requestGet( self::PATH_GET_POI, $arrParam);
        Hk_Util_Log::stop('dao_geodata_detail');
        $arrRet = json_decode($strRet, true);
        if (isset($arrRet['status']) && $arrRet['status'] == 0) {
            Bd_Log::addNotice('resGeoDetail', 1);
            return $arrRet;
        }
        Bd_Log::addNotice('resGeoDetail', 0);
        throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::LBS_API_ERROR, array('ret' => $strRet));

    }

    public function createPoi($strTitle, $strAddress, $arrTags,
        $floatLatitude, $floatLongitude, $intTableId, $intUniqId) {
        $arrParam = array(
            'title' => $strTitle,
            'address' => $strAddress,
            'tags' => implode(" ", $arrTags),
            'latitude' => $floatLatitude,
            'longitude' => $floatLongitude,
            'coord_type' => self::COORD_TYPE_BAIDU,
            'geotable_id' => $intTableId,
            'uniq_id' => $intUniqId,
        );

        Hk_Util_Log::start('dao_geodata_create');
        $strRet = $this->requestPost(self::PATH_CREATE_POI, $arrParam);
        Hk_Util_Log::stop('dao_geodata_create');
        $arrRet = json_decode($strRet, true);
        if (isset($arrRet['status']) && $arrRet['status'] == 0) {
            Bd_Log::addNotice('resGeoCreate', 1);
            return $arrRet;
        }
        Bd_Log::addNotice('resGeoCreate', 0);
        throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::LBS_API_ERROR, array('ret' => $strRet));
    }

    public function updatePoi($intLbsId, $strTitle, $strAddress, $arrTags,
        $floatLatitude, $floatLongitude, $intTableId, $intUniqId) {
        $arrParam = array(
            'id' => $intLbsId,
            'title' => $strTitle,
            'address' => $strAddress,
            'tags' => implode(" ", $arrTags),
            'latitude' => $floatLatitude,
            'longitude' => $floatLongitude,
            'coord_type' => self::COORD_TYPE_BAIDU,
            'geotable_id' => $intTableId,
            'uniq_id' => $intUniqId,
        );

        Hk_Util_Log::start('dao_geodata_update');
        $strRet = $this->requestPost(self::PATH_UPDATE_POI, $arrParam);
        Hk_Util_Log::stop('dao_geodata_update');
        $arrRet = json_decode($strRet, true);
        if (isset($arrRet['status']) && $arrRet['status'] == 0) {
            Bd_Log::addNotice('resGeoUpdate', 1);
            return $arrRet;
        }
        Bd_Log::addNotice('resGeoUpdate', 0);

        if($arrRet['status'] == 3003){
            $detail = $this->detailPoi($intTableId, $intLbsId);
            if($detail['status'] == 0 && $detail['poi'] === NULL){
                $objUser = new Hk_Service_Ucloud();
                $objUser->updateAppUserExt($intUniqId, array('lbsId' => 0));
            }
        }

        throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::LBS_API_ERROR, array('ret' => $strRet));
    }


    /**
     * @brief 删除用户坐标数据，支持批量删除
     * @param $intTableId
     * @param $intLbsId  批量删除则用','分隔
     * @param $intTotalDel 1为批量删除
     * @return mixed
     * @throws Napi_Exception
     */
    public function deletePoi($intTableId, $intLbsId, $intTotalDel = 0) {
        if($intTotalDel == 1){
            $arrParam = array(
                'ids' => $intLbsId,
                'geotable_id' =>$intTableId,
                'is_total_del' => 1
            );
        }else{
            $arrParam = array(
                'id' => $intLbsId,
                'geotable_id' => $intTableId,
            );
        }

        Hk_Util_Log::start('dao_geodata_delete');
        $strRet = $this->requestPost(self::PATH_DELETE_POI, $arrParam);
        Hk_Util_Log::stop('dao_geodata_delete');
        $arrRet = json_decode($strRet, true);
        if (isset($arrRet['status']) && ($arrRet['status'] == 0 || $arrRet['status'] == 21) ) {
            Bd_Log::addNotice('resGeoDelete', 1);
            return $arrRet;
        }
        Bd_Log::addNotice('resGeoDelete', 0);

        Bd_Log::warning("del user poi failed. $strRet");
    }

    public function searchNearbyPoi($intTableId, $strQuery, $floatLatitude, $floatLongitude,
        $intRadius, $arrTags, $intPage = 0, $intPageSize = 50) {
        $arrParam = array(
            'geotable_id' => $intTableId,
            'q' => $strQuery,
            'location' => "$floatLongitude,$floatLatitude",
            'coord_type' => self::COORD_TYPE_BAIDU,
            'radius' => $intRadius,
            'sortby' => self::SEARCH_SORT_BY,
            'page_index' => $intPage,
            'page_size' => $intPageSize,
        );
        if (is_array($arrTags) && count($arrTags) > 0) {
            $arrParam['tags'] = implode('', $arrTags);
        }

        Hk_Util_Log::start('dao_geose_nearby');
        $strRet = $this->requestGet(self::PATH_SEARCH_NEARBY, $arrParam);
        Hk_Util_Log::stop('dao_geose_nearby');
        $arrRet = json_decode($strRet, true);
        if (isset($arrRet['status']) && $arrRet['status'] == 0) {
            Bd_Log::addNotice('resGeoNearby', 1);
            return $arrRet;
        }
        Bd_Log::addNotice('resGeoNearby', 0);
        throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::LBS_API_ERROR, array('ret' => $strRet));
    }

    protected function requestGet($strPath, $arrParam) {
        $arrParam['ak'] = $this->arrConf['lbs']['ak'];
        $arrParam['log_id'] = intval(Bd_Log::genLogID());
        $arrParam['sn'] = self::caculateAKSN($this->arrConf['lbs']['sk'], $strPath, $arrParam);

        $url = 'http://api.map.baidu.com'.$strPath.'?'.http_build_query($arrParam);

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_TIMEOUT_MS, 1000);

        for ($i = 0; $i < 2; $i++) {
            $ret = curl_exec($curl);
            if (curl_getinfo($curl, CURLINFO_HTTP_CODE) == 200 && !empty($ret)) {
                break;
            }
        }
        if(false === $ret){
            $errno = curl_errno($curl);
            $msg   = curl_error($curl);
            curl_close($curl);
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::LBS_API_ERROR, array('msg' => "curl $url error: $errno"));
        }

        curl_close($curl);

        return $ret;
    }

    protected function requestPost($strPath, $arrParam) {

        $arrParam['ak'] = $this->arrConf['lbs']['ak'];
        $arrParam['log_id'] = intval(Bd_Log::genLogID());
        $arrParam['sn'] = self::caculateAKSN($this->arrConf['lbs']['sk'], $strPath, $arrParam, 'POST');

        $url = 'http://api.map.baidu.com'.$strPath;
        $data = http_build_query($arrParam);

        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array('Content-Length: ' . strlen($data)));
        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_TIMEOUT_MS, 1000);

        for ($i = 0; $i < 2; $i++) {
            $ret = curl_exec($curl);
            if (curl_getinfo($curl, CURLINFO_HTTP_CODE) == 200 && !empty($ret)) {
                break;
            }
        }
        if(false === $ret){
            $errno = curl_errno($curl);
            $msg   = curl_error($curl);
            curl_close($curl);
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::LBS_API_ERROR, array('msg' => "curl $url error: $errno"));
        }

        curl_close($curl);

        return $ret;
    }

    public static function caculateAKSN($sk, $url, $arrParam, $strMethod = 'GET') {
        if ($strMethod == 'POST') {
            ksort($arrParam);
        }
        return md5(urlencode($url.'?'.http_build_query($arrParam).$sk));
    }

}
