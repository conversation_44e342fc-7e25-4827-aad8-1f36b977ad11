<?php


use Hbase\HbaseClient;
use Hbase\IOError;
use Hbase\IllegalArgument;
use Hbase\AlreadyExists;

use Thrift\Protocol\TBinaryProtocol;
use Thrift\Transport\TSocket;
use Thrift\Transport\TBufferedTransport;
use Thrift\Exception\TException;

/**
 * 通过thrift访问hbase，提供方法请参见：<br>
 * <code>
 * phplib/ext/hbase/Hbase.php
 * </code>
 *
 * @since 1.4 2019-07-29 解决空连接问题
 * @since 1.3 2019-02-20 bns耗时不统计进总耗时中
 * @since 1.2 2018-10-13 日志迁移到ral中
 * @since 1.0 2018-06-20 初始化
 *
 * @filesource bd/hbase/Thrift.php
 * <AUTHOR>
 * @version 1.4
 * @date    2019-07-29
 */
class Bd_Hbase_Thrift {

    const ERR_OK   = 0;
    const ERR_INST = -1;         # 获取实例失败
    const ERR_CONN = -2;         # 连接错误
    const ERR_EXEC = -3;         # 命令执行失败

    private $host  = "";
    private $port  = 0;
    private $retry = 0;         # 重试次数
    private $namespace = "";    # 命名空间
    private $stimeout;          # 发送超时
    private $rtimeout;          # 接收超时

    private $instance;

    private $client = NULL;
    private $prot   = NULL;
    private $initConnect  = false;         # 是否执行过连接，减少空连接同时防止connect过多导致雪崩
    private static $timer = array();

    public function __construct($instance, $namespace, $stimeout, $rtimeout, $retry = 0) {
        $this->instance  = $instance;
        $this->namespace = $namespace;

        self::start("call_bns");
        $service = "hbase";
        $server  = Bd_Bns_Client::getInstance()->getServiceInstance($service, $instance, Bd_Bns_Const::LB_RANDOM, "hbase");
        self::stop("call_bns");
        if (false === $server) {
            $this->log("instance get failed", self::ERR_INST, array(), RAL_LOG_WARN);
            return;
        }

        $this->host     = $server["host"];
        $this->port     = $server["port"];
        $this->retry    = $retry;
        $this->stimeout = $stimeout;
        $this->rtimeout = $rtimeout;
        $this->init();
    }

    /**
     * 初始化hbase连接
     */
    private function init() {
        $socket = new TSocket($this->host, $this->port);
        $socket->setSendTimeout($this->stimeout);
        $socket->setRecvTimeout($this->rtimeout);

        $transport    = new TBufferedTransport($socket);
        $this->prot   = new TBinaryProtocol($transport);
        $this->client = new HbaseClient($this->prot);
    }

    /**
     * 拼接指定集群下指定namespace对应的表
     *
     * @param string       $tableName
     * @return string
     */
    public function getTableName($tableName) {
        if ($this->namespace === "") {
            return $tableName;
        }
        return "{$this->namespace}:{$tableName}";
    }

    /**
     * 执行hbase命令，获取命令结果，日志将输出在/path/to/log/hbase中<br>
     * 失败返回NULL
     *
     * @param string       $method
     * @param array        $input
     * @return mixed:ret|NULL
     */
    public function __call($method, $input) {
        $args = array(
            "tried"  => 0,
            "method" => $method,
            'req_start_time' => gettimeofday(true),
            'reqStartTime' => gettimeofday(true),
        );
        if ($this->isDisabled($method)) {   # 禁止某些方法使用
            $this->log("method disabled", self::ERR_EXEC, $args, RAL_LOG_WARN);
            return false;
        } elseif (!method_exists($this->client, $method)) {
            $this->log("method not exist", self::ERR_EXEC, $args, RAL_LOG_WARN);
            return NULL;
        }
        if (false === $this->getConn()) {   # 创建|复用连接，防止空连接
            $this->log("no hbase connect", self::ERR_EXEC, $args, RAL_LOG_WARN);
            return NULL;
        }

        $needRt = false;                    # 是否需重试
        $timer  = "read";
        for ($tried = 0; $tried <= $this->retry; $tried++) {    # 重试，根据不同的错误类型决定是否重试
            $args["tried"] = $tried;
            self::start($timer);
            try {
                $ret = call_user_func_array(array($this->client, $method), $input);
            } catch (IOError $e) {          # hbaseIO出错
                $errmsg = $e->getMessage();
                $needRt = true;
                $ret    = false;
            } catch (IllegalArgument $e) {  # hbase参数错误
                $errmsg = $e->getMessage();
                $needRt = false;
                $ret    = false;
            } catch (AlreadyExists $e) {    # 操作表出错，表操作已禁止，正常不会抛出此异常
                $errmsg = $e->getMessage();
                $needRt = false;
                $ret    = false;
            } catch (TException $e) {       # thrift错误
                $errmsg = $e->getMessage();
                $needRt = true;
                $ret    = false;
            }
            self::stop($timer);
            if (false === $ret) {
                $this->log($errmsg, self::ERR_EXEC, $args, RAL_LOG_WARN);
                if ($needRt) {              # 继续重试
                    continue;
                }
            }
            break;
        }
        if (false === $ret) {
            return false;
        }
        $args["res_data"] = $ret;
        $this->log("OK", self::ERR_OK, $args);
        return $ret;
    }

    /**
     * 判断当前hbase连接状态
     *
     * @return boolean
     */
    private function getConn() {
        if ($this->prot->getTransport()->isOpen()) {    # hbase连接正常
            return true;
        }
        if (true === $this->initConnect) {              # 已执行connect但未成功
            return false;
        }

        $args   = array(
            "tried"  => 0,
            "method" => "connect",
            'req_start_time' => gettimeofday(true),
            'reqStartTime' => gettimeofday(true),
        );
        $errmsg = "connect failed";
        $timer  = "connect";
        for ($tried = 0; $tried <= $this->retry; $tried++) {    # 根据重试进行连接尝试
            $args["tried"] = $tried;
            self::start($timer);
            try {
                $this->prot->getTransport()->open();
            } catch (TException $e) {
                $errmsg = $e->getMessage();
            }
            self::stop($timer);
            if (false === $this->prot->getTransport()->isOpen()) {
                $this->log($errmsg, self::ERR_CONN, $args, RAL_LOG_WARN);
                continue;
            }
            break;
        }
        $this->initConnect = true;                      # 连接初始化状态标示
        if (false === $this->prot->getTransport()->isOpen()) {
            return false;
        }
        $this->log("OK", self::ERR_OK, $args);
        return true;
    }

    /**
     * 关闭hbase连接
     */
    private function close() {
        if (NULL === $this->prot) {
            return;
        }
        $this->prot->getTransport()->close();
    }

    /**
     * 判断方法是否被禁用
     *
     * @return boolean
     */
    private function isDisabled($method) {
        $disable = array(
            "enableTable"  => 1,
            "disableTable" => 1,
            "createTable"  => 1,
            "deleteTable"  => 1,
            "compact"      => 1,
            "majorCompact" => 1,
            "getTableNames"   => 1,
            "getTableRegions" => 1,
            "getColumnDescriptors" => 1,
        );
        return isset($disable[$method]) ? true : false;
    }

    /**
     * 计时器
     *
     * @param string      $name
     * @return boolean
     */
    private static function start($name) {
        if (!isset(self::$timer[$name])) {
            self::$timer[$name] = new Bd_Timer(false, Bd_Timer::PRECISION_US);
        }
        self::$timer[$name]->reset();
        return self::$timer[$name]->start();
    }

    /**
     * 关闭并重置计时器，返回计时器时间，单位：ms
     *
     * @param string      $name
     * @return float
     */
    private static function stop($name) {
        if (!isset(self::$timer[$name])) {
            return 0;
        }
        return self::$timer[$name]->stop();
    }

    /**
     * 记录hbase实例客户端访问日志
     *
     * @since 2019-01-16 增加链路追踪日志
     *
     * @param string      $msg
     * @param int         $errno
     * @param array       $arg
     * @param const       $level
     */
    private function log($msg = "", $errno = 0, array $arg = array(), $level = RAL_LOG_SUM_SUCC) {
        $tried = isset($arg["tried"]) ? $arg["tried"] : 0;
        unset($arg["tried"]);

        if (function_exists('ral_create_span')) {
            $span = ral_create_span();
        } else {
            $span = array(
                'spanid' => 0,
                'content_tracing' => false,     # 是否进行内容采样
            );
        }

        $uri    = isset($_SERVER['REQUEST_URI']) ? @parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH) : '';
        $logArg = array(              # 通用日志参数
            "uri"       => $uri,
            'spanid'    => $span["spanid"],
            "cost"      => 0,
            'conv'      => 'hbase',
            'prot'      => 'thrift',
            "retry"     => "{$tried}/{$this->retry}",
            "service"   => $this->instance,
            "stimeout"  => $this->stimeout,
            "rtimeout"  => $this->rtimeout,
            "remote_ip" => empty($this->host) ? "" : "{$this->host}:{$this->port}",
            "err_no"    => $errno,
            "err_info"  => $msg,
        );
        # 内容采样
        if ($span["content_tracing"]) {
            $logArg["res_data"] = isset($arg["res_data"]) ? serialize($arg["res_data"]) : "";
        }
        unset($arg["res_data"]);
        # 耗时统计，bns不再计入总耗时
        foreach (self::$timer as $name => $timer) {
            $logArg[$name]   = floatval(number_format($timer->getTotalTime() / 1000, 3, ".", ""));
            self::$timer[$name]->reset();
            if ("call_bns" !== $name) {
                $logArg["cost"] += $logArg[$name];          # 交互总耗时
            }
        }
        $caller = 'Hbase';
        $logArg = array_merge($logArg, $arg);

        ral_set_log(RAL_LOG_MODULE, defined("MAIN_APP") ? MAIN_APP : '');
        ral_set_log(RAL_LOG_USERIP, Bd_Ip::getUserIp());
        ral_write_log($level, $caller, $logArg);
    }

    public function __destruct() {
        $this->close();
    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
