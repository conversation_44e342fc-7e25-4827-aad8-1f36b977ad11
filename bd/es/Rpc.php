<?php


/**
 * 访问es通用restApi
 *
 * @since 2.1 2020-05-21 支持auth
 * @since 2.0 2018-10-13 不再使用httpful，使用ral进行交互
 * @since 1.0 2018-08-13 初始化
 *
 * @filesource bd/es/Rpc.php
 * <AUTHOR>
 * @version 2.1
 * @date    2020-05-21
 */
class Bd_Es_Rpc {

    const ERR_OK   = 0;
    const ERR_INST = -1;         # 获取实例失败
    const ERR_CONN = -2;         # 连接错误
    const ERR_RPC  = -3;         # 请求失败

    private $auth;
    private $ralName;

    private static $timer = array();

    public function __construct($ralName, $auth = "") {
        $this->auth    = $auth;
        $this->ralName = $ralName;
    }

    /**
     * 请求es服务，请求的方法必须在bd/es/endpoints中实现<br>
     * 参数为array，$params
     *
     * @param string      $func
     * @param array       $input
     * @return mixed:array|boolean
     */
    public function __call($func, $input) {
        $arg = array(
            "method" => $func,
        );
        $ep  = self::getEndPoint(ucfirst($func));
        if (false === $ep) {                # 创建endpoint，请求各种参数实现在endpoint中
            Bd_Log::warning("endpoint {$ep} create failed", self::ERR_RPC, $arg);
            return false;
        }
        if (isset($input[0])) {             # 解析参数列表
            $params = is_array($input[0]) ? $input[0] : false;
        } else {
            $params = array();
        }
        if (false === $params) {            # 参数错误
            Bd_Log::warning("params must be array", self::ERR_RPC, $arg);
            return false;
        }
        if (false === $ep->setParams($params)) {
            return false;
        }

        $uri    = $ep->getUri();
        $method = $ep->getMethod();
        $body   = $ep->getBody();
        $header = array(
            "Content-Type" => "application/json",
        );
        if (!empty($this->auth)) {
            $header["Authorization"] = sprintf("Basic %s", base64_encode($this->auth));
        }

        ral_set_querystring("");
        ral_set_pathinfo($uri);
        ral_set_log(RAL_LOG_MODULE, defined("MAIN_APP") ? MAIN_APP : '');
        ral_set_log(RAL_LOG_USERIP, Bd_Ip::getUserIp());
        $resp   = ral($this->ralName, $method, $body, rand(), $header);
        if (false === $resp) {                  # 请求失败
            return false;
        }
        return $resp;
    }

    /**
     * 获取指定方法对应的endpoint
     *
     * @param string       $name
     * @return mixed:object|boolean
     */
    private static function getEndPoint($name) {
        $cname = "Bd_Es_Endpoints_{$name}";
        if (false === class_exists($cname)) {
            return false;
        }
        return new $cname();
    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
