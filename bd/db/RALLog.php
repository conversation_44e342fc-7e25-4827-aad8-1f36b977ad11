<?php


class Bd_Db_RALLog {

    // Error
    public static $COMMON_ERRNO    = 1000;
    public static $CONNECT_ERRNO   = 1001;
    public static $QUERY_ERRNO     = 1002;
    public static $TRANSFORM_ERRNO = 1003;

    public static $COMMON_ERROR    = 'common error';
    public static $CONNECT_ERROR   = 'mysql connect error';
    public static $QUERY_ERROR     = 'query error';
    public static $TRANSFORM_ERROR = 'transform error';

    /**
     * 获取上游的caller_uri
     *
     * @return $caller_uri
     */
    private static function getCallerUri() {
        $caller_uri = '';
        // 增加关联关系统计数据节点
        if (isset($_SERVER['HTTP_X_BD_RAWURL']) && !empty($_SERVER['HTTP_X_BD_RAWURL'])) {
            // ui---->service
            $caller_uri = $_SERVER['HTTP_X_BD_RAWURL'];
        } elseif (isset($_REQUEST['method'])) {
            // service---->service
            $caller_uri = $_REQUEST['method'];
        } else {
            // script------->service add unknow
            $caller_uri = 'unknow';
        }
        return $caller_uri;
    }

    /**
     * @brief 打印NOTICE日志
     *
     * @param $logtype 日志级别
     * @param $caller 调用者
     * @param $service ral中的服务
     * @param $method 方法
     * @param $remote_ip 连接的IP
     * @param $cost 总耗时
     * @param $connect 连接耗时
     * @param $read 读耗时
     * @param $write 写耗时
     * @param $trans 解析SQL耗时
     * @param $dbname 数据库实例名
     * @param $sql SQL
     */
    public static function notice($logtype, $caller, $service, $method, $remote_ip, $cost, $connect, $read, $write, $trans, $dbname, $sql, $query_count) {
        $log = array(
            'service' => $service,
            'method' => $method,
            'prot' => 'mysql',
            'remote_ip' => $remote_ip,
            'cost' => $cost,
            'connect' => $connect,
            'read' => $read,
            'write' => $write,
            'trans' => $trans,
            'dbname' => $dbname,
            'table' => self::getTable($sql),
            'sql'   => substr($sql,0,32),
            'pid'   => posix_getpid(),
            'query_count' => $query_count,
        );

        ral_set_log(RAL_LOG_MODULE, defined("MAIN_APP") ? MAIN_APP : '');
        ral_write_log($logtype, $caller, $log);
    }

    /**
     * @brief 打印WARNING日志
     *
     * @param $logtype 日志级别
     * @param $caller 调用者
     * @param $service ral中的服务
     * @param $method 方法
     * @param $remote_ip 连接的IP
     * @param $cost 总耗时
     * @param $connect 连接耗时
     * @param $read 读耗时
     * @param $write 写耗时
     * @param $trans 解析SQL耗时
     * @param $dbname 数据库实例名
     * @param $sql SQL
     * @param $err_no 错误号
     * @param $err_info 错误信息
     */
    public static function warning($logtype, $caller, $service, $method, $remote_ip, $cost, $connect, $read, $write, $trans, $dbname, $sql, $err_no, $err_info, $extra = null) {
        $log = array(
            'service' => $service,
            'method' => $method,
            'prot' => 'mysql',
            'remote_ip' => $remote_ip,
            'cost' => $cost,
            'connect' => $connect,
            'read' => $read,
            'write' => $write,
            'trans' => $trans,
            'dbname' => $dbname,
            'table' => self::getTable($sql),
            'sql' => $sql,
            'err_no' => $err_no,
            'ralCode' => $err_no,
            'err_info' => $err_info,
        );

        if (null !== $extra) {
            $log['extra'] = $extra;
        }
        ral_set_log(RAL_LOG_MODULE, defined("MAIN_APP") ? MAIN_APP : '');
        ral_write_log($logtype, $caller, $log);
    }

    /**
     * @brief 开始一次RPC过程
     *
     * @param $caller 调用者
     * @param $method 方法
     * @param $isConnect 是否为连接
     * @return 返回rpc过程信息
     */
    public static function startRpc($caller, $method, $isConnect = false) {
        if (function_exists('ral_create_span')) {
            $span = ral_create_span();
        } else {
            $span = array(
                'spanid' => 0,
                'content_tracing' => false,
            );
        }
        $info = array(
            'caller' => $caller,
            'method' => $method,
            'req_start_time' => gettimeofday(true),
            'reqStartTime' => gettimeofday(true),
            'spanid' => $span['spanid'],
            'content_tracing' => $span['content_tracing'],
            'is_connect' => $isConnect,
            'caller_uri' => self::getCallerUri(),
        );
        return $info;
    }

    /**
     * @brief 结束一次RPC并打印日志
     *
     * @2018-11-19 增加慢查询日志，单独记录到slowlog中
     *
     * @param $success RPC是否成功
     * @param $info RPC过程的信息
     * @param $conf 服务配置
     * @param $mysql mysqli对象
     * @return
     */
    public static function endRpc($success, $info, $conf, $mysql, $isSubmit = 0) {
        if ($success) {
            $logtype = RAL_LOG_SUM_SUCC;
        } elseif (isset($info['nth_retry']) && $info['nth_retry'] < $info['retry_num']) {
            $logtype = RAL_LOG_TALK_FAIL;
        } else {
            $logtype = RAL_LOG_SUM_FAIL;
        }

        if (isset($info['nth_retry'])) {
            $retry = $info['nth_retry'] . '/' . $info['retry_num'];
        } else {
            $retry = '0/0';
        }

        $cost = round((gettimeofday(true) - $info['req_start_time']) * 1000, 3);
        $is_connect = $info['is_connect'];

        $log = array(
            'spanid' => $info['spanid'],
            'service' => empty($conf['service']) ? $conf['dbname'] : $conf['service'],
            'method' => $info['method'],
            'conv' => 'mysql',
            'prot' => 'mysql',
            'retry' => $retry,
            'remote_ip' => $conf['host'] . ':' . $conf['port'],
            'req_start_time' => $info['req_start_time'],
            'reqStartTime' => $info['req_start_time'],
            'cost' => $cost,
            'talk' => $info['talk'] ? $info['talk'] / 1000 : $cost,
            'connect' => $is_connect ? $cost : 0,
            'write' => 0,
            'read' => $is_connect ? 0 : $cost,
            'dbname' => $conf['dbname'],
            'table' => self::getTable($info["sql"]),
            'err_no' => $mysql ? $mysql->errno : 0,
            'ralCode' => $mysql ? $mysql->errno : 0,
            'err_info' => ($mysql && $mysql->errno != 0) ? $mysql->error : 'OK',
        );

        if (isset($info['caller_uri'])) {
            $log['caller_uri'] = $info['caller_uri'];
        } else {
            $log['caller_uri'] = self::getCallerUri();
        }
        $log['is_submit'] = $isSubmit;

        if (isset($info['caller_uri'])) {
            $log['caller_uri'] = $info['caller_uri'];
        } else {
            $log['caller_uri'] = self::getCallerUri();
        }

        if (isset($info['is_submit'])) {
            $log['is_submit'] = $info['is_submit'];
        }

        if ($info['method'] == 'query') {
            if ($success) {
                if (isset($info['query_count'])) {
                    $log['query_count'] = $info['query_count'];
                } else {
                    $log['affected_rows'] = $mysql->affected_rows;
                }
            }
            if ($info['content_tracing']) {             # 进行内容采样，全量采样
                $log['sql'] = $info['sql'];
                if (isset($info['res_data']) && is_array($info['res_data'])) {
                    $log['res_data'] = serialize($info['res_data']);
                }
            } else {
                $log['sql'] = substr($info['sql'], 0, 512);
            }
            self::slowLog($log, $conf, $info["sql"]);   # 记录慢查询日志
        }

        ral_set_log(RAL_LOG_MODULE, defined("MAIN_APP") ? MAIN_APP : '');
        ral_write_log($logtype, $info['caller'], $log);
    }

    /**
     * 记录query慢查询日志
     *
     * @param array       $logArg
     * @param array       $conf
     * @param string      $sql
     */
    private static function slowLog($logArg, $conf, $sql) {
        $ratio = Bd_Conf::getConf("/db/cluster/{$logArg["service"]}/slow_log_ratio");
        if (false !== $ratio) {         # 阈值不能小于500
            $ratio = intval($ratio);
            $ratio = $ratio < 500 ? 500 : $ratio;
        } else {
            $ratio = 1000;
        }
        if ($logArg["talk"] < $ratio) {
            return;
        }
        $arg = array(
            "uri"     => strval($_SERVER['REQUEST_URI']),
            'service' => $logArg['service'],
            'retry'   => $logArg['retry'],
            'connect' => $logArg['connect'],
            'talk'    => $logArg['talk'],
            'cost'    => $logArg['cost'],
            'dbname'  => $logArg['dbname'],
            'table'   => $logArg['table'],
            'app'     => defined("MAIN_APP") ? MAIN_APP : '',
            'sql'     => substr($sql, 0, 3072),
        );
        Bd_AppEnv::setCurrApp("slowlog");
        Bd_Log::notice("", 0, $arg);
        Bd_AppEnv::setCurrApp();
    }

    /**
     * 获取SQL表名
     *
     * @param string      $sql
     * @return string
     */
    private static function getTable($sql) {
        $table = "";
        if (preg_match("/ (from|FROM)\s+([a-zA-Z0-9]+) /", $sql, $matches)) {
            if (!empty($matches[2])) {
                $table = $matches[2];
                return "" . $table;
            }
        }
        if (preg_match("/ (tbl[a-zA-Z0-9]+) /", $sql, $matches)) {
            if (!empty($matches[1])) {
                $table = $matches[1];
                return "" . $table;
            }
        }
        return $table;
    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
