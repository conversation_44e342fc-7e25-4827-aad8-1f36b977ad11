<?php
/***************************************************************************
 *
 * Copyright (c) 2010 Baidu.com, Inc. All Rights Reserved
 * $Id: Log.php,v 1.3 2011/01/21 12:05:28 liushi Exp $
 * $Id: Log.php,v 1.4 2011/03/21 12:05:28 chenqiuwu Inf $
 *
 **************************************************************************/

/**
 * @file Log.php
 * @desc wiki详见 http://man.baidu.com/inf/odp/lib/log/
 * <AUTHOR>
 * @date 2009/08/04 10:31:44
 * @version $Revision: 1.2 $
 * @brief class for logging
 *
 **/


/*********************************************
 * format string 格式，取自lighttpd文档
 * 前面标记 - 代表ODP的Log库不支持
 * 行为不一致的，均有注释说明
 * 后面的 === 之后的，是ODP Log库扩展的功能
 ====== ================================
 Option Description
 ====== ================================
 %%     a percent sign
 %h     name or address of remote-host
 -%l    ident name (not supported)
 -%u    authenticated user
 %t     timestamp of the end-time of the request //param, show current time, param specifies strftime format
 -%r    request-line
 -%s    status code
 -%b    bytes sent for the body
 %i     HTTP-header field //param
 %a     remote address
 %A     local address
 -%B    same as %b
 %C     cookie field (not supported) //param
 %D     time used in ms
 %e     environment variable //param
 %f     physical filename
 %H     request protocol (HTTP/1.0, ...)
 %m     request method (GET, POST, ...)
 -%n    (not supported)
 -%o    `response header`_
 %p     server port
 -%P    (not supported)
 %q     query string
 %T     time used in seconds //support param, s, ms, us, default to s
 %U     request URL
 %v     server-name
 %V     HTTP request host name
 -%X    connection status
 -%I    bytes incomming
 -%O    bytes outgoing
 ====== ================================
 %L     Log level
 %N     line number
 %E     err_no
 %l     log_id
 %u     user
 %S     strArray, support param, takes a key and removes the key from %S
 %M     error message
 %x     ODP extension, supports various param, like log_level, line_number etc.

 currently supported param for %x:
 log_level, line, class, function, err_no, err_msg, log_id, app, function_param, argv, encoded_str_array

 in %x, prepend u_ to key to urlencode before its value
*************************************************/
class Bd_Log {

    const LOG_LEVEL_FATAL   = 0x01;
    const LOG_LEVEL_WARNING = 0x02;
    const LOG_LEVEL_NOTICE  = 0x04;
    const LOG_LEVEL_TRACE   = 0x08;
    const LOG_LEVEL_DEBUG   = 0x10;

    const LOG_OMP_AND_NORMAL = 0; // 打印 k-v log， 包括 .log 和 .log.new 日志
    const LOG_OMP_ONLY       = 1; // 只打印 k-v log的 .log.new 日志
    const LOG_NORMAL_ONLY    = 2; // 只打印 k-v log 的 .log 日志
    const LOG_JSON_ONLY      = 3; // 只打印 json 格式日志

    public static $arrLogLevels = array(
        self::LOG_LEVEL_FATAL   => 'FATAL',
        self::LOG_LEVEL_WARNING => 'WARNING',
        self::LOG_LEVEL_NOTICE  => 'NOTICE',
        self::LOG_LEVEL_TRACE   => 'TRACE',
        self::LOG_LEVEL_DEBUG   => 'DEBUG',
    );
    public static $current_instance;

    protected $intLevel;
    protected $strLogFile;
    protected $bolAutoRotate;
    protected $addNotice   = array();
    protected $pbAddNotice = array();
    protected $objWriter   = null;

    protected $confPblog   = array();

    private static $arrInstance = array();
    private $current_args;
    public $current_file;
    public $current_line;

    private static $intWritePbLog = null;
    private static $bolIsOmp    = null;
    private static $strLogPath  = null;
    private static $strDataPath = null;

    private static $lastLogs    = array();
    private static $lastLogSize = 0;
    private static $logWriters  = array();

    const DEFAULT_FORMAT     = '%L: %t [%f:%N] errno[%E] logId[%l] uri[%U] user[%u] refer[%{referer}i] cookie[%{cookie}i] %S %M';
    const DEFAULT_FORMAT_STD = '%L: %{%m-%d %H:%M:%S}t %{app}x * %{pid}x [logid=%l requestId=%r spanid=%{spanid}x force_sampling=%{force_sampling}x filename=%f lineno=%N errno=%{err_no}x %{encoded_str_array}x errmsg=%{u_err_msg}x]';
    const DEFAULT_FORMAT_STD_DETAIL = '%L: %{%m-%d %H:%M:%S}t %{app}x * %{pid}x [logid=%l filename=%f lineno=%N errno=%{err_no}x %{encoded_str_array}x errmsg=%{u_err_msg}x cookie=%{u_cookie}x]';
    // TRACING 打印APP日志格式  增加 module ,spanid ,force_sampling
    const FORMAT_TRACING_FROM = 'logId[%l]';
    const FORMAT_TRACING_TO   = 'logId[%l] requestId[%r] module[%{app}x] spanid[%{spanid}x] force_sampling[%{force_sampling}x]';

    /**
     * __construct
     *
     * @param mixed $arrLogConfig
     * @access private
     * @return void
     */
    private function __construct($arrLogConfig) {
        $this->intLevel      = $arrLogConfig['level'];
        $this->bolAutoRotate = $arrLogConfig['auto_rotate'];
        $this->strLogFile    = $arrLogConfig['log_file'];
        $this->strFormat     = $arrLogConfig['format'];
        $this->strFormatWF   = $arrLogConfig['format_wf'];
        $this->confPblog     = $arrLogConfig['confPblog'];

        $strLogBuf       = $arrLogConfig['log_buf'];
        //$flagBuf = 'cli' == PHP_SAPI ? Bd_Log_File::WRITER_NOBUF : Bd_Log_File::WRITER_BUF ;      // cli采用nobuf
        $this->objWriter = new Bd_Log_File($arrLogConfig['log_file'] , $strLogBuf);
    }

    /**
     * @brief ODP环境下日志的前缀为AppName，非ODP环境需要配置指定前缀
     *
     * @return  public static function
     * @retval
     * @see
     * @note
     * <AUTHOR>
     * @date 2012/07/31 17:10:32
     **/
    public static function getLogPrefix() {
        if (defined('IS_ODP') && IS_ODP == true) {
            return Bd_AppEnv::getCurrApp();
        }
        if (defined('MODULE')) {
            return MODULE;
        } else {
            return 'unknow';
        }
    }

    /**
     * @brief 日志打印的根目录
     * @return  public static function
     * @retval
     * @see
     * @note
     * <AUTHOR>
     * @date 2012/07/31 17:15:59
     **/
    public static function getLogPath() {
        if (defined('IS_ODP') && IS_ODP == true) {
            return LOG_PATH;
        }
        if (self::$strLogPath === null) {
            $ret = Bd_Conf::getConf('/log/log_path');
            if (false !== $ret) {
                self::$strLogPath = $ret;
            } else {
                self::$strLogPath = './';
            }
        }
        return self::$strLogPath;
    }

    /**
     * @brief 日志库依赖的数据文件根目录
     * @return  public static function
     * @retval
     * @see
     * @note
     * <AUTHOR>
     * @date 2012/07/31 17:16:30
     **/
    public static function getDataPath() {
        if (defined('IS_ODP') && IS_ODP == true) {
            return DATA_PATH;
        }
        if (self::$strDataPath === null) {
            $ret = Bd_Conf::getConf('/log/data_path');
            if (false !== $ret) {
                self::$strDataPath = $ret;
            } else {
                self::$strDataPath = "./";
            }
        }
        return self::$strDataPath;
    }

    /**
     * isOMP
     * @brief 是否打印omp日志  ，默认会打印 omp日志和普通日志
     * @static
     * @access public
     * @return int
     */
    public static function isOMP() {
        if(self::$bolIsOmp !== null) {
            return self::$bolIsOmp;
        }

        // 优先读取app conf 下的 log.conf 配置
        $retApp = Bd_Conf::getAppConf('/log/is_omp', self::getLogPrefix());
        if(false !== $retApp && $retApp <= self::LOG_JSON_ONLY && $retApp >= self::LOG_OMP_AND_NORMAL) {
            self::$bolIsOmp = intval($retApp);
            return self::$bolIsOmp;
        }

        // 如果 app conf 下未设置，那么读取 全局conf 下的 log.conf
        $ret = Bd_Conf::getConf('/log/is_omp');
        if(false !== $ret && $ret <= self::LOG_JSON_ONLY && $ret >= self::LOG_OMP_AND_NORMAL) {
            self::$bolIsOmp = intval($ret);
            return self::$bolIsOmp;
        }

        // 默认的 OMP 类型
        self::$bolIsOmp = self::LOG_OMP_AND_NORMAL;
        return self::$bolIsOmp;
    }

    /**
     * getInstance
     * 获取指定App的log对象，默认为当前App
     * @param mixed $app
     * @param mixed $logType
     * @static
     * @access public
     * @return void
     */
    public static function getInstance($app = null,$logType = null) {
        if (empty($app)) {
            $app = self::getLogPrefix();
        }

        if (empty(self::$arrInstance[$app])) {
            $g_log_conf   = Bd_Conf::getConf('/log/');
            $app_log_conf = Bd_Conf::getAppConf('log', $app);

            // app配置优先
            if (is_array($app_log_conf)) {
                $g_log_conf = array_merge($g_log_conf, $app_log_conf);
            }

            // 生成路径
            $logPath = self::getLogPath();
            if ($g_log_conf['use_sub_dir'] == "1") {
                if (!self::bdLazyIsDir($logPath."/$app")) {
                    @mkdir($logPath."/$app");
                }
                $log_file = $logPath."/$app/$app.log";
            } else {
                $log_file = $logPath."/$app.log";
            }

            // 不知道干嘛的
            if ($logType == "stf") {
                $logDir   = dirname($log_file)."/".$logType."/";
                if (!file_exists($logDir)) {
                    @mkdir($logDir);
                }
                $log_file = $logDir.$app."_".$logType.".log";
            }

            //get log format
            if (isset($g_log_conf['format'])) {
                $format = $g_log_conf['format'];
            } else {
                $format = self::DEFAULT_FORMAT;
            }

            if (isset($g_log_conf['format_wf'])) {
                $format_wf = $g_log_conf['format_wf'];
            } else {
                $format_wf = $format;
            }

            if (isset($g_log_conf['log_buf'])) {
                $log_buf = $g_log_conf['log_buf'];
            } else {
                // default nobuf
                $log_buf = Bd_Log_File::WRITER_NOBUF;
            }

            $format    = str_replace(self::FORMAT_TRACING_FROM, self::FORMAT_TRACING_TO, $format);
            $format_wf = str_replace(self::FORMAT_TRACING_FROM, self::FORMAT_TRACING_TO, $format_wf);

            // get pblog conf
            $confPb    = array();
            $confPb['pb_autolog']      = intval($g_log_conf['b2log']['pb_autolog']);
            $confPb['pb_proto_name']   = $g_log_conf['b2log']['pb_proto_name'];
            $confPb['pb_message_name'] = $g_log_conf['b2log']['pb_message_name'];
            $confPb['pb_log_dir']      = self::getLogPath() . '/' . $app ;

            $log_conf  = array(
                'level'       => intval($g_log_conf['level']),
                'auto_rotate' => false,
                'log_file'    => $log_file,
                'format'      => $format,
                'format_wf'   => $format_wf,
                'confPblog'   => $confPb,
                'log_buf'     => $log_buf,
            );
            self::$arrInstance[$app] = new Bd_Log($log_conf);
        }
        return self::$arrInstance[$app];
    }

    /**
     * debug
     *
     * @param mixed $str
     * @param int $errno
     * @param mixed $arrArgs
     * @param int $depth
     * @static
     * @access public
     * @return void
     */
    public static function debug($str, $errno = 0, $arrArgs = null, $depth = 0) {
        if(self::isOMP() == self::LOG_JSON_ONLY) {
            // JSON 格式
            return  self::getInstance()->writeLog(self::LOG_LEVEL_DEBUG, $str, $errno, $arrArgs, $depth + 1, ".json");
        }

        // k-v 格式
        if (self::isOMP() == self::LOG_OMP_AND_NORMAL || self::isOMP() == self::LOG_OMP_ONLY) {
            $ret = self::getInstance()->writeLog(self::LOG_LEVEL_DEBUG, $str, $errno, $arrArgs, $depth + 1, '.new', self::DEFAULT_FORMAT_STD);
        }
        if (self::isOMP() == self::LOG_OMP_AND_NORMAL || self::isOMP() == self::LOG_NORMAL_ONLY) {
            $ret = self::getInstance()->writeLog(self::LOG_LEVEL_DEBUG, $str, $errno, $arrArgs, $depth + 1);
        }
        return $ret;
    }

    /**
     * trace
     *
     * @param mixed $str
     * @param int $errno
     * @param mixed $arrArgs
     * @param int $depth
     * @static
     * @access public
     * @return void
     */
    public static function trace($str, $errno = 0, $arrArgs = null, $depth = 0) {
        if(self::isOMP() == self::LOG_JSON_ONLY) {
            return  self::getInstance()->writeLog(self::LOG_LEVEL_TRACE, $str, $errno, $arrArgs, $depth + 1, ".json");
        }

        if (self::isOMP() == self::LOG_OMP_AND_NORMAL || self::isOMP() == self::LOG_OMP_ONLY) {
            $ret = self::getInstance()->writeLog(self::LOG_LEVEL_TRACE, $str, $errno, $arrArgs, $depth + 1, '.new', self::DEFAULT_FORMAT_STD);
        }
        if (self::isOMP() == self::LOG_OMP_AND_NORMAL || self::isOMP() == self::LOG_NORMAL_ONLY) {
            $ret = self::getInstance()->writeLog(self::LOG_LEVEL_TRACE, $str, $errno, $arrArgs, $depth + 1);
        }
        return $ret;
    }

    /**
     * notice
     *
     * @param mixed $str
     * @param int $errno
     * @param mixed $arrArgs
     * @param int $depth
     * @static
     * @access public
     * @return void
     */
    public static function notice($str, $errno = 0, $arrArgs = null, $depth = 0) {
        if(self::isOMP() == self::LOG_JSON_ONLY) {
            return self::getInstance()->writeLog(self::LOG_LEVEL_NOTICE, $str, $errno, $arrArgs, $depth + 1, ".json");
        }

        if (self::isOMP() == self::LOG_OMP_AND_NORMAL || self::isOMP() == self::LOG_OMP_ONLY) {
            $strFormat     = self::DEFAULT_FORMAT_STD;
            if ((boolean)Bd_Conf::getConf("/log/OMP/cookie")) {
                $strFormat = self::DEFAULT_FORMAT_STD_DETAIL;
            }
            $ret = self::getInstance()->writeLog(self::LOG_LEVEL_NOTICE, $str, $errno, $arrArgs, $depth + 1, '.new', $strFormat);
        }
        if (self::isOMP() == self::LOG_OMP_AND_NORMAL || self::isOMP() == self::LOG_NORMAL_ONLY) {
            $ret = self::getInstance()->writeLog(self::LOG_LEVEL_NOTICE, $str, $errno, $arrArgs, $depth + 1);
        }
    }

    /**
     * warning
     *
     * @param mixed $str
     * @param int $errno
     * @param mixed $arrArgs
     * @param int $depth
     * @static
     * @access public
     * @return void
     */
    public static function warning($str, $errno = 0, $arrArgs = null, $depth = 0) {
        if(self::isOMP() == self::LOG_JSON_ONLY) {
            return self::getInstance()->writeLog(self::LOG_LEVEL_WARNING, $str, $errno, $arrArgs, $depth + 1, ".json");
        }

        if (self::isOMP() == self::LOG_OMP_AND_NORMAL || self::isOMP() == self::LOG_OMP_ONLY) {
            $ret = self::getInstance()->writeLog(self::LOG_LEVEL_WARNING, $str, $errno, $arrArgs, $depth + 1, '.new', self::DEFAULT_FORMAT_STD);
        }
        if (self::isOMP() == self::LOG_OMP_AND_NORMAL || self::isOMP() == self::LOG_NORMAL_ONLY) {
            $ret = self::getInstance()->writeLog(self::LOG_LEVEL_WARNING, $str, $errno, $arrArgs, $depth + 1);
        }
    }

    /**
     * fatal
     *
     * @param mixed $str
     * @param int $errno
     * @param mixed $arrArgs
     * @param int $depth
     * @static
     * @access public
     * @return void
     */
    public static function fatal($str, $errno = 0, $arrArgs = null, $depth = 0) {
        if(self::isOMP() == self::LOG_JSON_ONLY) {
            return self::getInstance()->writeLog(self::LOG_LEVEL_FATAL, $str, $errno, $arrArgs, $depth + 1, ".json");
        }

        if (self::isOMP() == self::LOG_OMP_AND_NORMAL || self::isOMP() == self::LOG_OMP_ONLY) {
            $ret = self::getInstance()->writeLog(self::LOG_LEVEL_FATAL, $str, $errno, $arrArgs, $depth + 1, '.new', self::DEFAULT_FORMAT_STD);
        }
        if (self::isOMP() == self::LOG_OMP_AND_NORMAL || self::isOMP() == self::LOG_NORMAL_ONLY) {
            $ret = self::getInstance()->writeLog(self::LOG_LEVEL_FATAL, $str, $errno, $arrArgs, $depth + 1);
        }
    }

    /**
     * @param $level
     * @param $content
     * @param null $file
     * @return int 0 is ok, and other is error
     */
    public static function customlog($level, $content, $file = null) {
        // todo log
        file_put_contents($file, $content . PHP_EOL, FILE_APPEND);
        return 0;
    }

    /**
     * addNotice
     *
     * @param mixed $key
     * @param mixed $value
     * @static
     * @access public
     * @return void
     */
    public static function addNotice($key, $value) {
        $log  = self::getInstance();
        if (!isset($value)) {
            $value = $key;
            $key   = '@';
        }
        $info = is_array($value) ? strtr(strtr(var_export($value, true),
        array("  array (\n"=>'{', "array (\n"=>'{', ' => '=> ':',",\n"=> ',',)),
        array('{  '=> '{', ":\n{"=>':{', '  ),  ' => '},', '),' => '},', ',)'=>'}', ',  '=>',',))
        : $value;
        $log->addNotice[$key] = $info;
    }

    public static function clearNoticeFields() {
        self::getInstance()->addNotice = [];
    }

    /**
     * @desc
     *  查看日志请用 codex 命令
     *  b2log配置项见 http://project.baidu.com/b2log/doc/b2log-conf.html
     *  由于so不同暂时 php , hhvm 用两套代码
     *  todo = 2套代码合并 ， hhvm , php 均使用定时加载文件形式 reload 配置
     * @param unknown $protoName
     * @param unknown $msgName
     * @param string $arrArgs
     * @return boolean
     */
    public static function pbNotice($protoName = null, $msgName = null) {
        $confPb = self::getInstance()->confPblog;
        if (empty($protoName)) {
            $protoName = $confPb['pb_proto_name'];
        }
        if (empty($msgName)) {
            $msgName = $confPb['pb_message_name'];
        }
        self::pbAddNotice($protoName, 'logid', LOG_ID);

        // 跟bd_protobuf配套使用
        $objBdProtobuf = new Bd_Protobuf();
        $protoFile = $objBdProtobuf->getProtoFile($protoName , self::getLogPrefix());

        // php 多进程锁 , hhvm 多线程锁
        if( 1 == $_ENV['HHVM'] ){
            $arrConf = array('b2log_dir' => $confPb['pb_log_dir'], 'multi_thread' => "1",);
            $strConf = json_encode($arrConf);

            if( ! is_callable('odp_b2log_write_kv')){
                error_log('stlog pb fail is_callable fail');
                return false;
            }

            if( ! odp_b2log_write_kv( $strConf , $protoFile , $msgName , empty(self::getInstance()->pbAddNotice[$protoName]) ? array() : self::getInstance()->pbAddNotice[$protoName] ) ){
                error_log('odp_b2log_write_kv fail');
                return false;
            }

        }
        else{
            $arrConf = array('b2log_dir' => $confPb['pb_log_dir'], 'multi_process' => "1",);
            $strConf = json_encode($arrConf);

            if( ! odp_b2log_init( $strConf, $protoFile , $msgName) ){
                error_log('odp_b2log_init fail');
                return false;
            }

            // 使用 apc做 conf过期判断
            if( $objBdProtobuf->checkProtoNeedReload($protoName , self::getLogPrefix() )
                    || ( is_callable('apc_fetch')
                            && $strConf != apc_fetch($protoName)
                    )
            ){
                if( is_callable('apc_store')){
                    apc_store($protoName, $strConf , 86400);
                }

                if( ! odp_b2log_reload( $strConf , $protoFile , $msgName )){
                    error_log('odp_b2log_reload fail');
                    return false;
                }
            }

            // 使用 notice格式自动打印pb日志，兼容一些产品线应用，可以被 addNotice覆盖
            if($confPb['pb_autolog'])
            {
                self::getInstance()->setCurLog(self::LOG_LEVEL_NOTICE , '');
                self::getInstance()->pbParseFormat($protoName, self::getInstance()->getFormat(self::LOG_LEVEL_NOTICE));
            }

            foreach (self::getInstance()->pbAddNotice[$protoName] as $key => $value){
                odp_b2log_add_value( $protoFile , $key , $value );
            }

            if( ! odp_b2log_write( $protoFile ) ){
                error_log('odp_b2log_write fail');
                return false;
            }
        }

        return true;
    }

    /**
     * @desc
     * @param unknown $protoName
     * @param unknown $key
     * @param unknown $value
     */
    public static function pbAddNotice( $protoName , $key , $value )
    {
        self::getInstance()->pbAddNotice[$protoName][$key] = $value;
    }

    /**
     * @desc pb用
     * @param unknown $format
     */
    public function pbParseFormat($protoName , $format)
    {
        $matches = array();
        $regex = '/%(?:{([^}]*)})?(.)/';
        preg_match_all($regex, $format, $matches);
        $prelim = array();
        $action = array();
        $prelim_done = array();

        $len = count($matches[0]);
        for ($i = 0; $i < $len; $i++) {
            $code = $matches[2][$i];
            $param = $matches[1][$i];
            switch ($code) {
            case 'h':
                $this->pbAddNotice($protoName , 'CLIENT_IP', defined('CLIENT_IP') ? CLIENT_IP : Bd_Log::getClientIp());
                break;

            case 't':
                $this->pbAddNotice($protoName , 'TIME_STR', $param == '' ? strftime('%y-%m-%d %H:%M:%S') : strftime(var_export($param, true)));
                break;

            case 'i':
                $key = 'HTTP_' . str_replace('-', '_', strtoupper($param));

                $this->pbAddNotice($protoName ,$key, isset($_SERVER[$key]) ? $_SERVER[$key] : '');
                break;
            case 'a':
                $this->pbAddNotice($protoName ,'CLIENT_IP', defined('CLIENT_IP') ? CLIENT_IP : Bd_Log::getClientIp());
                break;
            case 'A':
                $this->pbAddNotice($protoName ,'SERVER_ADDR', isset($_SERVER['SERVER_ADDR']) ? $_SERVER['SERVER_ADDR'] : '');
                break;
            case 'C':
                if ($param == '') {
                    $this->pbAddNotice($protoName ,'SERVER_ADDR', isset($_SERVER['HTTP_COOKIE']) ? $_SERVER['HTTP_COOKIE'] : '');
                } else {
                    $param = var_export($param, true);
                    $this->pbAddNotice($protoName ,$param, isset($_COOKIE[$param]) ? $_COOKIE[$param] : '');
                }
                break;
            case 'D':
                $this->pbAddNotice($protoName ,'REQUEST_TIME_MS', defined('REQUEST_TIME_US') ? microtime(true) * 1000 - REQUEST_TIME_US / 1000 : '');
                break;
            case 'e':
                $param = var_export($param, true);
                $this->pbAddNotice($protoName ,$param, getenv($param) !== false ? getenv($param) : '');
                break;
            case 'f':
                $this->pbAddNotice($protoName ,'FILE_NAME', Bd_Log::$current_instance->current_file);
                break;
            case 'H':
                $this->pbAddNotice($protoName ,'SERVER_PROTOCOL', isset($_SERVER['SERVER_PROTOCOL']) ? $_SERVER['SERVER_PROTOCOL'] : '');
                break;
            case 'm':
                $this->pbAddNotice($protoName ,'REQUEST_METHOD', isset($_SERVER['REQUEST_METHOD']) ? $_SERVER['REQUEST_METHOD'] : '');
                break;
            case 'p':
                $this->pbAddNotice($protoName ,'SERVER_PORT', isset($_SERVER['SERVER_PORT']) ? $_SERVER['SERVER_PORT'] : '');
                break;
            case 'q':
                $this->pbAddNotice($protoName ,'QUERY_STRING', isset($_SERVER['QUERY_STRING']) ? $_SERVER['QUERY_STRING'] : '');
                break;
            case 'T':
                switch ($param) {
                case 'ms':
                    $this->pbAddNotice($protoName ,'REQUEST_TIME_MS', defined('REQUEST_TIME_US') ? microtime(true) * 1000 - REQUEST_TIME_US / 1000 : '');
                    break;
                case 'us':
                    $this->pbAddNotice($protoName ,'REQUEST_TIME_US', defined('REQUEST_TIME_US') ? microtime(true) * 1000000 - REQUEST_TIME_US : '');
                    break;
                default:
                    $this->pbAddNotice($protoName ,'REQUEST_TIME_S', defined('REQUEST_TIME_US') ? microtime(true) - REQUEST_TIME_US / 1000000 : '');
                }
                break;
                case 'U':
                    $this->pbAddNotice($protoName ,'REQUEST_URI', isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : '');
                    break;
                case 'v':
                    $this->pbAddNotice($protoName ,'HOSTNAME', isset($_SERVER['HOSTNAME']) ? $_SERVER['HOSTNAME'] : '');
                    break;
                case 'V':
                    $this->pbAddNotice($protoName ,'HTTP_HOST', isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : '');
                    break;

                case 'L':
                    $this->pbAddNotice($protoName ,'LOG_LEVEL', Bd_Log::$current_instance->current_log_level);
                    break;
                case 'N':
                    $this->pbAddNotice($protoName ,'CURRENT_LINE', Bd_Log::$current_instance->current_line);
                    break;
                case 'E':
                    $this->pbAddNotice($protoName ,'CURRENT_ERR_NO', Bd_Log::$current_instance->current_err_no);
                    break;
                case 'l':
                    $this->pbAddNotice($protoName ,'LOG_ID', Bd_Log::genLogID());
                    break;
                case 'u':
                    if (!isset($prelim_done['user'])) {
                        $prelim[] = "\$____user____ = Bd_Passport::getUserInfoFromCookie();";
                        $prelim_done['user'] = true;
                    }
                    $this->pbAddNotice($protoName ,'USER', (defined('CLIENT_IP') ? CLIENT_IP : Bd_Log::getClientIp()) . ' ' . $____user____['uid'] . ' ' . $____user____['uname']);
                    break;
                case 'S':
                    if ($param == '') {
                        $this->pbSetArgs($protoName);
                    } else {
                        $param_name = var_export($param, true);
                        if (!isset($prelim_done['S_' . $param_name])) {
                            $prelim[] = "if (isset(Bd_Log::\$current_instance->current_args[{$param_name}])) {\n                                \$____curargs____[{$param_name}] = Bd_Log::\$current_instance->current_args[{$param_name}];\n                                unset(Bd_Log::\$current_instance->current_args[{$param_name}]);\n                            } else \$____curargs____[{$param_name}] = '';";
                            $prelim_done['S_' . $param_name] = true;
                        }
                        $this->pbAddNotice($protoName ,$param, $____curargs____[$param_name]);
                    }
                    break;
                case 'M':
                    $this->pbAddNotice($protoName ,'CURRENT_ERR_MSG', Bd_Log::$current_instance->current_err_msg);
                    break;
                case 'x':
                    $need_urlencode = false;
                    if (substr($param, 0, 2) == 'u_') {
                        $need_urlencode = true;
                        $param = substr($param, 2);
                    }
                    $value = '';
                    switch ($param) {
                    case 'log_level':
                    case 'line':
                    case 'class':
                    case 'function':
                    case 'err_no':
                    case 'err_msg':
                        $value = Bd_Log::$current_instance->current_ . $param;
                        break;
                    case 'log_id':
                        $value = Bd_Log::genLogID();
                        break;
                    case 'app':
                        $value = Bd_Log::getLogPrefix();
                        break;
                    case 'function_param':
                        $value = Bd_Log::flattenArgs(Bd_Log::$current_instance->current_function_param);
                        break;
                    case 'argv':
                        $value = isset($GLOBALS['argv']) ? Bd_Log::flattenArgs($GLOBALS['argv']) : '';
                        break;
                    case 'pid':
                        $value = posix_getpid();
                        break;
                    case 'encoded_str_array':
                        $this->pbSetArgs($protoName , true);
                        break;
                    case 'cookie':
                        $value = isset($_SERVER['HTTP_COOKIE']) ? $_SERVER['HTTP_COOKIE'] : '';
                        break;
                    default:
                        $value = "''";
                    }
                    if ($need_urlencode) {
                        $this->pbAddNotice($protoName ,$param, rawurlencode($value));
                    } else {
                        $this->pbAddNotice($protoName ,$param, $value);
                    }
                    break;
                    case '%':
                        $action[] = "'%'";
                        break;
                    default:
                        $action[] = "''";
            }
        }
        return true;
    }

    /**
     * @desc
     * @param string $bolStd
     */
    public function pbSetArgs($protoName , $bolNeedUrlencode = false) {
        foreach ($this->current_args as $k => &$v) {
            if (is_array($v)
                || is_object($v)
            ){
                $v = serialize($v);
            }
            if(! $bolNeedUrlencode){
                $this->pbAddNotice($protoName, strval($k), strval($v));
            }
            else{
                $this->pbAddNotice($protoName , strval(rawurlencode($k)) , strval(rawurlencode($v)) );
            }
        }
    }

    /**
     * @desc 华佗平台消息追踪用
     */
    public static function getForceSampling() {
        if (defined('FORCE_SAMPLING')) {
            return FORCE_SAMPLING;
        }

        if (!empty($_SERVER['HTTP_X_BD_FORCE_SAMPLING'])
            && intval(trim($_SERVER['HTTP_X_BD_FORCE_SAMPLING'])) === 1) {
            define('FORCE_SAMPLING', trim($_SERVER['HTTP_X_BD_FORCE_SAMPLING']));
        } else {
            define('FORCE_SAMPLING', 0);
        }
        return FORCE_SAMPLING;
    }

    /**
     * @desc 华佗平台消息追踪用
     */
    public static function getParentSpanid() {
        if (defined('SPANID')) {
            return SPANID;
        }

        if (!empty($_SERVER['HTTP_X_BD_SPANID'])
            && trim($_SERVER['HTTP_X_BD_SPANID'])) {
            define('SPANID', trim($_SERVER['HTTP_X_BD_SPANID']));
        } else {
            define('SPANID', '0');
        }
        return SPANID;
    }

    // 生成 requestId
    public static function genRequestID() {
        if (defined('REQUEST_ID')) {
            return REQUEST_ID;
        }

        if(isset($_SERVER['HTTP_UBER_TRACE_ID'])) {
            define('REQUEST_ID', trim($_SERVER['HTTP_UBER_TRACE_ID']));
        } else {
            // gen a requestId
            $traceId = microtime(true) * 10000 .rand(10000, 99999);
            $requestId = sprintf("%x:%x:%x:%x", $traceId, $traceId, 0, 1);
            define('REQUEST_ID', $requestId);
        }
        return REQUEST_ID;
    }

    // 生成logid
    public static function genLogID() {
        if (defined('LOG_ID')) {
            return LOG_ID;
        }

        if (!empty($_SERVER['HTTP_X_BD_LOGID']) && intval(trim($_SERVER['HTTP_X_BD_LOGID'])) !== 0) {
            define('LOG_ID', trim($_SERVER['HTTP_X_BD_LOGID']));
        } elseif (isset($_REQUEST['logid']) && intval($_REQUEST['logid']) !== 0) {
            define('LOG_ID', intval($_REQUEST['logid']));
        } else {
            $arr   = gettimeofday();
            $logId = ((($arr['sec']*100000 + $arr['usec']/10) & 0x7FFFFFFF) | 0x80000000);
            define('LOG_ID', $logId);
        }
        return LOG_ID;
    }

    // 获取客户端ip
    public static function getClientIp() {
        return Bd_Ip::getClientIp();
    }

    /**
     * @desc 设置某些log信息
     */
    private function setCurLog($intLevel, $str, $errno = 0, $arrArgs = null, $depth = 0, $filename_suffix = '', $log_format = null) {
        //assign data required
        $this->current_log_level = self::$arrLogLevels[$intLevel];

        //build array for use as strargs
        $_arr_args   = false;
        $_add_notice = false;
        if (is_array($arrArgs) && count($arrArgs) > 0) {
            $_arr_args = true;
        }
        if (!empty($this->addNotice)) {
            $_add_notice = true;
            $this->addNotice['optime'] = gettimeofday(true);
        }

        if ($_arr_args && $_add_notice) { //both are defined, merge
            $this->current_args = $arrArgs + $this->addNotice;
        } elseif (!$_arr_args && $_add_notice) { //only add notice
            $this->current_args = $this->addNotice;
        } elseif ($_arr_args && !$_add_notice) { //only arr args
            $this->current_args = $arrArgs;
        } else { //empty
            $this->current_args = array();
        }

        $this->current_err_no = $errno;
        $this->current_err_msg = $str;

        // 不调用 args，减少内存消耗
        if (defined('DEBUG_BACKTRACE_IGNORE_ARGS')) {
            $trace = debug_backtrace( DEBUG_BACKTRACE_IGNORE_ARGS , $depth + 2);
        } else {
            $trace = debug_backtrace();
        }

        $depth2 = $depth + 1;
        if ($depth >= count($trace)) {
            $depth  = count($trace) - 1;
            $depth2 = $depth;
        }

        $this->current_file = isset( $trace[$depth]['file'] )
            ? $trace[$depth]['file'] : "" ;
        $this->current_line = isset( $trace[$depth]['line'] )
            ? $trace[$depth]['line'] : "";
        $this->current_function = isset( $trace[$depth2]['function'] )
            ? $trace[$depth2]['function'] : "";
        $this->current_class = isset( $trace[$depth2]['class'] )
            ? $trace[$depth2]['class'] : "" ;
        $this->current_function_param = isset( $trace[$depth2]['args'] )
            ? $trace[$depth2]['args'] : "";

        self::$current_instance = $this;
    }

    private function writeLog($intLevel, $str, $errno = 0, $arrArgs = null, $depth = 0, $filename_suffix = '', $log_format = null) {
        if ($intLevel > $this->intLevel
            || !isset(self::$arrLogLevels[$intLevel])) {
            return false;
        }

        //log file name
        $strLogFile = $this->strLogFile;
        if (($intLevel & self::LOG_LEVEL_WARNING)
            || ($intLevel & self::LOG_LEVEL_FATAL)) {
            $strLogFile .= '.wf';
        }

        $strLogFile .= $filename_suffix;

        //增加了一层函数调用，depth++
        $this->setCurLog($intLevel, $str, $errno, $arrArgs, $depth + 1, $filename_suffix, $log_format);

        if(self::$bolIsOmp == self::LOG_JSON_ONLY) {
            $now = DateTime::createFromFormat('U.u', number_format(microtime(true), 6, '.', ''));
            $logFields = [
                "level" => self::$arrLogLevels[$intLevel],
                "time" =>  $now->format("Y-m-d H:i:s.u"), // 当前时间，精确到微妙
                "file" => $this->current_file . ":" . $this->current_line,
                "msg" => $str,
                "logId" => Bd_Log::genLogID(),
                "requestId" => Bd_Log::genRequestID(),
                "module" => Bd_Log::getLogPrefix(),
                "uri" => isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : '',
                "errno" => $errno,
                "refer" => isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '',
                "cookie" => isset($_SERVER['HTTP_COOKIE']) ? $_SERVER['HTTP_COOKIE'] : '',
            ];
            $str = json_encode($logFields + $this->current_args, JSON_UNESCAPED_SLASHES|JSON_UNESCAPED_UNICODE);
        } else {
            // k-v 格式log 拼接，按照预先设定的 format 拼接
            if ($log_format === null) {
                $format = $this->getFormat($intLevel);
            } else {
                $format = $log_format;
            }
            $str = $this->getLogString($format);
        }

        if ($str == '') {
            return false;
        }

        // 日志文件加上年月日配置
        if ($this->bolAutoRotate) {
            $strLogFile .= '.'.date('YmdH');
        }

        // 保留最近N条日志，不知道有啥用
        if (self::$lastLogSize > 0) {
            self::$lastLogs[] = $str;
            if (count(self::$lastLogs) > self::$lastLogSize) {
                array_shift(self::$lastLogs);
            }
        }

        // 使用std_writer echo($str) ，不知道有啥用
        foreach (self::$logWriters as $writer) {
            $writer->write($str);
        }

        // 使用缓存writer进行打印
        $this->objWriter->setOptions(
            array(
                'arrLogFile' => array(
                    $strLogFile => $strLogFile ,
                ),
            )
        );
        return $this->objWriter->log($str, $strLogFile);

        //return file_put_contents($strLogFile, $str, FILE_APPEND);
    }

    // added support for self define format
    private function getFormat($level) {
        if ($level == self::LOG_LEVEL_FATAL || $level == self::LOG_LEVEL_WARNING) {
            $fmtstr = $this->strFormatWF;
        } else {
            $fmtstr = $this->strFormat;
        }
        return $fmtstr;
    }

    public function getLogString($format) {
        $md5val = md5($format);
        $func   = "_bd_log_$md5val";
        if (function_exists($func)) {
            return $func();
        }
        $dataPath = self::getDataPath();
        $filename = $dataPath . '/log/'.$md5val.'.php';
        if (!file_exists($filename)) {
            $tmp_filename = $filename . '.' . posix_getpid() . '.' . rand();

            if (!self::bdLazyIsDir($dataPath . '/log')) {
                @mkdir($dataPath . '/log');
            }
            $fnstr = $this->parseFormat($format);
            //fix:机器磁盘不够的时候,可能文件不能写完整，比如产生一个空文件
            $n = file_put_contents($tmp_filename, $fnstr);
            if ($n === strlen($fnstr)) {
                rename($tmp_filename, $filename);
            } else {
                unlink($tmp_filename);
            }
        }
        $str = '';
        if (file_exists($filename)) {
            include_once($filename);
            if (function_exists($func)) {
                $str = $func();
            } else {
                unlink($filename);
            }
        }
        return $str;
    }

    /**
     * parseFormat
     * parse format and generate code
     * @param mixed $format
     * @access public
     * @return void
     */
    public function parseFormat($format) {
        $matches = array();
        $regex  = '/%(?:{([^}]*)})?(.)/';
        preg_match_all($regex, $format, $matches);
        $prelim = array();
        $action = array();
        $prelim_done = array();

        $len = count($matches[0]);
        for ($i = 0; $i < $len; $i++) {
            $code  = $matches[2][$i];
            $param = $matches[1][$i];
            switch ($code) {
            case 'h':
                $action[] = "(defined('CLIENT_IP')? CLIENT_IP : Bd_Log::getClientIp())";
                break;
            case 't':
                $action[] = ($param == '')? "strftime('%y-%m-%d %H:%M:%S')" : "strftime(" . var_export($param, true) . ")";
                break;
            case 'i':
                $key = 'HTTP_' . str_replace('-', '_', strtoupper($param));
                $key = var_export($key, true);
                $action[] = "(isset(\$_SERVER[$key])? \$_SERVER[$key] : '')";
                break;
            case 'a':
                $action[] = "(defined('CLIENT_IP')? CLIENT_IP : Bd_Log::getClientIp())";
                break;
            case 'A':
                $action[] = "(isset(\$_SERVER['SERVER_ADDR'])? \$_SERVER['SERVER_ADDR'] : '')";
                break;
            case 'C':
                if ($param == '') {
                    $action[] = "(isset(\$_SERVER['HTTP_COOKIE'])? \$_SERVER['HTTP_COOKIE'] : '')";
                } else {
                    $param = var_export($param, true);
                    $action[] = "(isset(\$_COOKIE[$param])? \$_COOKIE[$param] : '')";
                }
                break;
            case 'D':
                $action[] = "(defined('REQUEST_TIME_US')? (microtime(true) * 1000 - REQUEST_TIME_US/1000) : '')";
                break;
            case 'e':
                $param = var_export($param, true);
                $action[] = "((getenv($param) !== false)? getenv($param) : '')";
                break;
            case 'f':
                $action[] = 'Bd_Log::$current_instance->current_file';
                break;
            case 'H':
                $action[] = "(isset(\$_SERVER['SERVER_PROTOCOL'])? \$_SERVER['SERVER_PROTOCOL'] : '')";
                break;
            case 'm':
                $action[] = "(isset(\$_SERVER['REQUEST_METHOD'])? \$_SERVER['REQUEST_METHOD'] : '')";
                break;
            case 'p':
                $action[] = "(isset(\$_SERVER['SERVER_PORT'])? \$_SERVER['SERVER_PORT'] : '')";
                break;
            case 'q':
                $action[] = "(isset(\$_SERVER['QUERY_STRING'])? \$_SERVER['QUERY_STRING'] : '')";
                break;
            case 'T':
                switch($param) {
                case 'ms':
                    $action[] = "(defined('REQUEST_TIME_US')? (microtime(true) * 1000 - REQUEST_TIME_US/1000) : '')";
                    break;
                case 'us':
                    $action[] = "(defined('REQUEST_TIME_US')? (microtime(true) * 1000000 - REQUEST_TIME_US) : '')";
                    break;
                default:
                    $action[] = "(defined('REQUEST_TIME_US')? (microtime(true) - REQUEST_TIME_US/1000000) : '')";
                }
                break;
                case 'U':
                    $action[] = "(isset(\$_SERVER['REQUEST_URI'])? \$_SERVER['REQUEST_URI'] : '')";
                    break;
                case 'v':
                    $action[] = "(isset(\$_SERVER['HOSTNAME'])? \$_SERVER['HOSTNAME'] : '')";
                    break;
                case 'V':
                    $action[] = "(isset(\$_SERVER['HTTP_HOST'])? \$_SERVER['HTTP_HOST'] : '')";
                    break;

                case 'L':
                    $action[] = 'Bd_Log::$current_instance->current_log_level';
                    break;
                case 'N':
                    $action[] = 'Bd_Log::$current_instance->current_line';
                    break;
                case 'E':
                    $action[] = 'Bd_Log::$current_instance->current_err_no';
                    break;
                case 'l':
                    $action[] = "Bd_Log::genLogID()";
                    break;
                case 'r':
                    $action[] = "Bd_Log::genRequestID()";
                    break;
                case 'u':
                    if (!isset($prelim_done['user'])) {
                        $prelim[] = '$____user____ = Bd_Passport::getUserInfoFromCookie();';
                        $prelim_done['user'] = true;
                    }
                    $action[] = "((defined('CLIENT_IP') ? CLIENT_IP: Bd_Log::getClientIp()) . ' ' . \$____user____['uid'] . ' ' . \$____user____['uname'])";
                    break;
                case 'S':
                    if ($param == '') {
                        $action[] = 'Bd_Log::$current_instance->get_str_args()';
                    } else {
                        $param_name = var_export($param, true);
                        if (!isset($prelim_done['S_'.$param_name])) {
                            $prelim[] =
                                "if (isset(Bd_Log::\$current_instance->current_args[$param_name])) {
                                    \$____curargs____[$param_name] = Bd_Log::\$current_instance->current_args[$param_name];
                                    unset(Bd_Log::\$current_instance->current_args[$param_name]);
                                } else \$____curargs____[$param_name] = '';";
                            $prelim_done['S_'.$param_name] = true;
                    }
                    $action[] = "\$____curargs____[$param_name]";
                    }
                    break;
                case 'M':
                    $action[] = 'Bd_Log::$current_instance->current_err_msg';
                    break;
                case 'x':
                    $need_urlencode = false;
                    if (substr($param, 0, 2) == 'u_') {
                        $need_urlencode = true;
                        $param = substr($param, 2);
                    }
                    switch($param) {
                    case 'log_level':
                    case 'line':
                    case 'class':
                    case 'function':
                    case 'err_no':
                    case 'err_msg':
                        $action[] = 'Bd_Log::$current_instance->current_'.$param;
                        break;
                    case 'log_id':
                        $action[] = "Bd_Log::genLogID()";
                        break;
                    case 'app':
                        $action[] = "Bd_Log::getLogPrefix()";
                        break;
                    case 'function_param':
                        $action[] = 'Bd_Log::flattenArgs(Bd_Log::$current_instance->current_function_param)';
                        break;
                    case 'argv':
                        $action[] = '(isset($GLOBALS["argv"])? Bd_Log::flattenArgs($GLOBALS["argv"]) : \'\')';
                        break;
                    case 'pid':
                        $action[] = 'posix_getpid()';
                        break;
                    case 'encoded_str_array':
                        $action[] = 'Bd_Log::$current_instance->get_str_args_std()';
                        break;
                    case 'cookie':
                        $action[] = "(isset(\$_SERVER['HTTP_COOKIE'])? \$_SERVER['HTTP_COOKIE'] : '')";
                        break;
                    case 'spanid' :
                        $action []= 'Bd_Log::getParentSpanid()';
                        break;
                    case 'force_sampling' :
                        $action []= 'Bd_Log::getForceSampling()';
                        break;
                    default:
                        $action[] = "''";
                    }
                    if ($need_urlencode) {
                        $action_len = count($action);
                        $action[$action_len-1] = 'rawurlencode(' . $action[$action_len-1] . ')';
                    }
                    break;
                    case '%':
                        $action[] =  "'%'";
                        break;
                    default:
                        $action[] = "''";
            }
        }

        $strformat = preg_split($regex, $format);
        $code   = var_export($strformat[0], true);
        for ($i = 1; $i < count($strformat); $i++) {
            $code = $code . ' . ' . $action[$i-1] . ' . ' . var_export($strformat[$i], true);
        }
        $code .=  ' . "\n"';
        $pre   = implode("\n", $prelim);

        $cmt   = "Used for app " . self::getLogPrefix() . "\n";
        $cmt  .= "Original format string: " . str_replace('*/', '* /', $format);

        $md5val = md5($format);
        $func   = "_bd_log_$md5val";
        $str    = "<?php \n/*\n$cmt\n*/\nfunction $func() {\n$pre\nreturn $code;\n}";
        return $str;
    }

    /**
     * flattenArgs
     * helper functions for use in generated code
     * @param mixed $args
     * @static
     * @access public
     * @return void
     */
    public static function flattenArgs($args) {
        if (!is_array($args)) return '';
        $str = array();
        foreach ($args as $a) {
            $str[] = preg_replace('/[ \n\t]+/', " ", $a);
        }
        return implode(', ', $str);
    }

    /**
     * get_str_args
     *
     * @access public
     * @return void
     */
    public function get_str_args() {
        $strArgs = '';
        foreach ($this->current_args as $k => &$v) {
            if (is_array($v) || is_object($v)) {
                $v = serialize($v);
            }
            $strArgs .= ' '.$k.'['.$v.']';
        }
        return $strArgs;
    }

    /**
     * get_str_args_std
     * ORP Log Specification
     * @access public
     * @return void
     */
    public function get_str_args_std() {
        $args = array();
        foreach ($this->current_args as $k => &$v) {
            if (is_array($v) || is_object($v)) {
                $v  = serialize($v);
            }
            $args[] = rawurlencode($k).'='.rawurlencode($v);
        }
        return implode(' ', $args);
    }

    /**
     * 设置允许记录最新的N条日志信息
     * @param int $size
     */
    public static function setLastLogSize($size) {
        self::$lastLogSize = $size;
    }

    /**
     * 获取最近的日志
     * @return array
     */
    public static function getLastLogs() {
        return self::$lastLogs;
    }

    /**
     * 注册一个新的logWriter
     * @param Bd_Log_Writer $writer
     */
    private static function registerWriter(Bd_Log_Writer $writer) {
        self::$logWriters[] = $writer;
    }

    /**
     * 使用标准输出
     * @return boolean
     */
    public static function useLogerStdOut() {
        static $used = false;
        if ($used) {
            return true;
        }
        self::registerWriter(new Bd_Log_Writer_Std());
        $used = true;
    }

    /**
     * @desc bd_lazy_is_dir , 兼容php
     * @param unknown $file
     * @return boolean
     */
    private static function bdLazyIsDir($file) {
        return is_dir($file);
    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
