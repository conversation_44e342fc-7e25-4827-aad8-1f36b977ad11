<?php


/**
 * bns-php 扩展访问的包装类，访问bns服务，获取bns服务对应的实例列表和服务配置<br>
 * 使用了文件缓存机制缓存查询结果，此包装类确保尽可能返回信息，即便在bns不可访问时。<br>
 * 注意：在服务S被删除后，若本地文件缓存存在S，此包装类查询S仍然会返回信息<br>
 * bns返回码代表成功：<br>
 * -   0: 成功<br>
 * - -16: 低于阈值（正常实例数少于设定值）<br>
 *
 * @since 1.5 2019-02-20 拆分缓存模块，独立缓存模块
 * @since 1.1 2018-12-26 代码整理，删除无用代码
 * @since 1.0 2018-05-24 初始化
 *
 * @filesource bd/bns/BnsRpc.php
 * <AUTHOR>
 * @version 1.5
 * @date    2019-02-20
 */
class Bd_Bns_BnsRpc {

    private $errno  = 0;
    private $errmsg = "";

    private $timeout;
    private $cacheInst;

    private $instp = "inst_";
    private $confp = "conf_";
    private $authinstp = "authinst_";

    public function __construct($timeout = 1.5, $ttl = 50) {
        $this->timeout   = $timeout * 1000;
        $this->cacheInst = new Bd_Bns_Cache($ttl);
    }

    /**
     * 获取服务或服务组的实例配置列表，失败：返回空<br>
     * 成功：bns返回实例数组，单个实例数据结构如下<br>
     * <code>
     * array (<br>
     *     'service_name' => 'napi.na.bjcq',<br>
     *     'host'         => '3465b169-f3c1-42a9-9561-5306fb567d3f',<br>
     *     'ip'           => '*************',<br>
     *     'tag'          => '',<br>
     *     'multi_port'   => 'main=8099',<br>
     *     'container_id' => '',<br>
     *     'deploy_path'  => '/home/<USER>',<br>
     *     'port'         => 8099,<br>
     *     'status'       => 0,<br>
     *     'load'         => 4,<br>
     *     'offset'       => 309,<br>
     * )
     * </code>
     *
     * @param string       $bnsName
     * @return array
     */
    public function getServiceInstance($bnsName) {
        $ckey = $this->instp . $bnsName;
        $list = $this->cacheInst->get($ckey);
        if (!empty($list)) {
            return $list;
        }

        $code = 0;
        $msg  = "";
        $list = get_instance_by_service($bnsName, $code, $msg, $this->timeout);
        $this->setErrInfo($msg, $list);
        if (0 === $code || -16 == $code) {
            $this->cacheInst->set($ckey, $list);
            return $list;
        }
        return array();
    }

    /**
     * 获取服务的访问控制名单(get_auth_instance_by_service)
     *
     * @param string       $bnsName
     * @return array
     */
    public function getAuthInstance($bnsName) {
        $ckey = $this->authinstp . $bnsName;
        $list = $this->cacheInst->get($ckey);
        if (!empty($list)) {
            return $list;
        }

        $code = 0;
        $msg  = "";
        $list = get_auth_instance_by_service($bnsName, $code, $msg, $this->timeout);
        $this->setErrInfo($code, $msg);
        if (0 === $code || -16 == $code) {
            $this->cacheInst->set($ckey, $list);
            return $list;
        }
        return array();
    }

    /**
     * 获取bns服务配置
     *
     * @param string      $bnsName
     * @return array
     */
    public function getServiceConf($bnsName) {
        $ckey = $this->confp . $bnsName;
        $conf = $this->cacheInst->get($ckey);
        if (!empty($conf)) {
            return $conf;
        }

        $code = 0;
        $msg  = "";
        $conf = get_service_conf($bnsName, $code, $msg, $this->timeout);
        $this->setErrInfo($code, $msg);
        if (0 === $code) {
            $this->cacheInst->set($ckey, $conf);
            return $conf;
        }
        return array();
    }

    public function getErrno() {
        return $this->errno;
    }

    public function getErrmsg() {
        return $this->errmsg;
    }

    private function setErrInfo($errno, $errmsg) {
        $this->errno  = $errno;
        $this->errmsg = $errmsg;
    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */