<?php


/**
 * bns客户端，配置读取位置：conf/bns/services/serviceName.conf<br>
 * 优先使用bns配置，如果无bns配置则使用local配置
 *
 * @filesource bd/bns/Client.php
 * <AUTHOR>
 * @version 1.0
 * @date    2018-05-24
 */
class Bd_Bns_Client {

    private $bnsRpc = NULL;

    private static $inst = NULL;

    private function __construct() {
        $ttl     = Bd_Bns_Const::CACHE_TTL;
        $timeout = Bd_Bns_Const::RPC_TIMEOUT;
        $this->bnsRpc = new Bd_Bns_BnsRpc($timeout, $ttl);
    }

    public static function getInstance() {
        if (NULL === self::$inst) {
            self::$inst = new self();
        }
        return self::$inst;
    }

    /**
     * 获取配置实例列表，支持local和bns方式获取<br>
     * 优先通过bns获取，获取实例列表后，可根据$lb策略挑选服务实例<br>
     * - LB_NONE   : 全部实例<br>
     * - LB_RANDOM : 随机实例
     *
     * @param string       $service
     * @param string       $name
     * @param const        $lb       负载均衡策略
     * @param string        $svc
     * @return mixed:array|boolean
     */
    public function getServiceInstance($service, $name, $lb = Bd_Bns_Const::LB_RANDOM, $svc = "") {
         $servers = $this->getLocalInstance($service, $name, $svc);
         if($servers == false || count($servers) <= 0) {
             return false;
         }

         return $servers[0];
    }

    /**
     * 随机挑选服务实例策略，返回挑选成功实例
     *
     * @param array        $servers
     * @return array
     */
    private static function stgRandom(array $servers) {
        $cnt  = count($servers);
        if (1 === $cnt) {
            $cr = 0;
        } else {
            $cr = mt_rand(0, $cnt - 1);
        }
        return $servers[$cr];
    }

    /**
     * 获取本地配置实例列表，配置方法参见conf/bns.conf
     *
     * @param string       $service
     * @param string       $name
     * @param string       $svc
     * @return mixed:array|boolean
     */
    private function getLocalInstance($service, $name, $svc = "") {
        $conf = Bd_Bns_Conf::localConf($service, $svc);
        if (false === $conf) {
            return false;
        } elseif (!isset($conf[$name])) {
            return false;
        }
        $local    = $conf[$name];

        $servers  = array();
        $defPort  = intval($local["Port"]);
        foreach ($local["Server"] as $inst) {
            $host = $inst["Host"];
            $port = isset($inst["Port"]) ? intval($inst["Port"]) : $defPort;
            $servers[] = array(
                "host" => $host,
                "port" => $port,
            );
        }
        return empty($servers) ? false : $servers;
    }

    /**
     * 通过bns服务获取线上实例列表，过滤异常状态实例
     *
     * @param string       $service
     * @param string       $name
     * @param string       $svc
     * @return mixed:array|boolean
     */
    private function getBnsInstance($service, $name, $svc = "") {
        $conf = Bd_Bns_Conf::bnsConf($service, $svc);
        if (false === $conf) {
            return false;
        } elseif (!isset($conf[$name])) {
            return false;
        }
        $bnsName = $conf[$name];

        $ret     = $this->bnsRpc->getServiceInstance($bnsName);     # 获取实例列表
        if (empty($ret)) {
            return false;
        }
        $servers = array();
        foreach ($ret as $inst) {
            if (0 !== $inst["status"]) {    # 机器状态异常，需要剔除
                continue;
            }
            $servers[] = array(
                "host" => $inst["ip"],
                "port" => $inst["port"],
            );
        }
        return empty($servers) ? false : $servers;
    }
}

/* vim: set expandtab ts=4 sw=4 sts=4 tw=100: */
