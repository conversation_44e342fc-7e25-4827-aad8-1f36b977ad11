<?php
/**************************************************************************
 *
 * Copyright (c) 2019 Zuoyebang.com, Inc. All Rights Reserved
 *
 * @file    ErrorCode.php
 * @date    2020-03-17
 *
 **************************************************************************/

class Lxjxcrm_Const_Errors
{
    // 成功
    const ERROR_NO_ERROR = 0;
    // 成功
    const ERROR_SUCCESS = 1;
    // 权限不足
    const ERROR_UN_AUTH = 2;
    // 数据库事务异常
    const ERROR_DB_TRANSACTION = 3;
    // 上传图像缺失
    const ERROR_IMAGE_NOT_EXIST = 4;
    // 上传图片失败
    const ERROR_IMAGE_UPLOAD = 5;
    // 数据库服务异常
    const ERROR_DB_ERROR = 6;
    // 用户信息异常
    const ERROR_USER_INFO = 7;

    // 缺少参数
    const ERROR_PARAMETER_LACK = 101;
    // 数据无修改
    const ERROR_DATA_NON_MODIFY = 102;
    // 当前用户信息不存在
    const ERROR_CRM_USER_NOT_EXIST = 103;
    // 插入数据为空
    const ERROR_INSERT_EMPTY_DATA = 104;
    // 参数逻辑值异常
    const ERROR_PARAMETER_VALUE = 105;

    // 添加联系人失败
    const ERROR_CONTACT_CREATE = 201;
    // 修改联系人失败
    const ERROR_CONTACT_MODIFY = 202;
    // 联系人不存在
    const ERROR_CONTACT_NOT_EXIST = 203;
    // 删除联系人失败
    const ERROR_CONTACT_DELETE = 204;
    // 联系人数量超限
    const ERROR_CONTACT_AMOUNT = 205;

    // 添加跟踪记录失败
    const ERROR_TRACE_CREATE = 301;


    const ERROR_SCHOOL_CREATE              = 401;                  // 创建学校失败
    const ERROR_SCHOOL_NOT_EXIST = 402;                            // 学校不存在
    const ERROR_SCHOOL_CLAIMED_YET = 403;                          // 学校已被认领-不可重复认领
    const ERROR_SCHOOL_UN_CLAIMED = 404;                           // 学校未被当前用户认领-不可释放
    const ERROR_SCHOOL_RELEASE_AUTH = 405;                         // 无释放学校权限
    const ERROR_SCHOOL_MODIFY = 406;                               // 修改学校信息失败
    const ERROR_SCHOOL_ALLOCATE_USER = 407;                        // 分配用户不存在
    const ERROR_SCHOOL_DELETE = 408;                               // 删除学校失败
    const ERROR_SCHOOL_NAME_DUPLICATE = 409;                       // 学校名称已存在

    // 拜访计划创建失败
    const ERROR_VISIT_SCHEME_CREATE = 501;
    // 拜访计划不存在
    const ERROR_VISIT_SCHEME_NOT_EXIST = 502;
    // 拜访计划不是待拜访状态
    const ERROR_VISIT_SCHEME_NOT_WAIT = 503;
    // 拜访计划不是拜访中状态
    const ERROR_VISIT_SCHEME_NOT_VISIT = 504;
    // 拜访计划更新失败
    const ERROR_VISIT_SCHEME_UPDATE = 505;
    // 拜访计划删除失败
    const ERROR_VISIT_SCHEME_DELETE = 506;
    // 拜访计划不是待签退状态
    const ERROR_VISIT_SCHEME_NOT_SIGN_IN = 507;

    // 创建外勤记录失败
    const ERROR_VISIT_RECORD_CREATE = 550;
    // 外勤记录不存在
    const ERROR_VISIT_RECORD_NOT_EXIST = 551;
    // 更新外勤记录失败
    const ERROR_VISIT_RECORD_UPDATE = 552;
    // 签到位置偏差过大
    const ERROR_SIGN_POINT_OFFSET = 553;

    // 报表不存在
    const ERROR_TABLE_NOT_EXIST = 601;

    // 部门不存在
    const ERROR_DEPART_NOT_EXIST = 701;
    // 更新部门失败
    const ERROR_DEPART_UPDATE = 702;
    // 创建部门失败
    const ERROR_DEPART_CREATE = 703;

    // 添加0元课提报失败
    const ERROR_APPLY_CREATE = 801;
    // 0元课不存在
    const ERROR_FREE_CLASS_NOT_EXIST = 802;
    // 0元课更新失败
    const ERROR_FREE_CLASS_UPDATE_FAILED = 803;
    // 0元课审批失败
    const ERROR_FREE_CLASS_APPROVE_FAILED = 804;

    // 自定义区县ID已存在
    const ERROR_COUNTY_ID_DUPLICATE = 901;
    // 添加自定义区县失败
    const ERROR_COUNTY_CREATE = 902;
    // 更新区县失败
    const ERROR_COUNTY_MODIFY = 903;

    // 日程时间冲突
    const ERROR_SCHEDULE_TIME_CONFLICT = 1001;
    // 日程不存在
    const ERROR_SCHEDULE_NOT_EXIST = 1002;
    // 日程不可取消
    const ERROR_SCHEDULE_REMOVE = 1003;

    // 待审批任务不存在
    const ERROR_NO_APPROVE_TASK = 1101;

    // 讲座不存在
    const ERROR_LECTURE_EXISTENCE = 1201;
    // 讲座不可取消
    const ERROR_LECTURE_CANCEL = 1202;
    // 讲座不可撤回
    const ERROR_LECTURE_WITHDRAW = 1203;
    // 讲座状态不允许修改
    const ERROR_LECTURE_STATUS = 1204;
    // 非创建人不可撤回
    const ERROR_LECTURE_AUTH = 1205;
    // 讲座客户信息不存在
    const ERROR_LECTURE_CUSTOMER = 1206;
    // 讲座客户不允许增删
    const ERROR_LECTURE_CUSTOMER_ADD_DEL = 1207;
    // 发送讲座增删客户的钉钉通知失败
    const ERROR_CUSTOMER_ADD_DEL_DD_MSG = 1208;
    // 讲座更新失败
    const ERROR_LECTURE_UPDATE_FAILED = 1209;
    // 审批不存在或不是待审核状态
    const ERROR_BPM_PROCESS_NOT_EXIST = 1301;
    // 审批更新失败
    const ERROR_BPM_PROCESS_UPDATE_FAILED = 1302;

    # 拜访
    // 拜访事项超过条数限制
    const ERROR_VISIT_MATTER_NUM_INVALID = 20001;
    // 拜访意向参数错误
    const ERROR_VISIT_MATTER_PURPOSE_INVALID = 20002;
    // 拜访方向参数错误
    const ERROR_VISIT_MATTER_DIRECTION_INVALID = 20003;
    // 拜访事项未确认KP原因参数错误
    const ERROR_VISIT_MATTER_NO_CONTACT_REASON_INVALID = 20004;
    // 拜访议题参数错误
    const ERROR_VISIT_MATTER_TOPIC_INVALID = 20005;
    // 拜访行动和结果参数错误
    const ERROR_VISIT_MATTER_ACTION_RESULT_INVALID = 20006;
    // 预计招生时间参数错误
    const ERROR_VISIT_ENROLL_TIME_INVALID = 20007;
    // 预计招生量参数错误
    const ERROR_VISIT_ENROLL_NUM_INVALID = 20008;
    // 拜访事项联系人不能为空
    const ERROR_MATTER_CONTACT_INVALID = 20009;
    // 拜访事项联系人职务不能为空
    const ERROR_MATTER_CONTACT_POSITION_INVALID = 20010;
    // 拜访事项联系人电话错误
    const ERROR_MATTER_CONTACT_PHONE_INVALID = 20011;
    // 当前拜访计划无法编辑
    const ERROR_VISIT_CANT_MODIFY = 20012;
    // 当前拜访计划无法完成拜访报告
    const ERROR_VISIT_CANT_SUBMIT = 20013;
    // 最多导出7天的拜访计划
    const ERROR_VISIT_EXPORT_TIME_LIMIT = 20014;
    // 签到失败
    const ERROR_VISIT_SIGN_IN_FAILED = 20015;

    protected static $_errorMeg = array(
        self::ERROR_NO_ERROR                               => '未发生错误',
        self::ERROR_SUCCESS                                => '成功',
        self::ERROR_UN_AUTH                                => '权限不足',
        self::ERROR_DB_TRANSACTION                         => '数据库异常',
        self::ERROR_IMAGE_NOT_EXIST                        => '缺少上传图片',
        self::ERROR_IMAGE_UPLOAD                           => '上传图片失败',
        self::ERROR_DB_ERROR                               => '数据库异常',
        self::ERROR_USER_INFO                              => '用户信息异常',
        self::ERROR_PARAMETER_LACK                         => '参数缺少',
        self::ERROR_DATA_NON_MODIFY                        => '数据无修改',
        self::ERROR_CRM_USER_NOT_EXIST                     => '当前用户信息丢失',
        self::ERROR_INSERT_EMPTY_DATA                      => '无效数据',
        self::ERROR_PARAMETER_VALUE                        => '参数逻辑值异常',
        self::ERROR_CONTACT_CREATE                         => '添加联系人失败',
        self::ERROR_CONTACT_MODIFY                         => '修改联系人失败',
        self::ERROR_CONTACT_NOT_EXIST                      => '联系人不存在',
        self::ERROR_CONTACT_DELETE                         => '删除联系人失败',
        self::ERROR_CONTACT_AMOUNT                         => '已有联系人数量超限',
        self::ERROR_TRACE_CREATE                           => '添加追踪记录失败',

        self::ERROR_SCHOOL_CREATE                                    => '创建学校失败',
        self::ERROR_SCHOOL_NOT_EXIST                                 => '学校不存在',
        self::ERROR_SCHOOL_CLAIMED_YET                               => '学校已被认领',
        self::ERROR_SCHOOL_UN_CLAIMED                                => '学校不可释放',
        self::ERROR_SCHOOL_RELEASE_AUTH                              => '没有权限释放学校',
        self::ERROR_SCHOOL_MODIFY                                    => '修改学校信息失败',
        self::ERROR_SCHOOL_ALLOCATE_USER                             => '分配用户不存在',
        self::ERROR_SCHOOL_DELETE                                    => '删除学校失败',
        self::ERROR_SCHOOL_NAME_DUPLICATE                            => '学校已存在，请检查后重新输入',

        self::ERROR_VISIT_SCHEME_CREATE                    => '创建拜访计划失败',
        self::ERROR_VISIT_SCHEME_NOT_EXIST                 => '拜访计划不存在',
        self::ERROR_VISIT_SCHEME_NOT_WAIT                  => '拜访计划不是待拜访，不可签到',
        self::ERROR_VISIT_SCHEME_NOT_VISIT                 => '拜访计划不是拜访中，不可签退',
        self::ERROR_VISIT_SCHEME_UPDATE                    => '更新拜访计划失败',
        self::ERROR_VISIT_SCHEME_DELETE                    => '删除拜访计划失败',
        self::ERROR_VISIT_RECORD_CREATE                    => '创建外勤记录失败',
        self::ERROR_VISIT_RECORD_NOT_EXIST                 => '外勤记录不存在',
        self::ERROR_VISIT_RECORD_UPDATE                    => '更新外勤记录失败',
        self::ERROR_SIGN_POINT_OFFSET                      => '位置偏差过大',
        self::ERROR_TABLE_NOT_EXIST                        => '报表不存在',
        self::ERROR_DEPART_NOT_EXIST                       => '部门不存在',
        self::ERROR_DEPART_UPDATE                          => '更新部门失败',
        self::ERROR_DEPART_CREATE                          => '创建部门失败',
        self::ERROR_APPLY_CREATE                           => '添加0元课提报失败',
        self::ERROR_COUNTY_ID_DUPLICATE                    => '区县ID重复',
        self::ERROR_COUNTY_CREATE                          => '添加区县失败',
        self::ERROR_COUNTY_MODIFY                          => '更新区县失败',
        self::ERROR_SCHEDULE_TIME_CONFLICT                 => '日程时间冲突',
        self::ERROR_SCHEDULE_NOT_EXIST                     => '日程不存在',
        self::ERROR_SCHEDULE_REMOVE                        => '日程不可以取消',
        self::ERROR_NO_APPROVE_TASK                        => '待审批任务不存在',
        self::ERROR_LECTURE_EXISTENCE                      => '讲座信息不存在',
        self::ERROR_LECTURE_CANCEL                         => '讲座不可取消',
        self::ERROR_LECTURE_WITHDRAW                       => '讲座不可撤回',
        self::ERROR_LECTURE_STATUS                         => '当前状态不允许修改',
        self::ERROR_LECTURE_AUTH                           => '权限受限',
        self::ERROR_LECTURE_CUSTOMER                       => '讲座客户不存在',
        self::ERROR_LECTURE_CUSTOMER_ADD_DEL               => '讲座客户不允许增删',
        self::ERROR_CUSTOMER_ADD_DEL_DD_MSG                => '发送讲座增删客户的钉钉通知失败',
        self::ERROR_BPM_PROCESS_NOT_EXIST                  => '审批不存在或不是待审核状态',
        self::ERROR_BPM_PROCESS_UPDATE_FAILED              => '审批更新失败',
        self::ERROR_FREE_CLASS_NOT_EXIST                   => '0元课不存在',
        self::ERROR_FREE_CLASS_UPDATE_FAILED               => '0元课更新失败',
        self::ERROR_LECTURE_UPDATE_FAILED                  => '讲座更新失败',
        self::ERROR_VISIT_MATTER_NUM_INVALID               => '拜访事项超过条数限制',
        self::ERROR_VISIT_MATTER_PURPOSE_INVALID           => '拜访意向参数错误',
        self::ERROR_VISIT_MATTER_DIRECTION_INVALID         => '拜访方向参数错误',
        self::ERROR_VISIT_MATTER_NO_CONTACT_REASON_INVALID => '拜访事项未确认KP原因参数错误',
        self::ERROR_VISIT_MATTER_TOPIC_INVALID             => '拜访议题参数错误',
        self::ERROR_VISIT_MATTER_ACTION_RESULT_INVALID     => '拜访行动和结果参数错误',
        self::ERROR_VISIT_ENROLL_TIME_INVALID              => '预计招生时间参数错误',
        self::ERROR_VISIT_ENROLL_NUM_INVALID               => '预计招生量参数错误',
        self::ERROR_MATTER_CONTACT_INVALID                 => '拜访事项联系人不能为空',
        self::ERROR_MATTER_CONTACT_POSITION_INVALID        => '拜访事项联系人职务不能为空',
        self::ERROR_MATTER_CONTACT_PHONE_INVALID           => '拜访事项联系人电话错误',
        self::ERROR_VISIT_CANT_MODIFY                      => '当前拜访计划无法编辑',
        self::ERROR_VISIT_CANT_SUBMIT                      => '当前拜访计划无法完成拜访报告',
        self::ERROR_VISIT_SCHEME_NOT_SIGN_IN               => '拜访计划不是待签退，不可签退',
        self::ERROR_VISIT_EXPORT_TIME_LIMIT                => '最多导出7天的拜访计划',
        self::ERROR_VISIT_SIGN_IN_FAILED                   => '签到失败',
    );

    public static function getErrorMsg($errorCode)
    {
        if (isset(self::$_errorMeg[$errorCode])) {
            return self::$_errorMeg[$errorCode];
        }

        return '未知错误';
    }

    public static function isSuccess($errorCode)
    {
        return (self::ERROR_NO_ERROR === $errorCode || self::ERROR_SUCCESS === $errorCode);
    }

}
