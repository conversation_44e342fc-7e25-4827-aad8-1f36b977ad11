<?php
/**
 * Created by PhpStorm.
 * User: liu<PERSON><PERSON>i
 * Date: 2020/3/2
 * Time: 9:02 PM
 */
class Lxjxcrm_Const_Const
{
    const IMPORT_MAX_TOTAL = 50000;       //导出的最大的数量
    const CACHE_USERINFO_EXPIRE = 86400;  // 缓存1天
    const SESSION_EXPIRE = 2592000;  // 过期时间，86400 * 30（1个月）
    const KICK_EXPIRE = 604800;       // 踢出的lxjxcrmuss对应session存活时间，86400 * 7（7天）
    const REDIS_NAME_LXJX_CRM = 'laxinjinxiao';       // 踢出的lxjxcrmuss对应session存活时间，86400 * 7（7天）


    const RC4_KEY_PASSWORD = '@#SA~saS123D#@1_D';    // 密码rc4加解密密钥
    const RC4_KEY_LXJXCRMUSS = '@#JD~jdWJDf3#@3_C';       // 登录标识rc4加解密密钥
    const RC4_KEY_LXJXCRMOSS = '@#AD~fdWJD12#@5_N';       // oauth登录rc4密钥
    const RC4_KEY_PHONE = 'asC12sh4f12d7vdoDsf';     // 手机号rc4加解密密钥


    // 登录类型配置
    const LOGIN_TYPE_MOBILE = 'mobile';     // mobile
    const LOGIN_TYPE_PC = 'pc';   // pc

    /**
     * 可以无限制通过验证码登录的app
     * @var array
     */
    public static $WHITE_APP = [
        'lxjxschool',
    ];

    // zybuss被踢出原因
    const KICK_DEFAULT = 0;            // session已过期
    const KICK_PASSWORDSET = 1;            // 修改密码
    const KICK_PHONESET = 2;            // 修改手机号
    const KICK_OVERFLOW = 3;            // 端内部限额已满
    const KICK_UNIQSESSION = 4;            // 单点登录被踢出
    public static $KICK_MAP = [
        self::KICK_DEFAULT => '登录过期，请重新登录',
        self::KICK_PASSWORDSET => '您的账号已修改密码，请重新登录',
        self::KICK_PHONESET => '您的账号已重置手机号，请重新登录',
        self::KICK_OVERFLOW => '您的账号已在其他设备登录，请重新登录',
        self::KICK_UNIQSESSION => '您的账号已在其他设备登录，请重新登录',
    ];

    public static $OS_TYPE_MAP = [
        'unknown' => 0,
        'android' => 1,
        'ios' => 2,
        'pcweb' => 3,
        'windows' => 4,
    ];

    // 0元课提报审核状态--待处理
    const FREE_CLASS_APPROVAL_TODO          = 0;
    // 0元课提报审核状态--通过
    const FREE_CLASS_APPROVAL_PASSED        = 1;
    // 0元课提报审核状态--不通过
    const FREE_CLASS_APPROVAL_REJECTED      = 2;
    // 0元课提报审核状态--驳回
    const FREE_CLASS_APPROVAL_DENIED        = 3;

    public static $approvalStatus = array(
        self::FREE_CLASS_APPROVAL_TODO,
        self::FREE_CLASS_APPROVAL_PASSED,
        self::FREE_CLASS_APPROVAL_REJECTED,
        self::FREE_CLASS_APPROVAL_DENIED,
    );

    # 联系人缓存key 前缀
    const CACHE_CONTACT_PREFIX = 'lxjxcrm_schoolcontacts_detail_';
    # 拜访
    // 拜访事项最多5条
    const MAX_MATTER_NUM = 5;
    // 字数-10
    const WORD_NUM_5 = 5;
    // 字数-10
    const WORD_NUM_10 = 10;
    // 字数-20
    const WORD_NUM_20 = 20;
    // 字数-100
    const WORD_NUM_100 = 100;
    // 字数-200
    const WORD_NUM_200 = 200;
    # 拜访计划操作类型
    // 创建
    const VISIT_ACT_TYPE_CREATE = 'create';
    // 创建
    const VISIT_ACT_TYPE_MODIFY = 'modify';
    // 创建
    const VISIT_ACT_TYPE_SUBMIT = 'submit';
}
