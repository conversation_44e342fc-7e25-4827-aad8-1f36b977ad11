<?php
/**
 * @filename IPS.php
 * @brief 接入IPS登录
 * @date 2024/08/01
 */
class Lxjxcrm_IPS
{
    const IPS_SID         = 'lxjxcrm';
    const IPS_SECRET_TEST = 'a48a779c15bf7e6b07fde4a42172209c';
    const IPS_SECRET      = '8112bc8e28b4d1f11cd4ba36c911b50f';
    const IPS_PATH        = '/';

    private static $ips = NULL;

    private function __construct()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$ips)) {
            //
            if (Hk_Util_Env::RunEnvTest === Hk_Util_Env::getRunEnv()) {
                //测试环境
                self::$ips = new Saaslib_Service_IPSV2(self::IPS_SID, self::IPS_SECRET_TEST, self::IPS_PATH);
            } else {
                self::$ips = new Saaslib_Service_IPSV2(self::IPS_SID, self::IPS_SECRET, self::IPS_PATH);
            }
        }
        return self::$ips;
    }

    // 这个接口暂时未实现, 参考的是 openkp/login/login
    public static function logIn()
    {
        $protocal    = isset($_SERVER["HTTP_X_FORWARDED_PROTO"]) && "https" === $_SERVER["HTTP_X_FORWARDED_PROTO"] ? "https" : "http";
        $host        = explode(":", $_SERVER["HTTP_HOST"])[0];
        $prefix      = sprintf("%s://%s", $protocal, $host);
        $queryString = isset($_SERVER['QUERY_STRING']) ? "?" . $_SERVER['QUERY_STRING'] : "?auth=ips";
        $queryString = preg_replace("#&?(logout=1|redirect=1|ticket=[^&]+)#", "", $queryString);

        // 登录成功后的业务系统页面，example请替换业务系统自己的路径
        $refer = sprintf("%s%s%s", $prefix, "/static/fe-xcrm/#/", $queryString);
        $refer2 = sprintf("%s%s%s", $prefix, "/lxjxcrm/login/login", $queryString);

        // 验证ticket、种session
        $ips = self::getInstance();
        $res = $ips->validateAndSession($_GET["ticket"]);
        if (empty($res)) {
            // IPS回跳的业务系统地址
            $redirectUrl = $refer2;
            $ips->login($redirectUrl);
            return;
        }

        // 登录成功，跳转业务系统页面
        header("Location:" . $refer);
        return;
    }

    // 这个接口暂时未实现, 参考的是 openkp/login/login
    public static function logOut()
    {
        $protocal    = isset($_SERVER["HTTP_X_FORWARDED_PROTO"]) && "https" === $_SERVER["HTTP_X_FORWARDED_PROTO"] ? "https" : "http";
        $host        = explode(":", $_SERVER["HTTP_HOST"])[0];
        $queryString = isset($_SERVER['QUERY_STRING']) ? "?" . $_SERVER['QUERY_STRING'] : "?auth=ips";
        $queryString = preg_replace("#&?(logout=1|ticket=[^&]+)#", "", $queryString);
        $queryString .= "&redirect=1";

        // 登出后会停留在IPS登录页，再次点击登录时回跳此地址
        $redirectUrl = sprintf("%s://%s/lxjxcrm/login/login%s", $protocal, $host, $queryString);

        // 登出
        $ips = self::getInstance();
        $ips->logout($redirectUrl);
    }

    /**
     * 获取IPS用户数据
     * {
     *   "uid": 9999,
     *   "uname": "youxiangqianzhui", // 一般是邮箱前缀，但是有43个用户特殊（老的喵宝）是邮箱全称
     *   "zhname": "姓名",
     *   "emplId": "Z000501", // 看起来是工号
     *   "email": "<EMAIL>" // 邮箱全称
     * }
     * @return bool/mix
     * @time   2020-02-17 07:33:51
     */
    static public function getIpsUserInfo()
    {
        // 获取当前用户session
        $ips = self::getInstance();
        return $ips->getSession();
    }
}
