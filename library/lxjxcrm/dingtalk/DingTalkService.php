<?php
/**
 * Created by PhpStorm.
 * User: liujiaqi
 * Date: 2020/3/2
 * Time: 3:39 PM
 */
include "TopSdk.php";
date_default_timezone_set('Asia/Shanghai');


class Lxjxcrm_Dingtalk_DingTalkService
{
    public $departmentIdList = [];

    /**
     * 获取access_token
     * @param $source
     * @return bool|SimpleXMLElement
     */
    public function getToken()
    {
        $client    = new DingTalkClient(DingTalkConstant::$CALL_TYPE_OAPI, DingTalkConstant::$METHOD_GET, DingTalkConstant::$FORMAT_JSON);
        $req       = new OapiGettokenRequest();
        $appKey    = Lxjxlib_Util_Conf::getAppEnvConf('login/dingtalk/appkey');
        $appSecret = Lxjxlib_Util_Conf::getAppEnvConf('login/dingtalk/appsecret');
        $req->setAppkey($appKey);
        $req->setAppsecret($appSecret);
        $resp = $client->execute($req, '', "https://oapi.dingtalk.com/gettoken");
        if ($resp->errcode == 0) {
            return $resp->access_token;
        } else {
            Bd_Log::warning('[获取钉钉access_token失败：]' . $resp->errmsg);

            return false;
        }

    }

    /**
     * 获取用户id
     * @param $code
     * @param $accessToken
     */
    public function getUid($code, $accessToken)
    {
        $client = new DingTalkClient(DingTalkConstant::$CALL_TYPE_OAPI, DingTalkConstant::$METHOD_GET, DingTalkConstant::$FORMAT_JSON);
        $req    = new OapiUserGetuserinfoRequest();
        $req->setCode($code);
        $resp = $client->execute($req, $accessToken, "https://oapi.dingtalk.com/user/getuserinfo");
        if ($resp->errcode == 0) {
            return $resp->userid;
        } else {
            Bd_Log::warning('[获取钉钉uid失败：]' . $resp->errmsg);
            return false;
        }
    }

    /**
     * 获取用户姓名
     * 会返回{"errcode":0,"sys_level":2,"is_sys":true,"name":"姓名01","errmsg":"ok","deviceId":"f0c26080e123af46bc1166edc0b848ba","userid":"02857574719842592"}
     * @param $code
     * @param $accessToken
     */
    public function getName($code, $accessToken)
    {
        $client = new DingTalkClient(DingTalkConstant::$CALL_TYPE_OAPI, DingTalkConstant::$METHOD_GET, DingTalkConstant::$FORMAT_JSON);
        $req    = new OapiUserGetuserinfoRequest();
        $req->setCode($code);
        $resp = $client->execute($req, $accessToken, "https://oapi.dingtalk.com/user/getuserinfo");
        if ($resp->errcode == 0) {
            return $resp->name;
        } else {
            Bd_Log::warning('[获取钉钉uid失败：]' . $resp->errmsg);
            return false;
        }
    }

    /**
     * 获取用户信息详情
     * @param $uid
     * @param $accessToken
     */
    public function getUserInfo($uid, $accessToken)
    {
        $client = new DingTalkClient(DingTalkConstant::$CALL_TYPE_OAPI, DingTalkConstant::$METHOD_GET, DingTalkConstant::$FORMAT_JSON);
        $req    = new OapiUserGetRequest;
        $req->setUserid($uid);
        $resp = $client->execute($req, $accessToken, "https://oapi.dingtalk.com/user/get");
        if ($resp->errcode == 0) {
            return $resp;
        } else {
            Bd_Log::warning('[根据uid获取钉钉用户失败：]' . $resp->errmsg);
            return false;
        }
    }


    /**
     * 获取部门下的用户列表
     * @param $departmentId
     * @param $offset
     * @param $size
     * @param $accessToken
     * @return bool|mixed|ResultSet|SimpleXMLElement
     */
    public function getUserListByDepartmentId($departmentId, $offset, $size, $accessToken)
    {
        $client = new DingTalkClient(DingTalkConstant::$CALL_TYPE_OAPI, DingTalkConstant::$METHOD_GET, DingTalkConstant::$FORMAT_JSON);
        $req    = new OapiUserListbypageRequest;
        $req->setDepartmentId($departmentId);
        $req->setOffset($offset);
        $req->setSize($size);
        $resp = $client->execute($req, $accessToken, "https://oapi.dingtalk.com/user/listbypage");
        if ($resp->errcode == 0) {
            return $resp->userlist;
        } else {
            Bd_Log::warning('[获取钉钉获取部门下的用户列表失败：]' . $resp->errmsg);
            return false;
        }
    }

    /**
     * 递归获取部门下的用户列表
     * @param $departmentId
     * @param $offset
     * @param $size
     * @param $accessToken
     * @return bool|mixed|ResultSet|SimpleXMLElement
     */
    public function getDepartmentListByDepartmentId($departmentIdList, $accessToken)
    {
        $this->departmentIdList = array_merge($this->departmentIdList, $departmentIdList);
        $client                 = new DingTalkClient(DingTalkConstant::$CALL_TYPE_OAPI, DingTalkConstant::$METHOD_GET, DingTalkConstant::$FORMAT_JSON);
        $idList                 = [];
        foreach ($departmentIdList as $departmentId) {
            usleep(50);
            $req = new OapiDepartmentListIdsRequest;
            $req->setId($departmentId);
            $resp = $client->execute($req, $accessToken, "https://oapi.dingtalk.com/department/list_ids");
            if ($resp->errcode == 0) {
                $idList = array_merge($idList, $resp->sub_dept_id_list);
            } else {
                Bd_Log::warning('[递归获取部门下的用户列表失败：]' . $resp->errmsg);
                return false;
            }
        }

        if ($idList) {
            return $this->getDepartmentListByDepartmentId($idList, $accessToken);
        }

    }

    public function sendMessage($ddUid, $msg, $accessToken)
    {
        $content = [
            'msgtype' => 'text',
            'text'    => [
                'content' => $msg,
            ],
        ];

        Bd_Log::addNotice('dingDingMsg', json_encode($content, JSON_UNESCAPED_UNICODE));

        $client = new DingTalkClient(DingTalkConstant::$CALL_TYPE_OAPI, DingTalkConstant::$METHOD_POST, DingTalkConstant::$FORMAT_JSON);
        $req    = new OapiMessageCorpconversationAsyncsendV2Request;
        $req->setMsg($content);
        $req->setAgentId(Lxjxlib_Util_Conf::getAppEnvConf('login/dingtalk/agentId'));
        $req->setUseridList($ddUid);
        $req->setToAllUser(false);
        $resp = $client->execute($req, $accessToken, "https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2");
        if ($resp->errcode !== 0) {
            Bd_Log::warning('[发送工作通知失败：]' . $resp->errmsg);
            return false;
        }
        return true;
    }

    /**
     * 查询日程
     * @param $eventId
     * @param $accessToken
     * @return bool
     */
    public function calendarDetail($eventId, $accessToken)
    {
        $client = new DingTalkClient(DingTalkConstant::$CALL_TYPE_OAPI, DingTalkConstant::$METHOD_POST, DingTalkConstant::$FORMAT_JSON);
        $req    = new OapiCalendarV2EventDetailRequest;
        $req->setEventId($eventId);
        $req->setCalendarId('primary');
        $req->setAgentid(Lxjxlib_Util_Conf::getAppEnvConf('login/dingtalk/agentId'));
        $resp = $client->execute($req, $accessToken, "https://oapi.dingtalk.com/topapi/calendar/v2/event/detail");
        if ($resp->errcode != 0) {
            Bd_Log::warning('[获取钉钉日程详情失败[' . $eventId . ']：]' . $resp->errmsg);
            return false;
        }
        return $resp;
    }

    /**
     * 修改日程
     * @param $event
     * @param $accessToken
     * @return bool
     */
    public function calendarUpdate($event, $accessToken)
    {
        $client = new DingTalkClient(DingTalkConstant::$CALL_TYPE_OAPI, DingTalkConstant::$METHOD_POST, DingTalkConstant::$FORMAT_JSON);
        $req    = new OapiCalendarV2EventUpdateRequest;
        $req->setEvent($event);
        $req->setAgentid(Lxjxlib_Util_Conf::getAppEnvConf('login/dingtalk/agentId'));
        $resp = $client->execute($req, $accessToken, "https://oapi.dingtalk.com/topapi/calendar/v2/event/update");
        if ($resp->errcode != 0) {
            Bd_Log::warning('[更新钉钉日程失败：]' . $resp->errmsg);
            return false;
        }
        return $resp;
    }

    /**
     * 取消日程
     * @param $eventId
     * @param $accessToken
     * @return bool
     */
    public function calendarCancel($eventId, $accessToken)
    {
        $client = new DingTalkClient(DingTalkConstant::$CALL_TYPE_OAPI, DingTalkConstant::$METHOD_POST, DingTalkConstant::$FORMAT_JSON);
        $req    = new OapiCalendarV2EventCancelRequest();
        $req->setEventId($eventId);
        $req->setCalendarId('primary');
        $req->setAgentid(Lxjxlib_Util_Conf::getAppEnvConf('login/dingtalk/agentId'));
        $resp = $client->execute($req, $accessToken, "https://oapi.dingtalk.com/topapi/calendar/v2/event/cancel");
        if ($resp->errcode != 0) {
            Bd_Log::warning('[取消钉钉日程失败[' . $eventId . ']：]' . $resp->errmsg);
            return false;
        }
        return true;
    }

    /**
     * 查看员工信息
     * @param $userId
     * @param $accessToken
     * @return false|mixed|ResultSet|SimpleXMLElement
     */
    public function getUserInfoV2($userId, $accessToken)
    {
        $client = new DingTalkClient(DingTalkConstant::$CALL_TYPE_OAPI, DingTalkConstant::$METHOD_POST, DingTalkConstant::$FORMAT_JSON);
        $req    = new OapiV2UserGetRequest();
        $req->setUserid($userId);
        $resp = $client->execute($req, $accessToken, "https://oapi.dingtalk.com/topapi/v2/user/get");
        if ($resp->errcode != 0) {
            Bd_Log::warning('[获取用户信息失败[' . $userId . ']：]' . $resp->errmsg);
            return false;
        }
        return $resp;
    }
}