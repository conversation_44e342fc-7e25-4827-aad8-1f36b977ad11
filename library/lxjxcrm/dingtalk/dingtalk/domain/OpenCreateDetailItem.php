<?php

/**
 * 卡片的具体信息
 * <AUTHOR> create
 */
class OpenCreateDetailItem
{
	
	/** 
	 * 是否可以补卡
	 **/
	public $can_reissue_card;
	
	/** 
	 * 打卡周期
	 **/
	public $card_cycle;
	
	/** 
	 * 打卡频次的设置?"cardFrequency":[  ?????????? 1,//周天  ?????????? 2,//周一  ?????????? 3,//周二  ?????????? 4,//周三  ?????????? 5,//周四  ?????????? 6,//周五  ?????????? 7//周六  ???????]
	 **/
	public $card_frequency;
	
	/** 
	 * 用于记录每天打卡规则
	 **/
	public $card_rule_item_paramlist;
	
	/** 
	 * 班级Id列表
	 **/
	public $class_ids;
	
	/** 
	 * 班级名称列表（与ID 列表一一对应）
	 **/
	public $class_names;
	
	/** 
	 * 指定人发布打卡 人员列表{ ????????????"378537900":[ //班级 ????????????????{ ????????????????????"stuId":"15953231155863104", ????????????????????"stuName":"aa" ????????????????}... ???????????????? ????????????] ????????}
	 **/
	public $class_selected_students;
	
	/** 
	 * 打卡内容
	 **/
	public $content;
	
	/** 
	 * 打卡开始时间
	 **/
	public $effect_date;
	
	/** 
	 * 上传相册，图片，录音，盯盘的信息
	 **/
	public $medias;
	
	/** 
	 * 是否需要计量
	 **/
	public $need_metering;
	
	/** 
	 * 提醒的小时
	 **/
	public $remind_hour;
	
	/** 
	 * 提醒分钟设置
	 **/
	public $remind_minute;
	
	/** 
	 * 选择的角色
	 **/
	public $target_role;
	
	/** 
	 * 模板ID
	 **/
	public $template_id;
	
	/** 
	 * 打卡名称
	 **/
	public $title;
	
	/** 
	 * 计量单位
	 **/
	public $unit_of_measurement;	
}
?>