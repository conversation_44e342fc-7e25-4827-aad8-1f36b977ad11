<?php

/**
 * 返回的数据实体
 * <AUTHOR> create
 */
class DeployPackageDoModel
{
	
	/** 
	 * 应用ID
	 **/
	public $app_id;
	
	/** 
	 * 自动装载
	 **/
	public $auto_install;
	
	/** 
	 * 客户端
	 **/
	public $client_id;
	
	/** 
	 * 部署时间
	 **/
	public $deploy_time;
	
	/** 
	 * 描述
	 **/
	public $desc;
	
	/** 
	 * 英文名
	 **/
	public $english_name;
	
	/** 
	 * 扩展信息
	 **/
	public $extend_info;
	
	/** 
	 * fallbackBaseUrl
	 **/
	public $fallback_base_url;
	
	/** 
	 * 更新时间
	 **/
	public $gmt_create;
	
	/** 
	 * 更新时间
	 **/
	public $gmt_modified;
	
	/** 
	 * 灰度
	 **/
	public $gray;
	
	/** 
	 * 灰度码
	 **/
	public $gray_code;
	
	/** 
	 * 灰度时间
	 **/
	public $gray_time;
	
	/** 
	 * 图标
	 **/
	public $icon_url;
	
	/** 
	 * ID
	 **/
	public $id;
	
	/** 
	 * 租户ID
	 **/
	public $inst_id;
	
	/** 
	 * 是否删除
	 **/
	public $is_deleted;
	
	/** 
	 * 主URL
	 **/
	public $main_url;
	
	/** 
	 * 名称
	 **/
	public $name;
	
	/** 
	 * 在线
	 **/
	public $online;
	
	/** 
	 * 包ID
	 **/
	public $package_id;
	
	/** 
	 * 包地址
	 **/
	public $package_url;
	
	/** 
	 * 插件映射
	 **/
	public $plugin_refs;
	
	/** 
	 * 插件大小
	 **/
	public $plugin_size;
	
	/** 
	 * 插件 url
	 **/
	public $plugin_url;
	
	/** 
	 * 预发状态
	 **/
	public $pre;
	
	/** 
	 * 预发布时间
	 **/
	public $pre_time;
	
	/** 
	 * 预装载
	 **/
	public $preset;
	
	/** 
	 * 上线状态
	 **/
	public $prod;
	
	/** 
	 * 回滚自哪个部署包
	 **/
	public $rollback_from;
	
	/** 
	 * 大小
	 **/
	public $size;
	
	/** 
	 * 标语
	 **/
	public $slogan;
	
	/** 
	 * 版本
	 **/
	public $version;
	
	/** 
	 * vhost
	 **/
	public $vhost;
	
	/** 
	 * 发布窗口ID
	 **/
	public $window_id;	
}
?>