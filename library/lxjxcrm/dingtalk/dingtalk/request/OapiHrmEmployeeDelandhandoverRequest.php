<?php
/**
 * dingtalk API: dingtalk.oapi.hrm.employee.delandhandover request
 * 
 * <AUTHOR> create
 * @since 1.0, 2019.12.16
 */
class OapiHrmEmployeeDelandhandoverRequest
{
	/** 
	 * 确认离职对象
	 **/
	private $dismissionInfoWithHandOver;
	
	/** 
	 * 操作人userid
	 **/
	private $opUserid;
	
	private $apiParas = array();
	
	public function setDismissionInfoWithHandOver($dismissionInfoWithHandOver)
	{
		$this->dismissionInfoWithHandOver = $dismissionInfoWithHandOver;
		$this->apiParas["dismission_info_with_hand_over"] = $dismissionInfoWithHandOver;
	}

	public function getDismissionInfoWithHandOver()
	{
		return $this->dismissionInfoWithHandOver;
	}

	public function setOpUserid($opUserid)
	{
		$this->opUserid = $opUserid;
		$this->apiParas["op_userid"] = $opUserid;
	}

	public function getOpUserid()
	{
		return $this->opUserid;
	}

	public function getApiMethodName()
	{
		return "dingtalk.oapi.hrm.employee.delandhandover";
	}
	
	public function getApiParas()
	{
		return $this->apiParas;
	}
	
	public function check()
	{
		
		RequestCheckUtil::checkNotNull($this->opUserid,"opUserid");
	}
	
	public function putOtherTextParam($key, $value) {
		$this->apiParas[$key] = $value;
		$this->$key = $value;
	}
}
