<?php
/**
 * dingtalk API: dingtalk.oapi.user.delete request
 * 
 * <AUTHOR> create
 * @since 1.0, 2018.07.25
 */
class OapiUserDeleteRequest
{
	/** 
	 * 员工唯一标识ID（不可修改）
	 **/
	private $userid;
	
	private $apiParas = array();
	
	public function setUserid($userid)
	{
		$this->userid = $userid;
		$this->apiParas["userid"] = $userid;
	}

	public function getUserid()
	{
		return $this->userid;
	}

	public function getApiMethodName()
	{
		return "dingtalk.oapi.user.delete";
	}
	
	public function getApiParas()
	{
		return $this->apiParas;
	}
	
	public function check()
	{
		
	}
	
	public function putOtherTextParam($key, $value) {
		$this->apiParas[$key] = $value;
		$this->$key = $value;
	}
}
