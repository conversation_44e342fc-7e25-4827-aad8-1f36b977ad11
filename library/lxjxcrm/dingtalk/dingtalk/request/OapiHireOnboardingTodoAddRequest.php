<?php
/**
 * dingtalk API: dingtalk.oapi.hire.onboarding.todo.add request
 * 
 * <AUTHOR> create
 * @since 1.0, 2020.09.11
 */
class OapiHireOnboardingTodoAddRequest
{
	/** 
	 * userId
	 **/
	private $userid;
	
	private $apiParas = array();
	
	public function setUserid($userid)
	{
		$this->userid = $userid;
		$this->apiParas["userid"] = $userid;
	}

	public function getUserid()
	{
		return $this->userid;
	}

	public function getApiMethodName()
	{
		return "dingtalk.oapi.hire.onboarding.todo.add";
	}
	
	public function getApiParas()
	{
		return $this->apiParas;
	}
	
	public function check()
	{
		
		RequestCheckUtil::checkNotNull($this->userid,"userid");
	}
	
	public function putOtherTextParam($key, $value) {
		$this->apiParas[$key] = $value;
		$this->$key = $value;
	}
}
