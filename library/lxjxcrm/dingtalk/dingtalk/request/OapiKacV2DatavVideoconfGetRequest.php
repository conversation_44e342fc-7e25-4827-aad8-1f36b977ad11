<?php
/**
 * dingtalk API: dingtalk.oapi.kac.v2.datav.videoconf.get request
 * 
 * <AUTHOR> create
 * @since 1.0, 2020.07.13
 */
class OapiKacV2DatavVideoconfGetRequest
{
	/** 
	 * 请求对象类型
	 **/
	private $request;
	
	private $apiParas = array();
	
	public function setRequest($request)
	{
		$this->request = $request;
		$this->apiParas["request"] = $request;
	}

	public function getRequest()
	{
		return $this->request;
	}

	public function getApiMethodName()
	{
		return "dingtalk.oapi.kac.v2.datav.videoconf.get";
	}
	
	public function getApiParas()
	{
		return $this->apiParas;
	}
	
	public function check()
	{
		
	}
	
	public function putOtherTextParam($key, $value) {
		$this->apiParas[$key] = $value;
		$this->$key = $value;
	}
}
