<?php
/**
 * dingtalk API: dingtalk.oapi.ats.job.query request
 * 
 * <AUTHOR> create
 * @since 1.0, 2021.02.26
 */
class OapiAtsJobQueryRequest
{
	/** 
	 * 招聘业务标识
	 **/
	private $bizCode;
	
	/** 
	 * 分页游标，传空时默认为第一页
	 **/
	private $cursor;
	
	/** 
	 * 查询参数
	 **/
	private $queryParam;
	
	/** 
	 * 分页大小，最大200
	 **/
	private $size;
	
	private $apiParas = array();
	
	public function setBizCode($bizCode)
	{
		$this->bizCode = $bizCode;
		$this->apiParas["biz_code"] = $bizCode;
	}

	public function getBizCode()
	{
		return $this->bizCode;
	}

	public function setCursor($cursor)
	{
		$this->cursor = $cursor;
		$this->apiParas["cursor"] = $cursor;
	}

	public function getCursor()
	{
		return $this->cursor;
	}

	public function setQueryParam($queryParam)
	{
		$this->queryParam = $queryParam;
		$this->apiParas["query_param"] = $queryParam;
	}

	public function getQueryParam()
	{
		return $this->queryParam;
	}

	public function setSize($size)
	{
		$this->size = $size;
		$this->apiParas["size"] = $size;
	}

	public function getSize()
	{
		return $this->size;
	}

	public function getApiMethodName()
	{
		return "dingtalk.oapi.ats.job.query";
	}
	
	public function getApiParas()
	{
		return $this->apiParas;
	}
	
	public function check()
	{
		
		RequestCheckUtil::checkNotNull($this->bizCode,"bizCode");
		RequestCheckUtil::checkNotNull($this->size,"size");
		RequestCheckUtil::checkMaxValue($this->size,200,"size");
	}
	
	public function putOtherTextParam($key, $value) {
		$this->apiParas[$key] = $value;
		$this->$key = $value;
	}
}
