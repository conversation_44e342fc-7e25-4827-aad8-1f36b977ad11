<?php

/**
 * @befie   dao基类
 * @file    BaseDao.php
 * <AUTHOR>
 * @version 1.0
 * @since
 * @date    2020/03/04 16:28
 */
class Lxjxcrm_Common_BaseDao extends Hk_Common_BaseDao
{
    protected $_fields = null; // 查询字段
    protected $_pk = 'id'; // 主键

    /**
     * 获取反向的字段配置
     * @return array
     */
    public function getReversedFieldsMap()
    {
        $newMap = array_flip($this->arrFieldsMap);
        return is_array($newMap) ? $newMap : [];
    }

    /**
     * 获取正向的字段配置
     * @return array
     */
    public function getFieldsMap()
    {
        return $this->arrFieldsMap;
    }

    /**
     * 获取所有字段
     * @return array
     */
    public function getAllFields()
    {
        return array_keys($this->arrFieldsMap);
    }

    /**
     * 获取主键
     * @return string
     */
    public function getPrimaryKey()
    {
        return $this->_pk ? $this->_pk : 'id';
    }

    /**
     * 获取查询字段
     * @return array
     */
    public function getQueryFields()
    {
        $arrFields = $this->_fields ? $this->_fields : $this->getAllFields();
        return $arrFields;
    }

    /**
     * 查询带分页的列表数据
     * @param $pn
     * @param $rn
     * @param null $arrConds
     * @param null $arrFields
     * @param array $arrAppends
     * @return array
     */
    public function getPageList($pn, $rn, $arrConds = null, $arrFields = null, $arrAppends = [])
    {
        $result = [
            'total' => 0,
            'pn' => $pn,
            'rn' => $rn,
            'list' => [],
        ];

        $count = $this->getCntByConds($arrConds);
        if (empty($count)) {
            return $result;
        }

        $result['total'] = intval($count);

        if (null === $arrFields) {
            $arrFields = $this->getQueryFields();
        }

        $pn = $pn <= 0 ? 0 : $pn * $rn;
        $arrAppends[] = "limit $pn, $rn";
        $data = $this->getListByConds($arrConds, $arrFields, null, $arrAppends);
        if (!empty($data)) {
            $result['list'] = $data;
        }

        return $result;
    }
}