<?php

/**
 * @desc    bmp客户端
 * @file    Client.php
 * <AUTHOR>
 * @date    2021-01-20 14:08
 */
class Lxjxcrm_Bpm_Client
{
    private $request;
    const SUCCESS_CODE = 200;

    public function __construct()
    {
        $this->request = new Lxjxcrm_Bpm_Request();
    }

    /**
     * 创建审批流
     * @param $emailPrefix  :员工邮箱前缀
     * @param $applyItems   :申请事项
     * @param $processDefId :流程定义id
     * @param $processTitle :流程标题
     * @return array|bool|mixed|string|null
     */
    public function createProcess($emailPrefix, $processDefId, $processTitle, $applyItems)
    {
        # bpm配置
        $bpmConfig = Lxjxlib_Util_Conf::getAppEnvConf('bpm/config');
        if (empty($bpmConfig['processDefId'])) {
            throw new Lxjxlib_Const_Exception(Lxjxlib_Const_ExceptionCodes::ACTION_CONF_ERROR, 'BPM配置文件缺失');
        }
        $method = '/api/process/create';
        $params = [
            'userId'       => $emailPrefix,
            'applyItems'   => !is_string($applyItems) ? json_encode($applyItems, JSON_UNESCAPED_UNICODE) : $applyItems,
            'processDefId' => $processDefId,
            'processTitle' => $processTitle,
        ];

        $ret = $this->request->curlPost($method, $params);
        return $ret;
    }

    /**
     * 创建审批流
     * @param $emailPrefix
     * @param $processDefId
     * @param $schoolNameList
     * @param $applyItemsBatch
     * @return array|bool|mixed|null|string
     * @throws Lxjxlib_Const_Exception
     */
    public function createProcessBatch($emailPrefix, $processDefId, $schoolNameList, $applyItemsBatch)
    {
        # bpm配置
        $bpmConfig = Lxjxlib_Util_Conf::getAppEnvConf('bpm/config');
        if (empty($bpmConfig['processDefId'])) {
            throw new Lxjxlib_Const_Exception(Lxjxlib_Const_ExceptionCodes::ACTION_CONF_ERROR, 'BPM配置文件缺失');
        }
        $method = '/api/process/create';

        $paramsBatch = [];
        foreach ($applyItemsBatch as $k => $applyItems) {
            $params = [
                'userId'       => $emailPrefix,
                'applyItems'   => !is_string($applyItems) ? json_encode($applyItems, JSON_UNESCAPED_UNICODE) : $applyItems,
                'processDefId' => $processDefId,
                'processTitle' => $schoolNameList[$k] . '0元课提报',
            ];
            $paramsBatch[] = $params;
        }

        $ret = $this->request->curlPostBatch($method, $paramsBatch);
        return $ret;
    }



    /**
     * 获取流程参与人
     * @param $emailPrefix
     * @param $processInstId
     * @return array|bool|mixed|string|null
     */
    public function getOperatorList($emailPrefix, $processInstId)
    {
        $method = '/api/process/taskComment';
        $params = [
            'processInstId' => $processInstId,
            'uid'           => $emailPrefix,
        ];

        $ret = $this->request->curlGet($method, $params);
        return $ret;
    }
}