<?php

/**
 * @befie   高德地图API
 * @file    library/lxjxcrm/services/Amap.php
 * <AUTHOR>
 * @version 1.0
 * @since
 * @date    2020-08-31
 */
class Lxjxcrm_Service_Amap
{
    // const KEY = 'f3c7321dca62b33cda91c4c303f07fee'; 不知道是谁的
    const KEY = '28afa9b353309ef0c14729d39ba09f7d'; // serviceId = 273598 @王新宇02 提供

    public static function getGeoInfo($address, $city = '', $tmpApiKey = '')
    {
        $url = "https://restapi.amap.com/v3/geocode/geo";
        $reqParams = [
            'key' => ($tmpApiKey)? $tmpApiKey : self::KEY,
            'address' => $address,
        ];
        if ($city) {
            $reqParams['city'] = $city;
        }

        // 原来的代码是下面注释的这样。实际上并没有传入'URLENCODE'，因为callRequest的第四个参数是timeout，等价于下面的代码。
        // $ret = Lxjxlib_Util_Curl::callRequest($url, $reqParams, 'GET', 'URLENCODE');

        // 实际调用的代码
        $ret = Lxjxlib_Util_Curl::callRequest($url, $reqParams);

        return $ret;
    }
}
