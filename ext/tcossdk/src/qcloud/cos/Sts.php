<?php

namespace QCloud\Cos;

/**
 * wiki地址： https://cloud.tencent.com/document/product/598/33416
 * demo地址： https://github.com/tencentyun/qcloud-cos-sts-sdk/tree/master/php
 */

class Sts {
	

	// 允许操作的action
	static $allowActions = array(
		// 所有 action 请看文档 https://cloud.tencent.com/document/product/436/31923
        // 简单上传
        'name/cos:PutObject',
        'name/cos:PostObject',
        // 分片上传
        'name/cos:InitiateMultipartUpload',
        'name/cos:ListMultipartUploads',
        'name/cos:ListParts',
        'name/cos:UploadPart',
        'name/cos:CompleteMultipartUpload',
        // 下载
        'name/cos:GetObject'
	);

    /**
     * 获取临时key
     * @param  string  $secretId  
     * @param  string  $secretKey
     * @param  string  $bucket
     * @param  int     $appId
     * @param  string  $region
     * @param  int     $expire
     * @return boolean|array
     */	
	public function getTempKeys($secretId, $secretKey, $bucket, $appId, $region, $expire){
		$url    = 'https://sts.tencentcloudapi.com/';
        $domain = 'sts.tencentcloudapi.com';
        $action = 'GetFederationToken';
        $method = 'POST';

        $result = null;
        try{
            $policy = array(
                'version'=> '2.0',
                'statement'=> array(
                    array(
                        'action'=> self::$allowActions,
                        'effect'=> 'allow',
                        'principal'=> array('qcs'=> array('*')),
                        'resource'=> array(
                            'qcs::cos:' . $region . ':uid/' . $appId . ':' . ($bucket.'-'.$appId) . '/*'
                        )
                    )
                )
            );
            $policyStr = str_replace('\\/', '/', json_encode($policy));
            $params    = array(
                'SecretId'        => $secretId,
                'Timestamp'       => time(),
                'Nonce'           => rand(10000, 20000),
                'Action'          => $action,
                'DurationSeconds' => $expire,
                'Version'         => '2018-08-13',
                'Name'            => 'cos',
                'Region'          => $region,
                'Policy'          => urlencode($policyStr)
            );
            $params['Signature'] = $this->getSignature($params, $secretKey, $method, $domain);
            // 获取数据
            $$result = $this->curlSend($url, $params);
            return $$result;
        }catch(Exception $e){
            if($result == null){
                $result = "error: " . + $e->getMessage();
            }else{
                $result = json_encode($result);
            }
            return false;
        }
	}

    /**
     * curl发送数据包
     * @param  string  $url 
     * @param  array   $params 参数体
     * @return boolean|array
     */
	private function curlSend($url, $params){
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER,0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST,0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $this->json2str($params));
        $result = curl_exec($ch);
        if(curl_errno($ch)){
        	$result = curl_error($ch);
        }
        curl_close($ch);
        $result = json_decode($result, true);
        if (isset($result['Response'])) {
            $result = $result['Response'];
            if(isset($result['Error'])){
                return false;
                //throw new Exception("get cam failed");
            }
            $result['startTime'] = $result['ExpiredTime'] - $config['durationSeconds'];
        }
        $result = $this->backwardCompat($result);
        return $result;
	}

	// v2接口的key首字母小写，v3改成大写，此处做了向下兼容
    private function backwardCompat($result) {
        if(!is_array($result)){
            return false;
            //throw new Exception($result + " must be a array");
        }
        $compat = array();
        foreach ($result as $key => $value) {
            if(is_array($value)) {
                $compat[lcfirst($key)] = $this->backwardCompat($value);
            } elseif ($key == 'Token') {
                $compat['sessionToken'] = $value;
            } else {
                $compat[lcfirst($key)] = $value;
            }
        }
        return $compat;
    }

	// 计算临时密钥用的签名
    private function getSignature($opt, $key, $method, $domain) {
        $formatString = $method . $domain . '/?' . $this->json2str($opt, 1);
        $sign = hash_hmac('sha1', $formatString, $key);
        $sign = base64_encode($this->hex2bin($sign));
        return $sign;
    }

    // 临时密钥计算样例
    private function hex2bin($data) {
        $len = strlen($data);
        return pack("H" . $len, $data);
    }
    // obj 转 query string
    private function json2str($obj, $notEncode = false) {
        ksort($obj);
        $arr = array();
        if(!is_array($obj)){
            return false;
            //throw new Exception($obj + " must be a array");
        }
        foreach ($obj as $key => $val) {
            array_push($arr, $key . '=' . ($notEncode ? $val : rawurlencode($val)));
        }
        return join('&', $arr);
    }


}
