<?php


namespace QCloud\Cos;


/**
 * v5版本签名
 */
class SignatureV5 {


    private $accessKey;           // string: access key.
    private $secretKey;           // string: secret key.

    public function __construct($accessKey, $secretKey) {
        $this->accessKey = $accessKey;
        $this->secretKey = $secretKey;
        date_default_timezone_set("PRC");
    }


    public function createAuthorization($method, $host, $path, $expires = "+30 minutes") {
        $signTime = (string)(time() - 60) . ';' . (string)(strtotime($expires));
        $httpString = strtolower($method) . "\n" . urldecode($path) .
            "\n\nhost=" . $host. "\n";
        $sha1edHttpString = sha1($httpString);
        $stringToSign = "sha1\n$signTime\n$sha1edHttpString\n";
        $signKey = hash_hmac('sha1', $signTime, $this->secretKey);
        $signature = hash_hmac('sha1', $stringToSign, $signKey);
        $authorization = 'q-sign-algorithm=sha1&q-ak='. $this->accessKey .
            "&q-sign-time=$signTime&q-key-time=$signTime&q-header-list=host&q-url-param-list=&" .
            "q-signature=$signature";
        return $authorization;
    }

    public function createPresignedUrl($bucket, $host, $path, $expires = "+30 minutes") {
        if ($path[0] != '/') {
            $path = '/' . $path;
        }
        $authorization = $this->createAuthorization("GET", $host, $path, $expires);

        $uri = $host . $path;
        $uri = $uri."?sign=".urlencode($authorization);
        return $uri;
    }
}
