<?php

/**
 * Licensed to <PERSON><PERSON><PERSON> under one or more contributor license
 * agreements. See the NOTICE file distributed with this work for
 * additional information regarding copyright ownership.
 *
 * <PERSON><PERSON><PERSON> licenses this file to you under the Apache License,
 * Version 2.0 (the "License"); you may not use this file except in
 * compliance with the License. You may obtain a copy of the License at:
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * PHP Version 5
 *
 * @file     CAS/ProxyChain/Trusted.php
 * @category Authentication
 * @package  PhpCAS
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.apache.org/licenses/LICENSE-2.0  Apache License 2.0
 * @link     https://wiki.jasig.org/display/CASC/phpCAS
 */

/**
 * A proxy-chain definition that defines a chain up to a trusted proxy and
 * delegates the resposibility of validating the rest of the chain to that
 * trusted proxy.
 *
 * @class    CAS_ProxyChain_Trusted
 * @category Authentication
 * @package  PhpCAS
 * <AUTHOR> <<EMAIL>>
 * @license  http://www.apache.org/licenses/LICENSE-2.0  Apache License 2.0
 * @link     https://wiki.jasig.org/display/CASC/phpCAS
 */
class CAS_ProxyChain_Trusted
extends CAS_ProxyChain
implements CAS_ProxyChain_Interface
{

    /**
     * Validate the size of the the list as compared to our chain.
     *
     * @param array $list list of proxies
     *
     * @return bool
     */
    protected function isSizeValid (array $list)
    {
        return (sizeof($this->chain) <= sizeof($list));
    }

}
