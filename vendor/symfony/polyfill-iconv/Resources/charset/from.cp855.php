<?php

static $data = array (
  ' ' => ' ',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '	' => '	',
  '
' => '
',
  '' => '',
  '' => '',
  '
' => '
',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  ' ' => ' ',
  '!' => '!',
  '"' => '"',
  '#' => '#',
  '$' => '$',
  '%' => '%',
  '&' => '&',
  '\'' => '\'',
  '(' => '(',
  ')' => ')',
  '*' => '*',
  '+' => '+',
  ',' => ',',
  '-' => '-',
  '.' => '.',
  '/' => '/',
  0 => '0',
  1 => '1',
  2 => '2',
  3 => '3',
  4 => '4',
  5 => '5',
  6 => '6',
  7 => '7',
  8 => '8',
  9 => '9',
  ':' => ':',
  ';' => ';',
  '<' => '<',
  '=' => '=',
  '>' => '>',
  '?' => '?',
  '@' => '@',
  'A' => 'A',
  'B' => 'B',
  'C' => 'C',
  'D' => 'D',
  'E' => 'E',
  'F' => 'F',
  'G' => 'G',
  'H' => 'H',
  'I' => 'I',
  'J' => 'J',
  'K' => 'K',
  'L' => 'L',
  'M' => 'M',
  'N' => 'N',
  'O' => 'O',
  'P' => 'P',
  'Q' => 'Q',
  'R' => 'R',
  'S' => 'S',
  'T' => 'T',
  'U' => 'U',
  'V' => 'V',
  'W' => 'W',
  'X' => 'X',
  'Y' => 'Y',
  'Z' => 'Z',
  '[' => '[',
  '\\' => '\\',
  ']' => ']',
  '^' => '^',
  '_' => '_',
  '`' => '`',
  'a' => 'a',
  'b' => 'b',
  'c' => 'c',
  'd' => 'd',
  'e' => 'e',
  'f' => 'f',
  'g' => 'g',
  'h' => 'h',
  'i' => 'i',
  'j' => 'j',
  'k' => 'k',
  'l' => 'l',
  'm' => 'm',
  'n' => 'n',
  'o' => 'o',
  'p' => 'p',
  'q' => 'q',
  'r' => 'r',
  's' => 's',
  't' => 't',
  'u' => 'u',
  'v' => 'v',
  'w' => 'w',
  'x' => 'x',
  'y' => 'y',
  'z' => 'z',
  '{' => '{',
  '|' => '|',
  '}' => '}',
  '~' => '~',
  '' => '',
  '�' => 'ђ',
  '�' => 'Ђ',
  '�' => 'ѓ',
  '�' => 'Ѓ',
  '�' => 'ё',
  '�' => 'Ё',
  '�' => 'є',
  '�' => 'Є',
  '�' => 'ѕ',
  '�' => 'Ѕ',
  '�' => 'і',
  '�' => 'І',
  '�' => 'ї',
  '�' => 'Ї',
  '�' => 'ј',
  '�' => 'Ј',
  '�' => 'љ',
  '�' => 'Љ',
  '�' => 'њ',
  '�' => 'Њ',
  '�' => 'ћ',
  '�' => 'Ћ',
  '�' => 'ќ',
  '�' => 'Ќ',
  '�' => 'ў',
  '�' => 'Ў',
  '�' => 'џ',
  '�' => 'Џ',
  '�' => 'ю',
  '�' => 'Ю',
  '�' => 'ъ',
  '�' => 'Ъ',
  '�' => 'а',
  '�' => 'А',
  '�' => 'б',
  '�' => 'Б',
  '�' => 'ц',
  '�' => 'Ц',
  '�' => 'д',
  '�' => 'Д',
  '�' => 'е',
  '�' => 'Е',
  '�' => 'ф',
  '�' => 'Ф',
  '�' => 'г',
  '�' => 'Г',
  '�' => '«',
  '�' => '»',
  '�' => '░',
  '�' => '▒',
  '�' => '▓',
  '�' => '│',
  '�' => '┤',
  '�' => 'х',
  '�' => 'Х',
  '�' => 'и',
  '�' => 'И',
  '�' => '╣',
  '�' => '║',
  '�' => '╗',
  '�' => '╝',
  '�' => 'й',
  '�' => 'Й',
  '�' => '┐',
  '�' => '└',
  '�' => '┴',
  '�' => '┬',
  '�' => '├',
  '�' => '─',
  '�' => '┼',
  '�' => 'к',
  '�' => 'К',
  '�' => '╚',
  '�' => '╔',
  '�' => '╩',
  '�' => '╦',
  '�' => '╠',
  '�' => '═',
  '�' => '╬',
  '�' => '¤',
  '�' => 'л',
  '�' => 'Л',
  '�' => 'м',
  '�' => 'М',
  '�' => 'н',
  '�' => 'Н',
  '�' => 'о',
  '�' => 'О',
  '�' => 'п',
  '�' => '┘',
  '�' => '┌',
  '�' => '█',
  '�' => '▄',
  '�' => 'П',
  '�' => 'я',
  '�' => '▀',
  '�' => 'Я',
  '�' => 'р',
  '�' => 'Р',
  '�' => 'с',
  '�' => 'С',
  '�' => 'т',
  '�' => 'Т',
  '�' => 'у',
  '�' => 'У',
  '�' => 'ж',
  '�' => 'Ж',
  '�' => 'в',
  '�' => 'В',
  '�' => 'ь',
  '�' => 'Ь',
  '�' => '№',
  '�' => '­',
  '�' => 'ы',
  '�' => 'Ы',
  '�' => 'з',
  '�' => 'З',
  '�' => 'ш',
  '�' => 'Ш',
  '�' => 'э',
  '�' => 'Э',
  '�' => 'щ',
  '�' => 'Щ',
  '�' => 'ч',
  '�' => 'Ч',
  '�' => '§',
  '�' => '■',
  '�' => ' ',
);

$result =& $data;
unset($data);

return $result;
