<?php

static $data = array (
  ' ' => ' ',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '	',
  '' => '',
  '' => '',
  '' => '',
  '	' => '',
  '
' => '',
  '' => '',
  '' => '',
  '
' => '
',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  ' ' => '',
  '!' => '',
  '"' => '',
  '#' => '',
  '$' => '',
  '%' => '
',
  '&' => '',
  '\'' => '',
  '(' => '',
  ')' => '',
  '*' => '',
  '+' => '',
  ',' => '',
  '-' => '',
  '.' => '',
  '/' => '',
  0 => '',
  1 => '',
  2 => '',
  3 => '',
  4 => '',
  5 => '',
  6 => '',
  7 => '',
  8 => '',
  9 => '',
  ':' => '',
  ';' => '',
  '<' => '',
  '=' => '',
  '>' => '',
  '?' => '',
  '@' => ' ',
  'A' => ' ',
  'B' => 'â',
  'C' => 'ä',
  'D' => 'à',
  'E' => 'á',
  'F' => 'ã',
  'G' => 'å',
  'H' => 'ç',
  'I' => 'ñ',
  'J' => '¢',
  'K' => '.',
  'L' => '<',
  'M' => '(',
  'N' => '+',
  'O' => '|',
  'P' => '&',
  'Q' => 'é',
  'R' => 'ê',
  'S' => 'ë',
  'T' => 'è',
  'U' => 'í',
  'V' => 'î',
  'W' => 'ï',
  'X' => 'ì',
  'Y' => 'ß',
  'Z' => '!',
  '[' => '$',
  '\\' => '*',
  ']' => ')',
  '^' => ';',
  '_' => '¬',
  '`' => '-',
  'a' => '/',
  'b' => 'Â',
  'c' => 'Ä',
  'd' => 'À',
  'e' => 'Á',
  'f' => 'Ã',
  'g' => 'Å',
  'h' => 'Ç',
  'i' => 'Ñ',
  'j' => '¦',
  'k' => ',',
  'l' => '%',
  'm' => '_',
  'n' => '>',
  'o' => '?',
  'p' => 'ø',
  'q' => 'É',
  'r' => 'Ê',
  's' => 'Ë',
  't' => 'È',
  'u' => 'Í',
  'v' => 'Î',
  'w' => 'Ï',
  'x' => 'Ì',
  'y' => '`',
  'z' => ':',
  '{' => '#',
  '|' => '@',
  '}' => '\'',
  '~' => '=',
  '' => '"',
  '�' => 'Ø',
  '�' => 'a',
  '�' => 'b',
  '�' => 'c',
  '�' => 'd',
  '�' => 'e',
  '�' => 'f',
  '�' => 'g',
  '�' => 'h',
  '�' => 'i',
  '�' => '«',
  '�' => '»',
  '�' => 'ð',
  '�' => 'ý',
  '�' => 'þ',
  '�' => '±',
  '�' => '°',
  '�' => 'j',
  '�' => 'k',
  '�' => 'l',
  '�' => 'm',
  '�' => 'n',
  '�' => 'o',
  '�' => 'p',
  '�' => 'q',
  '�' => 'r',
  '�' => 'ª',
  '�' => 'º',
  '�' => 'æ',
  '�' => '¸',
  '�' => 'Æ',
  '�' => '¤',
  '�' => 'µ',
  '�' => '~',
  '�' => 's',
  '�' => 't',
  '�' => 'u',
  '�' => 'v',
  '�' => 'w',
  '�' => 'x',
  '�' => 'y',
  '�' => 'z',
  '�' => '¡',
  '�' => '¿',
  '�' => 'Ð',
  '�' => 'Ý',
  '�' => 'Þ',
  '�' => '®',
  '�' => '^',
  '�' => '£',
  '�' => '¥',
  '�' => '·',
  '�' => '©',
  '�' => '§',
  '�' => '¶',
  '�' => '¼',
  '�' => '½',
  '�' => '¾',
  '�' => '[',
  '�' => ']',
  '�' => '¯',
  '�' => '¨',
  '�' => '´',
  '�' => '×',
  '�' => '{',
  '�' => 'A',
  '�' => 'B',
  '�' => 'C',
  '�' => 'D',
  '�' => 'E',
  '�' => 'F',
  '�' => 'G',
  '�' => 'H',
  '�' => 'I',
  '�' => '­',
  '�' => 'ô',
  '�' => 'ö',
  '�' => 'ò',
  '�' => 'ó',
  '�' => 'õ',
  '�' => '}',
  '�' => 'J',
  '�' => 'K',
  '�' => 'L',
  '�' => 'M',
  '�' => 'N',
  '�' => 'O',
  '�' => 'P',
  '�' => 'Q',
  '�' => 'R',
  '�' => '¹',
  '�' => 'û',
  '�' => 'ü',
  '�' => 'ù',
  '�' => 'ú',
  '�' => 'ÿ',
  '�' => '\\',
  '�' => '÷',
  '�' => 'S',
  '�' => 'T',
  '�' => 'U',
  '�' => 'V',
  '�' => 'W',
  '�' => 'X',
  '�' => 'Y',
  '�' => 'Z',
  '�' => '²',
  '�' => 'Ô',
  '�' => 'Ö',
  '�' => 'Ò',
  '�' => 'Ó',
  '�' => 'Õ',
  '�' => '0',
  '�' => '1',
  '�' => '2',
  '�' => '3',
  '�' => '4',
  '�' => '5',
  '�' => '6',
  '�' => '7',
  '�' => '8',
  '�' => '9',
  '�' => '³',
  '�' => 'Û',
  '�' => 'Ü',
  '�' => 'Ù',
  '�' => 'Ú',
  '�' => '',
);

$result =& $data;
unset($data);

return $result;
