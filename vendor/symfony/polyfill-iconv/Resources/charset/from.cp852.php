<?php

static $data = array (
  ' ' => ' ',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '	' => '	',
  '
' => '
',
  '' => '',
  '' => '',
  '
' => '
',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  ' ' => ' ',
  '!' => '!',
  '"' => '"',
  '#' => '#',
  '$' => '$',
  '%' => '%',
  '&' => '&',
  '\'' => '\'',
  '(' => '(',
  ')' => ')',
  '*' => '*',
  '+' => '+',
  ',' => ',',
  '-' => '-',
  '.' => '.',
  '/' => '/',
  0 => '0',
  1 => '1',
  2 => '2',
  3 => '3',
  4 => '4',
  5 => '5',
  6 => '6',
  7 => '7',
  8 => '8',
  9 => '9',
  ':' => ':',
  ';' => ';',
  '<' => '<',
  '=' => '=',
  '>' => '>',
  '?' => '?',
  '@' => '@',
  'A' => 'A',
  'B' => 'B',
  'C' => 'C',
  'D' => 'D',
  'E' => 'E',
  'F' => 'F',
  'G' => 'G',
  'H' => 'H',
  'I' => 'I',
  'J' => 'J',
  'K' => 'K',
  'L' => 'L',
  'M' => 'M',
  'N' => 'N',
  'O' => 'O',
  'P' => 'P',
  'Q' => 'Q',
  'R' => 'R',
  'S' => 'S',
  'T' => 'T',
  'U' => 'U',
  'V' => 'V',
  'W' => 'W',
  'X' => 'X',
  'Y' => 'Y',
  'Z' => 'Z',
  '[' => '[',
  '\\' => '\\',
  ']' => ']',
  '^' => '^',
  '_' => '_',
  '`' => '`',
  'a' => 'a',
  'b' => 'b',
  'c' => 'c',
  'd' => 'd',
  'e' => 'e',
  'f' => 'f',
  'g' => 'g',
  'h' => 'h',
  'i' => 'i',
  'j' => 'j',
  'k' => 'k',
  'l' => 'l',
  'm' => 'm',
  'n' => 'n',
  'o' => 'o',
  'p' => 'p',
  'q' => 'q',
  'r' => 'r',
  's' => 's',
  't' => 't',
  'u' => 'u',
  'v' => 'v',
  'w' => 'w',
  'x' => 'x',
  'y' => 'y',
  'z' => 'z',
  '{' => '{',
  '|' => '|',
  '}' => '}',
  '~' => '~',
  '' => '',
  '�' => 'Ç',
  '�' => 'ü',
  '�' => 'é',
  '�' => 'â',
  '�' => 'ä',
  '�' => 'ů',
  '�' => 'ć',
  '�' => 'ç',
  '�' => 'ł',
  '�' => 'ë',
  '�' => 'Ő',
  '�' => 'ő',
  '�' => 'î',
  '�' => 'Ź',
  '�' => 'Ä',
  '�' => 'Ć',
  '�' => 'É',
  '�' => 'Ĺ',
  '�' => 'ĺ',
  '�' => 'ô',
  '�' => 'ö',
  '�' => 'Ľ',
  '�' => 'ľ',
  '�' => 'Ś',
  '�' => 'ś',
  '�' => 'Ö',
  '�' => 'Ü',
  '�' => 'Ť',
  '�' => 'ť',
  '�' => 'Ł',
  '�' => '×',
  '�' => 'č',
  '�' => 'á',
  '�' => 'í',
  '�' => 'ó',
  '�' => 'ú',
  '�' => 'Ą',
  '�' => 'ą',
  '�' => 'Ž',
  '�' => 'ž',
  '�' => 'Ę',
  '�' => 'ę',
  '�' => '¬',
  '�' => 'ź',
  '�' => 'Č',
  '�' => 'ş',
  '�' => '«',
  '�' => '»',
  '�' => '░',
  '�' => '▒',
  '�' => '▓',
  '�' => '│',
  '�' => '┤',
  '�' => 'Á',
  '�' => 'Â',
  '�' => 'Ě',
  '�' => 'Ş',
  '�' => '╣',
  '�' => '║',
  '�' => '╗',
  '�' => '╝',
  '�' => 'Ż',
  '�' => 'ż',
  '�' => '┐',
  '�' => '└',
  '�' => '┴',
  '�' => '┬',
  '�' => '├',
  '�' => '─',
  '�' => '┼',
  '�' => 'Ă',
  '�' => 'ă',
  '�' => '╚',
  '�' => '╔',
  '�' => '╩',
  '�' => '╦',
  '�' => '╠',
  '�' => '═',
  '�' => '╬',
  '�' => '¤',
  '�' => 'đ',
  '�' => 'Đ',
  '�' => 'Ď',
  '�' => 'Ë',
  '�' => 'ď',
  '�' => 'Ň',
  '�' => 'Í',
  '�' => 'Î',
  '�' => 'ě',
  '�' => '┘',
  '�' => '┌',
  '�' => '█',
  '�' => '▄',
  '�' => 'Ţ',
  '�' => 'Ů',
  '�' => '▀',
  '�' => 'Ó',
  '�' => 'ß',
  '�' => 'Ô',
  '�' => 'Ń',
  '�' => 'ń',
  '�' => 'ň',
  '�' => 'Š',
  '�' => 'š',
  '�' => 'Ŕ',
  '�' => 'Ú',
  '�' => 'ŕ',
  '�' => 'Ű',
  '�' => 'ý',
  '�' => 'Ý',
  '�' => 'ţ',
  '�' => '´',
  '�' => '­',
  '�' => '˝',
  '�' => '˛',
  '�' => 'ˇ',
  '�' => '˘',
  '�' => '§',
  '�' => '÷',
  '�' => '¸',
  '�' => '°',
  '�' => '¨',
  '�' => '˙',
  '�' => 'ű',
  '�' => 'Ř',
  '�' => 'ř',
  '�' => '■',
  '�' => ' ',
);

$result =& $data;
unset($data);

return $result;
