<?php

static $data = array (
  ' ' => ' ',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '	' => '	',
  '
' => '
',
  '' => '',
  '' => '',
  '
' => '
',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  ' ' => ' ',
  '!' => '!',
  '"' => '"',
  '#' => '#',
  '$' => '$',
  '%' => '%',
  '&' => '&',
  '\'' => '\'',
  '(' => '(',
  ')' => ')',
  '*' => '*',
  '+' => '+',
  ',' => ',',
  '-' => '-',
  '.' => '.',
  '/' => '/',
  0 => '0',
  1 => '1',
  2 => '2',
  3 => '3',
  4 => '4',
  5 => '5',
  6 => '6',
  7 => '7',
  8 => '8',
  9 => '9',
  ':' => ':',
  ';' => ';',
  '<' => '<',
  '=' => '=',
  '>' => '>',
  '?' => '?',
  '@' => '@',
  'A' => 'A',
  'B' => 'B',
  'C' => 'C',
  'D' => 'D',
  'E' => 'E',
  'F' => 'F',
  'G' => 'G',
  'H' => 'H',
  'I' => 'I',
  'J' => 'J',
  'K' => 'K',
  'L' => 'L',
  'M' => 'M',
  'N' => 'N',
  'O' => 'O',
  'P' => 'P',
  'Q' => 'Q',
  'R' => 'R',
  'S' => 'S',
  'T' => 'T',
  'U' => 'U',
  'V' => 'V',
  'W' => 'W',
  'X' => 'X',
  'Y' => 'Y',
  'Z' => 'Z',
  '[' => '[',
  '\\' => '\\',
  ']' => ']',
  '^' => '^',
  '_' => '_',
  '`' => '`',
  'a' => 'a',
  'b' => 'b',
  'c' => 'c',
  'd' => 'd',
  'e' => 'e',
  'f' => 'f',
  'g' => 'g',
  'h' => 'h',
  'i' => 'i',
  'j' => 'j',
  'k' => 'k',
  'l' => 'l',
  'm' => 'm',
  'n' => 'n',
  'o' => 'o',
  'p' => 'p',
  'q' => 'q',
  'r' => 'r',
  's' => 's',
  't' => 't',
  'u' => 'u',
  'v' => 'v',
  'w' => 'w',
  'x' => 'x',
  'y' => 'y',
  'z' => 'z',
  '{' => '{',
  '|' => '|',
  '}' => '}',
  '~' => '~',
  '' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => ' ',
  '�' => 'Ё',
  '�' => 'Ђ',
  '�' => 'Ѓ',
  '�' => 'Є',
  '�' => 'Ѕ',
  '�' => 'І',
  '�' => 'Ї',
  '�' => 'Ј',
  '�' => 'Љ',
  '�' => 'Њ',
  '�' => 'Ћ',
  '�' => 'Ќ',
  '�' => '­',
  '�' => 'Ў',
  '�' => 'Џ',
  '�' => 'А',
  '�' => 'Б',
  '�' => 'В',
  '�' => 'Г',
  '�' => 'Д',
  '�' => 'Е',
  '�' => 'Ж',
  '�' => 'З',
  '�' => 'И',
  '�' => 'Й',
  '�' => 'К',
  '�' => 'Л',
  '�' => 'М',
  '�' => 'Н',
  '�' => 'О',
  '�' => 'П',
  '�' => 'Р',
  '�' => 'С',
  '�' => 'Т',
  '�' => 'У',
  '�' => 'Ф',
  '�' => 'Х',
  '�' => 'Ц',
  '�' => 'Ч',
  '�' => 'Ш',
  '�' => 'Щ',
  '�' => 'Ъ',
  '�' => 'Ы',
  '�' => 'Ь',
  '�' => 'Э',
  '�' => 'Ю',
  '�' => 'Я',
  '�' => 'а',
  '�' => 'б',
  '�' => 'в',
  '�' => 'г',
  '�' => 'д',
  '�' => 'е',
  '�' => 'ж',
  '�' => 'з',
  '�' => 'и',
  '�' => 'й',
  '�' => 'к',
  '�' => 'л',
  '�' => 'м',
  '�' => 'н',
  '�' => 'о',
  '�' => 'п',
  '�' => 'р',
  '�' => 'с',
  '�' => 'т',
  '�' => 'у',
  '�' => 'ф',
  '�' => 'х',
  '�' => 'ц',
  '�' => 'ч',
  '�' => 'ш',
  '�' => 'щ',
  '�' => 'ъ',
  '�' => 'ы',
  '�' => 'ь',
  '�' => 'э',
  '�' => 'ю',
  '�' => 'я',
  '�' => '№',
  '�' => 'ё',
  '�' => 'ђ',
  '�' => 'ѓ',
  '�' => 'є',
  '�' => 'ѕ',
  '�' => 'і',
  '�' => 'ї',
  '�' => 'ј',
  '�' => 'љ',
  '�' => 'њ',
  '�' => 'ћ',
  '�' => 'ќ',
  '�' => '§',
  '�' => 'ў',
  '�' => 'џ',
);

$result =& $data;
unset($data);

return $result;
