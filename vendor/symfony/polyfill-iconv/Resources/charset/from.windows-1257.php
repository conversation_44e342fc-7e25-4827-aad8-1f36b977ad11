<?php

static $data = array (
  ' ' => ' ',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '	' => '	',
  '
' => '
',
  '' => '',
  '' => '',
  '
' => '
',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  ' ' => ' ',
  '!' => '!',
  '"' => '"',
  '#' => '#',
  '$' => '$',
  '%' => '%',
  '&' => '&',
  '\'' => '\'',
  '(' => '(',
  ')' => ')',
  '*' => '*',
  '+' => '+',
  ',' => ',',
  '-' => '-',
  '.' => '.',
  '/' => '/',
  0 => '0',
  1 => '1',
  2 => '2',
  3 => '3',
  4 => '4',
  5 => '5',
  6 => '6',
  7 => '7',
  8 => '8',
  9 => '9',
  ':' => ':',
  ';' => ';',
  '<' => '<',
  '=' => '=',
  '>' => '>',
  '?' => '?',
  '@' => '@',
  'A' => 'A',
  'B' => 'B',
  'C' => 'C',
  'D' => 'D',
  'E' => 'E',
  'F' => 'F',
  'G' => 'G',
  'H' => 'H',
  'I' => 'I',
  'J' => 'J',
  'K' => 'K',
  'L' => 'L',
  'M' => 'M',
  'N' => 'N',
  'O' => 'O',
  'P' => 'P',
  'Q' => 'Q',
  'R' => 'R',
  'S' => 'S',
  'T' => 'T',
  'U' => 'U',
  'V' => 'V',
  'W' => 'W',
  'X' => 'X',
  'Y' => 'Y',
  'Z' => 'Z',
  '[' => '[',
  '\\' => '\\',
  ']' => ']',
  '^' => '^',
  '_' => '_',
  '`' => '`',
  'a' => 'a',
  'b' => 'b',
  'c' => 'c',
  'd' => 'd',
  'e' => 'e',
  'f' => 'f',
  'g' => 'g',
  'h' => 'h',
  'i' => 'i',
  'j' => 'j',
  'k' => 'k',
  'l' => 'l',
  'm' => 'm',
  'n' => 'n',
  'o' => 'o',
  'p' => 'p',
  'q' => 'q',
  'r' => 'r',
  's' => 's',
  't' => 't',
  'u' => 'u',
  'v' => 'v',
  'w' => 'w',
  'x' => 'x',
  'y' => 'y',
  'z' => 'z',
  '{' => '{',
  '|' => '|',
  '}' => '}',
  '~' => '~',
  '' => '',
  '�' => '€',
  '�' => '‚',
  '�' => '„',
  '�' => '…',
  '�' => '†',
  '�' => '‡',
  '�' => '‰',
  '�' => '‹',
  '�' => '¨',
  '�' => 'ˇ',
  '�' => '¸',
  '�' => '‘',
  '�' => '’',
  '�' => '“',
  '�' => '”',
  '�' => '•',
  '�' => '–',
  '�' => '—',
  '�' => '™',
  '�' => '›',
  '�' => '¯',
  '�' => '˛',
  '�' => ' ',
  '�' => '¢',
  '�' => '£',
  '�' => '¤',
  '�' => '¦',
  '�' => '§',
  '�' => 'Ø',
  '�' => '©',
  '�' => 'Ŗ',
  '�' => '«',
  '�' => '¬',
  '�' => '­',
  '�' => '®',
  '�' => 'Æ',
  '�' => '°',
  '�' => '±',
  '�' => '²',
  '�' => '³',
  '�' => '´',
  '�' => 'µ',
  '�' => '¶',
  '�' => '·',
  '�' => 'ø',
  '�' => '¹',
  '�' => 'ŗ',
  '�' => '»',
  '�' => '¼',
  '�' => '½',
  '�' => '¾',
  '�' => 'æ',
  '�' => 'Ą',
  '�' => 'Į',
  '�' => 'Ā',
  '�' => 'Ć',
  '�' => 'Ä',
  '�' => 'Å',
  '�' => 'Ę',
  '�' => 'Ē',
  '�' => 'Č',
  '�' => 'É',
  '�' => 'Ź',
  '�' => 'Ė',
  '�' => 'Ģ',
  '�' => 'Ķ',
  '�' => 'Ī',
  '�' => 'Ļ',
  '�' => 'Š',
  '�' => 'Ń',
  '�' => 'Ņ',
  '�' => 'Ó',
  '�' => 'Ō',
  '�' => 'Õ',
  '�' => 'Ö',
  '�' => '×',
  '�' => 'Ų',
  '�' => 'Ł',
  '�' => 'Ś',
  '�' => 'Ū',
  '�' => 'Ü',
  '�' => 'Ż',
  '�' => 'Ž',
  '�' => 'ß',
  '�' => 'ą',
  '�' => 'į',
  '�' => 'ā',
  '�' => 'ć',
  '�' => 'ä',
  '�' => 'å',
  '�' => 'ę',
  '�' => 'ē',
  '�' => 'č',
  '�' => 'é',
  '�' => 'ź',
  '�' => 'ė',
  '�' => 'ģ',
  '�' => 'ķ',
  '�' => 'ī',
  '�' => 'ļ',
  '�' => 'š',
  '�' => 'ń',
  '�' => 'ņ',
  '�' => 'ó',
  '�' => 'ō',
  '�' => 'õ',
  '�' => 'ö',
  '�' => '÷',
  '�' => 'ų',
  '�' => 'ł',
  '�' => 'ś',
  '�' => 'ū',
  '�' => 'ü',
  '�' => 'ż',
  '�' => 'ž',
  '�' => '˙',
);

$result =& $data;
unset($data);

return $result;
