<?php

static $data = array (
  ' ' => ' ',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '	' => '	',
  '
' => '
',
  '' => '',
  '' => '',
  '
' => '
',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  ' ' => ' ',
  '!' => '!',
  '"' => '"',
  '#' => '#',
  '$' => '$',
  '%' => '٪',
  '&' => '&',
  '\'' => '\'',
  '(' => '(',
  ')' => ')',
  '*' => '*',
  '+' => '+',
  ',' => ',',
  '-' => '-',
  '.' => '.',
  '/' => '/',
  0 => '0',
  1 => '1',
  2 => '2',
  3 => '3',
  4 => '4',
  5 => '5',
  6 => '6',
  7 => '7',
  8 => '8',
  9 => '9',
  ':' => ':',
  ';' => ';',
  '<' => '<',
  '=' => '=',
  '>' => '>',
  '?' => '?',
  '@' => '@',
  'A' => 'A',
  'B' => 'B',
  'C' => 'C',
  'D' => 'D',
  'E' => 'E',
  'F' => 'F',
  'G' => 'G',
  'H' => 'H',
  'I' => 'I',
  'J' => 'J',
  'K' => 'K',
  'L' => 'L',
  'M' => 'M',
  'N' => 'N',
  'O' => 'O',
  'P' => 'P',
  'Q' => 'Q',
  'R' => 'R',
  'S' => 'S',
  'T' => 'T',
  'U' => 'U',
  'V' => 'V',
  'W' => 'W',
  'X' => 'X',
  'Y' => 'Y',
  'Z' => 'Z',
  '[' => '[',
  '\\' => '\\',
  ']' => ']',
  '^' => '^',
  '_' => '_',
  '`' => '`',
  'a' => 'a',
  'b' => 'b',
  'c' => 'c',
  'd' => 'd',
  'e' => 'e',
  'f' => 'f',
  'g' => 'g',
  'h' => 'h',
  'i' => 'i',
  'j' => 'j',
  'k' => 'k',
  'l' => 'l',
  'm' => 'm',
  'n' => 'n',
  'o' => 'o',
  'p' => 'p',
  'q' => 'q',
  'r' => 'r',
  's' => 's',
  't' => 't',
  'u' => 'u',
  'v' => 'v',
  'w' => 'w',
  'x' => 'x',
  'y' => 'y',
  'z' => 'z',
  '{' => '{',
  '|' => '|',
  '}' => '}',
  '~' => '~',
  '' => '',
  '�' => '°',
  '�' => '·',
  '�' => '∙',
  '�' => '√',
  '�' => '▒',
  '�' => '─',
  '�' => '│',
  '�' => '┼',
  '�' => '┤',
  '�' => '┬',
  '�' => '├',
  '�' => '┴',
  '�' => '┐',
  '�' => '┌',
  '�' => '└',
  '�' => '┘',
  '�' => 'β',
  '�' => '∞',
  '�' => 'φ',
  '�' => '±',
  '�' => '½',
  '�' => '¼',
  '�' => '≈',
  '�' => '«',
  '�' => '»',
  '�' => 'ﻷ',
  '�' => 'ﻸ',
  '�' => 'ﻻ',
  '�' => 'ﻼ',
  '�' => ' ',
  '�' => '­',
  '�' => 'ﺂ',
  '�' => '£',
  '�' => '¤',
  '�' => 'ﺄ',
  '�' => 'ﺎ',
  '�' => 'ﺏ',
  '�' => 'ﺕ',
  '�' => 'ﺙ',
  '�' => '،',
  '�' => 'ﺝ',
  '�' => 'ﺡ',
  '�' => 'ﺥ',
  '�' => '٠',
  '�' => '١',
  '�' => '٢',
  '�' => '٣',
  '�' => '٤',
  '�' => '٥',
  '�' => '٦',
  '�' => '٧',
  '�' => '٨',
  '�' => '٩',
  '�' => 'ﻑ',
  '�' => '؛',
  '�' => 'ﺱ',
  '�' => 'ﺵ',
  '�' => 'ﺹ',
  '�' => '؟',
  '�' => '¢',
  '�' => 'ﺀ',
  '�' => 'ﺁ',
  '�' => 'ﺃ',
  '�' => 'ﺅ',
  '�' => 'ﻊ',
  '�' => 'ﺋ',
  '�' => 'ﺍ',
  '�' => 'ﺑ',
  '�' => 'ﺓ',
  '�' => 'ﺗ',
  '�' => 'ﺛ',
  '�' => 'ﺟ',
  '�' => 'ﺣ',
  '�' => 'ﺧ',
  '�' => 'ﺩ',
  '�' => 'ﺫ',
  '�' => 'ﺭ',
  '�' => 'ﺯ',
  '�' => 'ﺳ',
  '�' => 'ﺷ',
  '�' => 'ﺻ',
  '�' => 'ﺿ',
  '�' => 'ﻁ',
  '�' => 'ﻅ',
  '�' => 'ﻋ',
  '�' => 'ﻏ',
  '�' => '¦',
  '�' => '¬',
  '�' => '÷',
  '�' => '×',
  '�' => 'ﻉ',
  '�' => 'ـ',
  '�' => 'ﻓ',
  '�' => 'ﻗ',
  '�' => 'ﻛ',
  '�' => 'ﻟ',
  '�' => 'ﻣ',
  '�' => 'ﻧ',
  '�' => 'ﻫ',
  '�' => 'ﻭ',
  '�' => 'ﻯ',
  '�' => 'ﻳ',
  '�' => 'ﺽ',
  '�' => 'ﻌ',
  '�' => 'ﻎ',
  '�' => 'ﻍ',
  '�' => 'ﻡ',
  '�' => 'ﹽ',
  '�' => 'ّ',
  '�' => 'ﻥ',
  '�' => 'ﻩ',
  '�' => 'ﻬ',
  '�' => 'ﻰ',
  '�' => 'ﻲ',
  '�' => 'ﻐ',
  '�' => 'ﻕ',
  '�' => 'ﻵ',
  '�' => 'ﻶ',
  '�' => 'ﻝ',
  '�' => 'ﻙ',
  '�' => 'ﻱ',
  '�' => '■',
);

$result =& $data;
unset($data);

return $result;
