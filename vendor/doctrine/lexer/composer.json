{"name": "doctrine/lexer", "type": "library", "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "keywords": ["php", "parser", "lexer", "annotations", "doc<PERSON>"], "homepage": "https://www.doctrine-project.org/projects/lexer.html", "license": "MIT", "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "require": {"php": "^7.2"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpstan/phpstan": "^0.11.8", "phpunit/phpunit": "^8.2"}, "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "lib/Doctrine/Common/Lexer"}}, "autoload-dev": {"psr-4": {"Doctrine\\Tests\\": "tests/Doctrine"}}, "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "config": {"sort-packages": true}}