<?php

/**
 * brief: 任务相关
 * @author: <EMAIL>
 */
class Hkzb_Const_CorrectionTask {

    /**
     * 通用状态: 1未开始；2未完成; 3已完成; 4已过期;
     */
    const COMMON_STATUS_LOCKED = 1;
    const COMMON_STATUS_UNDO = 2;
    const COMMON_STATUS_DONE = 3;
    const COMMON_STATUS_EXPIRED = 4;

    private static $Ins;
    private $serverTime;
    private $courseId2taskList; // courseId => [bindId_taskType => [starCnt, maxStar, startTime, stopTime]

    protected function __construct() {
    }

    /**
     * 单例
     * @return self
     */
    public static function getInstance() {
        if (!self::$Ins) {
            self::$Ins = new self();
        }

        return self::$Ins;
    }

    /**
     * 初始化
     * @param $input
     * @throws Hk_Util_Exception
     */
    public function init($input) {
        $this->serverTime = $input['serverTime'];
        $this->courseId2taskList = $input['courseId2taskList'];
        foreach ($this->courseId2taskList as $courseId => &$taskList) {
            foreach ($taskList as $key => &$task) {
                $arr = explode('_', $key);
                if (2 != count($arr)) {
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::SYSTEM_CRAZY, "invalid key of $key");
                }
                $mustKeys = ['starCnt', 'maxStar', 'startTime', 'stopTime'];
                foreach ($mustKeys as $key) {
                    if (!isset($task[$key])) {
                        throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::SYSTEM_CRAZY, "input course key $key invalid");
                    }
                }
                $bindId = intval($arr[0]);
                $taskType = intval($arr[1]);
                $isDone = $task['starCnt'] > 0; // 默认有星星就算完成
                if (10030 == $taskType) { // 巩固练习数据干预
                    $homeworkType = Hkzb_Const_CorrectionHomework::getInstance()->getHomeworkType($courseId);
                    $info = Hkzb_Const_CorrectionHomework::getInstance()->getHomeworkTimeInfo($courseId, $bindId);
                    $task['startTime'] = $info['startTime'];
                    $task['stopTime'] = $info['stopTime'];
                    if (Hkzb_Const_CorrectionHomework::HOMEWORK_TYPE_CORRECTION == $homeworkType) {
                        $task['stopTime'] = $info['correction']['first'];
                        $maxCorrectionCnt = Hkzb_Const_CorrectionHomework::getInstance()->getHomeworkCorrectMaxCnt($courseId);
                        if ($maxCorrectionCnt > 0) { // 订正次数>0
                            $isDone = $task['starCnt'] > 1; // 订正需要2颗星才算完成
                            if ($task['starCnt'] > 0) { // 已做，结束时间需要变
                                $task['stopTime'] = $info['correction']['redo'];
                            }
                        }
                    } else if (Hkzb_Const_CorrectionHomework::HOMEWORK_TYPE_NORMAL != $homeworkType) { // 只要是非普通巩固练习，都有二次完成逻辑
                        $task['stopTime'] = $info['correction']['first'];
                        $isDone = $task['starCnt'] > 1; // 需要2颗星才算完成
                        if ($task['starCnt'] > 0) { // 已做，结束时间需要变
                            $task['stopTime'] = $info['correction']['redo'];
                        }
                    }
                }
                $task['isDone'] = intval($isDone);
            }
        }
    }

    /**
     * 获取课程未完成的task列表
     * @return array
     */
    public function getCourseUndoTaskList() {
        $undoTaskList = array();
        foreach ($this->courseId2taskList as $courseId => $taskList) {
            $undoTaskList[$courseId] = array();
            foreach ($taskList as $key => $task) {
                if ($this->serverTime < $task['startTime']) {
                    continue;
                }
                if ($this->serverTime > $task['stopTime']) {
                    continue;
                }
                if ($task['isDone']) {
                    continue;
                }
                $undoTaskList[$courseId][$key] = $task;
            }
        }

        return $undoTaskList;
    }

    /**
     * 某章节是否有指定任务
     * @param $courseId
     * @param $lessonId
     * @param $taskType
     * @return bool
     */
    public function isHaveLessonTask($courseId, $lessonId, $taskType) {
        $key = "{$lessonId}_{$taskType}";
        return isset($this->courseId2taskList[$courseId]) && isset($this->courseId2taskList[$courseId][$key]);
    }

    /**
     * 获取任务状态 1未解锁；2未完成；3已完成；4已过期
     * @param $courseId
     * @param $lessonId
     * @param $taskType
     * @return int
     * @throws Hk_Util_Exception
     */
    public function getTaskStatus($courseId, $lessonId, $taskType) {
        $key = "{$lessonId}_{$taskType}";
        $task = $this->courseId2taskList[$courseId][$key];
        if (empty($task)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::SYSTEM_CRAZY, "task not found: $courseId $key $taskType");
        }

        if ($this->serverTime < $task['startTime']) {
            $status = self::COMMON_STATUS_LOCKED;
        } else if ($task['isDone']) {
            $status = self::COMMON_STATUS_DONE;
        } else if ($this->serverTime > $task['stopTime']) {
            $status = self::COMMON_STATUS_EXPIRED;
        } else {
            $status = self::COMMON_STATUS_UNDO;
        }
        return $status;
    }


}
