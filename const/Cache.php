<?php

/**
 * @befie   缓存相关常量
 * @file    const/Cache.php
 * <AUTHOR>
 * @version 1.0
 * @date    2019-09-19
 */
class Lxjxlib_Const_Cache
{
    // ----------------- codis start ----------------
    // redis集群名字

    const REDIS_NAME_PARENT = 'parent';   // codis-parent
    const REDIS_NAME_LXJX = 'laxinjinxiao';      // codis-laxinjinxiao
    const REDIS_NAME_LXJX_USER = 'laxinjinxiao';  // codis-laxinjinxiao

    // redis定义的key
    // 用户
    const RK_USER_INFO = 'lxjxuser_userinfo_%d'; // 用户信息
    const WX_USER_UID = 'lxjx-school-wxuser-%s-%s'; // tblWxUser uid缓存(appid,openid)

    // 微信
    const RK_WX_ACCESSTOKEN = 'lxjx-wx-at-%s-%s';
    const RK_WX_JSTICKET = 'lxjx-wx-jsticket-%s-%s';
    const WX_ACCESSTOKEN_LOCK = 'wx-accesstoken-lock-%s-%s'; //获取微信access_token的锁
    const WX_TEMPLATE_LIST = 'lxjx-wx-template-list-%s'; //微信模版列表
    const WX_SUBSCRIBE_LIST = 'lxjx-wx-subscribe-list-%s'; //微信订阅模版列表

    const RK_QYWX_JSTICKET = 'lxjx-qywx-jsticket-%s-%s';     //获取企微jsticket
    const RK_QYWX_AGENT_JSTICKET = 'lxjx-qywx-agent-jsticket-%s-%s'; //获取企微agent jsticket

    // 缓存时间
    const RK_EXPIRE_USERINFO   = 86400;   // 用户信息，缓存1天

    // crm学校
    const CACHE_CRM_SCHOOL_COUNTY   = 'lxjx_crm_school_county_'; // 区县-学校

    // ----------------- codis end ----------------
}