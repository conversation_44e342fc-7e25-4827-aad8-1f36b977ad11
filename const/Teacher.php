<?php
/**
 * Created by PhpStorm.
 * User: wang<PERSON><PERSON>@zuoyebang.com
 * Date: 2019/1/28
 * Time: 11:37 AM
 */

class Zb_Const_Teacher
{
    //状态
    const STATUS_OK      = 0; //未删除
    const STATUS_DELETED = 1; //已删除
    static $STATUS_ARRAY = array(
        self::STATUS_OK      => '未删除',
        self::STATUS_DELETED => '已删除',
    );

    //老师职责
    const DUTY_TEACHER   = 1; //主讲老师
    const DUTY_ASSISTANT = 2; //辅导老师
    static $DUTY_ARRAY   = array(
        self::DUTY_TEACHER  => '主讲老师',
        self::DUTY_ASSISTANT=> '辅导老师',
    );

    const RECOMMEND_YES   = 1; //推荐老师
    const RECOMMEND_NO    = 0; //不是推荐

    const XINGZHI_FULL      = 0; //全职教研
    const XINGZHI_PART      = 1; //兼职
    const XINGZHI_INNER     = 2; //内部
    const XINGZHI_ZHUAN     = 3; //全职主讲
    static $XINGZHI_ARRAY = array(
        self::XINGZHI_FULL  => '全职教研',
        self::XINGZHI_PART  => '兼职老师',
        self::XINGZHI_INNER => '内部',
        self::XINGZHI_ZHUAN => '全职主讲',
    );

    //老师所属类型
    const TYPE_PRIVATE = 0;
    const TYPE_PUBLIC  = 1;
    const TYPE_PRIVATE_LONG  = 2;
    const TYPE_PARENT_COURSE = 3;
    const TYPE_NONU = 10;
    static $TYPE_ARRAY = array(
        self::TYPE_PRIVATE              => '专题课',
        self::TYPE_PUBLIC               => '公开课',
        self::TYPE_PRIVATE_LONG         => '班课',
        self::TYPE_PARENT_COURSE        => '家长课',
        self::TYPE_NONU                  => '未分组',
    );

    //归属
    const BELONG_IN = 1;
    const BELONG_OUT = 2;
    static $BELONG_ARRAY = array(
        self::BELONG_IN => '自有',
        self::BELONG_OUT => '外部'
    );
}