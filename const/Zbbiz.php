<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   Zbbiz.php
 * <AUTHOR>
 * @brief
 **/

class Zb_Const_Zbbiz {
    const PAGE_TYPE_DETAIL       = 'detail';
    const PAGE_TYPE_LIST         = 'list';
    const PAGE_TYPE_CART         = 'cart';
    const PAGE_TYPE_PAYDETAIL    = 'paydetail';
    const PAGE_TYPE_PAY          = 'pay';
    const PAGE_TYPE_RECOMMEND    = 'recommend';
    const PAGE_TYPE_OTHER        = 'other';
    //canbuy
    const SKU_CANBUY_NO     = 0;   // 课购买
    const SKU_CANBUY_YES    = 1;   // 课购买
    // 单品是否可支付
    public static $skuCanbuyStatus = array(
        self::SKU_CANBUY_NO  => '不可支付',
        self::SKU_CANBUY_YES =>'可支付',
    );
    //saletype
    const SKU_SALE_TYPE_SALE    = 1;
    const SKU_SALE_TYPE_PRESALE = 2;
    public static $skuSaleType = array(
        self::SKU_SALE_TYPE_SALE    => '全额支付',
        self::SKU_SALE_TYPE_PRESALE => '预售',
    );
    //canusecoupon
    const SKU_CANUSECOUPON_YES = 1;
    const SKU_CANUSECOUPON_NO  = 2;
    public static $skuCanuseCouponMap = array(
        self::SKU_CANBUY_YES => '可用',
        self::SKU_CANBUY_NO => '不可用',
    );
    //gifttype
    const GIFT_TYPE_SKU = 'sku';
    const GIFT_TYPE_COUPON = 'coupon';
    public static $giftTypeMap = array(
        self::GIFT_TYPE_COUPON => '优惠券',
        self::GIFT_TYPE_SKU    => 'sku',
    );

    //bizType
    const BIZ_TYPE_MAIN = 1;//联报 多课 多科策略
    const BIZ_TYPE_FIRSTTRADE = 2; //首单立减
    const BIZ_TYPE_PRESENT = 3; //买就送
    public static $bizTypeMap = array(
        self::BIZ_TYPE_MAIN => '联报、多科...策略',
        self::BIZ_TYPE_FIRSTTRADE => '首单立减',
        self::BIZ_TYPE_PRESENT => '买就送',
    );
    //subbiztyp1表示指定商品联报折扣，2指定范围联报折扣，3：表示指定商品折扣 4表示商品范围折扣
    const SUB_BIZ_TYPE_COMB_APPOINT = 1;//指定商品联报折扣
    const SUB_BIZ_TYPE_COMB_SELECT = 2;//指定范围联报折扣
    const SUB_BIZ_TYPE_SELECT_APPOINT = 3;//表示指定商品折扣
    const SUB_BIZ_TYPE_SELECT_SELECT = 4;//表示商品范围折扣

    //cannotbyresaontype
    const BIZ_CANNOTBUY_REASONTYPE_PREGROUP = 4;
    public static $canNotBuyReasonTypeMap = array(
        self::BIZ_CANNOTBUY_REASONTYPE_PREGROUP => '已预约过同续报课程',
    );

    #上架策略 显示隐藏
    const ONLINE_SHOW_STATUS = 1;
    const ONLINE_HIDE_STATUS = 2;
    public static $onlineIsShowMap = [
        self::ONLINE_SHOW_STATUS => '上架显示',
        self::ONLINE_HIDE_STATUS => '上架隐藏'
    ];

    #可见用户范围业务渠道channel隐藏
    const ONLINE_CHANNEL_LESSION = 1; //一课列表页
    const ONLINE_CHANNEL_INTEGRAL  = 2; //积分商城列表页

    //上架渠道关系
    static public $channelMap = array(
        self::ONLINE_CHANNEL_LESSION   => "一课列表页" ,
        self::ONLINE_CHANNEL_INTEGRAL   => "积分商城列表页" ,
    );

    const NOT_LEC_CHANNEL = 1;//非lec渠道
    const IS_LEC_CHANNEL  = 2;//lec渠道
}

