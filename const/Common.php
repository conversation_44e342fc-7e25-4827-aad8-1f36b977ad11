<?php

/**
 * @befie   公共常量
 * @file    const/Command.php
 * <AUTHOR>
 * @version 1.0
 * @date    2019-09-19
 */
class Lxjxlib_Const_Common
{
    // nmq topic
    const NMQ_TOPIC_LXJX_COMM  = 'lxjxcomm';
    const NMQ_TOPIC_LXJX_LXJXSCH = 'lxjxsch'; // lxjxsch

    const IMG_UPLOAD_MAX_SIZE = 2097152; // 上传图片限制

    // 公共缓存时间
    const EXPIRE_YEAR       = 31536000; // 1年
    const EXPIRE_MONTH      = 2592000; // 1月
    const EXPIRE_WEEK       = 604800; // 1周
    const EXPIRE_THREE_DAYS = 259200; // 3天
    const EXPIRE_DAY        = 86400; // 1天
    const EXPIRE_TWELVE     = 43200; // 12小时
    const EXPIRE_FOUR_HOUT  = 14400; // 4小时
    const EXPIRE_THOUR      = 7200; // 2小时
    const EXPIRE_HOUR       = 3600; // 1小时
    const EXPIRE_HALF_HOUR  = 1800; // 30分钟
    const EXPIRE_QUARTER    = 900; // 15分钟
    const EXPIRE_ONEMINUTE  = 60; // 1分钟

    // 数据布尔状态：0-否 1-是
    const DB_BOOL_STATUS_NO  = 0;
    const DB_BOOL_STATUS_YES = 1;

    // 数据状态：1正常，2删除
    const DB_STATUS_OK     = 1;
    const DB_STATUS_DELETE = 2;

    // Bos配置
    const BOS_HOST_LXJX   = 'https://lxjx-static.zybang.com';
    const BOS_BUCKET_LXJX = 'zyb-lxjx';

    // COS域名
    const COS_DOMAIN_LXJXSCHOOL = "zyb-laxin-lxjxschool-1253445850.cos.ap-beijing.myqcloud.com";
    const COS_DOMAIN_LXJX       = "zyb-laxin-lxjx-1253445850.cos.ap-beijing.myqcloud.com";
    // COS上传目录类型 1-默认，web直传目录
    const COS_PREFIX_TYPE_DEFAULT = 1;
    // COS默认一级上传目录
    const COS_PREFIX_DEFAULT = 'courseware';
    // COS默认一级上传目录-测试环境
    const COS_PREFIX_TEST = 'test';
    // COS上传目录映射
    const COS_PREFIX_TYPE_MAP = [
        self::COS_PREFIX_TYPE_DEFAULT => self::COS_PREFIX_DEFAULT,
    ];
    // cos项目配置文件的key
    const COS_CONFIG_NAME_LXJX       = 'lxjx';
    const COS_CONFIG_NAME_LXJXSCHOOL = 'lxjxschool';

    /**
     * LXJX-SCHOOL : COS各个目录说明 : 带 $ 的为变量
     */

    /**
     * 试题专用:
     * question/download_file/".$sourceId."/pdf_analysis/".$filename
     * 试题/下载文件/$加密tid/pdf带解析文件/$文件名
     */

    /**
     * 组卷专用:
     * exam/download_file/user_exam/".$sourceId."/zip/".$filename
     * 试卷/下载文件/用户组卷/$加密examId/zip文件/$文件名
     */

    /**
     * 视频素材专用:
     * video_material/英语_人教PEP版（三起）_五年级_上学期_Unit 6 In a nature park/cover/There is a forest.jpg
     * 视频素材/$学科_教材版本_年级_学期_单元_小节/封面/$文件名        //cover 代表封面
     *
     * video_material/英语_人教PEP版（三起）_五年级_上学期_Unit 6 In a nature park/origin/英语_人教PEP版（三起）_五年级_上学期_Unit 6 In a nature park_0_0_There is a forest_知识讲解&复习_剪辑类_UGC.mp4
     * 视频素材/$学科_教材版本_年级_学期_单元_小节/源文件/$文件名      //origin 代表源文件
     *
     * video_material/英语_人教PEP版（三起）_五年级_上学期_Unit 6 In a nature park/show/There is a forest.mp4
     * 视频素材/$学科_教材版本_年级_学期_单元_小节/观看文件/$文件名    //show 代表观看使用的文件
     *
     * video_material/英语_人教PEP版（三起）_五年级_上学期_Unit 6 In a nature park/download/There is a forest.mp4
     * 视频素材/$学科_教材版本_年级_学期_单元_小节/下载文件/$文件名    //download 代表下载使用的文件
     *
     */

    /**
     * 测评专用
     * test/report     //测评报告
     */

    const MAIL_FROM_QQQY = 'qqqy'; // qq企业
    const MAIL_FROM_YX1  = 'service1'; // 新增邮箱1
    const MAIL_FROM_YX2  = 'service2'; // 新增邮箱2
    const MAIL_FROM_YX3  = 'service3'; // 新增邮箱
    const MAIL_FROM_YX4  = 'service4'; // 新增邮箱
    const MAIL_FROM_YX5  = 'service5'; // 新增邮箱
    const MAIL_FROM_YX6  = 'service6'; // 新增邮箱
    const MAIL_FROM_YX7  = 'service7'; // 新增邮箱
    const MAIL_FROM_YX8  = 'service8'; // 新增邮箱
    const MAIL_FROM_YX9  = 'service9'; // 新增邮箱
    const MAIL_FROM_YX10 = 'service10'; // 新增邮箱

    // 邮件任务发件人列表-有序
    const MAIL_JOB_FROM_ARR = [
        self::MAIL_FROM_QQQY,
        self::MAIL_FROM_YX1,
        self::MAIL_FROM_YX2,
        self::MAIL_FROM_YX3,
        self::MAIL_FROM_YX4,
        self::MAIL_FROM_YX5,
        self::MAIL_FROM_YX6,
        self::MAIL_FROM_YX7,
        self::MAIL_FROM_YX8,
        self::MAIL_FROM_YX9,
        self::MAIL_FROM_YX10,
    ];

    const MAIL_SERVER_INTERNAL = 'internal'; // 公司内部邮箱标识

    const CLIENT_OS_PC      = 'pc';
    const CLIENT_OS_ANDROID = 'android';
    const CLIENT_OS_IOS     = 'ios';

    // 海鸥CRM系统
    const LXJX_SYSTEM_CRM = 'lxjxcrm';
    // 好课帮系统
    const LXJX_SYSTEM_HKB = 'lxjxschool';
    // 猛犸象系统
    const LXJX_SYSTEM_MIS = 'lxjxmis';
    // LXJX活动服务
    const LXJX_SYSTEM_ACTIVITY = 'lxjxactivity';

    // LXJX团队成员钉钉手机号--刘佳琪
    const DING_ACCOUNT_LJQ = '***********';
    // LXJX团队成员钉钉手机号--梁东方
    const DING_ACCOUNT_LDF = '***********';
    // LXJX团队成员钉钉手机号--姚少琛
    const DING_ACCOUNT_YSC = '***********';
    // LXJX团队成员钉钉手机号--张玉田
    const DING_ACCOUNT_ZYT = '***********';
    // LXJX团队成员钉钉手机号--闫立敏
    const DING_ACCOUNT_YLM = '***********';

    // 已授权标示
    const ACCREDIT_FLAG_YES = 1;
    // 未授权标示
    const ACCREDIT_FLAG_NO = 0;

    // 开关配置项状态值--是
    const CONFIG_SWITCH_FLAG_YES = 1;
    // 开关配置项状态值--否
    const CONFIG_SWITCH_FLAG_NO = 0;

    //sourceType    资源类型：DB中定义的常量以该配置为准
    const SOURCE_TYPE_PPT = 1;               //ppt = 课件
    const SOURCE_TYPE_TID = 2;               //题目
    const SOURCE_TYPE_EXAM = 3;              //试卷
    const SOURCE_TYPE_VOLUME = 4;            //组卷
    const SOURCE_TYPE_VIDEO = 5;             //视频
    const SOURCE_TYPE_HOT_QUESTION = 6;      //百川热搜题

    //business      资源业务线：DB中定义的常量以该配置为准
    const BUSINESS_HKB = 1;                  //好课帮资源
    const BUSINESS_BC = 2;                   //百川资源
    const BUSINESS_JTX = 3;                  //简题库资源
    const BUSINESS_LXJX = 4;                   //好课帮热搜试卷资源


    const EXAM_TMP_ID = 0;

    // 下载试卷积分
    const DOWNLOAD_SCORE_EXAM = 2;
    // 下载单题积分
    const DOWNLOAD_SCORE_TID = 1;

    //是否下载
    const DOWNLOAND_ON  = 1; //已下载
    const DOWNLOAND_OFF = 2; //未下载

    // 试题篮题目上限
    const EXAM_BASKET_TID_UP = 30;

    const LXJX_CONTROL_LIMIT              = 'lxjx_control_limit';
    const LXJX_HOT_QUESTION_GRADE_SUBJECT = 'lxjx_hot_question_grade_subject';
    const LXJX_API_SKIP_SAFE_CHECK        = 'api_skip_safe_check'; //部分接口跳过安全机制

    //测评相关
    const TEST_INIT = 1;                                  //未开始
    const TEST_DOING = 2;                                 //进行中
    const TEST_FINISH = 3;                                //已结束


    const TEST_STATU_VIEW_NOT_TIME = 1; //未配置时间
    const TEST_STATU_VIEW_NOT_EXAM = 2; //已配置时间，未配置试卷

    const TEST_STATU_VIEW_NO_START                  = 3; //未到开始时间
    const TEST_STATU_VIEW_NO_WRITE                  = 4; //已到开始时间，未开始答题
    const TEST_STATU_VIEW_NO_FINISH                 = 5; //已作答，未交卷，可能还有剩余时间，未结束
    const TEST_STATU_VIEW_FINISH_NOT_END_NOT_ANSWER = 6; //已交卷，未结束，无答案
    const TEST_STATU_VIEW_FINISH_WHITH_ANSWER       = 7; //已交卷，有答案（进行中/结束）
    const TEST_STATU_VIEW_NOT_SUBMIT                = 8; //未交卷/未答题，已结束
    const TEST_STATU_VIEW_NOT_VIVAD                 = 9; //学校解绑/测评下架-已失效

    public static $arrTestStatus = [
        self::TEST_DOING  => [
            0                                               => Lxjxlib_Const_Common::TEST_STATU_VIEW_NO_FINISH,
            //已作答，未交卷，还有剩余时间，未结束
            Lxjxlib_Ds_School_TestRecord::ANSWER_SHOW_TIMELY  => self::TEST_STATU_VIEW_FINISH_WHITH_ANSWER,
            //已交卷，有答案（进行中/结束）
            Lxjxlib_Ds_School_TestRecord::ANSWER_SHOW_DELAYED => self::TEST_STATU_VIEW_FINISH_NOT_END_NOT_ANSWER,
            //已交卷，未结束，无答案
        ],
        self::TEST_FINISH => [
            Lxjxlib_Ds_School_TestExamAnswerSheet::TEST_EXAM_NOT_FINISH  => self::TEST_STATU_VIEW_NOT_SUBMIT,
            //未交卷/未答题，已结束
            Lxjxlib_Ds_School_TestExamAnswerSheet::TEST_EXAM_HAND_SUBMIT => self::TEST_STATU_VIEW_FINISH_WHITH_ANSWER,
            //已交卷，有答案（进行中/结束）
            Lxjxlib_Ds_School_TestExamAnswerSheet::TEST_EXAM_AUTO_SUBMIT => self::TEST_STATU_VIEW_FINISH_WHITH_ANSWER,
            //已交卷，有答案（进行中/结束）
        ],

    ];

    //测评缓存的key前缀
    const TEST_GET_EXAM_INFO            = 'lxjx_school_test_get_examinfo'; //试卷内容
    const TEST_USER_APPLY_RECORD        = 'lxjx_school_test_user_get_record'; //用户报名表
    const TEST_SCHOOL_BIND_CON          = 'lxjx_school_test_bind_con'; //测评学校绑定关系
    const TEST_USER_EXAM_ANSWER_TIME    = 'lxjx_school_user_exam_answer_time'; //考试作答时长
    const TEST_USER_HEART_BEAT_LASTTIME = 'lxjx_school_user_heart_beat_lasttime'; //用户最后一次心跳
    const TEST_USER_EXAM_LAST_TIME      = 'lxjx_school_user_exam_last_time'; //考试剩余时间
    const TEST_WRITE_LOG                = 'lxjx_school_test_log_conf'; //时间流水配置表
    const TEST_USER_EXAM_SUBMIT_RECORD  = 'lxjx_school_test_user_submit_record'; //单题提交答案key
    const TEST_USER_EXAM_ALL_ANSWER     = 'lxjx_school_test_user_all_answer'; //整张试卷答案key
    const TEST_USER_EXAM_SUBMIT_SIGN    = 'lxjx_school_test_user_submit_sign'; //单题提交标记key
    const TEST_RECORD_CONTENT           = 'lxjx_school_test_record_content'; //测评表缓存前缀key
    const TEST_USER_SUBMIT_EXAM         = 'lxjx_school_test_user_submit_exam'; //用户交卷的key
    const TEST_INFO_CACHE               = 'lxjx_test_full_info'; // 测评详情
    const TEST_SCHOOL_BIND_LIST         = 'lxjx_school_test_bind_list'; //测评学校绑定关系-一对多
    const TEST_EXAM_BIND_CON            = 'lxjx_exam_test_bind_con'; //测评试卷绑定关系
    const TEST_USER_EXAM_ANSWER         = 'lxjx_test_user_exam_answer'; //用户考试作答详情
    const TEST_SUBJECT_GRADE_POINT_TREE = 'lxjx_subject_grade_point'; // 年级学科知识点数
    const TEST_SINGLE_T_POINT           = 'lxjx_test_point_single_match'; // 单题最终匹配的知识点
    const TEST_USER_ALL_RECORD          = 'lxjx_school_test_user_all_record'; //用户所有报名表
    const TEST_USER_LIMIT_TIME          = 'lxjx_school_test_over_time'; //时间限制
    const TEST_USER_EXAMCORE_SUCC       = 'lxjx_school_user_examcore_succ'; //请求examcore成功
    const TEST_GET_EXAM_TIDS            = 'lxjx_school_test_get_examtids'; //请求exam的tidList
    const TEST_IMPORT_SCHOOL_ERROR_FILE = 'lxjx-lxjxmis-test-import-school-error-file-'; //导入学校错误学校信息
    const TEST_IMPORT_IS_EXIST_IMPOTR   = 'lxjx-lxjxmis-test-import-is_exist-import-'; //是否存在导入学校动作

    //小语阅读缓存的key前缀
    const XXYD_GET_EXAM_INFO = 'lxjx_school_xxyd_get_examinfo';                        //试卷内容
    const XXYD_GET_SUBTASK_LIST  = 'lxjx_school_xxyd_get_subtask_list_task_id_';       //父任务对应的子任务列表

    const SS_SUBMIT_EXAM = 'lxjx_school_act_ss_submit_';                               //速算单题提交key
    const SS_USER_EXAM_LAST_TIME = 'lxjx_school_act_ss_exam_last_time_';               //速算考试剩余时间
    const SS_TURN_TOP_FIVE_USER = 'lxjx_school_act_ss_turn_top_five_user_';            //速算轮次最高维度top5


    const HOT_QUESTIONS_SAVE_DRAFTINFO = 'lxjx_mis_hot_question_draftinfo';
    const LXJX_INTERFACE_LIMIT = 'lxjx_interface_limit';

    const LXJX_DOWNLOAD_VOLUME_TO_EMAIL = 'lxjx_school_download_volume_to_email'; //组卷下载到邮箱缓存

    public static $arrConstConf = [
        self::LXJX_CONTROL_LIMIT,
        self::TEST_USER_LIMIT_TIME,
        self::TEST_WRITE_LOG,
    ];

    // 字典配置名称
    const MAP_NAME_GZH_REPLY = 'hkb_gzh_reply'; // 好课帮公众号自动回复配置

    // 字典配置常量定义
    // 公众号自动回复-匹配模式
    const MAP_GZH_REPLY_FULL = 1; // 完全匹配
    const MAP_GZH_REPLY_BLUR = 2; // 模糊匹配
    // 公众号自动回复-回复消息类型
    const MAP_GZH_REPLY_MSG_TYPE_TEXT  = 1; // 文本
    const MAP_GZH_REPLY_MSG_TYPE_IMAGE = 2; // 图片

    // 星期日期值定义
    const MONDAY    = 1; // 周一
    const TUESDAY   = 2; // 周二
    const WEDNESDAY = 3; // 周三
    const THURSDAY  = 4; // 周四
    const FRIDAY    = 5; // 周五
    const SATURDAY  = 6; // 周六
    const SUNDAY    = 7; // 周日

    // 星期字符串映射，如果还有定制的展示需求，就在simple后面追加字段
    public static $WEEK_STR_MAP = [
        self::MONDAY    => [
            'simple' => '周一',
        ],
        self::TUESDAY   => [
            'simple' => '周二'
        ],
        self::WEDNESDAY => [
            'simple' => '周三',
        ],
        self::THURSDAY  => [
            'simple' => '周四',
        ],
        self::FRIDAY    => [
            'simple' => '周五',
        ],
        self::SATURDAY  => [
            'simple' => '周六',
        ],
        self::SUNDAY    => [
            'simple' => '周日'
        ],
    ];

    const TEST_PHONE_WHITE_LIST = [
        '颜昌彪'  => '18811226775',
        '孙龙'   => '13621075519',
        '姚少琛'  => '***********',
        '杨瑞鹏'  => '18634388163',
        '赵振方'  => '18969900364',
        '邹建双'  => '18514529180',
        '张志辉'  => '18303010475',
        '彭谦'   => '15300392295',
        '彭谦2'  => '18513622180',
        '周佩思'  => '13871157051',
        '周巧凤'  => '18233132384',
        '刘勃'   => '13119115375',
        '陈彩连'  => '13401150838',
        '张可'   => '13261087750',
        '任马勃'  => '13753975923',
        '林晓洲'  => '13686237805',
        '闫立敏'  => '***********',
        '郝矿杰'  => '15232198701',
        '尚鹏鹏'  => '13121867528',
        '宗云龙'  => '13051609287',
        '庞俊超'  => '13260075508',
        '何枫'   => '15708484744',
        '徐济豪'  => '18202775467',
        '张云朋'  => '18232598606',
        '刘姿姿'  => '18600806155',
        '张伟'   => '13820638855',
        '倪志强'  => '18380168827',
        '张小旭'  => '13260221050',
        '郭鹏飞1' => '17600198574',
        '郭鹏飞2' => '15203400518',
        '贾志涛'  => '15910848331',
        '李露西'  => '18210211532',
        '彭希'   => '13161115142',
        '龙志锋'  => '15011541519',
        '刘佳琪'  => '***********',
        '李伟斌'  => '18611260062',
        '董莎莎'  => '15901039225',
        '于志刚'  => '18730603667',
        '任马勃2' => '13436536734',
        '付林'   => '18810152014',
        '郭廷光'   => '18612031311',
        '周亚京' => '15631173358',
        '赵玥丹'  => '18292000599',
        '史媛媛'  => '18703621624',

    ];

    //各项目来源配置
    const SOURCE_BJX = 'bjx';  //北极星
    const SOURCE_BDTOOL = 'bdtool';  //BD助手

    // 手机号rc4加解密密钥
    const RC4_KEY_PHONE = 'asC12sh4f12d7vdoDsf';
}
