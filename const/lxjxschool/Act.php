<?php
/**
 * @brief   活动相关
 * @file    Act.php
 * <AUTHOR>
 * @version 1.0
 * @date    2021/1/6 下午4:59
 */

class Lxjxlib_Const_Lxjxschool_Act
{
    // 公共状态
    const STATUS_COMMON_NORMAL  = 1; // 正常
    const STATUS_COMMON_DELETED = 2; // 已删除

    // 活动状态-前端筛选用
    const STATUS_FE_ALL         = 0; // 默认-全部
    const STATUS_FE_UNDO        = 1; // 未开始
    const STATUS_FE_DOING       = 2; // 进行中
    const STATUS_FE_FINISH      = 4; // 已结束

    const STATUS_FE_DESC_UNDO   = '未开始';
    const STATUS_FE_DESC_DOING  = '进行中';
    const STATUS_FE_DESC_FINISH = '已结束';
    
    // 活动状态-C端页面使用
    const STATUS_CLIENT_UNDO    = 1; // 未开始
    const STATUS_CLIENT_DOING   = 2; // 进行中
    const STATUS_CLIENT_FINISH  = 3; // 已结束

    // 发布操作类型
    const PUBLISH_TYPE_UNDO     = 1; // 取消发布
    const PUBLISH_TYPE_DO       = 2; // 发布

    // 发布状态
    const PUBLISH_STATUS_ALL    = 0; // 全部-筛选用
    const PUBLISH_STATUS_UNDO   = 1; // 未发布
    const PUBLISH_STATUS_DONE   = 2; // 已发布

    public static $PUBLISH_STATUS_DESC_MAP = [
        self::PUBLISH_STATUS_UNDO => '未发布',
        self::PUBLISH_STATUS_DONE => '已发布',
    ];
    
    // fe-报名页，活动是否可以报名
    const ACT_CAN_APPLY_YES     = 1; // 可报名
    const ACT_CAN_APPLY_NO      = 0; // 不可报名

    // 活动类型
    const ACT_TYPE_XYYD         = 1; //小语阅读
    const ACT_TYPE_SS           = 2; // 速算
    const ACT_TYPE_POETRY       = 3; // 古诗词

    // 活动学校类型
    const ACT_SCHOOL_TYPE_COUNTRY   = 0; // 全国
    const ACT_SCHOOL_TYPE_PROVINCE  = 1; // 全省
    const ACT_SCHOOL_TYPE_CITY      = 2; // 全市
    const ACT_SCHOOL_TYPE_COUNTY    = 3; // 全区
    const ACT_SCHOOL_TYPE_SPECIFIC  = 4; // 指定学校

    // 学生任务表中的状态
    const ACT_STU_TASK_STATUS_NO    = 1; // 未完成
    const ACT_STU_TASK_STATUS_DOING = 2; // 进行中
    const ACT_STU_TASK_STATUS_FAILED= 3; // 失败
    const ACT_STU_TASK_STATUS_SUCC  = 4; // 成功

    // 学生任务前端展示状态
    const ACT_STU_TASK_STATUS_FE_PASS   = 1; // 已完成
    const ACT_STU_TASK_STATUS_FE_FAILED = 2; // 未通过
    const ACT_STU_TASK_STATUS_FE_NO     = 3; // 未完成

    // 默认头像
    const ACT_STU_DEFAULT_IMG           = 'https://zyb-laxin-lxjx-1253445850.cos.ap-beijing.myqcloud.com/upload/202105/20210519_default_head.png'; // 学生
    const ACT_TCH_DEFAULT_IMG           = 'https://zyb-laxin-lxjx-1253445850.cos.ap-beijing.myqcloud.com/upload/202105/20210519_default_head.png'; // 老师

    // 老师端-学生任务进度
    const STU_TASK_PLAN_STATUS_AHEAD    = 1; // 超前
    const STU_TASK_PLAN_STATUS_STANDARD = 2; // 达标
    const STU_TASK_PLAN_STATUS_BEHIND   = 3; // 落后


    // 是否为当前任务（进行中）
    const TASK_IS_CURRENT_YES           = 1; // 是
    const TASK_IS_CURRENT_NO            = 0; // 否

    // 父任务展示状态
    const TASK_FE_STATUS_WAITING        = 1; // 未开始
    const TASK_FE_STATUS_DOING          = 2; // 进行中
    const TASK_FE_STATUS_FINISH         = 3; // 已结束

    // 子任务展示状态
    const TASK_SUB_FE_STATUS_WAITING    = 1; // 未开始
    const TASK_SUB_FE_STATUS_DOING      = 2; // 进行中
    const TASK_SUB_FE_STATUS_FINISH     = 3; // 已结束
}