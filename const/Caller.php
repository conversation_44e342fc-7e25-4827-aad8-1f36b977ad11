<?php
/***************************************************************************
 *
 * Copyright (c) 2018 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/



/**
 * @file    Caller.php
 * <AUTHOR>
 * @date    2019-05-21
 * @brief   调用方常量
**/

class Zb_Const_Caller {
    //*TODO：目前仅履约服务用于调用方约束
    const MISSERVICE = 'misservice';//客服
    const XENG = 'xeng';//帮帮英语
    const OFC = 'ofc';//履约服务

    //*履约服务
    public static $ofcCallerMap = [
        self::MISSERVICE => 'misservice123',
        self::XENG => 'xeng123',
        self::OFC => 'ofc123',
    ];
}
