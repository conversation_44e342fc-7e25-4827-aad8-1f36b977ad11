<?php

class Zb_Const_Renew
{
    const RENEW_WINTER = 1;  // 寒续春
    const RENEW_SPRING = 2;  // 暑秋联报
    const RENEW_SUMMER = 3;  // 暑续秋
    const RENEW_AUTUMN = 4;  // 寒春联报
    const RENEW_NULL = 5;   // 无任何续报时期
    // 这个是一整年续报的时间节点，从寒续春开始，
    // 进入下一年需要把开始时间和结束时间也改正，这里只是大致的时间点，具体的需要根据每个季具体的判断
    const RENEW_SEASON  = array(
        self::RENEW_WINTER => array(
            'start_time' => '2021-1-10 10:00',
            'end_time'   => '2021-4-02 10:00:00',
        ),
        self::RENEW_SPRING => array(
            'start_time' => '2021-4-08 10:00:00',
            'end_time'   => '2021-8-05 00:00:00',
        ),
        self::RENEW_SUMMER => array(
            'start_time' => '2020-7-01 10:00:00',
            'end_time'   => '2020-10-20 00:00:00',
        ),
        self::RENEW_AUTUMN => array(
            'start_time' => '2021-11-05 20:00:00',
            'end_time'   => '2022-2-10 23:59:59',
        )
    );

    public static $gradeUpXuebu = array(
        11 => 1,
        12 => 1,
        13 => 1,
        14 => 1,
        15 => 1,
        16 => 20,
        2  => 20,
        3  => 20,
        4  => 30,
        5  => 30,
        6  => 30,
        7  => 30,
        63 => 60,
        62 => 60,
        64 => 60,
    );

    // 21年春季一期初三班课用户
    // 学季_学年_是否班课用户_年级_学期
    const SPRING_2021_NORMAL_4_1 = 1305;
    // 21年春季三期初三班课用户
    const SPRING_2021_NORMAL_4_3 = 1306;
    // 21年春季一期初三插班班课用户
    const SPRING_2021_SPECIAL_4_1 = 1318;
    // 21年春季高一班课用户
    const SPRING_2021_NORMAL_5 = 1319;
    // 21年春季高一插班班课用户
    const SPRING_2021_SPECIAL_5 = 1320;
    // 21年春季高二班课用户
    const SPRING_2021_NORMAL_6 = 1321;
    // 21年春季高二插班班课用户
    const SPRING_2021_SPECIAL_6 = 1322;
    // 21年春季一期高三班课用户
    const SPRING_2021_NORMAL_7_1 = 1323;
    // 21年春季一期高三插班班课用户
    const SPRING_2021_SPECIAL_7_1 = 1324;
    // 21年春季三期高三班课用户
    const SPRING_2021_NORMAL_7_3 = 1325;
    // 21年暑秋高中保价用户
    const SUMMER_2021_VIP_30     = 958;
    // 21年暑秋高一综合学科优惠用户
    const SUMMER_2021_DISCOUNT_5 = 1327;
    // 21年暑秋高二综合学科优惠用户
    const SUMMER_2021_DISCOUNT_6 = 1328;
    // 21年暑秋高一老生且高一综合优惠用户
    // 21年暑秋高一插班老生且高一综合学科优惠用户
    // 21年暑秋高二老生且高二综合优惠用户
    // 21年暑秋高二插班老生且高二综合优惠用户
    // 21年春季三期高三班课用户
    /**************************初中start**********************************************/
    // 21年暑秋初中保价用户
    const SUMMER_VIP_20  = 957;
    // 21年春季一期六年级班课用户
    const SPRING_2021_NORMAL_16_1  = 1334;
    // 21年春季二期六年级班课用户
    const SPRING_2021_NORMAL_16_2  = 1335;
    // 21年春季一期且二期六年级班课用户
    const SPRING_2021_NORMAL_16_2_1 = 1336;
    // 21年春季一期初一班课用户
    const SPRING_2021_NORMAL_2_1  = 1337;
    // 21年春季一期初一插班班课用户
    const SPRING_2021_SPECIAL_2_1  = 1338;
    // 21年春季二期初一班课用户
    const SPRING_2021_NORMAL_2_2  = 1339;
    // 21年春季一期初二班课用户
    const SPRING_2021_NORMAL_3_1  = 1340;
    // 21年春季一期初二插班班课用户
    const SPRING_2021_SPECIAL_3_1  = 1341;
    // 21年春季二期初二班课用户
    const SPRING_2021_NORMAL_3_2  = 1342;
    const SUMMER_2021_PRE_2       = 1343;
    const SUMMER_2021_PRE_3_9     = 1344; // 21年暑秋初二9元预约用户
    const SUMMER_2021_PRE_3_10    = 1345; // 21年暑秋初二10元预约用户
    const SUMMER_2021_PRE_4_9     = 1346; // 21年暑秋初三9元预约用户
    const SUMMER_2021_PRE_4_10    = 1347; // 21年暑秋初三10元预约用户
    /**************************初中end**********************************************/


    /*********/
    // 21年春季一期小学班课用户
    const SPRING_2021_NORMAL_1_1  = 1373;
    // 21年春季二期小学班课用户
    const SPRING_2021_NORMAL_1_2  = 1374;
    // 21年春季一期学前班课用户
    const SPRING_2021_NORMAL_60_1  = 1375;
    // 21年春季二期学前班课用户
    const SPRING_2021_NORMAL_60_2  = 1376;
    // 21年春季一期学前L班课用户
    const SPRING_2021_L_60_1  = 1377;
    // 21年春季一期学前非L班课用户
    const SPRING_2021_NOT_L_60_1 = 1378;
    // 21年春季二期学前L班课用户
    const SPRING_2021_L_60_2  = 1379;
    // 21年春季二期学前非L班课用户
    const SPRING_2021_NOT_L_60_2 = 1380;
    // 21年春季一期小学L班课用户
    const SPRING_2021_L_1_1  = 1381;
    // 21年春季一期小学非L班课用户
    const SPRING_2021_NOT_L_1_1 = 1382;
    // 21年春季二期小学L班课用户
    const SPRING_2021_L_1_2  = 1383;
    // 21年春季二期小学非L班课用户
    const SPRING_2021_NOT_L_1_2 = 1384;
    // 21年小学学前春季新X类用户
    const SPRING_2021_60_X  = 1385;
    // 21年小学学前春季新Y类用户
    const SPRING_2021_1_X  = 1385;
    // 小学学前班课用户
    const AUTUMN_2020_NORMAL_60 = 1401;
    /**********/

    const SPRING_2021_ROLES = array(
        self::SPRING_2021_NORMAL_4_1,
        self::SPRING_2021_NORMAL_4_3,
        self::SPRING_2021_NORMAL_5,
        self::SPRING_2021_NORMAL_6,
        self::SPRING_2021_NORMAL_7_1,
        self::SPRING_2021_NORMAL_7_3,
        self::SUMMER_2021_DISCOUNT_5,
        self::SUMMER_2021_DISCOUNT_6,
        self::SPRING_2021_SPECIAL_4_1,
        self::SPRING_2021_SPECIAL_5,
        self::SPRING_2021_SPECIAL_6,
        self::SPRING_2021_SPECIAL_7_1,
        self::SPRING_2021_NORMAL_16_1,
        self::SPRING_2021_NORMAL_16_2,
        self::SPRING_2021_NORMAL_16_2_1,
        self::SPRING_2021_NORMAL_2_1,
//        self::SPRING_2021_SPECIAL_2_1,
        self::SPRING_2021_NORMAL_2_2,
        self::SPRING_2021_NORMAL_3_1,
//        self::SPRING_2021_SPECIAL_3_1,
        self::SPRING_2021_NORMAL_3_2,
        self::SPRING_2021_NORMAL_1_1,
        self::SPRING_2021_NORMAL_1_2,
        self::SPRING_2021_NORMAL_60_1,
        self::SPRING_2021_NORMAL_60_2,
    );

    // 初一角色 1373 1374  1381 1382  1383 1384 1385
    const SPRING_2021_60_ROLES = array(
        self::SPRING_2021_NORMAL_60_1,
        self::SPRING_2021_NORMAL_60_2,
        self::SPRING_2021_L_60_1,
        self::SPRING_2021_NOT_L_60_1,
        self::SPRING_2021_L_60_2,
        self::SPRING_2021_NOT_L_60_2,
        self::SPRING_2021_60_X,
        self::AUTUMN_2020_NORMAL_60,
        self::SPRING_2021_NORMAL_1_1,
        self::SPRING_2021_NORMAL_1_2,
        self::SPRING_2021_L_1_1,
        self::SPRING_2021_NOT_L_1_1,
        self::SPRING_2021_L_1_2,
        self::SPRING_2021_NOT_L_1_2,
        self::SPRING_2021_1_X,
    );

    const SPRING_2021_1_ROLES = array(
        self::SPRING_2021_NORMAL_60_1,
        self::SPRING_2021_NORMAL_60_2,
        self::SPRING_2021_L_60_1,
        self::SPRING_2021_NOT_L_60_1,
        self::SPRING_2021_L_60_2,
        self::SPRING_2021_NOT_L_60_2,
        self::SPRING_2021_60_X,
        self::AUTUMN_2020_NORMAL_60,
        self::SPRING_2021_NORMAL_1_1,
        self::SPRING_2021_NORMAL_1_2,
        self::SPRING_2021_L_1_1,
        self::SPRING_2021_NOT_L_1_1,
        self::SPRING_2021_L_1_2,
        self::SPRING_2021_NOT_L_1_2,
        self::SPRING_2021_1_X,
    );

    // 初一角色 1334 1343  1335 1336
    const SPRING_2021_16_ROLES = array(
        self::SPRING_2021_NORMAL_16_1,
        self::SPRING_2021_NORMAL_16_2,
        self::SPRING_2021_NORMAL_16_2_1,
        self::SUMMER_2021_PRE_2,
    );
    // 初二角色 1337  957  1344 1345 1339
    const SPRING_2021_2_ROLES = array(
        self::SPRING_2021_NORMAL_2_1,
//        self::SPRING_2021_SPECIAL_2_1,
        self::SPRING_2021_NORMAL_2_2,
        self::SUMMER_2021_PRE_3_9,
        self::SUMMER_2021_PRE_3_10,
        self::SUMMER_VIP_20,
    );

    // 初三角色 1340 957  1346 1347  1342
    const SPRING_2021_3_ROLES = array(
        self::SPRING_2021_NORMAL_3_1,
//        self::SPRING_2021_SPECIAL_3_1,
        self::SPRING_2021_NORMAL_3_2,
        self::SUMMER_2021_PRE_4_9,
        self::SUMMER_2021_PRE_4_10,
        self::SUMMER_VIP_20,
    );

    // 新高一  1316  1318
    const SPRING_2021_4_ROLES = array(
        self::SPRING_2021_NORMAL_4_1,
        self::SPRING_2021_SPECIAL_4_1,
        self::SPRING_2021_NORMAL_4_3,
        self::SUMMER_2021_DISCOUNT_5,
    );

    // 新高二 1319 1320
    const SPRING_2021_5_ROLES = array(
        self::SPRING_2021_NORMAL_5,
        self::SPRING_2021_SPECIAL_5,
        self::SUMMER_2021_DISCOUNT_6,
    );

    // 新高三 1321 1322
    const SPRING_2021_6_ROLES = array(
        self::SPRING_2021_NORMAL_6,
        self::SPRING_2021_SPECIAL_6,
        self::SUMMER_2021_VIP_30,
    );
    // 老高三 1323
    const SPRING_2021_7_ROLES = array(
        self::SPRING_2021_NORMAL_7_1
    );

    // 年级对应的角色
    public static $gradeToRoles = array(
        '62' => self::SPRING_2021_60_ROLES,
        '11' => self::SPRING_2021_1_ROLES,
        '12' => self::SPRING_2021_1_ROLES,
        '13' => self::SPRING_2021_1_ROLES,
        '14' => self::SPRING_2021_1_ROLES,
        '15' => self::SPRING_2021_1_ROLES,
        '16' => self::SPRING_2021_16_ROLES,
        '2'  => self::SPRING_2021_2_ROLES,
        '3'  => self::SPRING_2021_3_ROLES,
        '4'  => self::SPRING_2021_4_ROLES,
        '5'  => self::SPRING_2021_5_ROLES,
        '6'  => self::SPRING_2021_6_ROLES,
        '7'  => self::SPRING_2021_7_ROLES
    );
}