<?php
/***************************************************************************
 *
 * Copyright (c) 2018 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/



/**
 * @file    Trade.php
 * <AUTHOR>
 * @date    2018-01-24
 * @brief   订单常量
 **/

class Zb_Const_Trade {

    const PAY_CALLBACK_URL      = '/trade/api/paynotify';
    const PAY_SOURCE            = 'zyb_fudao';
    const PAY_SECRET            = 'zybcouselesson';
    const REFUND_CALLBACK_URL   = '/trade/api/refundnotify';

    //订单状态
    const TRADE_STATUS_UNPAID         = 0;    // 待支付
    const TRADE_STATUS_PAID           = 1;    // 已支付
    const TRADE_STATUS_REFUNDED       = 2;    // 已退款
    const TRADE_STATUS_REFUNDPART     = 21;   // 部分退款
    const TRADE_STATUS_REFUNDING      = 3;    // 退款中 对应老系统中已冻结状态
    const TRADE_STATUS_BOUND          = 4;    // 预约订单绑定状态
    const TRADE_STATUS_CLOSED         = 5;    // 已关闭
    const TRADE_STATUS_HOLDING        = 6;    // 代扣预约
    const TRADE_STATUS_HOLDING_CANCEL = 7;    // 取消代扣预约
    const TRADE_STATUS_REFUND_PAUSE   = 8;    // 退款暂停


    public static $tradeStatusMap   = array(
        self::TRADE_STATUS_UNPAID   => '待支付',
        self::TRADE_STATUS_PAID     => '已支付',
        self::TRADE_STATUS_REFUNDED => '已退款',
        self::TRADE_STATUS_REFUNDPART=>'部分退款',
        self::TRADE_STATUS_REFUNDING=> '退款中',
        self::TRADE_STATUS_BOUND    => '已绑定',
        self::TRADE_STATUS_CLOSED   => '已关闭',
        self::TRADE_STATUS_HOLDING  => '已代扣预约',
        self::TRADE_STATUS_HOLDING_CANCEL=> '取消代扣预约',
        self::TRADE_STATUS_REFUND_PAUSE => '退款暂停',
    );


    //调课类型
    const CHANGE_ORDER_TYPE_NON     = 0;
    const CHANGE_ORDER_TYPE_OLD     = 1;
    const CHANGE_ORDER_TYPE_NEW     = 2;
    public static $changeOrderTypeMap   = array(
        self::CHANGE_ORDER_TYPE_NON => '未调课',
        self::CHANGE_ORDER_TYPE_OLD => '旧课程子订单',
        self::CHANGE_ORDER_TYPE_NEW => '新课程子订单',
    );

    //物流信息中订单号类型
    const TRANSPORT_INFO_TRADE_TYPE_PARENT = 1;
    const TRANSPORT_INFO_TRADE_TYPE_CHILD  = 2;
    public static $transportInfoTradeTypeMap = array(
        self::TRANSPORT_INFO_TRADE_TYPE_PARENT  => '父订单',
        self::TRANSPORT_INFO_TRADE_TYPE_CHILD   => '子订单',
    );

    //赠品退款类型
    const GIFT_INFO_REFUND_TYPE_BIND    = 1;
    const GIFT_INFO_REFUND_TYPE_SHARE   = 2;
    const GIFT_INFO_REFUND_TYPE_AVG     = 3;
    public static $giftInfoRefundTypeMap = array(
        self::GIFT_INFO_REFUND_TYPE_BIND    => '捆绑式',
        self::GIFT_INFO_REFUND_TYPE_SHARE   => '分享式',
        self::GIFT_INFO_REFUND_TYPE_AVG     => '均摊式',
    );


    //子订单类型
    const TRADE_TYPE_SUB             = 1;
    const TRADE_TYPE_PRE             = 2;
    public static $tradeTypeMap      = array(
        self::TRADE_TYPE_SUB         => '普通子订单',
        self::TRADE_TYPE_PRE         => '预付子订单',
    );

    //主订单类型
    const ORDER_TYPE_GENERAL         = 1;
    const ORDER_TYPE_PRE             = 2;
    public static $orderTypeMap      = array(
        self::ORDER_TYPE_GENERAL     => '普通主订单',
        self::ORDER_TYPE_PRE         => '预付主订单',
    );

    //订单渠道来源,有赞,一课等
    const ORDER_SOURCE_TYPE_YIKE                   = 0;
    const ORDER_SOURCE_TYPE_YOUZAN                 = 1;
    const ORDER_SOURCE_TYPE_HUANXIONG              = 2;
    const ORDER_SOURCE_TYPE_HUANXIONG_YOUZAN       = 3;
    const ORDER_SOURCE_TYPE_TOUFANG				   = 4;
    const ORDER_SOURCE_TYPE_DANGDANG               = 5;
    const ORDER_SOURCE_TYPE_PINGDUODUO             = 11;
    const ORDER_SOURCE_TYPE_TIANMAO                = 12;
    const ORDER_SOURCE_TYPE_JINGDONG               = 13;
    const ORDER_SOURCE_TYPE_YUNJI                  = 14;
    const ORDER_SOURCE_TYPE_DALINJIA               = 15;
    const ORDER_SOURCE_TYPE_HUANQIUBUSHOU          = 16;
    const ORDER_SOURCE_TYPE_BEIDIAN                = 17;
    const ORDER_SOURCE_TYPE_HUAMAI_JIANGSHUYIDONG  = 18;
    const ORDER_SOURCE_TYPE_YIZHAO_NIUWAPINGKE     = 19;
    const ORDER_SOURCE_TYPE_SUNING                 = 20;
    const ORDER_SOURCE_TYPE_DA_V_DIAN              = 21;
    const ORDER_SOURCE_TYPE_SH_MOUSHI              = 22;
    const ORDER_SOURCE_TYPE_HSDZJJ                 = 23;
    const ORDER_SOURCE_TYPE_WXW                    = 24;
    const ORDER_SOURCE_TYPE_HMXX                   = 25;
    const ORDER_SOURCE_TYPE_GMTS                   = 26;
    const ORDER_SOURCE_TYPE_YXC                    = 27;
    const ORDER_SOURCE_TYPE_XRKMM                  = 28;
    const ORDER_SOURCE_TYPE_AIYUE                  = 29;
    const ORDER_SOURCE_TYPE_SHXT                   = 30;
    const ORDER_SOURCE_TYPE_XK101                  = 31;
    const ORDER_SOURCE_TYPE_DDXT                   = 32;
    const ORDER_SOURCE_TYPE_XHJX                   = 33;
    const ORDER_SOURCE_TYPE_YKK                    = 34;
    const ORDER_SOURCE_TYPE_YIZHONG                = 35;
    const ORDER_SOURCE_TYPE_QZZM                   = 36;
    const ORDER_SOURCE_TYPE_WKQX                   = 37;
    const ORDER_SOURCE_TYPE_KBK                    = 38;
    const ORDER_SOURCE_TYPE_XZHK                   = 39;
    const ORDER_SOURCE_TYPE_LH                     = 40;
    const ORDER_SOURCE_TYPE_MMXX                   = 41;
    const ORDER_SOURCE_TYPE_XGX				       = 42;
    const ORDER_SOURCE_TYPE_SHZS				   = 43;
    const ORDER_SOURCE_TYPE_KXYB				   = 44;
    const ORDER_SOURCE_TYPE_SDBK				   = 45;
    const ORDER_SOURCE_TYPE_XZWH				   = 46;
    const ORDER_SOURCE_TYPE_ZXQZ				   = 47;
    const ORDER_SOURCE_TYPE_FANLI				   = 48;
    const ORDER_SOURCE_TYPE_WXXS				   = 49;
    const ORDER_SOURCE_TYPE_DBYX				   = 50;
    public static $orderSourceTypeMap    = array(
        self::ORDER_SOURCE_TYPE_YIKE                => '一课订单',
        self::ORDER_SOURCE_TYPE_YOUZAN              => '有赞订单',
        self::ORDER_SOURCE_TYPE_HUANXIONG           => '浣熊订单',
        self::ORDER_SOURCE_TYPE_HUANXIONG_YOUZAN    => '浣熊有赞订单',
        self::ORDER_SOURCE_TYPE_TOUFANG				=> '端外投放订单',
        self::ORDER_SOURCE_TYPE_DANGDANG			=> '当当-帮帮英语',
        self::ORDER_SOURCE_TYPE_PINGDUODUO          => '拼多多-作业帮官方旗舰店',
        self::ORDER_SOURCE_TYPE_TIANMAO             => '天猫-作业帮一课旗舰店',
        self::ORDER_SOURCE_TYPE_JINGDONG            => '京东-作业帮一课官方旗舰店',
        self::ORDER_SOURCE_TYPE_YUNJI               => '云集-作业帮一课',
        self::ORDER_SOURCE_TYPE_DALINJIA            => '达令家-作业帮官方旗舰店',
        self::ORDER_SOURCE_TYPE_HUANQIUBUSHOU       => '环球捕手-作业帮一课官方旗舰店',
        self::ORDER_SOURCE_TYPE_BEIDIAN             => '贝店-作业帮一课官方旗舰店',
        self::ORDER_SOURCE_TYPE_HUAMAI_JIANGSHUYIDONG=>'华脉-江苏移动',
        self::ORDER_SOURCE_TYPE_YIZHAO_NIUWAPINGKE  => '一招平台-牛蛙拼课',
        self::ORDER_SOURCE_TYPE_SUNING              => '苏宁-作业帮直播课旗舰店',
        self::ORDER_SOURCE_TYPE_DA_V_DIAN           => '大V店-作业帮直播课旗舰店',
        self::ORDER_SOURCE_TYPE_SH_MOUSHI           => '上海眸事',
        self::ORDER_SOURCE_TYPE_HSDZJJ              => '花生电子竞技',
        self::ORDER_SOURCE_TYPE_WXW                 =>  '微校网',
        self::ORDER_SOURCE_TYPE_HMXX                =>  '虎妈心选',
        self::ORDER_SOURCE_TYPE_GMTS                =>  '瓜满图书',
        self::ORDER_SOURCE_TYPE_YXC                 =>  '渝轩彩',
        self::ORDER_SOURCE_TYPE_XRKMM               =>  '向日葵妈妈',
        self::ORDER_SOURCE_TYPE_AIYUE               =>  '爱阅',
        self::ORDER_SOURCE_TYPE_SHXT                =>  '三好学堂',
        self::ORDER_SOURCE_TYPE_XK101               =>  '选课101',
        self::ORDER_SOURCE_TYPE_DDXT                =>  '叮当学堂',
        self::ORDER_SOURCE_TYPE_XHJX                =>  '薪火精选',
        self::ORDER_SOURCE_TYPE_YKK                 =>  '优课库',
        self::ORDER_SOURCE_TYPE_YIZHONG             =>  '易众',
        self::ORDER_SOURCE_TYPE_QZZM                =>  '亲子周末',
        self::ORDER_SOURCE_TYPE_WKQX                =>  '悟空睛选',
        self::ORDER_SOURCE_TYPE_KBK                 =>  '可比课',
        self::ORDER_SOURCE_TYPE_XZHK                =>  '小猪好课',
        self::ORDER_SOURCE_TYPE_LH                  =>  '猎河',
        self::ORDER_SOURCE_TYPE_MMXX                =>  '妈妈心选',
        self::ORDER_SOURCE_TYPE_XGX 			    =>  '习惯熊',
        self::ORDER_SOURCE_TYPE_SHZS 			    =>  '上海钟书',
        self::ORDER_SOURCE_TYPE_KXYB 			    =>  '开心壹佰',
        self::ORDER_SOURCE_TYPE_SDBK 			    =>  '山东布克',
        self::ORDER_SOURCE_TYPE_XZWH 			    =>  '讯之文化',
        self::ORDER_SOURCE_TYPE_ZXQZ 			    =>  '掌心亲子',
        self::ORDER_SOURCE_TYPE_FANLI 			    =>  '返利网',
        self::ORDER_SOURCE_TYPE_WXXS 			    =>  '无限向溯',
        self::ORDER_SOURCE_TYPE_DBYX 			    =>  '逗爸优选',
    );

    public static $omsOrderSourceList = array(
        self::ORDER_SOURCE_TYPE_DANGDANG,
        self::ORDER_SOURCE_TYPE_PINGDUODUO,
        self::ORDER_SOURCE_TYPE_TIANMAO,
        self::ORDER_SOURCE_TYPE_JINGDONG,
        self::ORDER_SOURCE_TYPE_YUNJI,
        self::ORDER_SOURCE_TYPE_DALINJIA,
        self::ORDER_SOURCE_TYPE_HUANQIUBUSHOU,
        self::ORDER_SOURCE_TYPE_BEIDIAN,
        self::ORDER_SOURCE_TYPE_HUAMAI_JIANGSHUYIDONG,
        self::ORDER_SOURCE_TYPE_YIZHAO_NIUWAPINGKE,
        self::ORDER_SOURCE_TYPE_SUNING,
        self::ORDER_SOURCE_TYPE_SUNING,
        self::ORDER_SOURCE_TYPE_DA_V_DIAN,
        self::ORDER_SOURCE_TYPE_SH_MOUSHI,
        self::ORDER_SOURCE_TYPE_HSDZJJ,
    );


    const ORDER_CHANNEL_YIKE                = 0;
    const ORDER_CHANNEL_HUANXIONG           = 1;
    const ORDER_CHANNEL_POINT_MALL          = 2;
    public static $orderChannelMap  = array(
        self::ORDER_CHANNEL_YIKE        => '一课订单',
        self::ORDER_CHANNEL_HUANXIONG   => '浣熊订单',
        self::ORDER_CHANNEL_POINT_MALL  => '积分商城',
    );

    //订单购买方式普通购买,拼团购买
    const ORDER_BUSINESS_TYPE_NOR           = 0;
    const ORDER_BUSINESS_TYPE_GROUPON       = 1;
    const ORDER_BUSINESS_TYPE_GIFT          = 2;        // 赠品
    const ORDER_BUSINESS_TYPE_WITHHOLD      = 3;        // 2019暑秋代扣单
    const ORDER_BUSINESS_TYPE_TRANSFER      = 4;
    const ORDER_BUSINESS_TYPE_CDKEY         = 5;   //课程兑换码cdKey兑换

    public static $orderBusinessTypeMap     = array(
        self::ORDER_BUSINESS_TYPE_NOR       => '普通购买',
        self::ORDER_BUSINESS_TYPE_GROUPON   => '拼团购买',
        self::ORDER_BUSINESS_TYPE_GIFT      => '赠品',
        self::ORDER_BUSINESS_TYPE_WITHHOLD  => '2019暑秋代扣单',
        self::ORDER_BUSINESS_TYPE_TRANSFER  => '转班产生订单',
        self::ORDER_BUSINESS_TYPE_CDKEY     => '课程兑换码cdKey兑换',
    );


    const PAY_CHANNEL_ALIPAY          = 1;   // 支付宝app支付
    const PAY_CHANNEL_WXPAY           = 2;   // 微信app支付
    const PAY_CHANNEL_QQPAY           = 3;   // 财付通app支付
    const PAY_CHANNEL_WXPAY_NATIVE    = 4;   // 微信扫码支付
    const PAY_CHANNEL_PCALIPAY        = 5;   // 支付宝PC支付
    const PAY_CHANNEL_WXPAY_JSAPI     = 6;   // 微信公众号支付
    const PAY_CHANNEL_WXPAY_H5        = 7;   // 微信触屏版h5支付
    const PAY_CHANNEL_APPLEPAY        = 8;   // 苹果支付
    const PAY_CHANNEL_ZYBCOINPAY      = 9;   // 作业帮代币支付
    const PAY_CHANNEL_APPLEPAY_SB     = 10;  // 苹果沙盒支付
    const PAY_CHANNEL_ALIPAY_H5       = 11;  // 支付宝触屏版h5支付
    const PAY_CHANNEL_NETPAY          = 12;  // 招行一网通支付
    const PAY_CHANNEL_WXPAY_MINIAPP   = 13;  // 微信小程序支付
    const PAY_CHANNEL_WISEHERO        = 14;  // 智慧英雄提现 转学币
    const PAY_CHANNEL_UMONEY          = 17;  // 百度有钱花支付
    const PAY_CHANNEL_YOUZAN          = 18;  // 有赞商城
    const PAY_CHANNEL_CDKEY           = 29;  // 课程兑换码
    const PAY_CHANNEL_MALL            = 30;  // 店铺支付



    public static $payChannelMap = array(
        self::PAY_CHANNEL_ALIPAY          => '支付宝',
        self::PAY_CHANNEL_WXPAY           => '微信',
        self::PAY_CHANNEL_QQPAY           => '财付通',
        self::PAY_CHANNEL_WXPAY_NATIVE    => '微信',
        self::PAY_CHANNEL_PCALIPAY        => '支付宝',
        self::PAY_CHANNEL_WXPAY_JSAPI     => '微信',
        self::PAY_CHANNEL_WXPAY_H5        => '微信',
        self::PAY_CHANNEL_APPLEPAY        => '苹果',
        self::PAY_CHANNEL_ZYBCOINPAY      => '学币',
        self::PAY_CHANNEL_APPLEPAY_SB     => '苹果沙盒',
        self::PAY_CHANNEL_ALIPAY_H5       => '支付宝',
        self::PAY_CHANNEL_NETPAY          => '一网通',
        self::PAY_CHANNEL_WXPAY_MINIAPP   => '微信小程序',
        self::PAY_CHANNEL_WISEHERO        => '智慧英雄',
        self::PAY_CHANNEL_UMONEY          => '百度有钱花',
        self::PAY_CHANNEL_YOUZAN          => '有赞商城',
        self::PAY_CHANNEL_CDKEY           => '课程兑换码',
        self::PAY_CHANNEL_MALL            => '店铺支付',
    );


    //退款渠道
    const REFUND_CHANNEL_ORI        = 1;
    const REFUND_CHANNEL_BAL        = 2;
    const REFUND_CHANNEL_TRA        = 3;
    const REFUND_CHANNEL_VIR        = 4;
    public static $refundChannelMap = array(
        self::REFUND_CHANNEL_ORI    => '原路退回',
        self::REFUND_CHANNEL_BAL    => '退到余额',
        self::REFUND_CHANNEL_TRA    => '线下转账',
        self::REFUND_CHANNEL_VIR    => '虚拟退款',
    );

    //寄送地址信息项长度限制
    public static $addressItemLength = array(
        'id'             => 30,  //20200604 添加
        'name'           => 30,
        'phone'          => 30,
        'province'       => 30,
        'city'           => 30,
        'prefecture'    => 30,
        'town'           => 30,
        'address'        => 400,
    );

    //实体类型
    const ENTITY_TYPE_COURSE = 1;
    const ENTITY_TYPE_MATERIAL = 2;
    const ENTITY_TYPE_GIFT = 3;
    public static $entityTypeMap = array(
        self::ENTITY_TYPE_COURSE => '课程',
        self::ENTITY_TYPE_MATERIAL => '教材',
        self::ENTITY_TYPE_GIFT => '赠品',
    );

    // 退款类型
    const REFUND_CASE_NORMAL = 1;
    const REFUND_CASE_TRANSFER = 2;
    const REFUND_CASE_COMPENSATION = 3;
    const REFUND_CASE_ADDITION = 4;
    public static $refundCaseMap = [
        self::REFUND_CASE_NORMAL => "普通退款",
        self::REFUND_CASE_TRANSFER => "转班导致退款",
        self::REFUND_CASE_COMPENSATION => "价补退款",
        self::REFUND_CASE_ADDITION => "多笔退款",
    ];

    //退款来源
    const REFUND_APPLY_FROM_STU     = 1;
    const REFUND_APPLY_FROM_CALL    = 2;
    const REFUND_APPLY_FROM_SYSTEM  = 3;
    const REFUND_APPLY_FROM_EXPIRE  = 4;
    const REFUND_APPLY_FROM_HUANXIONG = 5;
    const REFUND_APPLY_FROM_DINGJICE = 6;
    const REFUND_APPLY_FROM_TUAN = 7;
    const REFUND_APPLY_FROM_OMS = 8;
    const REFUND_APPLY_FROM_EMPTY_ADDRESS = 9;
    const REFUND_APPLY_FROM_FUDAOPEIXUN = 10;
    const REFUND_APPLY_FROM_LPC = 11;
    const REFUND_APPLY_FROM_LPCSERVER = 12;
    const REFUND_APPLY_FROM_TOUFANG = 13;
    const REFUND_APPLY_FROM_TOOLS_PLATFROM = 14;
    const REFUND_APPLY_FROM_IMC_BATCH_TOOL = 15;
    const REFUND_APPLY_FROM_NT = 16;

    public static $refundApplyFrom  = array(
        self::REFUND_APPLY_FROM_STU     => '学生',
        self::REFUND_APPLY_FROM_CALL    => '客服',
        self::REFUND_APPLY_FROM_SYSTEM  => '系统自动退款',
        self::REFUND_APPLY_FROM_EXPIRE  => '过期退',
        self::REFUND_APPLY_FROM_HUANXIONG  => '浣熊退',
        self::REFUND_APPLY_FROM_DINGJICE => '定级测导致的退款行为',
        self::REFUND_APPLY_FROM_TUAN => '拼团未成团系统退',
        self::REFUND_APPLY_FROM_OMS => 'OMS系统发起的退款',
        self::REFUND_APPLY_FROM_EMPTY_ADDRESS => '地址后置发起的退款',
        self::REFUND_APPLY_FROM_FUDAOPEIXUN => "辅导培训退款",
        self::REFUND_APPLY_FROM_LPC => "lpc退款",
        self::REFUND_APPLY_FROM_LPCSERVER => 'lpcserver退款',
        self::REFUND_APPLY_FROM_TOUFANG => '投放退款',
        self::REFUND_APPLY_FROM_TOOLS_PLATFROM => '工具平台',
        self::REFUND_APPLY_FROM_IMC_BATCH_TOOL => 'IMC工具平台',
        self::REFUND_APPLY_FROM_NT => 'NT服务',
    );
    public static $refundApplyName  = array(
        self::REFUND_APPLY_FROM_STU     => 'app',
        self::REFUND_APPLY_FROM_CALL    => 'callcenter',
        self::REFUND_APPLY_FROM_SYSTEM  => 'autorefund',
        self::REFUND_APPLY_FROM_EXPIRE  => 'expire',
        self::REFUND_APPLY_FROM_HUANXIONG  => 'huanxiong',
        self::REFUND_APPLY_FROM_DINGJICE => 'dingjice',
        self::REFUND_APPLY_FROM_TUAN => 'tuan',
        self::REFUND_APPLY_FROM_OMS => 'oms',
        self::REFUND_APPLY_FROM_EMPTY_ADDRESS => 'emptyaddr',
        self::REFUND_APPLY_FROM_FUDAOPEIXUN => "fudaopeixun",
        self::REFUND_APPLY_FROM_LPC => "lpc",
        self::REFUND_APPLY_FROM_LPCSERVER => 'lpcserver',
        self::REFUND_APPLY_FROM_TOUFANG => 'toufang',
        self::REFUND_APPLY_FROM_TOOLS_PLATFROM => 'tools-platform',
        self::REFUND_APPLY_FROM_IMC_BATCH_TOOL => 'imcbatchtool',
        self::REFUND_APPLY_FROM_NT => 'nt',
    );

    //一笔退流水详情退款状态
    const REFUND_DETAIL_STATUS_INIT     = 0;
    const REFUND_DETAIL_STATUS_START    = 1;
    const REFUND_DETAIL_STATUS_END      = 2;
    const REFUND_DETAIL_STATUS_ROLLBACK = 3;
    public static $refundDetailStatusMap =array(
        self::REFUND_DETAIL_STATUS_INIT => "初始化",
        self::REFUND_DETAIL_STATUS_START=> '待确认',
        self::REFUND_DETAIL_STATUS_END  => '已完成',
        self::REFUND_DETAIL_STATUS_ROLLBACK => "已回滚",
    );

    //一笔退流水总状态
    const REFUND_SUMMERY_STATUS_START    = 0;
    const REFUND_SUMMERY_STATUS_COMMIT   = 1;
    const REFUND_SUMMERY_STATUS_COMPLETE = 2;
    const REFUND_SUMMARY_STATUS_ROLLBACK = 3;
    public static $refundSummeryStatusMap= array(
        self::REFUND_SUMMERY_STATUS_START   => '待退款',
        self::REFUND_SUMMERY_STATUS_COMMIT  => '已提交退款', // 已提交退款到 pay模块
        self::REFUND_SUMMERY_STATUS_COMPLETE=> '完成退款',
        self::REFUND_SUMMARY_STATUS_ROLLBACK => "操作已回滚",
    );

    // 转班summary信息状态表
    const CHANGE_COURSE_SUMMERY_STATUS_START    = 0;
    const CHANGE_COURSE_SUMMERY_STATUS_COMMIT   = 1;
    const CHANGE_COURSE_SUMMERY_STATUS_FAILED   = 2;
    public static $changeCourseSummeryStatusMap= array(
        self::CHANGE_COURSE_SUMMERY_STATUS_START   => '待调课',
        self::CHANGE_COURSE_SUMMERY_STATUS_COMMIT  => '已调课完毕', // 已提交退款到 pay模块
        self::CHANGE_COURSE_SUMMERY_STATUS_FAILED  => '调课失败',
    );

    const CHANGE_COURSE_DETAIL_STATUS_INIT      = 0;
    const CHANGE_COURSE_DETAIL_STATUS_COMMIT    = 1;
    const CHANGE_COURSE_DETAIL_STATUS_CANCEL    = 2;
    public static $changeCourseDetailStatusMap =[
        self::CHANGE_COURSE_DETAIL_STATUS_INIT    => "调课初始化",
        self::CHANGE_COURSE_DETAIL_STATUS_COMMIT  => "调课已提交",
        self::CHANGE_COURSE_DETAIL_STATUS_CANCEL  => "调课已回滚",
    ];

    const TRANSFER_COURSE_TYPE_NO_PAY = 1;
    const TRANSFER_COURSE_TYPE_PAY = 2;
    const TRANSFER_COURSE_TYPE_REFUND = 3;
    public static $transferCourseTypeMap = [
        self::TRANSFER_COURSE_TYPE_NO_PAY => "同原价转班或者因优惠政策导致的既不支付也不退款的转班操作行为",
        self::TRANSFER_COURSE_TYPE_PAY => "转班过程中发生了支付行为的转班操作行为",
        self::TRANSFER_COURSE_TYPE_REFUND => "转班过程中发生了退款行为的转班操作行为",
    ];


    //调用来源（当前转约使用）
    const TRADE_SOURCE_APP = 1;//学生端
    const TRADE_SOURCE_CALL = 2;//客服
    const TRADE_SOURCE_SYS = 3;//系统
    const TRADE_SOURCE_DINGJICE = 4;//定级测
    const TRADE_SOURCE_HUANXIONG_CALL = 5;//浣熊callcenter
    const TRADE_SOURCE_LPC = 11;
    const TRADE_SOURCE_LPCSERVER = 12;
    const TRADE_SOURCE_TOUFANG = 13;
    const TRADE_SOURCE_TOOLS_PLATFROM = 14;
    const TRADE_SOURCE_IMC_BATCH_TOOL = 15; //imc自己的转班工具平台

    public static $tradeSourceMap = [
        self::TRADE_SOURCE_APP              => 'app',
        self::TRADE_SOURCE_CALL             => 'callcenter',
        self::TRADE_SOURCE_SYS              => 'system',
        self::TRADE_SOURCE_DINGJICE         => 'dingjice',
        self::TRADE_SOURCE_HUANXIONG_CALL   => 'hxcallcenter',
        self::TRADE_SOURCE_LPC              => 'lpc',
        self::TRADE_SOURCE_LPCSERVER        => 'lpcserver',
        self::TRADE_SOURCE_TOUFANG          => 'toufang',
        self::TRADE_SOURCE_TOOLS_PLATFROM   => 'tools-platform',
        self::TRADE_SOURCE_IMC_BATCH_TOOL   => 'imcbatchtool',
    ];


    const TCC_ORDER_SOURCE_PURCHASE_APP                 = 0;
    const TCC_ORDER_SOURCE_PURCHASE_YOUZAN              = 1;
    const TCC_ORDER_SOURCE_PURCHASE_HUANXIONG           = 2;
    const TCC_ORDER_SOURCE_PURCHASE_HUANXIONG_YOUZAN    = 3;
    const TCC_ORDER_SOURCE_TRANSFER_APP                 = 10;
    const TCC_ORDER_SOURCE_TRANSFER_HX                  = 11;
    const TCC_ORDER_SOURCE_TRANSFER_CALL                = 12;
    const TCC_ORDER_SOURCE_TRANSFER_DINGJICE            = 13;

    public static $tccOrderSourceMap = [
        self::TCC_ORDER_SOURCE_PURCHASE_APP                 => 'app下单',
        self::TCC_ORDER_SOURCE_PURCHASE_YOUZAN              => '有赞商城订单同步',
        self::TCC_ORDER_SOURCE_PURCHASE_HUANXIONG           => '浣熊订单',
        self::TCC_ORDER_SOURCE_PURCHASE_HUANXIONG_YOUZAN    => '浣熊有赞商城',
        self::TCC_ORDER_SOURCE_TRANSFER_APP                 => 'APP发起转班',
        self::TCC_ORDER_SOURCE_TRANSFER_HX                  => '浣熊转班',
        self::TCC_ORDER_SOURCE_TRANSFER_CALL                => '客服发起转班',
        self::TCC_ORDER_SOURCE_TRANSFER_DINGJICE            => '定级测转班',
    ];

    // 主订单 子订单表中extFlag字段使用说明
    const TRADE_BIT_IN_USE = 20;
    const SUB_TRADE_BIT_IN_USE = 20;

    // 主订单扩展标记位使用说明
    const TRADE_ADDRESS_DEFICIENCY_BIT_POS = 1; // 地址后置
    const TRADE_OVERSEE_ORDER_BIT_POS = 2;      // 资金监管订单

    // 子订单扩展标记位使用说明
    const TRANSFER_IN_PROCESS = 0;                    // 标记子订单处在转班流程中
    const SUB_TRADE_ADDRESS_DEFICIENCY_BIT_POS = 1;  // 标记子订单缺失必需地址
    const NEW_TYPE_TRANSFER_FLAG = 2;               // 标记子订单引入多退少补后的转出班
    const SPECIAL_REFUND_LOCK = 3;                 // 标记子订单引入多退少补后的转出班
    const SKU_ORDER_LOCK = 4;                     // 标记子订单是否处于锁定中
    const SKU_ORDER_INTERCEPT = 5;               // 标记子订单是否已拦截

    //*履约行为
    const OFC_OP_PURCHASE = 176001;//下单支付成功
    const OFC_OP_TRANSFER = 176003;//转班成功
    const OFC_OP_REFUND   = 176004;//申请退款
    const OFC_OP_RESEND   = 176006;//补寄

    public static $ofcOpMap = [
        self::OFC_OP_PURCHASE => 'purchase',
        self::OFC_OP_TRANSFER => 'transfer',
        self::OFC_OP_REFUND => 'refund',
        self::OFC_OP_RESEND => 'resend',
    ];

    //*子订履约状态
    const OFC_STATUS_DOING = 0;//履约执行中，未获得明确成功返回或callback，默认未0
    const OFC_STATUS_SUCC = 1;//履约成功
    const OFC_STATUS_IGNORE = 2;//履约忽略，各类业务逻辑，不进行履约，可以视为“成功”
    const OFC_STATUS_FAIL = 3;//明确失败，获得明确错误信息，会回写code、error

    public static $ofcStatusMap = [
        self::OFC_STATUS_DOING => '履约中',//
        self::OFC_STATUS_SUCC => '履约成功',
        self::OFC_STATUS_IGNORE => '履约忽略',
        self::OFC_STATUS_FAIL => '履约失败',
    ];

    //审核状态
    //TODO:审核状态机待完善@PM-李楠
    const REVIEW_STATUS_DEFAULT = 0;//默认状态
    const REVIEW_STATUS_WAIT = 1;//待审核
    const REVIEW_STATUS_PASS = 2;//审核通过
    const REVIEW_STATUS_FAIL = 3;//审核失败

    public static $reviewStatusMap = array(
        self::REVIEW_STATUS_DEFAULT => '默认状态',
        self::REVIEW_STATUS_WAIT => '待审核',
        self::REVIEW_STATUS_PASS => '审核成功',
        self::REVIEW_STATUS_FAIL => '审核失败',
    );

    // trade galaxy id
    const GALAXY_ID_ORDER = 2029;// 主交易订单id
    const GALAXY_ID_SUB_ORDER = 2030;//子订单id
    public static $galaxyIdOrderMap = array(
        self::GALAXY_ID_ORDER => '支付订单id',
        self::GALAXY_ID_SUB_ORDER => '子订单id',
    );

}
