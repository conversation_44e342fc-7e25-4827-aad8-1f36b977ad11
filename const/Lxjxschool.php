<?php
/**************************************************************************
 *
 * Copyright (c) 2019 Zuoyebang.com, Inc. All Rights Reserved
 *
 * @file    Lxjxschool.php
 * @date    1/11/21
 *
 **************************************************************************/

class Lxjxlib_Const_Lxjxschool
{
    // 调用lxjxschool秘钥
    const APP_SECRET = 'kid30o1fc3k9690123r3e37a6cb5lo01';

    // 订单操作类型--驳回
    const ORDER_CHECK_TYPE_DENY             = 1;
    // 订单操作类型--通过
    const ORDER_CHECK_TYPE_PASS             = 2;

    // 订单类型--不通过物流发货
    const ORDER_TYPE_WITHOUT_EXPRESS        = 0;
    // 订单类型--通过物流发货
    const ORDER_TYPE_WITH_EXPRESS           = 1;

    // 订单配送方式--助教配送
    const ORDER_ADDRESS_TYPE_ZJPS           = 1;
    // 订单配送方式--快递直邮
    const ORDER_ADDRESS_TYPE_KDZY           = 2;

    // 订单状态--待BD审核
    const ORDER_STATUS_TO_BD_CHECK          = 10100;
    // 订单状态--BD驳回
    const ORDER_STATUS_BD_DENIED            = 10200;
    // 订单状态--待运营审核
    const ORDER_STATUS_TO_YY_CHECK          = 10300;
    // 订单状态--运营驳回
    const ORDER_STATUS_YY_DENIED            = 10400;
    // 订单状态--待发货
    const ORDER_STATUS_TO_EXPRESS           = 10500;
    // 订单状态--已发货且待BD收货
    const ORDER_STATUS_TO_BD_RECEIVE        = 10600;
    // 订单状态--已发货且待用户收货
    const ORDER_STATUS_TO_USER_RECEIVE      = 10700;
    // 订单状态--用户收货完成
    const ORDER_STATUS_FINISHED             = 19999;

    // 用户端订单状态--全部
    const USER_ORDER_STATUS_ALL             = 0;
    // 用户端订单状态--待发货
    const USER_ORDER_STATUS_TO_EXPRESS      = 20100;
    // 用户端订单状态--待收货
    const USER_ORDER_STATUS_TO_RECEIVE      = 20101;
    // 用户端订单状态--已完结
    const USER_ORDER_STATUS_DONE            = 20102;

    // 运营端订单状态--待BD审核
    const YY_ORDER_STATUS_TO_BD_CHECK       = 20103;
    // 运营端订单状态--待运营审核
    const YY_ORDER_STATUS_TO_YY_CHECK       = 20104;
    // 运营端订单状态--已确认
    const YY_ORDER_STATUS_CONFIRMED         = 20105;
    // 运营端订单状态--已驳回
    const YY_ORDER_STATUS_DENIED            = 20106;

    // BD端订单状态--未处理
    const BD_ORDER_STATUS_TO_CHECK          = 20107;
    // BD端订单状态--已处理
    const BD_ORDER_STATUS_CHECKED           = 20108;
    // BD端订单状态--已完成
    const BD_ORDER_STATUS_DONE              = 20109;

    // BD端订单状态--已处理--北极星
    const BD_ORDER_STATUS_CHECKED__BJX      = 20110;

    // BD端订单状态--已完成--北极星
    const BD_ORDER_STATUS_DONE__BJX         = 20111;


    public static function getAtomStatusSet($facadeStatus)
    {
        switch ($facadeStatus)
        {
            case self::USER_ORDER_STATUS_TO_EXPRESS:  //用户-待发货
                return array(
                    self::ORDER_STATUS_TO_BD_CHECK,
                    self::ORDER_STATUS_TO_YY_CHECK,
                );
            case self::USER_ORDER_STATUS_TO_RECEIVE:  //用户-待收货
                return array(
                    self::ORDER_STATUS_TO_EXPRESS,
                    self::ORDER_STATUS_TO_BD_RECEIVE,
                    self::ORDER_STATUS_TO_USER_RECEIVE,
                );
            case self::USER_ORDER_STATUS_DONE:  //用户-已完成
            case self::BD_ORDER_STATUS_DONE:  //BD-已完成
                return array(
                    self::ORDER_STATUS_BD_DENIED,
                    self::ORDER_STATUS_YY_DENIED,
                    self::ORDER_STATUS_FINISHED,
                );
            case self::YY_ORDER_STATUS_TO_BD_CHECK:  //运营-待BD处理
            case self::BD_ORDER_STATUS_TO_CHECK:  //BD-待处理
                return array(
                    self::ORDER_STATUS_TO_BD_CHECK,
                );
            case self::YY_ORDER_STATUS_TO_YY_CHECK:  //运营-待审核
                return array(
                    self::ORDER_STATUS_TO_YY_CHECK,
                );
            case self::YY_ORDER_STATUS_CONFIRMED:  //运营-已确认
                return array(
                    self::ORDER_STATUS_TO_EXPRESS,
                    self::ORDER_STATUS_TO_BD_RECEIVE,
                    self::ORDER_STATUS_TO_USER_RECEIVE,
                    self::ORDER_STATUS_FINISHED,
                );
            case self::BD_ORDER_STATUS_CHECKED:  //BD-已处理
                return array(
                    self::ORDER_STATUS_TO_YY_CHECK,
                    self::ORDER_STATUS_TO_EXPRESS,
                    self::ORDER_STATUS_TO_BD_RECEIVE,
                    self::ORDER_STATUS_TO_USER_RECEIVE,
                );
            case self::YY_ORDER_STATUS_DENIED: //运营-驳回
                return array(
                    self::ORDER_STATUS_YY_DENIED,
                    self::ORDER_STATUS_BD_DENIED,
                );
            case self::BD_ORDER_STATUS_CHECKED__BJX:  //BD-已处理-北极星
                return [
                    self::ORDER_STATUS_TO_YY_CHECK,
                    self::ORDER_STATUS_TO_EXPRESS,
                    self::ORDER_STATUS_TO_BD_RECEIVE,
                ];
            case self::BD_ORDER_STATUS_DONE__BJX:  //BD-已完成-北极星
                return [
                    self::ORDER_STATUS_TO_USER_RECEIVE,
                    self::ORDER_STATUS_BD_DENIED,
                    self::ORDER_STATUS_YY_DENIED,
                    self::ORDER_STATUS_FINISHED,
                ];
            default:
                return array();
        }
    }

    public static function toUserOrderStatus($atomStatus)
    {
        switch ($atomStatus)
        {
            case self::ORDER_STATUS_TO_BD_CHECK:
            case self::ORDER_STATUS_TO_YY_CHECK:
                return self::USER_ORDER_STATUS_TO_EXPRESS;
            case self::ORDER_STATUS_TO_EXPRESS:
            case self::ORDER_STATUS_TO_BD_RECEIVE:
            case self::ORDER_STATUS_TO_USER_RECEIVE:
                return self::USER_ORDER_STATUS_TO_RECEIVE;
            case self::ORDER_STATUS_BD_DENIED:
            case self::ORDER_STATUS_YY_DENIED:
            case self::ORDER_STATUS_FINISHED:
                return self::USER_ORDER_STATUS_DONE;
            default:
                return 0;
        }
    }

    public static function getFacadeOrderStatusDesc($atomStatus)
    {
        switch ($atomStatus)
        {
            case self::ORDER_STATUS_TO_BD_CHECK:
            case self::ORDER_STATUS_TO_YY_CHECK:
                return '待发货';
            case self::ORDER_STATUS_TO_EXPRESS:
            case self::ORDER_STATUS_TO_BD_RECEIVE:
            case self::ORDER_STATUS_TO_USER_RECEIVE:
                return '待收货';
            case self::ORDER_STATUS_BD_DENIED:
            case self::ORDER_STATUS_YY_DENIED:
                return '已取消';
            case self::ORDER_STATUS_FINISHED:
                return '已完成';
            default:
                return '';
        }
    }

    public static function getUserOrderTitle($orderItem)
    {
        $orderStatus = $orderItem['orderStatus'];
        if (self::ORDER_STATUS_BD_DENIED == $orderStatus || self::ORDER_STATUS_YY_DENIED == $orderStatus) {
            // 驳回状态
            return '兑换单';
        }

        if (self::ORDER_STATUS_TO_BD_CHECK == $orderStatus) {
            return '专属助教处理中';
        }

        $addressType = $orderItem['addressType'];
        if (self::ORDER_ADDRESS_TYPE_KDZY == $addressType) {
            return '快递直邮';
        } else if (self::ORDER_ADDRESS_TYPE_ZJPS == $addressType) {
            return '专属助教派送';
        }

        return '';
    }

    /**
     * 获取派送方式文案
     * @param $type
     * @return string
     */
    public static function getAddresstypeContent($type)
    {
        $arr = [
            self::ORDER_ADDRESS_TYPE_ZJPS => '助教派送',
            self::ORDER_ADDRESS_TYPE_KDZY => '快递直邮',
        ];

        return isset($arr[$type]) ? $arr[$type] : '未知';
    }

    public static function getUserStatusDesc($userStatus)
    {
        switch ($userStatus)
        {
            case self::USER_ORDER_STATUS_ALL:
                return '全部';
            case self::USER_ORDER_STATUS_TO_RECEIVE:
                return '待收货';
            case self::USER_ORDER_STATUS_TO_EXPRESS:
                return '待发货';
            case self::USER_ORDER_STATUS_DONE:
                return '已完结';
            default:
                return '';
        }
    }

}
