<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file: FudaoMsgSignal.php
 * @author: huanghe <<EMAIL>>
 * @date: 2017/9/15 上午11:38
 * @brief: 课中互动信令列表
 */
class Hkzb_Const_FudaoMsgSignal
{
    //消息类型
    const SEND_EXERCISE_CARD = 31006; // 通知学生端取课间答题卡
    const NEW_MSG = 33001; // 有新的消息推送
    const FORBIDDEN_TALK = 33002; // 禁言推送
    const FREE_TALK = 33003; // 取消禁言推送
    const OPEN_SHOOT = 33004; // 开启弹幕
    const OPEN_ASSISTANG_SHOOT = 33005; // 仅辅导员开启弹幕
    const CLOSE_SHOOT = 33006; // 关闭弹幕
    const ZHUJIANG_LESSON_BEFORE_SIGNAL = 33007; // 课前签到主讲端消息推送
    const EVULATE = 33100; // 课间学生给老师点赞
    const YESNO = 31008; // 是否卡
    const CUR_LESSON_STATUS = 31009; // 上课状态（直播，暂停）
    const STUDENT_ATTEND = 31010; // 提醒主讲老师学生上线
    const PUSH_RANK = 31011; // 推送排行榜信息
    const PUSH_MEDAL = 31012; // 推送勋章信息
    const PUSH_RECOMMEND_COURSE = 31013; // 推送推荐课程信息
    const PUSH_COURSE_REMIND = 31014; // 上课提醒
    const STUDENT_CLIENT_COMMAND = 31015; // 学生端命令消息（CDN相关）
    const TEACHER_CLIENT_COMMAND = 31016; // 主讲端命令消息（CDN相关）
    const TEACHER_CLIENT_NOTICE = 31018; // 主讲端通知消息
    const COURSE_LESSON_RESTART = 31017; // 课程课节重开
    const SEND_EXERCISE_RESULT = 31021; // 推送答题结果
    const YESNO_RESULT = 31019; // 推送是否卡结果
    const PUSH_PRAISE_STUDENT = 31020; // 推送表扬学生
    const PUSH_YESNO_STOP = 31022; // 停止是否卡
    const STOP_EXERCISE = 31007; // 结束答题
    const FORBIDDEN_PUSH_TEAHCER = 31023; // 辅导老师禁言推送给主讲
    const STUDENT_LIST_EDIT = 31024; // 主讲端学生列表通知
    const EXAM_START = 31025; //开始考试
    const EXAM_STOP = 31026; //结束考试
    const ADD_SCORE = 31027; //加分
    const SPEEDEXAM_START = 31028; //竞速测试
    const INCLASSSIGN_ON = 31029; //签到开启
    const INCLASSSIGN_OFF = 31030; //签到结束
    const SEND_SCORE_RED_ENVELOPE = 31031;//课中发积分红包
    const CLOSE_SCORE_RED_ENVELOPE = 31032;//关闭积分红包
    const STUDENT_ENGLISH_RECORD = 31034;//===英语课中录音信令
    const STUDENT_ENGLISH_MATCH = 31035;//===英语课中匹配题信令
    const STUDENT_CLOSE_ENGLISH_RECORD = 31036;//===英语课中关闭录音题信令
    const STUDENT_CLOSE_ENGLISH_MATCH = 31037;//===英语课中关闭匹配题信令
    const STUDENT_PRAISE_ENGLISH_RECORD = 31038;//===表扬课中英语录音信令
    const SINGLE_LIVE = 31039;//===课中单点直播信令
    const LIVE_ENCODE_PARAMS = 31040;//直播流编码参数信令号
    const CLASS_COME_REMIND = 31041;//===课中班级到课提醒
    const MSG_TYPE_INCLASS_HTML_CACHE = 31042;//===html5插件缓存
    const TEACHER_ACTION_STATISTICS = 31043;  // 主讲端老师行为统计卡
    const SEND_PRIMARY_MATH = 31044;  //主讲端发送小学数学   新增
    const SEND_BARRAGE = 31045; //发送语音互动消息（弹幕）
    const MSG_INTERACTION_DIFFICULTY = 31046; //主讲端发送互动题难易程度信息
    const START_BARRAGE = 31047; //主讲开启语音互动（弹幕）
    const STOP_BARRAGE = 31048; //主讲关闭语音互动（弹幕）
    const ASSIST_VIDEO_COMMAND = 31049;//小数2.0旁路更换主讲推流cdn
    const SEND_LIVE_ROOM_SWITCH = 31050;//liveui 教师直播间切换通知学生
    const SEND_LIVE_ROOM_ACTIVE_STATUS = 31051;//liveui 教师直播间切换通知教师改状态
    const SEND_FINISH_ROOM = 31052;//liveui 通知班主任课前下课
    const SEND_FINISH_COUPON = 31053;//teacherlive 通知学生优惠券抢光了
    const TEACHER_LIVEROOM_PRESWITCH = 31054;//主讲老师预切直播间通知辅导老师
    const STUDENT_RETRY_ENTERROOM = 31055; //通知学生自动退出重进教室操作
    const SEND_NOTIFY_ASSISTANT_ROOM = 31056; //通知辅导老师

    //小数mvp课中互动
    const STUDENT_STATUS_BROADCAST = 37001; //学生状态广播
    const SEND_CHAT_HOT_WORDS = 37002; //学生发送聊天热词
    const SYN_INTERACT_STATUS = 37003; //组内同步互动状态
    const STUDENT_LONG_LINKS_BREAK = 37004; //组内同学长链接断开
    const STUDENT_GROUP_STATUS_BROADCAST = 37005; //分组界面广播
    const STUDENT_GROUP_MEMBERS_STATUS = 37006; //组内成员状态变更
    const XS_RECOVERY_NOTICE = 38001; //灾备通知
    const VIDEO_STATUS = 37008; //禁视频/解禁
    const STU_PUSH_STREAM = 37111; //推流、关流
    const SYN_POINTS = 37010; // 组内能量值排名及能量值广播
    const TEA_CUR_PAGE = 37112; //推流、关流

    const APPLY_MIC = 15001;  // 申请连麦
    const CANCEL_MIC = 15002;  // 取消连麦
    const SEND_OFFER = 15003;  // 发送offer
    const SEND_ANSWER = 15004;  // 发送answer
    const SEND_CANDIDATE = 15005;  // 发送ice cadidate
    const REFUSE_MIC = 15006;  // 拒绝连麦
    const HANGUP_MIC = 15007;  // 挂断
    const START_MIC = 15008;  // 开启连麦功能
    const STOP_MIC = 15009;  // 关闭连麦功能
    const APPLY_ACCEPT = 15010;  // 选择连麦学生
    const ACTION_CONNECT_STUDENT = 15011;  // 当前正在连麦的学生
    const HANGUP_BROADCAST = 15012;  // 广播老师已经挂断连麦
    const MIC_SUCCEESS = 15013;  // 广播老师已经连麦成功

    const FAKE_START_MIC = 40001; // 伪直播连麦开始
    const FAKE_CANCEL_MIC = 40002; // 伪直播连麦取消
    const FAKE_CONNECT_MIC = 40003; // 伪直播连麦上麦
    const FAKE_HANGUP_MIC = 40004; // 伪直播连麦挂断

    const APPLY_MIC_PRIVILEGE = 31033;  // 抢麦特权信息
    // 流媒体连麦专用
    const SIG_MIC_TO_ALL_STUDENT_UNMUTESTUDENT = 15015;  // 广播给所有学生那个学生被解除静音
    const SIG_MIC_TO_STUDENT_UNMUTESTUDENT = 15016;  // 通知学生恢复静音
    const SIG_MIC_TO_STUDENT_MUTESTUDENT = 15018;  // 通知学生被静音
    const SIG_MIC_TO_ALL_STUDENT_MUTESTUDENT = 15017;  // 广播给所有学生那个学生被静音
    const SIG_MIC_TO_STUDENT_UPDATE_SDP = 15020;  // 通知指定用户更新sdp
    const SIG_STUDENT_VIDEOBUFFER_CHANGE = 15021;

    // Zybrtc类信令
    const RTC_PUBSUCC = 16001; // 新流发布成功
    const RTC_ANSWER = 16002; // answer
    const RTC_CANCELPUB = 16003; // 取消流发布
    const RTC_FOLLOWER_NUM = 16004; // 订阅粉丝数通知

    //课中新题型相关信令(h5插件通用信令)
    const PLUGIN_COMMON_START_SIGNAL = 51000;//===H5插件通用开始信令
    const PUBLIC_STATE_START = 1;//开始
    const PUBLIC_STATE_UPDATE = 3;//更新
    const PUBLIC_STATE_END = 5;//结束

    const MARKETING_COMMON_START = 51006;//课中营销开始
    const MARKETING_COMMON_STOP = 51007;//课中营销结束
    const ADD_WECHAT_START = 51008;//添加督学微信
    const NOBOOK_PLANB_SIGNAL = 51004;  //目前只有nobook planB 使用的信令
    const TEACHERLIVE_AWARD_COMMON_SATRT = 51009; //课中寒春抽奖开始
    const TEACHERLIVE_AWARD_COMMON_STOP = 51010; //课中寒春抽奖结束

    const XS_LABEL_UPDATE_SIGNAL = 52001;  //学生组内称号更新
    const XS_SCORE_UPDATE_SIGNAL = 52002;  //学生组内获得学分更新
    const XS_H5_COMMON_PLUGIN_START = 36002; //小数h5通用插件开始信令
    const XS_TEACHER_PRAISE_STUDENT = 36001; //小数老师点赞学生表扬

    //中台使用长链接命令号
    //浣熊方使用
    const INTERACT_CMD_CLOUDBARRAGE_SWITCH = 83200; //语音弹幕开关
    const INTERACT_CMD_CLOUDBARRAGE_SEND = 83201; //语音弹幕发送
    const INTERACT_CMD_PLATFORM_ON = 83202;//上台互动 上台
    const INTERACT_CMD_PLATFORM_OFF = 83203;//上台互动 下台
    const INTERACT_CMD_PLATFORM_SCORE = 83204;//上台互动 学分推送

    //拉新0转正
    const LAXIN_ZERO_INCLASS_SEND_NOTICE   = 90001; //作业帮直播课课程小程序直播间置顶公告
    const LAXIN_ZERO_INCLASS_MARKETING     = 90002; //作业帮直播课课程小程序课中营销

    public static $SIGNO_MAP = array(

        self::SEND_EXERCISE_CARD => '发送答题卡',
        self::NEW_MSG => '有新的消息',
        self::FORBIDDEN_TALK => '禁言',
        self::FREE_TALK => '取消禁言',
        self::OPEN_SHOOT => '开启弹幕',
        self::OPEN_ASSISTANG_SHOOT => '仅辅导员开启弹幕',
        self::CLOSE_SHOOT => '关闭弹幕',
        self::EVULATE => '学生给老师点赞',
        self::YESNO => '发送是否卡',
        self::CUR_LESSON_STATUS => '上课状态修改',
        self::STUDENT_ATTEND => '学生上线',
        self::PUSH_RANK => '推送排行榜',
        self::PUSH_MEDAL => '推送勋章',
        self::PUSH_RECOMMEND_COURSE => '推送推荐课程',
        self::PUSH_COURSE_REMIND => '上课提醒',
        self::STUDENT_CLIENT_COMMAND => '学生端命令消息(CDN相关)',
        self::TEACHER_CLIENT_COMMAND => '主讲端命令消息（CDN相关)',
        self::TEACHER_CLIENT_NOTICE => '主讲端通知消息',
        self::COURSE_LESSON_RESTART => '课程课节重开',
        self::SEND_EXERCISE_RESULT => '推送答题结果',
        self::YESNO_RESULT => '是否卡结果',
        self::PUSH_PRAISE_STUDENT => '表扬学生',
        self::PUSH_YESNO_STOP => '停止是否卡',
        self::STOP_EXERCISE => '结束答题',
        self::FORBIDDEN_PUSH_TEAHCER => '辅导老师禁言推送给主讲',
        self::STUDENT_LIST_EDIT => '主讲端学生列表通知',
        self::EXAM_START => '开始考试',
        self::EXAM_STOP => '结束考试',
        self::ADD_SCORE => '加分',
        self::SPEEDEXAM_START => '竞速测试',
        self::INCLASSSIGN_ON => '签到开启',
        self::INCLASSSIGN_OFF => '签到结束',
        self::SEND_SCORE_RED_ENVELOPE => '发积分红包',
        self::CLOSE_SCORE_RED_ENVELOPE => '关闭积分红包',
        self::APPLY_MIC => '申请连麦',
        self::CANCEL_MIC => '取消连麦',
        self::SEND_OFFER => '发送offer',
        self::SEND_ANSWER => '发送answer',
        self::SEND_CANDIDATE => '发送ice cadidate',
        self::REFUSE_MIC => '拒绝连麦',
        self::HANGUP_MIC => '挂断',
        self::START_MIC => '开启连麦功能',
        self::STOP_MIC => '关闭连麦功能',
        self::APPLY_ACCEPT => '选择连麦学生',
        self::APPLY_MIC_PRIVILEGE => '抢麦特权信息',
        self::RTC_PUBSUCC => '新流发布成功',
        self::RTC_ANSWER => 'answer',
        self::RTC_CANCELPUB => '取消流发布',
        self::RTC_FOLLOWER_NUM => '订阅粉丝数通知',
        self::STUDENT_ENGLISH_RECORD => '英语课中录音',
        self::STUDENT_ENGLISH_MATCH => '英语课中匹配题',
        self::STUDENT_CLOSE_ENGLISH_RECORD => '英语课中关闭录音题',
        self::STUDENT_CLOSE_ENGLISH_MATCH => '英语课中关闭匹配题',
        self::STUDENT_PRAISE_ENGLISH_RECORD => '表扬课中英语录音',
        self::SINGLE_LIVE => '课中单点直播',
        self::LIVE_ENCODE_PARAMS => '直播流编码参数',
        self::CLASS_COME_REMIND => '课中班级到课提醒',
        self::ACTION_CONNECT_STUDENT => '当前正在连麦的学生',
        self::HANGUP_BROADCAST => '广播老师已经挂断连麦',
        self::MIC_SUCCEESS => '广播老师已经连麦成功',
        self::PLUGIN_COMMON_START_SIGNAL => 'H5插件通用开始信令',
        self::SEND_PRIMARY_MATH => '主讲端发送小数',
        self::SEND_BARRAGE => '发送语音互动消息（弹幕）',
        self::MSG_INTERACTION_DIFFICULTY => '主讲端发送互动题难易程度信息',
        self::START_BARRAGE => '主讲开启语音互动（弹幕）',
        self::STOP_BARRAGE => '主讲关闭语音互动（弹幕）',
        self::SIG_STUDENT_VIDEOBUFFER_CHANGE => '后台修改学生端视频buffer时间',
        self::MARKETING_COMMON_START => '课中营销开始',
        self::MARKETING_COMMON_STOP => '课中营销结束',
        self::XS_LABEL_UPDATE_SIGNAL => '学生组内称号更新',
        self::XS_SCORE_UPDATE_SIGNAL => '学生组内获得学分更新',
        self::XS_H5_COMMON_PLUGIN_START => '小数h5通用插件开始信令',
        self::XS_TEACHER_PRAISE_STUDENT => '小数老师点赞学生表扬',
        self::ASSIST_VIDEO_COMMAND => '小数2.0旁路更换主讲推流cdn',
        self::SEND_LIVE_ROOM_SWITCH => '教师直播间切换通知学生',
        self::SEND_LIVE_ROOM_ACTIVE_STATUS => '教师直播间切换通知教师改状态',
        self::SEND_FINISH_ROOM => '通知班主任课前下课',
        self::FAKE_START_MIC => '伪直播连麦开始',
        self::FAKE_CANCEL_MIC => '伪直播连麦取消',
        self::FAKE_CONNECT_MIC => '伪直播连麦上麦',
        self::FAKE_HANGUP_MIC => '伪直播连麦下麦',
        self::LAXIN_ZERO_INCLASS_SEND_NOTICE    => '直播间置顶公告',
        self::LAXIN_ZERO_INCLASS_MARKETING      => '课中营销',
    );

}
