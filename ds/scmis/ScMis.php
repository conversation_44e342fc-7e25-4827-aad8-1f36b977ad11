<?php
/**
 * @file: ScMis.php
 * @author: <EMAIL>
 * @date: 2017-11-11
 * @brief：销售系统公共库
 */

class Hkzb_Ds_Scmis_ScMis{
    const SERVICE_DOMAIN_OL = 'http://platmis.zuoyebang.cc/scmis';

    /**
     * 咨询指定课程能否上线
     * @param integer $intCourseId 课程ID
     * @return array
     */

     public static function askPublishCourse($intCourseId){
         $intCourseId = (int) $intCourseId;
         $arrOutput = [
            'errNo'=> 0,
            'errStr' => 'success'
         ];
        if(empty($intCourseId)){
            $arrOutput['errNo'] = 1;
            $arrOutput['errStr'] = '参数错误';
            return $arrOutput;
        }

        //ral调用
         $header = [
             'pathinfo'    => '/scmis/api/askpublishcourse',
             'querystring' => 'courseId='.$intCourseId,
         ];
        $ralRet = ral('fudao_scmis','get',[],rand(),$header);
        $ralRet = @json_decode($ralRet,true);

        //根据业务线的响应生成对应响应
        if($ralRet){
            $arrOutput['errNo']  = $ralRet['errNo'];
            $arrOutput['errStr'] = $ralRet['errstr'];
            //不等于0时 2 数据库失败时 联系研发
            if(2 === $arrOutput['errNo']){
                $arrOutput['errStr'].=',请稍后重试或【截图】并钉钉联系scmis的研发同学';

            }
            if(!is_integer($arrOutput['errNo'])){
                //兼容我们的nginx在一些异常情况下，会返回302 在这种情况下errNo会为null 避免调用方没有强验证的情况
                //使用网络原因代码
                $arrOutput['errNo'] = 2;
                $arrOutput['errStr'] = '咨询课程能否上线失败(网络原因，curl出错)，请【截图】并钉钉联系scmis的研发同学';
                Bd_Log::warning('Error[Service ScMis Curl failed],Detail:[errNo is not integer]');
            }
        }else{
            //网络原因
            $arrOutput['errNo'] = 2;
            $arrOutput['errStr'] = '咨询课程能否上线失败(网络原因，curl出错)，请【截图】并钉钉联系scmis的研发同学';
            Bd_Log::warning('Error[Service ScMis Curl failed],Detail:[errno:'.$arrOutput['errNo'].',errstr:'.$arrOutput['errStr'].']');
        }

        //添加notice
        $errNo  = isset($ralRet["errNo"]) ? $ralRet["errNo"] : "null";
        Bd_Log::addNotice("ScMisAskPublish_ErrNo",$errNo);

        return $arrOutput;
    }


}