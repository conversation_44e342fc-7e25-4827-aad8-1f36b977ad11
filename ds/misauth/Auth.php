<?php

/**
 * @file    Auth.php
 * <AUTHOR>
 * @date    2018-5-17
 * @brief   权限公用操作类
 *
 **/
class Hkzb_Ds_MisAuth_Auth
{
    const IPS_SID = "misauthonline";
    const IPS_SECRET = "809f71e3c5400cea0bbd10995e88c1c6";
    const IPS_PATH = "/";

    //没有登录2048
    const MISAUTH_AUTH_NOTLOGIN = -1001;
    //登录了不用验证权限
    const MISAUTH_AUTH_LOGINNOTAUTH = -1002;
    //登录了没有权限
    const MISAUTH_AUTH_LOGINNOPOWER = -1003;
    //登录了有权限
    const MISAUTH_AUTH_HASPOWER = -1004;

    private static $PLATFORM_DELETED_DESC_MAP = [
        self::MISAUTH_AUTH_NOTLOGIN => '没有登录',
        self::MISAUTH_AUTH_HASPOWER => '登录有权限',
        self::MISAUTH_AUTH_LOGINNOPOWER => '登录没有权限',
        self::MISAUTH_AUTH_LOGINNOTAUTH => '登录不用验证权限',
    ];

    private static $errNo = '';
    private static $data = '';
    private static $errStr = '';
    private static $userInfo = '';

    public static function Authenticate()
    {
        //匹配是否登录
        if(self::isLogin()){
            //匹配是否需要验证权限
            if(self::isNeedVaildate()){
                //匹配是否有权限
                if(self::isAuth()){
                    self::$errNo = self::MISAUTH_AUTH_HASPOWER;
                    self::$errStr = self::$PLATFORM_DELETED_DESC_MAP[self::MISAUTH_AUTH_HASPOWER];
                    self::$data = self::getDataAuth();
                }
            }
        }

        return self::display();
    }

    //验证是否登录
    private static function isLogin()
    {
//        $arrUserInfo = Saf_SmartMain::getUuapUserInfo();
        $ips = new Saaslib_Service_IPSV2(self::IPS_SID, self::IPS_SECRET, self::IPS_PATH, true);
        $arrUserInfo = $ips->getSession();

        if (empty($arrUserInfo) || empty($arrUserInfo['uname'])) {

            $arrUserInfo    = Saf_SmartMain::getUserInfo();

            if(!empty($arrUserInfo) && !empty($arrUserInfo['phone'])) {
                $objUser = new Hkzb_Dao_Misauth_User();
                $arrConds = array(
                    'deleted' => 0,
                    'userMobile' => $arrUserInfo['phone'],
                );
                $arrAppends = array(
                    "limit 1",
                );
                $user = $objUser->getRecordByConds($arrConds, ['userId', 'userMobile', 'userName', 'userAccount'], NULL, $arrAppends);

                $arrUserInfo['uname'] = $user['userAccount'];
            }
        }

        if (empty($arrUserInfo['uname'])) {
            $host         = $_SERVER['HTTP_HOST'];
            self::$errNo  = self::MISAUTH_AUTH_NOTLOGIN;
            self::$errStr = self::$PLATFORM_DELETED_DESC_MAP[self::MISAUTH_AUTH_NOTLOGIN];
            if (Hk_Util_Ip::isInnerIp()) {
                $idcConf= Bd_Conf::getConf('/idc');
                if(!isset($idcConf['cur']) || $idcConf['cur'] == 'test'){
                    self::$data['data'] = "https://ips.zuoyebang.cc/static/cas-fe/?sid=".self::IPS_SID."&service=" . urlencode("http://{$host}/misauthonline/login/login");
                }else if (strpos($host, 'www') === false) {
//                    self::$data['data'] = "https://cas.zuoyebang.cc/login?service=" . urlencode("http://" . $host . "/misauthonline/login/login");
                    self::$data['data'] = "https://ips.zuoyebang.cc/static/cas-fe/?sid=".self::IPS_SID."&service=" . urlencode("http://mis.zuoyebang.cc/misauthonline/login/login");
                } else {
//                    self::$data['data'] = "https://cas.zuoyebang.cc/login?service=" . urlencode("http://mis.zuoyebang.cc/misauthonline/login/login");
                    self::$data['data'] = "https://ips.zuoyebang.cc/static/cas-fe/?sid=".self::IPS_SID."&service=" . urlencode("http://mis.zuoyebang.cc/misauthonline/login/login");
                }
            } else {
                self::$data['data'] = "http://www.zuoyebang.com/passport/login?redirect=" . urlencode("http://$host/static/misauthonline");
            }
            Bd_Log::warning("[Hkzb_Ds_MisAuth_Auth] 没有登录，请跳转".self::$data['data']);

            return false;
        }

        $userInfo = self::getUserInfo($arrUserInfo);

        $arrUserInfo['nickName'] = $userInfo['userName'];
        $arrUserInfo['uid'] = $userInfo['userId'];
        $arrUserInfo['userMobile'] = $userInfo['userMobile'];

        if(!isset($arrUserInfo['isLogin'])){
            $arrUserInfo['isLogin'] = true;
        }

        self::$userInfo = $arrUserInfo;
        return true;
    }

    private static function getUserInfo($arrUserInfo)
    {
        $objUser = new Hkzb_Dao_Misauth_User();

        $arrConds = array(
            'deleted' => 0,
            'userAccount' => $arrUserInfo['uname'],
        );

        $arrAppends = array(
            "limit 1",
        );

        return $objUser->getRecordByConds($arrConds, ['userId','userMobile','userName'], NULL, $arrAppends);
    }

    //匹配是否需要验证权限
    private static function isNeedVaildate()
    {
        //调用misauth的获取数据权限接口
        $header = array(
            'pathinfo' => "/misauth/api/isauth",
            'refer' => $_SERVER['REQUEST_URI'],
        );

        $ret = ral('platmis', 'POST', [], 123, $header);
        
        if($ret == 1){
            //需要验证权限
            return true;
        }else{
            //不需要验证权限
            self::$errNo = self::MISAUTH_AUTH_LOGINNOTAUTH;
            self::$errStr = self::$PLATFORM_DELETED_DESC_MAP[self::MISAUTH_AUTH_LOGINNOTAUTH];
            self::$data = self::getDataAuth();
            return false;
        }
    }

    //匹配是否有权限
    private static function isAuth()
    {
        //调用misauth的获取数据权限接口
        $header = array(
            'pathinfo' => "/misauth/api/hasauth",
            'refer' => $_SERVER['REQUEST_URI'],
        );

        $arrParams = array(
            'uname' => self::$userInfo['uname']
        );

        $ret = ral('platmis', 'POST', $arrParams, 123, $header);

        if($ret == 1){
            return true;
        }else{
            self::$errNo = self::MISAUTH_AUTH_LOGINNOPOWER;
            self::$errStr = self::$PLATFORM_DELETED_DESC_MAP[self::MISAUTH_AUTH_LOGINNOPOWER];
            self::$data = [];
            return false;
        }
    }

    private static function getDataAuth()
    {
        //调用misauth的获取数据权限接口
        $header = array(
            'pathinfo' => "/misauth/api/getdataauth",
            'refer' => $_SERVER['REQUEST_URI'],
        );

        $arrParams = array(
            'uname' => self::$userInfo['uname']
        );

        $ret = ral('platmis', 'POST', $arrParams, 123, $header);

        return json_decode($ret,true);
    }


    /**
     * 匹配返回值
     */
    private static function display()
    {
        return [
            'errNo' => self::$errNo,
            'errStr' => self::$errStr,
            'data' => [
                'userInfo' => self::$userInfo,
                'dataAuth' => self::$data['data'],
            ]
        ];
    }

    /**
     * 保存用户访问的平台id
     */
    private static function setLastPlatform()
    {
        $platformId = '';
        $_objPlatform = new Hkzb_Dao_Misauth_Platform();
        $platformCondition = array(
            'deleted' => Hkzb_Const_Misauth::PLATFORM_DELETED_NO,
        );
        $platforms = $_objPlatform->getListByConds($platformCondition, ['platformId','platformKeyword']);

        foreach($platforms as $platform){
            if(strpos($_SERVER['REQUEST_URI'],$platform['platformKeyword']) !== false){
                $platformId = $platform['platformId'];
                break;
            }
        }

        if(!empty($platformId))
        {
            $objCache = Hk_Service_RedisClient::getInstance("zhibo");
            
            //获取redisKey
            $redisKey = Hkzb_Const_Misauth::API_REDIS_USER_LAST_PLATFORM.self::$userInfo['uname'];
            $setRes = $objCache->set($redisKey,$platformId);
            if($setRes == false)
            {
                Bd_Log::warning("setLastPlatform failed redisKey : ".$redisKey." | platformId : ".$platformId);
            }
        }
    }

}