<?php

/**
 * Created by PhpStorm.
 * User: zhang<PERSON><EMAIL>
 * Date: 2020/10/21
 * Time: 17:25
 */
class Lxjxlib_Ds_Crm_SchoolCustomer extends Lxjxlib_Ds_BaseData
{
    // TODO: LAXIN: 先注释掉公共方法，用到的话就会报错
    // use Lxjxlib_Ds_EsDataTrait;

    protected $daoClassName = 'Lxjxlib_Dao_Crm_SchoolCustomer';
    // TODO: LAXIN: 下线这个es?
    protected $esDaoClassName = 'Lxjxlib_Dao_Crm_BdlSchoolCustomer';

    /*
     * 删除状态
     */
    const STATUS_DELETED_NO  = 1;           //正常
    const STATUS_DELETED_YES = 2;           //删除

    public function __construct()
    {
        // TODO: LAXIN: 先注释掉公共方法，用到的话就会报错
        // $this->esDaoInIt();
        parent::__construct();
    }

    /**
     * 获取分页学校列表
     * @param $pn
     * @param $rn
     * @param $arrConds
     * @return array
     */
    public function getMoreSchoolList($pn, $rn, $arrConds)
    {
        $arrFields = [
            'id',
            'provinceId',
            'provinceName',
            'cityId',
            'cityName',
            'countyId',
            'countyName',
            'schoolName',
            'schoolId',
        ];

        $arrAppends = [
            'order by id desc',
        ];

        $arrConds['status'] = Lxjxlib_Const_Common::DB_STATUS_OK;

        $data = $this->getMoreList($pn, $rn, $arrConds, $arrFields, $arrAppends);
        return $data;
    }

    /**
     * 获取分页学校列表-ES
     * @param $pn
     * @param $rn
     * @param $arrConds
     * @return array
     */
    public function getMoreSchoolListES($pn, $rn, $arrConds, $withScore = false)
    {
        $arrFields = [
            'id',
            'provinceId',
            'provinceName',
            'cityId',
            'cityName',
            'countyId',
            'countyName',
            'schoolName',
            'schoolId',
        ];

        if (!$withScore) {
            $arrAppends = [
                'sort' => [
                    'id' => 'desc',
                ],
            ];
        }

        $arrConds['status'] = Lxjxlib_Const_Common::DB_STATUS_OK;

        $data = $this->getMoreListByCondsES($pn, $rn, $arrConds, $arrFields, $arrAppends, $withScore);

        return $data;
    }

    /**
     * 根据一组schoolId获取所相关的学校数据
     * @param array $schoolIdArr
     * @param array $arrFields
     * @return array
     */
    public function getSchoolInfo($schoolIdArr, $arrFields = [])
    {
        if (empty($schoolIdArr)) {
            return [];
        }
        $where = [
            'school_id in (' . implode(',', $schoolIdArr) . ')',
            'status' => Lxjxlib_Const_Common::DB_STATUS_OK,
        ];

        return $this->getListByConds($where, $arrFields);
    }

    /**
     * 根据一组schoolId获取所相关的学校数据-ES
     * @param       $schoolIdArr
     * @param array $arrFields
     * @return array|bool
     */
    public function getSchoolInfoES($schoolIdArr, $arrFields = [])
    {
        if (empty($schoolIdArr)) {
            return [];
        }
        $where = [
            'school_id in (' . implode(',', $schoolIdArr) . ')',
            'status' => Lxjxlib_Const_Common::DB_STATUS_OK,
        ];

        return $this->getListByCondsES($where, $arrFields);
    }

    /**
     * 获取区县学校
     * @param $countyId
     * @return array
     */
    public function getCountySchool($countyId)
    {
        $arrConds  = [
            'countyId' => $countyId,
            'status'   => Lxjxlib_Const_Common::DB_STATUS_OK,
        ];
        $arrFields = [
            'schoolId', 'schoolName', 'provinceName', 'cityName', 'countyName',
        ];
        $ret       = $this->getListByConds($arrConds, $arrFields);
        return $ret;
    }

    /**
     * 获取区县学校-ES
     * @param $countyId
     * @return array
     */
    public function getCountySchoolES($countyId)
    {
        $arrConds  = [
            'countyId' => $countyId,
            'status'   => Lxjxlib_Const_Common::DB_STATUS_OK,
        ];
        $arrFields = [
            'schoolId', 'schoolName', 'provinceName', 'cityName', 'countyName',
        ];
        $ret       = $this->getListByCondsES($arrConds, $arrFields);
        return $ret;
    }

    public function getPageSchoolList($pn, $rn, $arrConds)
    {
        $arrFields = [
            'schoolId',
            'provinceId',
            'provinceName',
            'cityId',
            'cityName',
            'countyId',
            'countyName',
            'schoolName',
        ];

        $arrConds['status'] = Lxjxlib_Const_Common::DB_STATUS_OK;

        $arrAppends = [
            'order by school_id desc',
        ];

        $data = $this->getPageList($pn, $rn, $arrConds, $arrFields, $arrAppends);
        return $data;
    }

    /**
     * 获取学校分页列表-ES
     * @param $pn
     * @param $rn
     * @param $arrConds
     * @return array
     */
    public function getPageSchoolListES($pn, $rn, $arrConds)
    {
        $arrFields = [
            'schoolId',
            'provinceId',
            'provinceName',
            'cityId',
            'cityName',
            'countyId',
            'countyName',
            'schoolName',
        ];

        $arrConds['status'] = Lxjxlib_Const_Common::DB_STATUS_OK;

        $arrAppends = [
            'sort' => [
                'school_id' => 'desc',
            ],
        ];

        $data = $this->getPageListByCondsES($pn, $rn, $arrConds, $arrFields, $arrAppends);
        return $data;
    }


    public function getSchoolList($params)
    {
        $conds['status'] = Lxjxlib_Const_Common::DB_STATUS_OK;
        if (!empty($params['provinceId'])) {
            $conds['provinceId'] = $params['provinceId'];
        }
        if (!empty($params['cityId'])) {
            $conds['cityId'] = $params['cityId'];
        }
        if (!empty($params['countyId'])) {
            $conds['countyId'] = $params['countyId'];
        }
        if (!empty($params['claimantUid'])) {  //学校负责的BD
            $conds[] = "claimant_uid IN (" . implode(',', $params['claimantUid']) . ")";
        }
        $fields = [
            'schoolId', 'schoolName', 'provinceName', 'cityName', 'countyName',
        ];

        return $this->getListByConds($conds, $fields);
    }
}