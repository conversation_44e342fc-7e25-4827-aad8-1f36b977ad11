<?php
/**
 * Created by PhpStorm.
 * User: zhang<PERSON><EMAIL>
 * Date: 2021/3/17
 * Time: 14:51
 */
class Lxjxlib_Ds_School_ActCalcStuQuickCalAnswer extends Lxjxlib_Ds_BaseData{

    protected $daoClassName = 'Lxjxlib_Dao_School_ActCalcStuQuickCalAnswer';

    public function __construct()
    {
       parent::__construct();
    }

    /**
     * 根据recordId获取详情
     * @param $recordId
     * @param $field
     * @return array|bool
     */
    public function getInfoByRecordId($recordId, $field) {
        if(empty($recordId) || empty($field)) {
            return false;
        }

        $conds = [
            'recordId' => $recordId,
        ];
        return $this->getRecordByConds($conds, $field);
    }

}