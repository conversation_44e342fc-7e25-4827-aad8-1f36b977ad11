<?php
/**************************************************************************
 *
 * Copyright (c) 2019 Zuoyebang.com, Inc. All Rights Reserved
 *
 * @file    UserExams.php
 * @date    2020-09-08
 *
 **************************************************************************/

/**
 * Class        Lxjxlib_Ds_School_UserExams
 * @date        2020-09-08
 * @desc
 */
class Lxjxlib_Ds_School_UserExams
{
    protected $_daoUserExams;
    protected $_daoUserExamTid;
    protected $_redis;

    /**组卷转换zip状态**/
    const STATUS_NO = 0;     //未开始,  状态为0的代表存量组卷
    const STATUS_START = 1;  //转换中,  新建的组卷状态为1，优先加入转换队列
    const STATUS_FINISH = 2; //转换成功
    const STATUS_FAIL = 3;   //转换失败

    /**
     * Lxjxlib_Ds_School_UserCollection constructor.
     */
    public function __construct()
    {
        $this->_daoUserExams    = new Lxjxlib_Dao_School_UserExams();
        $this->_daoUserExamsTid = new Lxjxlib_Dao_School_UserExamTids();
    }

    /**
     * @function        addTidToUserBasket
     *
     * @access          public
     * @date            2020-09-08
     * @desc            添加到试题篮
     *
     * @param           $uid
     * @param           $tid
     * @param           $subject
     * @param           $subjectName
     * @param           $business
     *
     * @return          int
     */
    public function addToUserBasket($uid, $tid, $subject, $subjectName, $business)
    {
        $this->_daoUserExamsTid->startTransaction();

        try {
            // 排序值默认1
            $sortValue = 1;

            // 获取试题篮中包含的题目
            $basketTidList = $this->_daoUserExamsTid->getTidListByExam($uid, Lxjxlib_Const_Common::EXAM_TMP_ID);

            if (!empty($basketTidList)) {
                // 检查tid是否存在
                $tidList = array_column($basketTidList, 'tid');
                if (in_array($tid, $tidList)) {
                    throw new Lxjxlib_Const_Exception(Lxjxlib_Const_ExceptionCodes::BASKET_TID_EXISTED);
                }

                // 检查学科是否兼容
                $subjects = array_column($basketTidList, 'subject');
                if (!in_array($subject, $subjects)) {
                    throw new Lxjxlib_Const_Exception(Lxjxlib_Const_ExceptionCodes::BASKET_TID_SUBJECT_DIFF);
                }

                // 排序值
                $sortValues = array_column($basketTidList, 'sortValue');

                rsort($sortValues);

                // 取最大排序值+1
                $sortValue = $sortValues[0] + 1;
            }

            $curTime = time();
            $basketItem = array(
                'uid'           => $uid,
                'tid'           => $tid,
                'examId'        => Lxjxlib_Const_Common::EXAM_TMP_ID,
                'subject'       => $subject,
                'subjectName'   => $subjectName,
                'business'      => $business,
                'sortValue'     => $sortValue,
                'isDeleted'     => Lxjxlib_Const_Common::DB_STATUS_OK,
                'createTime'    => $curTime,
                'updateTime'    => $curTime,
            );

            $ret = $this->_daoUserExamsTid->createRecord($basketItem);
            if (Lxjxlib_Const_ExceptionCodes::SUCCESS != $ret) {
                throw new Lxjxlib_Const_Exception($ret);
            }

            $tidAmount = $this->_daoUserExamsTid->getTidAmountByExam($uid, Lxjxlib_Const_Common::EXAM_TMP_ID, $tid);
            if ($tidAmount > 1) {
                throw new Lxjxlib_Const_Exception(Lxjxlib_Const_ExceptionCodes::BASKET_TID_EXISTED);
            }

            $this->_daoUserExamsTid->commit();

            return $ret;
        } catch (Lxjxlib_Const_Exception $exception) {
            $this->_daoUserExamsTid->rollback();
            return $exception->getCode();
        }
    }

    /**
     * @function        deleteTidFromUserBasket
     *
     * @access          public
     * @date            2020-09-08
     * @desc            从试题篮移除
     *
     * @param           $uid
     * @param           $tids
     *
     * @return          bool
     */
    public function deleteFromUserBasket($uid, $tids)
    {
        return $this->_daoUserExamsTid->deleteFromUserExam($uid, Lxjxlib_Const_Common::EXAM_TMP_ID, $tids);
    }

    /**
     * @function        getTidAmountInBasket
     *
     * @access          public
     * @date            2020-09-08
     * @desc            试题篮题目数量
     *
     * @param           $uid
     *
     * @return          int
     */
    public function getTidAmountInBasket($uid)
    {
        return $this->_daoUserExamsTid->getTidAmountByExam($uid, Lxjxlib_Const_Common::EXAM_TMP_ID);
    }

    /**
     * @function        getUserExamAmount
     *
     * @access          public
     * @date            2020-09-10
     * @desc            组卷数量
     *
     * @param           $uid
     *
     * @return          int
     */
    public function getUserExamAmount($uid)
    {
        return $this->_daoUserExams->getUserExamAmount($uid);
    }

    /**
     * @function        getUserExamList
     *
     * @access          public
     * @date            2020-09-08
     * @desc            用户组卷列表
     *
     * @param           $uid
     * @param           $pn
     * @param           $rn
     *
     * @return          array
     */
    public function getUserExamList($uid, $pn, $rn)
    {
        return $this->_daoUserExams->getUserExamsList($uid, $pn, $rn);
    }

    public function getUserExamById($examId)
    {
        return $this->_daoUserExams->getUserExamById($examId);
    }

    /**
     * @function        getTidInUserExam
     *
     * @access          public
     * @date            2020-09-08
     * @desc            用户组卷包含的tid
     *
     * @param           $uid
     * @param           $examId
     *
     * @return          array|false
     */
    public function getTidInUserExam($uid, $examId)
    {
        $ret = $this->_daoUserExamsTid->getTidListByExam($uid, $examId);
        return $ret;
    }

    public function getTidInExam($examId)
    {
        $ret = $this->_daoUserExamsTid->getTidListByExamNoUid($examId);
        return $ret;
    }

    /**
     * @function        isBasketFull
     *
     * @access          public
     * @date            2020-09-17
     * @desc            试题篮是否已满
     *
     * @param           $uid
     *
     * @return          bool
     */
    public function isBasketFull($uid)
    {
        $tidAmount = $this->_daoUserExamsTid->getTidAmountByExam($uid, Lxjxlib_Const_Common::EXAM_TMP_ID);
        return $tidAmount >= Lxjxlib_Const_Common::EXAM_BASKET_TID_UP ? true : false;
    }

    /**
     * @function        makeExamFromBasket
     *
     * @access          public
     * @date            2020-09-10
     * @desc            创建组卷
     *
     * @param           $uid
     * @param           $examId
     * @param           $examName
     * @param           $subject
     * @param           $tAmount
     * @param           $grade
     *
     * @return          array
     */
    public function makeExamFromBasket($uid, $examId, $examName, $subject, $tAmount, $grade)
    {
        $this->_daoUserExamsTid->startTransaction();

        $ret = array(
            'res'    => Lxjxlib_Const_ExceptionCodes::SUCCESS,
            'examId' => 0,
        );

        try {
            $where = array(
                'uid'       => $uid,
                'examId'    => Lxjxlib_Const_Common::EXAM_TMP_ID,
            );
            $upFields = array(
                'examId'    => $examId,
            );
            $upAmount = $this->_daoUserExamsTid->updateByConds($where, $upFields);
            if (false === $upAmount) {
                throw new Lxjxlib_Const_Exception(Lxjxlib_Const_ExceptionCodes::DB_UPDATE_ERR);
            }

            if (0 < $upAmount) {
                $curTime = time();

                // 添加用户组卷记录
                $examData = array(
                    'uid'           => $uid,
                    'subject'       => $subject,
                    'examId'        => $examId,
                    'examName'      => $examName,
                    'grade'         => $grade,
                    'tidAmount'     => $tAmount,
                    'isDeleted'     => Lxjxlib_Const_Common::DB_STATUS_OK,
                    'createTime'    => $curTime,
                    'updateTime'    => $curTime,
                    'status'        => self::STATUS_START,
                );

                // 添加用户组卷
                $res = $this->_daoUserExams->createRecord($examData);
                if (Lxjxlib_Const_ExceptionCodes::SUCCESS != $res) {
                    throw new Lxjxlib_Const_Exception($res);
                }

                $ret['examId']   = Hk_Util_IdCrypt::encodeQid($examId);

                $this->_daoUserExamsTid->commit();

                return $ret;
            } else {
                throw new Lxjxlib_Const_Exception(Lxjxlib_Const_ExceptionCodes::SYSTEM_CRAZY);
            }
        } catch (Lxjxlib_Const_Exception $e) {
            $this->_daoUserExamsTid->rollback();

            $ret['res'] = $e->getErrNo();
            return $ret;
        }
    }

    /**
     * @function        changeTidSortValue
     *
     * @access          public
     * @date            2020-09-11
     * @desc            调整题目排序
     *
     * @param           $uid
     * @param           $examId
     * @param           $tidSort
     *
     * @return          bool
     */
    public function changeTidSortValue($uid, $examId, $tidSort)
    {
        $this->_daoUserExamsTid->startTransaction();

        try {
            foreach ($tidSort as $tid => $sort) {
                $updateFields = array(
                    'sortValue' => $sort,
                );

                $where = array(
                    'uid'       => $uid,
                    'examId'    => $examId,
                    'tid'       => $tid,
                );

                // 调整排序
                $res = $this->_daoUserExamsTid->updateByConds($where, $updateFields);
                if (false === $res) {
                    throw new Lxjxlib_Const_Exception(Lxjxlib_Const_ExceptionCodes::DB_UPDATE_ERR);
                }
            }

            $this->_daoUserExamsTid->commit();

            return Lxjxlib_Const_ExceptionCodes::SUCCESS;
        } catch (Lxjxlib_Const_Exception $e) {
            $this->_daoUserExamsTid->rollback();
            return $e->getCode();
        }
    }

    /**
     * 组卷信息
     * @param $uid
     * @param $arrExamIds
     * @param $arrFields
     * @return array
     */
    public function getListByExamIds($uid, $arrExamIds, $arrFields)
    {
        $arrConds        = [
            'exam_id IN(' . implode(',', $arrExamIds) . ')',
        ];
        $arrConds['uid'] = $uid;

        return $this->_daoUserExams->getListByConds($arrConds, $arrFields, null);
    }

    /**
     * 根据条件查询结果集
     * @param $arrConds
     * @param array $arrFields
     * @param null $arrOptions
     * @param null $arrAppends
     * @return array|false
     */
    public function getListByConds($arrConds, $arrFields = [], $arrOptions = null, $arrAppends = null)
    {
        if (empty($arrFields)) {
            $arrFields = $this->_daoUserExamsTid->getAllFields();
        }
        return $this->_daoUserExamsTid->getListByConds($arrConds, $arrFields, $arrOptions, $arrAppends);
    }

    /**
     * @function        isTidInBasket
     *
     * @access
     * @date            2020-09-14
     * @desc            检查题目是否在试题篮中
     *
     * @param           $uid
     * @param           $tid
     *
     * @return          bool
     */
    public function isTidInBasket($uid, $tid)
    {
        $tidList = $this->getTidInUserExam($uid, Lxjxlib_Const_Common::EXAM_TMP_ID);
        if (empty($tidList)) {
            return false;
        }

        $tidArr = array_column($tidList, 'tid');
        return in_array($tid, $tidArr) ? true : false;
    }

    /**
     * @function        getAmountOfUserExams
     *
     * @access          public
     * @date            2020-09-15
     * @desc            根据tid获取组卷次数
     *
     * @param           $tid
     *
     * @return          int
     */
    public function getAmountOfUserExams($tid)
    {
        $where = array(
            'isDeleted' => Lxjxlib_Const_Common::DB_STATUS_OK,
            'tid'       => $tid,
            'exam_id != 0',
        );

        return (int)$this->_daoUserExamsTid->getCntByConds($where);
    }

    public function getUserExamAmountWithTidBatch($tidArr)
    {
        $ret = array();

        if (!is_array($tidArr) || empty($tidArr)) {
            return $ret;
        }

        foreach ($tidArr as $tid) {
            $ret[ $tid ] = 0;
        }

        $where = array(
            'isDeleted' => Lxjxlib_Const_Common::DB_STATUS_OK,
            'exam_id != 0',
        );
        $where[] = 'tid in(' . implode(',', $tidArr) . ')';

        // 查询字段
        $fields = array('tid', 'examId');

        $recList = $this->_daoUserExamsTid->getListByConds($where, $fields);
        if (!empty($recList)) {
            // 按tid计数
            foreach ($recList as $recItem) {
                $tid = $recItem['tid'];
                $ret[ $tid ] += 1;
            }
        }

        return $ret;
    }

    /**
     * @param $where
     * @param $fields
     */
    public function updateFields($where, $fields)
    {
        $fields['updateTime'] = time();
        return $this->_daoUserExams->updateByConds($where, $fields);
    }

}
