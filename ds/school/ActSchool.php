<?php

/**
 * Created by PhpStorm.
 * User: <EMAIL>
 * Date: 2021/1/4
 * Time: 14:23
 */
class Lxjxlib_Ds_School_ActSchool extends Lxjxlib_Ds_BaseData
{

    protected $daoClassName = 'Lxjxlib_Dao_School_ActSchool';

    const STATUS_NORMAL = 1;
    const STATUS_DEL    = 2;

    /**
     * 直辖市
     */
    public static $directCity = [
        110000,
        120000,
        500000,
        310000,
    ];

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 获取学校列表
     * @param $uid
     * @param array $fields
     * @return array|false
     */
    public function MultiGet($actIds, $fields = ['type', 'provinceId', 'cityId', 'countyId', 'schoolId', 'actId'])
    {
        if (empty($actIds)) {
            return [];
        }
        $actIdsStr = Lxjxlib_Util_Sql::inImplode($actIds);
        $rows      = $this->getListByConds(
            ["act_id in($actIdsStr)", 'status' => 1],
            $fields
        );
        return $rows;
    }

    public function checkSchool($actId, $userInfo)
    {
        $schools = $this->MultiGet([$actId]);
        return $this->checkSchoolList($schools, $userInfo);
    }

    /**
     * 校验活动范围
     * @param $actIds
     * @param $userInfoList
     * @param $actTeacherUidMap
     * @return array
     */
    public function multiCheckSchool($actIds, $userInfoList, $actTeacherUidMap)
    {
        $schools         = $this->MultiGet($actIds);
        $actSchools      = Lxjxlib_Util_Array::groupByColumn($schools, 'actId');
        $userInfoListKey = Lxjxlib_Util_Array::index($userInfoList, 'uid');

        $ret = [];
        foreach ($actSchools as $actId => $schools) {
            $ret[$actId] = $this->checkSchoolList($schools, $userInfoListKey[$actTeacherUidMap[$actId]]);
        }
        return $ret;
    }

    public function checkSchoolList($schools, $userInfo)
    {
        $schoolId   = $userInfo['schoolId'];
        $provinceId = $userInfo['provinceId'];
        $cityId     = $userInfo['cityId'];
        $countyId   = $userInfo['countyId'];
        foreach ($schools as $schoolItem) {
            //学校绑定类型 0-全国1-指定省2-指定市3-指定区4-指定学校
            if ($schoolItem['type'] == 0) {
                return true;
            }
            if ($schoolItem['type'] == 1 && $schoolItem['provinceId'] == $provinceId) {
                return true;
            }
            if ($schoolItem['type'] == 2 && $schoolItem['cityId'] == $cityId) {
                return true;
            }
            if ($schoolItem['type'] == 3 && $schoolItem['countyId'] == $countyId) {
                return true;
            }
            if ($schoolItem['type'] == 4 && $schoolItem['schoolId'] == $schoolId) {
                return true;
            }
        }
        return false;
    }

}
