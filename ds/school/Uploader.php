<?php

/**
 * Created by PhpStorm.
 */
class Lxjxlib_Ds_School_Uploader extends Lxjxlib_Ds_BaseData
{

    protected $daoClassName = 'Lxjxlib_Dao_School_Uploader';

    const RAND_MAX = 474042;

    /**
     * randName
     * @param $arrParams
     * @return bool
     */
    public function randName()
    {

        $randi = mt_rand(0, self::RAND_MAX);
        $record = $this->getRecordByConds([1 => 1], ['name'], null, ["limit $randi,1"]);
        if (empty($record)) {
            $cnt = $this->getCntByConds([]);
            $randi = mt_rand(0, $cnt);
            $record = $this->getRecordByConds([1 => 1], ['name'], null, ["limit $randi,1"]);
        }
        if (empty($record)) {
            Bd_Log::warning("randName 读不到值");
            return '';
        }
        return $record['name'];
    }

    /**
     * 判断tid和状态是否合法
     * @param $examId
     * @param $tids
     * @param int $business
     * @return bool
     * @throws Lxjxlib_Const_Exception
     */
    public function getTidInfo($examId, $tids = '', $business = Lxjxlib_Const_Search::HKB_EXAM_HOT_QUESTION)
    {
        if (empty($examId)) {
            return false;
        }
        $arrStatus = [
            Lxjxlib_Ds_School_Exam::EXAM_STATUS_INIT,
            Lxjxlib_Ds_School_Exam::EXAM_STATUS_OUT_SELF,
            Lxjxlib_Ds_School_Exam::EXAM_STATUS_WAIT_SELF,
            Lxjxlib_Ds_School_Exam::EXAM_STATUS_CHECKING,
        ];
        //判断tid合法性
        $arrConds = [
            'examId'   => $examId,
            'isDel'    => Lxjxlib_Ds_School_Exam::EXAM_ON,
            'business' => $business,

        ];
        //状态合法性
        $ret = $this->getRecordByConds($arrConds, ['status', 'tids']);
        if (empty($ret) || !in_array($ret['status'], $arrStatus)) {
            throw new Lxjxlib_Const_Exception(Lxjxlib_Const_ExceptionCodes::EXAM_DRAFT_ERROR);
        }
        if (!empty($tids)) {
            //判断tid合法性
            $diff = array_diff(explode(',', $tids), explode(',', $ret['tids']));
            if (!empty($diff)) {
                throw new Lxjxlib_Const_Exception(Lxjxlib_Const_ExceptionCodes::EXAM_TID_ERROR);
            }
        }

        return true;
    }

}