<?php
/**
 * @desc    用户点赞表 相关数据操作
 * <AUTHOR>
 * @version 1.0
 * @date    2020/12/28
 */

class Lxjxlib_Ds_School_UserLike
{
    
    private $objDao;

    const LIKE_STATUS_NORMAL = 1; //正常
    const LIKE_STATUS_DEL    = 0; //删除

    private $_searchFields = [
        'sourceId', 'id', 'createTime', 'status', 'uid'
    ];

    /**
     * Lxjxlib_Ds_School_UserLike constructor.
     */
    public function __construct()
    {
        $this->objDao = new Lxjxlib_Dao_School_UserLike();
    }

    /**
     * 查询用户点赞记录
     * @param $uid
     * @param $sourceType
     * @param $business
     * @param array $sourceIds
     * @return array
     */
    public function getUserLike($uid, $sourceType, $business, $sourceIds = [])
    {
        $where['uid']        = $uid;
        $where['status']     = Lxjxlib_Ds_School_UserLike::LIKE_STATUS_NORMAL;
        $where['sourceType'] = $sourceType;
        $where['business']   = $business;
        if (!empty($sourceIds) && is_array($sourceIds)) {
            $where[] = 'source_id IN (' . implode(',', $sourceIds) . ')';
        }

        return $this->objDao->getListByConds($uid, $where, $this->_searchFields);
    }

    /**
     * 修改数据
     * @param $uid
     * @param $where
     * @param $updateField
     * @return bool
     */
    public function updateRecord($uid, $where, $updateField)
    {
        return $this->objDao->updateByConds($uid, $where, $updateField);
    }

    /**
     * 新增数据
     * @param $uid
     * @param $addField
     * @return bool
     */
    public function addRecord($uid, $addField)
    {
        return $this->objDao->insertRecords($uid, $addField);
    }

    /**
     * 查询
     * @param $uid
     * @param array $where
     * @return array
     */
    public function getRecordByConds($uid, $where)
    {
        $r = [];
        if (empty($where)) {
            return $r;
        }
        return $this->objDao->getRecordByConds($uid, $where, $this->_searchFields);
    }

}
