<?php
/**
 * Created by PhpStorm.
 * User: <EMAIL>
 * Date: 2021/1/4
 * Time: 14:23
 */
class Lxjxlib_Ds_School_Act extends Lxjxlib_Ds_BaseData{

    protected $daoClassName = 'Lxjxlib_Dao_School_Act';

    public $dsActTchApply;
    public $dsClassUserList;
    public $dsClassInfo;
    public $dsActSchool;
    public $time;

    /**
     * status 字段枚举
     */
    const STATUS_NORMAL  = 1;       //正常
    const STATUS_DELETED = 2;       //已删除

    /**
     * act_type活动类型枚举
     */
    const ACT_TYPE_XYYD = 1;        //小语阅读
    const ACT_TYPE_SS   = 2;        //速算
    const ACT_TYPE_GUSHI = 3;       // 古诗词

    public static $actMap = [
        self::ACT_TYPE_XYYD => '阅读打卡',
        self::ACT_TYPE_SS   => '速算比赛',
        self::ACT_TYPE_GUSHI=> '诗词大会',
    ];

    /**
     * 活动状态
     */
    const STATUS_TO_DOING = 1;          //进行中
    const STATUS_TO_START = 2;          //待开始
    const STATUS_TO_OVER  = 3;          //已结束


    public function __construct()
    {
        parent::__construct();
        $this->dsActTchApply      = new Lxjxlib_Ds_School_ActTchApply();
        $this->dsClassUserList    = new Lxjxlib_Ds_School_ClassUserList();
        $this->dsClassInfo        = new Lxjxlib_Ds_School_ClassInfo();
        $this->dsActSchool        = new Lxjxlib_Ds_School_ActSchool();
        $this->time               = Lxjxlib_Util_Time::getTimeForXyyd();
    }

    /**
     * 获取活动详情
     * @param $actId
     * @param $field
     * @return array|bool
     */
    public function getInfoById($actId, $field) {
        if(empty($actId) || empty($field)) {
            return false;
        }

        $conds = [
            'id'     => $actId,
            'status' => self::STATUS_NORMAL,
        ];

        return $this->getRecordByConds($conds, $field);
    }

    /**
     * 通过活动ids获取活动列表
     * @param array $actIds    活动ids
     * @param array $field     查询字段数组
     * @param int $type       活动类型
     * @return array           活动列表
     */
    public function getActListByIds($actIds, $field)
    {
        $actConds[] = 'id in(' . implode(',', $actIds) . ')';
        $actConds['status']  = self::STATUS_NORMAL;
        $activityList = $this->getListByConds($actConds, $field);
        return $activityList;
    }
    /**
     * 通过活动ids获取某类活动
     * @param array $actIds    活动ids
     * @param array $field     查询字段数组
     * @param int $type       活动类型
     * @return array           活动列表
     */
    public function getActListByIdsType($actIds, $field, $type = Lxjxlib_Const_Lxjxschool_Act::ACT_TYPE_XYYD)
    {
        if (empty($actIds)) {
            return false;
        }
        if (is_array($actIds)) {
            $actConds[] = 'id in(' . implode(',', $actIds) . ')';
        } else {
            $actConds['id'] = intval($actIds);
        }
        $actConds['status']  = self::STATUS_NORMAL;
        $actConds['actType'] = $type;
        $activityList = $this->getListByConds($actConds, $field);
        return $activityList;
    }

    /**
     * 获取学生参加班级和活动的对应关系、年级和活动的关系
     * @param $classList
     * @param $actList
     * @return array
     */
    public function getUserActClassAndGradeMap($classList, $actList) {
        $actClassMap = $actGradeMap = [];
        foreach ($classList as $value) {
            foreach ($actList as $item) {
                if($value['createrUid'] == $item['uid']) {
                    $actClassMap[$item['actId']] = $value['classId'];
                    $actGradeMap[$item['actId']] = $value['gradeId'];
                }
            }
        }

        return [$actClassMap, $actGradeMap];
    }

    /**
     * 校验活动是否属于学生参加的班级对应的年级范围
     * @param $actIds
     * @param $actGradeMap
     * @return array
     */
    public function getActListByUserGrade($actIds, $actGradeMap) {
        $activityList = [];
        $allActivityList = $this->getActListByIds($actIds, ['id', 'actName', 'startTime', 'endTime', 'ext', 'actType']);
        $list = [];
        //校验当前学生所在班级的年级是否在活动范围内
        foreach ($allActivityList as $value) {
            $ext = json_decode($value['ext'], true);
            //对应年级
            if(isset($actGradeMap[$value['id']])) {
                //学生参加该活动所属年级
                $gradeId = $actGradeMap[$value['id']];
                //该活动的年级是否包含用户参加该活动的年级: 不包含则直接过滤掉
                if(isset($ext['gradeList'][$gradeId])) {
                    //只有小语阅读有书籍信息
                    if($value['actType'] == Lxjxlib_Ds_School_Act::ACT_TYPE_XYYD) {
                        $gradeBooks = $ext['gradeList'][$gradeId];
                        $gradeBookIds = [];
                        foreach ($gradeBooks as $item) {
                            $gradeBookIds[$item['bookId']] = $item['sort'];
                        }
                        $value['gradeBookIds'] = $gradeBookIds;
                    }
                    $list[] = $value;
                }
            }
        }

        return $list;
    }

    /**
     * 获取是否有一个活动对应多个班级需要退班的情况
     * @param $actList
     * @param $classList
     * @param $userInfoList
     * @return array
     */
    public function getActForMoreClass($actList,  $classList, $userInfoList) {
        $actKeyUidArr = $actForMoreTuid = $actNeedMoveClass = $actForMoreClass = [];

        //活动=>[老师uid]
        foreach ($actList as $key => $value) {
            $actKeyUidArr[$value['actId']][] = $value['uid'];
        }
        foreach ($actKeyUidArr as $key => $value) {
            if(count($value) > 1) {
                $actForMoreTuid[$key] = $value;
            }
        }

        //不存在直接返回空
        if(empty($actForMoreTuid)) {
            return [];
        }

        //根据老师uid,从班级列表list中获取班级信息
        foreach ($classList as $key => $value) {
            foreach ($actForMoreTuid as $k => $v) {
                if(in_array($value['createrUid'], $v)) {
                    $actForMoreClass[$k][] = ['classId' => $value['classId'], 'classNo' => $value['classNo'], 'gradeId' => $value['gradeId'], 'uid' => $value['createrUid']];
                }
            }
        }

        //老师list信息
        $userInfoKeyList = Lxjxlib_Util_Array::index($userInfoList, 'uid');
        //活动list信息
        $actListForAct = $this->getActListByIds(array_keys($actForMoreClass), ['id', 'actName']);
        $actListForActKey = Lxjxlib_Util_Array::index($actListForAct, 'id');

        //组织数据
        foreach ($actForMoreClass as $key => $value) {
            if(isset($actListForActKey[$key])) {
                foreach ($value as $k => $v) {
                    $uid = $v['uid'];
                    if(isset($userInfoKeyList[$uid])) {
                        $value[$k]['teacherName'] = $userInfoKeyList[$uid]['realName'];
                        $value[$k]['schoolName']  = $userInfoKeyList[$uid]['school'];
                        $value[$k]['classNo']     = $v['classNo'];
                        $value[$k]['grade']       = Zb_Const_GradeSubject::$GRADE[intval($v['gradeId'])];
                    }
                }
                $actNeedMoveClass[] = [
                    'actId'     => $key,                                    //活动id
                    'actName'   => $actListForActKey[$key]['actName'],      //活动名称
                    'classList' => $value,                                  //班级列表
                ];
            }
        }

        return $actNeedMoveClass[0];                                        //暂时展示一个活动的数据
    }

    /**
     * 活动类型排序
     * @param $xyActivityList
     * @param $ssActivityList
     * @param $gscActivityList
     * @return array
     */
    public function sortActType($xyActivityList, $ssActivityList, $gscActivityList) {
        //活动内部排序
        list($xyActivityList,  $xyTypeStatus,  $xyMinStartTime,  $xyMaxEndTime)  = $this->sortAct($xyActivityList);
        list($ssActivityList,  $ssTypeStatus,  $ssMinStartTime,  $ssMaxEndTime)  = $this->sortAct($ssActivityList);
        list($gscActivityList, $gscTypeStatus, $gscMinStartTime, $gscMaxEndTime) = $this->sortAct($gscActivityList);

        //活动间排序
        $actSort = $sortActType = [];
        if(!empty($xyActivityList)) {
            $actSort[] = [
                'type'         => Lxjxlib_Const_Lxjxschool_Act::ACT_TYPE_XYYD,
                'status'       => $xyTypeStatus,
                'minStartTime' => $xyMinStartTime,
                'maxEndTime'   => $xyMaxEndTime,
            ];
        }
        if(!empty($ssActivityList)) {
            $actSort[] = [
                'type'         => Lxjxlib_Const_Lxjxschool_Act::ACT_TYPE_SS,
                'status'       => $ssTypeStatus,
                'minStartTime' => $ssMinStartTime,
                'maxEndTime'   => $ssMaxEndTime,
            ];
        }
        if(!empty($gscActivityList)) {
            $actSort[] = [
                'type'         => Lxjxlib_Const_Lxjxschool_Act::ACT_TYPE_POETRY,
                'status'       => $gscTypeStatus,
                'minStartTime' => $gscMinStartTime,
                'maxEndTime'   => $gscMaxEndTime,
            ];
        }

        if(!empty($actSort)) {
            $actStatusArr = array_column($actSort, 'status');
            $actStatusArr = array_values(array_unique($actStatusArr));

            //判断状态种类数量
            if(count($actStatusArr) == 1) {
                //只有一种状态:开始时间升序、结束时间倒叙
                $actSort = $this->sortActTypeByTime($actSort);
            } elseif (count($actStatusArr) == 2) {
                //两种状态
                $actTypeStatusGroup = [];
                foreach ($actSort as $value) {
                    $actTypeStatusGroup[$value['status']][] = $value;
                }
                ksort($actTypeStatusGroup);

                foreach ($actTypeStatusGroup as $key => $value) {
                    $actTypeStatusGroup[$key] = $this->sortActTypeByTime($value);
                }

                $actSort = [];
                foreach ($actTypeStatusGroup as $value) {
                    foreach ($value as $v) {
                        $actSort[] = $v;
                    }
                }

            } elseif (count($actStatusArr) == 3) {
                //三种状态:按照活动状态排序
                $actSort = $this->sortActTypeByStatus($actSort);
            }
            $sortActType = array_column($actSort, 'type');
        }

        return [$sortActType, $xyActivityList, $ssActivityList, $gscActivityList];
    }

    /**
     * 同一个类型内部活动的排序
     * @param $activityList
     * @return array
     */
    private function sortAct($activityList) {
        if(empty($activityList)) {
            return [[], 0, 0, 0];
        }

        //活动类型状态
        $actTypeStatus = 0;
        /*
         * 排序规则：
         * 1. 进行中->未开始->已结束
         * 2. 开始时间越早越靠前；开始时间相同，则结束时间越早，则越靠前
         */
        $doingActList = $waitActList = $overActList = $startTimeList = $endTimeList = [];
        $minStartTime = $maxEndTime = 0;
        foreach ($activityList as $value) {
            if($value['startTime'] <= $this->time && $value['endTime'] >= $this->time) {
                $doingActList[] = $value;
            } elseif($value['startTime'] > $this->time) {
                $waitActList[] = $value;
            } elseif ($value['endTime'] < $this->time) {
                $overActList[] = $value;
            }
            $startTimeList[] = $value['startTime'];
            $endTimeList[]   = $value['endTime'];
        }

        sort($startTimeList);
        rsort($endTimeList);
        $minStartTime = $startTimeList[0];       //最早开始时间
        $maxEndTime   = $endTimeList[0];         //最晚结束时间


        //活动类型状态优先级: 进行中 > 待开始 > 已结束
        if(!empty($overActList)) {
            $actTypeStatus = Lxjxlib_Ds_School_Act::STATUS_TO_OVER;
        }
        if(!empty($waitActList)) {
            $actTypeStatus = Lxjxlib_Ds_School_Act::STATUS_TO_START;
        }
        if(!empty($doingActList)) {
            $actTypeStatus = Lxjxlib_Ds_School_Act::STATUS_TO_DOING;
        }

        //活动内部排序: 进行中 > 待开始 > 已结束
        $list = [];
        if (!empty($doingActList)) {
            $list = array_merge($list, $this->sortActList($doingActList));
        }
        if (!empty($waitActList)) {
            $list = array_merge($list, $this->sortActList($waitActList));
        }
        if (!empty($overActList)) {
            $list = array_merge($list, $this->sortActList($overActList));
        }

        return [$list, $actTypeStatus, $minStartTime, $maxEndTime];
    }

    /**
     * 同一个类型内部活动的排序: 开始时间正序、结束时间倒叙
     * @param $list
     * @return mixed
     */
    function sortActList($list) {
        foreach ($list as $key => $value) {
            $startTimeOrder[$key] = $value['startTime'];
            $endTimeOrder[$key]   = $value['endTime'];
        }
        array_multisort($startTimeOrder, SORT_ASC, $endTimeOrder, SORT_DESC, $list);
        return $list;
    }

    /**
     * 相同状态活动类型排序策略: 最小开始时间升序、最大结束时间倒叙
     * @param $list
     * @return mixed
     */
    private function sortActTypeByTime($list) {
        foreach ($list as $key => $value) {
            $sortStartTime[$key] = $value['minStartTime'];
            $sortEndTime[$key]   = $value['maxEndTime'];
        }
        array_multisort($sortStartTime, SORT_ASC, $sortEndTime, SORT_DESC, $list);
        return $list;
    }

    /**
     * 不同活动类型排序策略：进行中 > 待开始 > 已结束
     * @param $list
     * @return mixed
     */
    private function sortActTypeByStatus($list) {
        foreach ($list as $key => $value) {
            $sortStatus[$key] = $value['status'];
        }
        array_multisort($sortStatus, SORT_ASC, $list);
        return $list;
    }

}