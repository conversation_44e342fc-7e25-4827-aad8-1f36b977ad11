<?php
/**
 * Created by PhpStorm.
 * User: zhang<PERSON><EMAIL>
 * Date: 2021/1/4
 * Time: 19:03
 */
class Lxjxlib_Ds_School_ActBook extends Lxjxlib_Ds_BaseData{

    protected $daoClassName = 'Lxjxlib_Dao_School_ActBook';

    /**
     * status 字段枚举
     */
    const STATUS_NORMAL  = 1;       //正常
    const STATUS_DELETED = 2;       //删除


    /**
     * 获取书
     * @param $bookId
     * @param $isStatus
     * @return array
     */
    public function getBookInfo($bookId, $isStatus = true)
    {
        $arrConds = [
            'id' => $bookId,
        ];
        if ($isStatus) {
            $arrConds['status'] = Lxjxlib_Const_Lxjxschool_Act::STATUS_COMMON_NORMAL;
        }
        $book = $this->getRecordByConds($arrConds, $this->objDao->getFields());

        return $book;
    }
}