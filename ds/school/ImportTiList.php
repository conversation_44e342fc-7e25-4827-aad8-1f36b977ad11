<?php
/**
 * Created by PhpStorm.
 * User: pangjun<PERSON><PERSON>@zuoyebang.com
 * Date: 2021/5/19
 * Time: 11:28 上午
 */
class Lxjxlib_Ds_School_ImportTiList extends Lxjxlib_Ds_BaseData {

    protected $daoClassName = 'Lxjxlib_Dao_School_ImportTiList';

    const TYPE_VERTICAL = 1;    //竖式题目

    const STATUS_WATING   = 0;    //待处理
    const STATUS_SUCCESS  = 1;    //成功
    const STATUS_ERROR    = 2;    //失败

    public static $statusMap = [
        self::STATUS_WATING     => '等待中',
        self::STATUS_SUCCESS    => '成功',
        self::STATUS_ERROR      => '失败',
    ];

    const ERRNO_10001 = 10001; //'创建题目参数错误';
    const ERRNO_10002 = 10002; //'GES访问异常-未获取到结构化数据';
    const ERRNO_10003 = 10003; //'访问题库创建题目异常';
    const ERRNO_10004 = 10004; //'当前知识点+题目已经存在';

    public static $errNoMap = [
        self::ERRNO_10001 => '创建题目参数错误',
        self::ERRNO_10002 => 'GES访问异常-未获取到结构化数据',
        self::ERRNO_10003 => '访问题库创建题目异常',
        self::ERRNO_10004 => '当前知识点+题目已经存在',
    ];

    const DELETED_NO  = 0;//未删除
    const DELETED_YES = 1;//已删除
}