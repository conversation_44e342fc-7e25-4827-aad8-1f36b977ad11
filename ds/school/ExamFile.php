<?php
/**
 * @brief 试卷文件操作
 * <AUTHOR>
 */


class Lxjxlib_Ds_School_ExamFile extends Lxjxlib_Ds_BaseData
{

    protected $daoClassName = 'Lxjxlib_Dao_School_ExamHkbFile';

    public function resetExamFiles($records, $examId, $business)
    {
        $this->startTransaction();
        try {
            $ret = $this->update([
                'examId' => $examId,
                'isDel'  => 0,
//                'business' => $business
            ], ['isDel' => 1]);
            if ($ret === false) {
                throw new Exception('break');
            }

            foreach ($records as $record) {
                $ret = $this->insertRecords($record);
                if ($ret === false) {
                    throw new Exception('break');
                }
            }
            $this->commit();
        } catch (Throwable $ex) {
            $this->rollback();
            return false;
        }
    }

    public function update($conds, $sets)
    {
        return $this->updateByConds($conds, $sets);
    }

    public function getFiles($examId, $fields = null)
    {
        if (empty($fields)) {
            $fields = $this->objDao->getFields();
        }
        $conds = ['examId' => $examId, 'isDel' => 0];
        return $this->getListByConds($conds, $fields, null, ['order by type asc']);
    }

}
