<?php
/**
 * Created by PhpStorm.
 * User: pangjun<PERSON><PERSON>@zuoyebang.com
 * Date: 2020/10/16
 * Time: 2:59 下午
 */
class Lxjxlib_Ds_School_TestConverge extends Lxjxlib_Ds_BaseData{

    protected $daoClassName = 'Lxjxlib_Dao_School_TestConverge';

    /**
     * deleted 字段枚举
     */
    const DELETED_YES   = 1;  //已删除
    const DELETED_NO    = 0;  //未删除

    /**
     * groupType字段聚合方式
     */
    const GROUP_TYPE_CLASS  = 1;    //=1则groupId为classNo（按照班级分组）
    const GROUP_TYPE_SCHOOL = 2;    //=2则groupId为schoolId（按照学校分组）
    const GROUP_TYPE_COUNTY = 3;    //=3则groupId为countyId（按照区县分组）
    const GROUP_TYPE_CITY   = 4;    //=4则groupId为cityId（按照城市分组）

    /**
     * type字段，用于区分是哪种类型的数据，需要关注的字段
     */
    const TYPE_OVERALL  = 1;  //整体参与情况 subjectId、groupId、groupType、attendSchoolNum、attendStudentNum、submitStudentNum
    const TYPE_SCORE    = 2;  //答题得分分布 subjectId、gradeId、groupId、groupType、submitStudentNum、scoreStage
    const TYPE_DURATION = 3;  //答题时间分布 subjectId、gradeId、groupId、groupType、submitStudentNum、durationSum
    const TYPE_POINT    = 4;  //知识点正确率分布 subjectId、gradeId、groupId、groupType、pointId、pointRightNum
    const TYPE_ABILITY  = 5;  //能力分布 subjectId、gradeId、groupId、groupType、pointId、pointRightNum
}