<?php
/**
 * Created by PhpStorm.
 * User: pangjun<PERSON><PERSON>@zuoyebang.com
 * Date: 2021/1/4
 * Time: 11:21 上午
 */
class Lxjxlib_Ds_School_ClassUserList extends Lxjxlib_Ds_BaseData{

    protected $daoClassName = 'Lxjxlib_Dao_School_ClassUserList';

    /**
     * status 字段枚举：加入班级、退出班级
     */
    const STATUS_ADD  = 0;    //加入班级
    const STATUS_QUIT = 1;    //退出班级

    public $dsActTchApply;
    public $dsClassInfo;

    public function __construct()
    {
        parent::__construct();
        $this->dsActTchApply      = new Lxjxlib_Ds_School_ActTchApply();
        $this->dsClassInfo        = new Lxjxlib_Ds_School_ClassInfo();
    }


    /**
     * 退出班级
     * @param $uid
     * @param $classId
     * @return bool
     */
    public function ClassOut($uid, $classId)
    {
        $conds = ['uid' => $uid, 'classId' => $classId];
        return $this->updateByConds($conds, ['status' => self::STATUS_QUIT, 'updateTime' => time()]);
    }


    /**
     * 获取班级uid用户列表
     * @param $classid
     * @return array
     */
    public function getClassUids($classid)
    {
        $conds=['class_id'=>$classid,'status'=>self::STATUS_ADD];
        $uids = $this->getListByConds($conds, ['uid']);
        return $uids;
    }

    /**
     * 获取班级uid用户列表
     * @param $classid
     * @return array
     */
    public function getStudentUidCnts($classIds)
    {
        $classIdsStr = implode(',', $classIds);
        $conds = ["class_id in ($classIdsStr)", 'status' => self::STATUS_ADD];
        $uidCnts = $this->getListByConds($conds, ['count(*) cnt', 'classId'], null, 'group by class_id');
        return $uidCnts;
    }

    /**
     * 批量获取班级学生
     *
     * @param array $classIds
     * @return array|false
     * <AUTHOR>
     * @date 2021-04-29
     */
    public function multiGetClassStu($classIds)
    {
        if (!is_array($classIds) || empty($classIds)) {
            return null;
        }
        $classIdsStr = implode(',', $classIds);
        $conds = ["class_id in ($classIdsStr)", 'status' => self::STATUS_ADD];
        $stu = $this->getListByConds($conds, null, null);
        return $stu;
    }

    /**
     * 获取用户classId
     * @param $uid
     * @return array|bool
     */
    public function getUserClass($uid) {
        if(empty($uid)) {
            return false;
        }

        $conds = [
            'uid'    => $uid,
            'status' => self::STATUS_ADD,
        ];
        $list = $this->getListByConds($conds, ['classId']);
        if(empty($list)) {
            return false;
        }

        $classIds = array_column($list, 'classId');
        return $classIds;
    }

    /**
     * 根据班级ids,获取每个班级对应的学生数量
     * @param $classIds
     * @return array|bool
     */
    public function getClassStuNumByClassIds($classIds) {
        if(empty($classIds)) {
            return false;
        }

        $field = [
            'classId',
            'count(*) as stuNum',
        ];
        $conds = [
            'class_id in(' . implode(",", $classIds) . ')',
            'status' => self::STATUS_ADD,
        ];
        $appends = [
            'group by classId'
        ];

        return $this->getListByConds($conds, $field, null, $appends);

    }

    /**
     * 判断用户是否在某个班级
     * @param $uid
     * @param $classId
     * @return bool
     */
    public function checkUserIsInClass($uid, $classId) {
        if(empty($uid) || empty($classId)) {
            return false;
        }
        $conds = [
            'uid'     => $uid,
            'classId' => $classId,
            'status'  => self::STATUS_ADD,
        ];
        $info = $this->getRecordByConds($conds, ['id']);
        return !empty($info) ? true : false;
    }

    /**
     * 判断用户某个班级状态
     * @param $uid
     * @param $classId
     * @return array
     */
    public function checkUserInClassStatus($uid, $classId) {
        $status    = $this->checkUserIsInClass($uid, $classId);
        $classInfo = $this->dsClassInfo->getClassInfo($classId, true);
        $isInClass = ($status == true) ? self::STATUS_ADD : self::STATUS_QUIT;
        $inClassInfo = [
            'isInClass' => $isInClass,
            'classNo'   => $classInfo['classNo'],
            'grade'     => Zb_Const_GradeSubject::$GRADE[intval($classInfo['gradeId'])],
        ];
        return $inClassInfo;
    }




}