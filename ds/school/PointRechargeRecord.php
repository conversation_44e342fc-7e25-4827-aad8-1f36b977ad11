<?php
/**
 * @file PointRechargeRecord.php
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/22
 */

class Lxjxlib_Ds_School_PointRechargeRecord extends Lxjxlib_Ds_BaseData
{
    protected $daoClassName = 'Lxjxlib_Dao_School_PointRechargeRecord';

    public function addRecord($uid, $recordId, $rechargePoint, $operatorRole, $operatorId)
    {
        $curtime = time();

        $insertData = [
            'uid'           => $uid,
            'recordId'      => $recordId,
            'rechargePoint' => $rechargePoint,
            'operatorRole'  => $operatorRole,
            'operatorId'    => $operatorId,
            'createTime'    => $curtime,
            'updateTime'    => $curtime,
        ];

        return $this->objDao->insertRecords($insertData);
    }

}