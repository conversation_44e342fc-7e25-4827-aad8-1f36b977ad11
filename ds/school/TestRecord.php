<?php

/**
 * Created by PhpStorm.
 * User: pangjun<PERSON><PERSON>@zuoyebang.com
 * Date: 2020/10/15
 * Time: 11:11 上午
 */
class Lxjxlib_Ds_School_TestRecord extends Lxjxlib_Ds_BaseData {

    protected $daoClassName = 'Lxjxlib_Dao_School_TestRecord';

    protected $daoTestRecord;
    protected $daoTestExamBind;
    protected $daoTestVersionList;

    private $nowTime;

    /**
     * status 字段枚举：保存未发布、已发布、已下架（只有发布的评测才能下架）
     */
    const STATUS_SAVE = 0;    //保存未发布
    const STATUS_PUBLISH = 1;    //已发布
    const STATUS_SALE_OFF = 2;    //已下架

    /**
     * deleted 字段枚举
     */
    const DELETED_YES = 1;  //已删除
    const DELETED_NO = 0;  //未删除

    /*
     * 学生查看答案时间设置
     */
    const ANSWER_SHOW_TIMELY = 1;                      //学生交完卷后出答案
    const ANSWER_SHOW_DELAYED = 2;                      //考试结束后出答案

    /*
     * 区域报告成绩定义设置
     */
    const TYPE_REPORT_SCORE_CONF_DEFAULT = 1;           //默认
    const TYPE_REPORT_SCORE_CONF_SELF = 2;           //自定义

    /*
     * 评测类型
     */
    const TYPE_IS_INNER_FORMAL = 1;                     //正式测评
    const TYPE_IS_INNER_TEST = 2;                     //内部测评



    /*
     * 区域报告成绩定义等级
     */
    const GRADE_EXCELLENT   = 'S';                      //优秀
    const GRADE_GOOD        = 'A';                      //良好
    const GRADE_STANDARD    = 'B';                      //达标
    const GRADE_UNQUALIFIED = 'C';                      //暂未达标

    /**测评学科类型 */
    const TYPE_SUBJECT_TEST = 1;  //学科测评
    const TYPE_MENTAL_TEST  = 2;  //心理测评
    const TYPE_JOB_TEST     = 3;  //职业测评

    public static $arrTestType = [
        //self:: TYPE_SUBJECT_TEST => '学科测评',
        self:: TYPE_MENTAL_TEST  => '心理健康',
        self:: TYPE_JOB_TEST     =>'职业测评',
    ];

    const TEST_MENTA_EXAM_ID = 10001;//心理测评默认写死examId
    const TEST_MOLD_EXAM_ID = 10002;//职业测评默认写死examId

    /*
     * 区域报告成绩定义等级描述映射
     */
    public static $gradeDescMap = [
        self::GRADE_EXCELLENT   => '优秀',
        self::GRADE_GOOD        => '良好',
        self::GRADE_STANDARD    => '达标',
        self::GRADE_UNQUALIFIED => '暂未达标',
    ];

    /*
     * 区域报告成绩定义默认设置
     */
    public static $reportScoreConfDefault = [
        self::GRADE_EXCELLENT   => ['start' => 90,'end' => 100],
        self::GRADE_GOOD        => ['start' => 80,'end' => 90],
        self::GRADE_STANDARD    => ['start' => 60,'end' => 80],
        self::GRADE_UNQUALIFIED => ['start' => 0, 'end' => 60],
    ];

    public function __construct()
    {
        parent::__construct();
        $this->daoTestRecord      = new Lxjxlib_Dao_School_TestRecord();
        $this->daoTestExamBind    = new Lxjxlib_Dao_School_TestExamBind();
        $this->daoTestVersionList = new Lxjxlib_Dao_School_TestVersionList();
        $this->nowTime            = time();
    }

    /**
     * 通过testId进行更新
     * @param $testId
     * @param $arrParam
     * @return bool
     */
    public function updateByTestId($testId, $arrParam)
    {

        $testId = intval($testId);
        if (0 >= $testId || empty($arrParam)) {
            return false;
        }

        $arrConds = [
            'testId' => $testId,
        ];

        return $this->daoTestRecord->updateByConds($arrConds, $arrParam);
    }

    /**
     * 获取评测信息详情
     * @param  integer  $testId  测评id
     * @return array
     */
    public function getTestInfo($testId)
    {
        $testId = intval($testId);
        if (0 >= $testId) {
            return false;
        }
        $conds = [
            'testId'  => $testId,
            'deleted' => self::DELETED_NO,
        ];
        return $this->getRecordByConds($conds);
    }

    /**
     * 校验是否可以修改测评信息
     * @param json  $examList  试卷绑定json串
     * @return boolean
     */
    public function checkIsEdit($examList)
    {
        if (empty($examList)) {
            return true;
        }

        $startTimeList = [];
        foreach ($examList as $value) {
            foreach ($value as $item) {
                //只取设置了时间的考试，没有设置时间的考试不用管
                if (!empty($item['startTime'])) {
                    $startTimeList[] = $item['startTime'];
                }
            }
        }

        //如果为空，说明所有的考试都没有设置时间，则可以编辑
        if (empty($startTimeList)) {
            return true;
        }

        //排序，然后取最早的一场考试时间
        sort($startTimeList);

        //如果最早的一场测评已经开始(比现在早)，则不能再编辑
        if (time() > $startTimeList[0]) {
            return false;
        }

        return true;
    }

    /**
     * 查询测评信息
     * @param $arrTestId
     * @param bool|false $isAll
     * @return array
     */
    public function getInfoByTestId($arrTestId, $isFlag = false, $time = Lxjxlib_Const_Common::EXPIRE_DAY)
    {
        if (empty($arrTestId)) {
            return [];
        }
        if ($isFlag) {
            //已结束
            $arrStatus = [self::STATUS_PUBLISH, self::STATUS_SALE_OFF];
        } else {
            $arrStatus = [self::STATUS_PUBLISH];
        }

        $ret      = [];
        $arrNotId = [];

        $redis    = Hk_Service_RedisClient::getInstance(Lxjxlib_Const_Cache::REDIS_NAME_LXJX);
        //先查询缓存
        foreach ((array)$arrTestId as $testId) {
            $key = Lxjxlib_Const_Common::TEST_RECORD_CONTENT . '_' . $testId;
            $res = $redis->get($key);
            if ($res) {
                $res = json_decode($res, true);
                if (in_array($res['status'], $arrStatus)) {
                    $ret[] = $res;
                }
            } else {
                $arrNotId[] = $testId;
            }
        }

        $res = [];
        if ($arrNotId) {
            $arrField = [
                'testType','testId','examList', 'testType', 'answerShow', 'testId', 'name', 'content', 'status', 'posterPid','classNum','ext'
            ];
            $arrConds = [
                'test_id IN(' . implode(',', $arrNotId) . ')',
            ];

            $arrConds['deleted'] = self::DELETED_NO;
            $res                 = $this->getListByConds($arrConds, $arrField);
            if ($res) {
                foreach ($res as $item) {
                    if (in_array($item['status'], $arrStatus)) {
                        $ret[] = $item;
                    }
                    //查了就存
                    $key = Lxjxlib_Const_Common::TEST_RECORD_CONTENT . '_' . $item['testId'];
                    $redisRet = $redis->setEx($key, $time, json_encode($item));
                    if ($redisRet === false) {
                        Bd_Log::warning('test,getInfoByTestId，存redis,set失败，testId=' . $item['testId'] . ',ret=' . json_encode($redisRet));
                    }
                }
            }
        }
        return $ret;

    }

}