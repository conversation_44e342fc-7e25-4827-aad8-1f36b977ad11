<?php

/**
 * @brief   课程配置
 * @file    ds/bjx/CourseStage.php
 * <AUTHOR>
 * @date    2021-06-08
 */
class Lxjxlib_Ds_Bjx_CourseStage
{
    private $_daoCourseStage;

    public function __construct()
    {
        $this->_daoCourseStage = new Lxjxlib_Dao_Bjx_CourseStage();
    }

    /**
     * 新增课程配置
     * @param $data
     * @return bool
     * @throws
     */
    public function addNewRecord($data)
    {
        $this->_daoCourseStage->startTransaction();
        try {
            $history = $this->getInfoByStage($data['businessStage'], $data['season'], $data['applyStage'], $data['applySource'], ['id']);
            if (!empty($history)) {
                throw new Exception('课程已存在', Lxjxlib_Const_ExceptionCodes::COURSE_STAGE_HAS_ALREADY_EXIST);
            }

            $data['createTime'] = $data['updateTime'] = time();

            $ret = $this->_daoCourseStage->insertRecords($data);
            if (empty($ret)) {
                throw new Exception('保存课程信息失败', Lxjxlib_Const_ExceptionCodes::DB_INSERT_ERR);
            }

            $this->_daoCourseStage->commit();
        } catch (Exception $e) {
            $this->_daoCourseStage->rollback();
            throw new Lxjxlib_Const_Exception($e->getCode(), $e->getMessage());
        }

        return $ret;
    }

    /**
     * 更新课程信息
     * @param $courseStageid int 期次ID
     * @param $data array 更新的数据
     * @return bool
     * @throws
     */
    public function updateCourseStageById($courseStageid, $data)
    {
        $info = $this->getInfoById($courseStageid);
        if (empty($info)) {
            throw new Lxjxlib_Const_Exception(Lxjxlib_Const_ExceptionCodes::COURSE_STAGE_NOT_FOUND);
        }
        $this->_daoCourseStage->startTransaction();
        try {
            if ($info['businessStage'] != $data['businessStage'] || $info['season'] != $data['season'] || $info['applyStage'] != $data['applyStage'] || $info['applySource'] != $data['applySource']) {
                $history = $this->getInfoByStage($data['businessStage'], $data['season'], $data['applyStage'], $data['applySource'], ['id']);
                if (!empty($history)) {
                    throw new Exception('课程已存在', Lxjxlib_Const_ExceptionCodes::COURSE_STAGE_HAS_ALREADY_EXIST);
                }
            }

            $arrConds = [
                'id'        => $courseStageid,
                'status'    => Lxjxlib_Const_Bjx_CourseStage::COURSE_STAGE_STATUS_NORMAL,
            ];
            $data['updateTime'] = time();

            $ret = $this->_daoCourseStage->updateByConds($arrConds, $data);
            if ($ret === false) {
                Lxjxlib_Util_Log::warning("Update regcord error, id:{$courseStageid} data:" . json_encode($data, JSON_UNESCAPED_UNICODE));
                throw new Exception('保存课程信息失败', Lxjxlib_Const_ExceptionCodes::DB_INSERT_ERR);
            }

            $this->_daoCourseStage->commit();
        } catch (Exception $e) {
            $this->_daoCourseStage->rollback();
            throw new Lxjxlib_Const_Exception($e->getCode(), $e->getMessage());
        }

        return $ret;
    }

    /**
     * 获取分页列表
     * @param      $pn
     * @param      $rn
     * @param      $arrConds
     * @param null $arrFields
     * @param null $appends
     * @return array
     */
    public function getPageList($pn, $rn, $arrConds, $arrFields = null, $appends = null)
    {
        $ret = $this->_daoCourseStage->getPageList($pn, $rn, $arrConds, $arrFields, $appends);
        return $ret;
    }

    /**
     * 根据业务阶段+课程阶段（唯一）获取课程
     * @param      $businessStage
     * @param      $season
     * @param      $applyStage
     * @param null $arrFields
     * @return array|false
     */
    public function getInfoByStage($businessStage, $season, $applyStage, $applySource, $arrFields = null)
    {
        $arrConds = [
            'businessStage' => $businessStage,
            'season'        => $season,
            'applyStage'    => $applyStage,
            'applySource'   => $applySource,
            'status'        => Lxjxlib_Const_Bjx_CourseStage::COURSE_STAGE_STATUS_NORMAL,
        ];
        $info = $this->_daoCourseStage->getRecordByConds($arrConds, $arrFields ?? $this->_daoCourseStage->getAllFields());
        return $info;
    }

    /**
     * 根据自增id获取课程
     * @param $courseStageId
     * @param $arrFields
     * @return array|false
     */
    public function getInfoById($courseStageId, $arrFields = null)
    {
        $arrConds = [
            'id' => $courseStageId,
            'status' => Lxjxlib_Const_Bjx_CourseStage::COURSE_STAGE_STATUS_NORMAL,
        ];
        $info = $this->_daoCourseStage->getRecordByConds($arrConds, $arrFields ?? $this->_daoCourseStage->getAllFields());
        return $info;
    }

    /**
     * 获取所有课程
     * @param $arrFields
     * @return array|false
     */
    public function getAllStage($arrFields = null)
    {
        $arrFields = $arrFields ?? ['id', 'businessStage', 'season', 'applyStage', 'startTime', 'endTime'];
        $appends = [
            'order by id desc',
        ];
        $ret = $this->_daoCourseStage->getListByConds(null, $arrFields, null, $appends);
        return $ret;
    }

    /**
     * 格式化课程信息
     * @param $item
     * @return mixed
     */
    public function populateExtInfo($extInfo)
    {
        $imgInfo = [
            'qrInfo' => [
                'qrx' => 0,
                'qry' => 0,
                'qrw' => 170,
                'qrh' => 170,
                'poi' => 0,
            ],
            'postInfo' => [
                'pw' => 720,
                'ph' => 1280,
            ],
        ];
        if (!empty($extInfo) && isset($extInfo['qrInfo'])) {
            $imgInfo['qrInfo']['qrx'] = intval($extInfo['qrInfo']['qrx']);
            $imgInfo['qrInfo']['qry'] = intval($extInfo['qrInfo']['qry']);
            $imgInfo['qrInfo']['qrw'] = intval($extInfo['qrInfo']['qrw']);
            $imgInfo['qrInfo']['qrh'] = intval($extInfo['qrInfo']['qrh']);
            $imgInfo['qrInfo']['poi'] = intval($extInfo['qrInfo']['poi']);
        }
        if (!empty($extInfo) && isset($extInfo['qostInfo'])) {
            $imgInfo['postInfo']['pw'] = intval($extInfo['postInfo']['pw']);
            $imgInfo['postInfo']['ph'] = intval($extInfo['postInfo']['ph']);
        }

        return $imgInfo;
    }
}