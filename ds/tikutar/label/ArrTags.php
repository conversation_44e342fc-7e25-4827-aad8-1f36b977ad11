<?php
/**
 * 标签配置
 */
class Hkzb_Ds_TikuTar_Label_ArrTags
{
    public static $dataDictionary = array(
        'zbDifficulty' => array(
            1=>'1星',
            2=>'2星',
            3=>'3星',
            4=>'4星',
            5=>'5星',
        ),
        'zbProvince' => array(
            1 => '北京',
            2 => '天津',
            3 => '上海',
            4 => '重庆',
            5 => '内蒙古',
            6 => '新疆',
            7 => '广西',
            8 => '宁夏',
            9 => '西藏',
            10 => '黑龙江',
            11 => '吉林',
            12 => '辽宁',
            13 => '河北',
            14 => '山西',
            15 => '江苏',
            16 => '浙江',
            17 => '安徽',
            18 => '福建',
            19 => '江西',
            20 => '山东',
            21 => '河南',
            22 => '湖北',
            23 => '湖南',
            24 => '广东',
            25 => '海南',
            26 => '四川',
            27 => '贵州',
            28 => '云南',
            29 => '陕西',
            30 => '甘肃',
            31 => '青海',
            32 => '台湾',
            33 => '香港',
            34 => '澳门',
        ),
        'zbYear'  => array(
            1 => '2018',
            2 => '2017',
            3 => '2016',
            4 => '2015',
            5 => '2014',
            6 => '2013',
            7 => '2012',
            8 => '2011',
        ),
        'zbSource' => array(
            1 => '月考',
            2 => '期中测试',
            3 => '期末测试',
            4 => '中考真题',
            5 => '自主招生',
            6 => '竞赛试题',
            7 => '一模',
            8 => '二模',
            9 => '讲义',
            10 => '课件',
            11 => '堂堂测',
            12 => '每日一练',
            13 => '单元测试',
            14 => '课后作业',
            15 => '练习册',
            16 => '小升初试题',
            17 => '同步',
            18 => '竞赛',
            19 => '高考真题',
            20 => '高考模拟/联考模拟',
            21 => '月考与单元测',
            22 => '原创题',
            23 => '优秀教辅题',
            24 => '会考试题/学业考试',
        ),
        'category' => array(
            1 => '单选题',
            2 => '多选题',
            3 => '填空题',
            5 => '判断题',
            13 => '听力',
            14 => '完形填空',
            15 => '阅读理解',
            18 => '连线题',
            19 => '拖拽题',
            20 => '课中跟读',
            21 => '课下跟读',
            22 => '组句子',
            23 => '组单词',
            24 => '主观题',
            25 => '模仿朗读',
            26 => '听后回答',
            27 => '听后选择',
            28 => '听后填空',
            29 => '听后转述',

        ),
        'zbPower' => array(
            1 => '识记能力',
            2 => '理解分析能力',
            3 => '概括能力',
            4 => '表达能力',
            5 => '逻辑推理能力',
            6 => '空间想象能力',
            7 => '几何直观能力',
            8 => '数据分析能力',
            9 => '运算能力',
            10=> '模型思想能力',
            11 => '应用与创新能力',
            12 => '语音能力',
            13 => '词汇能力',
            14 => '语法能力',
            15 => '听力能力',
            16 => '口语能力',
            17 => '阅读能力',
            18 => '写作能力',
            19 => '理解能力',
            20 => '分析综合能力',
            21 => '鉴赏评价能力',
            22 => '表达应用能力',
            23 => '探究能力',
            24 => '推理能力',
            25 => '数学能力',
            26 => '实验能力',
            27 => '抽象概括',
            28 => '运算',
            29 => '逻辑推理',
            30 => '空间',
            31 => '几何直观',
            32 => '数据分析',
            33 => '模型',
            34 => '应用与创新',
            35 => '观察能力',
            36 => '分析概括能力',
            37 => '计算能力',
            38 => '应用与综合分析能力',
            39 => '记忆能力',
            40 => '信息提取能力',
            41 => '概念辨析能力',
            42 => '实验操作能力',
            43 => '分析推理能力',
            44 => '实验方案设计能力',
            45 => '知识迁移',
            46 => '实验探究',
            47 => '创新思维',
            48 => '数学运算能力',
            49 => '直观想象能力',
            50 => '实验与探究能力',
            51 => '获取信息能力',
            52 => '综合运用能力',
            53 => '获取和解读信息能力',
            54 => '调动和运用知识能力',
            55 => '描述和阐释事物能力',
            56 => '论证和探究问题能力',
            57 => '记忆及概念辨析能力',
            58 => '信息提取及知识迁移能力',
            59 => '实验操作及方案设计能力',
            60 => '实验探究能力',
            61 => '空间思维能力',
            62 => '方程思想能力',
            63 => '创新应用能力',
            64 => '数学抽象能力',
            65 => '数学建模能力',
            66 => '数据分析能力',
        ),
        'zbModule' => array(
            1 => '语言基础知识',
            2 => '语言综合运用',
            3 => '古诗词',
            4 => '文言文阅读',
            5 => '记叙文阅读',
            6 => '说明文阅读',
            7 => '名著阅读',
            8 => '文化文学常识',
            9 => '作文',
            10 => '古诗词鉴赏',
            11 => '议论文阅读',
        ),
        'zbForm' => array(
            1 => '基础实验题',
            2 => '计算题',
            3 => '推断题',
            4 => '工业流程题',
            5 => '压轴题',
            6 => '易错题',
            7 => '自主招生题',
            8 => '实验探究题',
            9 => '流程题',
            10 => '实验题',
            11 => '图像题',
            12 => '信息提取题',
            13 => '坐标题',
            14 => '表格题',
            15 => '概念题',
            16 => '描述类问题',
            17 => '地理事象判断问题',
            18 => '原因分析类问题',
            19 => '辨析评价阐述类问题',
            20 => '意义影响类问题',
            21 => '对策措施类问题',
        )
    );

    public static $tagsList = array(
        //公共
        'tagsKey_0'  => array(
            'zbDifficulty' => array(
                1,2,3,4,5
            ),
            'zbProvince' => array(
                1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,
            ),
            'zbYear'  => array(
                1,2,3,4,5,6,7,8
            ),
            'category' => array(
                1,2,3,5,24
            ),
        ),
        //小学语文
        "tagsKey_1_1" => array(
            'zbSource' => array(
                9,10,11,12,13,14,2,3,14,15,16
            ),
            'zbPower' => array(
                1,2,3,4
            ),
            'category' => array(
                1,2,3,5,22,18,19,24
            ),
            'zbModule' => array(
                1,2,3,4,5,6,7,8,9
            ),
        ),
        //小学数学
        "tagsKey_2_1" => array(
            'zbSource' => array(
                9,10,11,12,13,2,3,14,15,16,17,18
            ),
            'zbPower' => array(
                9,7,61,5,62,8,63
            ),
            'category' => array(
                1,2,3,5,24
            ),
        ),
        //小学英语
        "tagsKey_3_1" => array(
            'zbSource' => array(
                9,10,11,12,13,2,3,14,15,16
            ),
            'zbPower' => array(
                12,13,14,15,16,17,18
            ),
            'category' => array(
                1,3,5,14,15,23,22,18,19,20,21,24
            ),
        ),
        //初中语文
        "tagsKey_1_20" => array(
            'zbPower' => array(
                1,19,20,21,22,23
            ),
            'category' => array(
                1,2,3,15,24
            ),
            'zbModule' => array(
                1,2,10,4,5,6,11,7,9
            ),
        ),
        //初中数学
        "tagsKey_2_20" => array(
            'zbPower' => array(
                27,28,29,30,31,32,33,34
            ),
            'category' => array(
                1,2,3,24
            ),
        ),
        //初中英语
        "tagsKey_3_20" => array(
            'zbPower' => array(
                12,13,14,15,16,17,18,
            ),
            'category' => array(
                1,3,14,15,24,25,26,27,28,29
            ),
        ),
        //初中物理
        "tagsKey_4_20" => array(
            'zbPower' => array(
                35,19,26,36,37,38,
            ),
            'category' => array(
                1,2,3,24
            ),
        ),
        //初中化学
        "tagsKey_5_20" => array(
            'zbPower' => array(
                57,58,43,59,37,60
            ),
            'category' => array(
                1,2,3,24
            ),
            'zbForm' => array(
                1,2,3,4,5,6,7,8
            ),
        ),
        //高中语文
        "tagsKey_1_30" => array(
            'zbSource' => array(
                19,20,2,3,21,22,9,15,23,24,6,
            ),
            'zbPower' => array(
                1,19,20,21,22,23
            ),
            'category' => array(
                1,2,15,24
            ),
        ),
        //高中数学
        "tagsKey_2_30" => array(
            'zbPower' => array(
                48,5,49,64,65,66
                //48,5,49,30,31,32,33,34,64,65,66
            ),
            'category' => array(
                1,3,24
            ),
            'zbSource' => array(
                19,20,2,3,21,22,9,15,23,24,6
            ),
        ),
        //高中英语
        "tagsKey_3_30" => array(
            'zbPower' => array(
                12,13,14,15,16,17,18
            ),
            'category' => array(
                1,2,14,15,24
            ),
            'zbSource' => array(
                19,20,2,3,21,22,9,15,23,24,6
            ),
        ),
        //高中物理
        "tagsKey_4_30" => array(
            'zbPower' => array(
                19,24,20,25,26,23
            ),
            'category' => array(
                1,2,3,24
            ),
            'zbSource' => array(
                19,20,2,3,21,22,9,15,23,24,6
            ),
        ),
        //高中化学
        "tagsKey_5_30" => array(
            'zbPower' => array(
                57,58,43,59,37,60
            ),
            'category' => array(
                1,2,3,24
            ),
            'zbForm' => array(
                3,9,10
            ),
            'zbSource' => array(
                19,20,2,3,21,22,9,15,23,24,6
            ),
        ),
        //高中生物
        "tagsKey_6_30" => array(
            'zbPower' => array(
                19,50,51,52
            ),
            'category' => array(
                1,2,3,24
            ),
            'zbForm' => array(
                8,11,12,13,14,2,15
            ),
            'zbSource' => array(
                19,20,2,3,21,22,9,15,23,24,6
            ),
        ),
        //高中政治
        "tagsKey_7_30" => array(
            'zbPower' => array(
                53,54,55,56
            ),
            'category' => array(
                1,2,24
            ),
            'zbSource' => array(
                19,20,2,3,21,22,9,15,23,24,6
            ),
        ),
        //高中历史
        "tagsKey_8_30" => array(
            'zbPower' => array(
                53,54,55,56
            ),
            'category' => array(
                1,2,24
            ),
            'zbSource' => array(
                19,20,2,3,21,22,9,15,23,24,6
            ),
        ),
        //高中地理
        "tagsKey_9_30" => array(
            'zbPower' => array(
                53,54,55,56
            ),
            'category' => array(
                1,2,24
            ),
            'zbForm' => array(
                16,17,18,19,20,21
            ),
            'zbSource' => array(
                19,20,2,3,21,22,9,15,23,24,6
            ),
        ),
    );
    
    /**
     * 根据id获取标签名字
     * @param  [type] $labelId  标签id
     * @param  [type] $labelKey 标签key
     * @return booler | string          
     */
    public static function getLabelNameById($labelId,$labelKey)
    {
        if(!$labelId || !$labelKey) {
            Bd_log::Warning("Error[getLabelNameById param empty]");
            return false;
        }
        $arrLabelList = self::getLabelListByKey($labelKey);
        if(!$arrLabelList) {
            Bd_log::Warning("Error[getLabelListByKey empty]");
            return fales;
        }
        return isset($arrLabelList[$labelId])?$arrLabelList[$labelId]:false;
    }

    /**
     * 根据keys获取标签列表
     * @param  [type] $labelKey [description]
     * @return [type]           [description]
     */
    public static function getLabelListByKey($labelKey)
    {
        if(empty($labelKey)) {
            Bd_log::Warning("Error[getLabelListByKey param empty]");
            return false;
        }
        if($labelList = self::$dataDictionary[$labelKey])
        {
            return $labelList;
        }
        return false;
    }

}
