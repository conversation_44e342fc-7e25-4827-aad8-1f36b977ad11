<?php

/**
 * @file    ExamLog.php
 * <AUTHOR>
 * @data    2017-09-06
 * @desc    日志系统
 * @deprecated 废弃
 */
class Hkzb_Ds_Fudao_Exam_ExamLog {

    // 试卷操作类型id与文案对应关系
    const EXAM_PAPER_ACTION_CREATE = 1;  // 新增试卷
    const EXAM_PAPER_ACTION_DELETE = 2;  // 删除试卷
    const EXAM_PAPER_ACTION_BIND   = 3;  // 绑定试卷
    const EXAM_PAPER_ACTION_UNBIND = 4;  // 解绑试卷
    const EXAM_PAPER_ACTION_EDIT   = 5;  // 编辑试卷
    static $EXAM_PAPER_ACTION_MAP = array(
        self::EXAM_PAPER_ACTION_CREATE => "新增试卷",
        self::EXAM_PAPER_ACTION_DELETE => "删除试卷",
        self::EXAM_PAPER_ACTION_BIND   => "绑定试卷",
        self::EXAM_PAPER_ACTION_UNBIND => "解绑试卷",
        self::EXAM_PAPER_ACTION_EDIT   => "编辑试卷",
    );

    //学生考试行为类型
    const STUDENT_EXAM_ACTION_BEGINEXAM = 1;  // 开始考试
    const STUDENT_EXAM_ACTION_SUBMITEXAM = 2;   // 提交试卷（结束考试）
    static $STUDENT_EXAM_ACTION_MAP = array(
        self::STUDENT_EXAM_ACTION_BEGINEXAM  => "开始考试",
        self::STUDENT_EXAM_ACTION_SUBMITEXAM => "提交试卷",
    );

    private $_objDaoExamPaperOperateLog;

    public function __construct(){
        $this->_objDaoExamPaperOperateLog = new Hkzb_Dao_Fudao_Exam_ExamPaperOperateLog();
    }

    /**
     * 添加 试卷操作记录
     * @param $intExamId
     * @param $intUid
     * @param $intActionId
     * @param array $arrExt
     * @return bool
     */
    public function addExamPaperOperateLog($examId, $uid, $actionId, $arrActionDetail = array()) {
        $examId   = intval($examId);
        $uid      = intval($uid);
        $actionId = intval($actionId);

        $arrAction = array(
            self::EXAM_PAPER_ACTION_CREATE,
            self::EXAM_PAPER_ACTION_DELETE,
            self::EXAM_PAPER_ACTION_BIND,
            self::EXAM_PAPER_ACTION_UNBIND,
            self::EXAM_PAPER_ACTION_EDIT,
        );
        if ( 0 == $examId || 0 == $uid || !in_array($actionId, $arrAction) ) {
            Bd_Log::warning("Error[Param error] Detail[examId:$examId  uid: $uid actionId: $actionId]");
            return false;
        }

        $arrFields = array(
            'examId'        => $examId,
            'operatorUid'   => $uid,
            'operateAction' => $actionId,
            'operateDetail' => json_encode($arrActionDetail),
            'createTime'    => time(),
        );

        $ret = $this->_objDaoExamPaperOperateLog->insertRecords( $arrFields );
        if (!$ret){
            $lastSql = $this->_objDaoExamPaperOperateLog->getLastSQL();
            Bd_Log::warning("Error[Db insert error] Detail[lastSql: $lastSql]");
        }

        return $ret;
    }


}