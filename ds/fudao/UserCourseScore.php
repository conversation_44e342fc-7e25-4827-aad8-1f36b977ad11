<?php
/**
 * Copyright (c) 2017 zuoyebang.com, Inc. All Rights Reserved
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @file: UserCourseScore.php
 * @date: 2017/5/26
 * @time: 15:43
 * @desc:
 */

class Hkzb_Ds_Fudao_UserCourseScore
{
    const USER_COURSE_SCORE_STATUS_NORMAL  = 0;  //正常
    const USER_COURSE_SCORE_STATUS_DELETED = 1; //删除（如退款）

    public function __construct()
    {
        $this->_objDaoUserCourseScore = new Hkzb_Dao_Fudao_UserCourseScore();
    }

    /**
     * 添加课程学分记录
     * @param $arrFields
     * @return bool
     */
    public function addUserCourseScore($arrFields)
    {
        $arrData = array(
            'uid'        => isset($arrFields['uid']) ? intval($arrFields['uid']) : 0,
            'courseId'   => isset($arrFields['courseId']) ? intval($arrFields['courseId']) : 0,
            'score'      => isset($arrFields['score']) ? intval($arrFields['score']) : 0,
            'deleted'    => isset($arrFields['deleted']) ? intval($arrFields['deleted']) : self::USER_COURSE_SCORE_STATUS_NORMAL,
            'createTime' => time(),
        );
        if ($arrData['uid'] <= 0 || $arrData['courseId'] <= 0 || $arrData['score'] < 0) {
            return false;
        }
        $ret = $this->_objDaoUserCourseScore->insertRecords($arrData);

        return $ret;
    }

    /**
     * 获取用户课程加分信息
     * @param $intStudentUid
     * @param $intCourseId
     * @param array $arrFields
     * @return array|bool|false
     */
    public function getUserCourseScore($intStudentUid, $intCourseId, $arrFields = array())
    {
        if ($intStudentUid <= 0 || $intCourseId <= 0) {
            return false;
        }
        if (empty($arrFields)) {
            $arrFields = Hkzb_Dao_Fudao_UserCourseScore::$allFields;
        }
        $arrConds = array(
            'uid'      => $intStudentUid,
            'courseId' => $intCourseId,
        );
        $ret      = $this->_objDaoUserCourseScore->getRecordByConds($arrConds, $arrFields);

        return $ret;
    }

    /**
     * 更新课程学分信息
     * @param $intStudentUid
     * @param $intCourseId
     * @param $arrFields
     * @param array $ext
     * @return bool
     */
    public function updateUserCourseScore($intStudentUid, $intCourseId, $score)
    {
        if ($intStudentUid <= 0 || $intCourseId <= 0) {
            return false;
        }
        $userCourseScoreInfo = self::getUserCourseScore($intStudentUid, $intCourseId);
        if ($userCourseScoreInfo === false) {
            return false;
        }
        if (empty($userCourseScoreInfo)) {
            return false;
        }
        $arrConds  = array(
            'uid'      => $intStudentUid,
            'courseId' => $intCourseId,
        );
        $arrFields = array(
            "score = score + $score",
        );
        $res       = $this->_objDaoUserCourseScore->updateByConds($arrConds, $arrFields);

        return $res;
    }
}