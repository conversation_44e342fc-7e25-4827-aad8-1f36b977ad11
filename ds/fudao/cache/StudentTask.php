<?php

/**
 * @file    StudentTask.php
 * <AUTHOR>
 * @date    2018-04-23
 * @brief   直播课缓存系统 -- 用户任务系统
字段array（
courseId,
lessonId,
type,任务类型
replayTime,回放时长
playbackPercent,回放进度
subjectOne,科目类型单字
lessonName,章节名称
expiryTime,过期时间
createTime,创建时间
status,任务状态0未完成，1已完成
）
 **/
class Hkzb_Ds_Fudao_Cache_StudentTask {

//    protected $_objCodis;
    protected $_objStored;

    const FUDAO_ZHIBO_STUDENT_TASK_CACH = "FUDAO_ZHIBO_STUDENT_TASK_CACH";
    const FUDAO_ZHIBO_STUDENT_TASK_CACH_FRAME = "FUDAO_ZHIBO_STUDENT_TASK_CACH_FRAME";
    const TASK_TYPE_INSTRUCT_COURSE = 1;//指导课
    const TASK_TYPE_PLAYBACK = 2;//回放
    const TASK_TYPE_HOMEWORK = 3;//作业

    public function __construct() {
//        $this->_objCodis = Hk_Service_RedisClient::getInstance("zbcourse");
        $this->_objStored = Hkzb_Util_StoredService::getZhiboInstance();
    }

    /**
     * 获取任务信息
     * @param $intUid
     * @return array
     */
    public function getStudentTaskListbyUid( $intUid ) {
        $intUid = intval($intUid);
        if ($intUid <= 0) {
            return array('taskNum'=>0,'taskList'=>array(),'frame'=>0);
        }
        $strKey = self::FUDAO_ZHIBO_STUDENT_TASK_CACH . $intUid;

        $strValue = $this->_objStored->get($strKey);

        $ret = json_decode($strValue, true);

        if (empty( $ret ) || !is_array($ret)) {
            return array('taskNum'=>0,'taskList'=>array(),'frame'=>0);
        }
        $time = time();
        $arrCourseList = array();
        foreach ($ret as $key =>$task) {
            if ($task['expiryTime'] < $time || $task['status'] == 1) {
                continue;
            }
            $arrCourseList[$key] = $task;
        }
        $taskNum = count($arrCourseList);
        $arrOutput['taskNum'] = isset($taskNum)? $taskNum : 0;
        $arrOutput['taskList'] = $arrCourseList;
        $arrOutput['frame'] = 0;
        $alertKey  = self::FUDAO_ZHIBO_STUDENT_TASK_CACH_FRAME . $intUid;
        $alertValue = $this->_objStored->get($alertKey);
        if ($alertValue == 1) {
            $arrOutput['frame'] = 1;
        }
        return $arrOutput;
    }

    /**
     * 把当前任务完成状态置为无
     * @param $intUid
     * @return bool
     */
    public function updateTaskFrame($intUid) {
        $alertKey  = self::FUDAO_ZHIBO_STUDENT_TASK_CACH_FRAME . $intUid;
        $this->_objStored->set($alertKey,0);
        return true;
    }

    /**
     * 提交作业完成任务
     *
     */
     public function doneHomework($intUid,$courseId,$lessonId){
         $updateArr = array('status'=>1);
         $this->updateStudentTask($intUid,self::TASK_TYPE_HOMEWORK,$courseId,$lessonId,$updateArr);
         return true;
     }

    /**
     * 更新任务信息
     * @param $intUid
     * @return array
     */
    public function updateStudentTask( $intUid,$type,$courseId,$lessonId = 0,$updateArr = array() ) {
        $intUid = intval($intUid);
        if ($intUid <= 0 || empty($updateArr) || $courseId <=0) {
            return false;
        }

        $strKey = self::FUDAO_ZHIBO_STUDENT_TASK_CACH . $intUid;

        $strValue = $this->_objStored->get($strKey);

        $ret = json_decode($strValue, true);

        if (empty( $ret ) || !is_array($ret)) {
            Bd_Log::warning("studentTask not exist;");
            return false;
        }

        $numKey = $intUid . '_' . $type . '_' . $courseId . '_' . $lessonId;
        if (!empty($ret[$numKey])) {
            foreach ($updateArr as $k => $v) {
                $ret[$numKey][$k] = $v;
            }
            $res = $this->_objStored->set($strKey,json_encode($ret));
            if ( !empty($updateArr['status']) && $updateArr['status'] == 1 ) {
                $alertKey  = self::FUDAO_ZHIBO_STUDENT_TASK_CACH_FRAME . $intUid;
                $this->_objStored->set($alertKey,1);
                $this->_objStored->expire($alertKey, 3600 * 24);
            }
            return $res;
        }
        Bd_Log::warning("studentTask not exist;" . $numKey);
        return false;

    }
    /**
     * 添加任务信息
     * @param $intUid
     * @return array
     */
    public function addStudentTask( $intUid,$type,$courseId,$lessonId=0,$addArr = array() ) {
        $intUid = intval($intUid);
        if ($intUid <= 0 || empty($addArr)) {
            return array();
        }

        $strKey = self::FUDAO_ZHIBO_STUDENT_TASK_CACH . $intUid;

        $strValue = $this->_objStored->get($strKey);

        $strValue = json_decode($strValue, true);

        $numKey = $intUid . '_' . $type . '_' . $courseId . '_' . $lessonId;

        if ( empty($strValue[$numKey]) ) {
            $strValue[$numKey] = $addArr;
        }else{
            return 1;
        }

        $res = $this->_objStored->set($strKey, json_encode($strValue));
        // 设置过期时间
        $this->_objStored->expire($strKey, 3600 * 24 * 15);

        return $res;
    }
    /**
     * 删除任务信息
     * @param $intUid
     * @return array
     */
    public function deleteStudentTask( $intUid,$type,$courseId,$lessonId = 0) {
        $intUid = intval($intUid);
        if ($intUid <= 0 || $courseId <=0) {
            return array();
        }

        $strKey = self::FUDAO_ZHIBO_STUDENT_TASK_CACH . $intUid;

        $numKey = $intUid . '_' . $type . '_' . $courseId . '_' . $lessonId;

        $strValue = $this->_objStored->get($strKey);

        $strValue = json_decode($strValue, true);

        if (empty( $strValue ) || !is_array($strValue)) {
            return array();
        }
        if(!empty($strValue[$numKey])){
            unset($strValue[$numKey]);
            $res = $this->_objStored->set($strKey,json_encode($strValue));
            return $res;
        }
        return array();
    }
    //更新回放进度
    public function updateStudentTaskPercent( $intUid,$lessonId,$percentNum ) {
        $intUid = intval($intUid);
        if ($intUid <= 0 || $percentNum < 0 || $lessonId <=0) {
            return false;
        }

        $strKey = self::FUDAO_ZHIBO_STUDENT_TASK_CACH . $intUid;

        $strValue = $this->_objStored->get($strKey);

        $ret = json_decode($strValue, true);

        if (empty( $ret ) || !is_array($ret)) {
            return true;
        }
        // //获取课程信息
        $objDsLesson = new Hkzb_Ds_Fudao_Lesson();
        $courseInfo = $objDsLesson->getPackCourseIdByLesson($lessonId);
        if(empty($courseInfo)) {
            Bd_Log::warning("studentTask courseInfo empty detail:json_encode($courseInfo)");
            return false;
        }
        $courseId = $courseInfo['courseId'];
        $numKey = $intUid . '_' . self::TASK_TYPE_PLAYBACK . '_' . $courseId . '_' . $lessonId;
        if (!empty($ret[$numKey])) {
            if ($percentNum > $ret[$numKey]['progressRate']) {
                $updateArr['progressRate'] = $percentNum;
                if ($percentNum >= 98) {
                    $updateArr['status'] = 1;
                }
                $this -> updateStudentTask( $intUid,self::TASK_TYPE_PLAYBACK,$courseId,$lessonId,$updateArr );
            }

        }
        return true;

    }

    /**
     * @param $intUid
     * @param $type
     * @param $courseId
     * @param $lessonId
     * @return array
     */
    public function createStudentTask( $type,$courseId=0,$lessonId=0,$data ) {
        $request = array();
        $isParamOk = $this->checkParam($type,$courseId,$lessonId,$data);

        if(!$isParamOk['status']){
            //若是参数未检验通过
            $request['status'] = false;
            $request['data']   = $isParamOk['data'];
            return $request;
        }

        if(self::TASK_TYPE_INSTRUCT_COURSE == $type){
            $request = $this->addTaskInstructCourse();
        }
        if(self::TASK_TYPE_PLAYBACK == $type){
            $request = $this->addTaskPlayback($lessonId,$courseId,$data);
        }
        if(self::TASK_TYPE_HOMEWORK == $type ){
            //先检验是否绑定了作业
            $lessonHomework = (new Hkzb_Ds_Fudao_Exam_ExamPaperRelation())->getExamInfoByLessonIdExamType($lessonId,Hkzb_Ds_Fudao_Exam_ExamPaperRelation::RELATION_TYPE_BIND_HOMEWORK);
            if(!empty($lessonHomework)){
                $request = $this->addTaskHomework($lessonId,$courseId,$data);
            }
        }
        return $request;

    }

    /**
     * @param $intUid
     * @param $type
     * @param $courseId
     * @param $lessonId
     * @return array
     */
    private function checkParam($type,$courseId,$lessonId,$data) {
        if(!in_array($type,[1,2,3])){
            return ['status'=>false,'data'=>'任务类型不符合'];
        }
        if(self::TASK_TYPE_HOMEWORK == $type && (empty($lessonId)||empty($courseId)) ){
            //作业类型，却没有lessonId
            //data为arrStudentUid
            if(empty($lessonId)){
                return ['status'=>false,'data'=>'缺少参数：lessonId'];
            }
            if(empty($courseId)){
                return ['status'=>false,'data'=>'缺少参数：courseId'];
            }
            if(!is_array($data)){
                return ['status'=>false,'data'=>'学生id数据不正确'];
            }
        }
        return ['status'=>true,'data'=>''];
    }

    /**
     * @param $lessonId
     * @param $courseId
     * @param $data
     * @return bool
     *
     */
    private function addTaskHomework($lessonId,$courseId,$data) {
        //课程id处理成打包课
        //查询打包课id
        $objCourse = new Hkzb_Ds_Fudao_Course();
        $arrCourseInfo = $objCourse->getCourseInfo($courseId);
        if(isset($arrCourseInfo['pack']) && 2==$arrCourseInfo['pack']){
            //是打包课
            $coursePackId = $arrCourseInfo['extData']['packId'];
            $coursePackId = intval($coursePackId[0]);
        }
        //任务中保存的为打包课

        $courseId = isset($coursePackId)?$coursePackId:$courseId;
        //添加作业任务


        $expireyTime = strtotime(date('Y-m-d',strtotime('+1 day'))) + 7*24*3600;
        foreach($data as $key=>$studentUid){
            $cacheData = array(
                'type' => self::TASK_TYPE_HOMEWORK,
                'status'=>0,
                'expiryTime'=>$expireyTime,
                'courseId'=>$courseId,
                'lessonId'=>$lessonId,
                'createTime'=>time(),
            );
            $res = $this->addStudentTask($studentUid,self::TASK_TYPE_HOMEWORK,$courseId,$lessonId,$cacheData);
        }
        return true;
    }

    private function addTaskPlayback($lessonId,$courseId,$data) {
        //添加回放任务

        $expireyTime = time() + 15*24*3600;
        foreach($data['studentUidlist'] as $key=>$studentUid){
            $cacheData = array(
                'type' => self::TASK_TYPE_PLAYBACK,
                'status'=>0,
                'expiryTime'=>$expireyTime,
                'totalTime'=>$data['totalTime'],
                'progressRate'=>0,
                'courseId'=>$courseId,
                'lessonId'=>$lessonId,
                'createTime'=>time(),
            );
            $res = $this->addStudentTask($studentUid,self::TASK_TYPE_PLAYBACK,$courseId,$lessonId,$cacheData);
        }
        return true;
    }


    /**
     * 任务
     * @param array $arrParams
     * @return boolean|array
     */
    public function addTask($lessonId)
    {

        $lessonId = intval($lessonId); //必须

        if ($lessonId <= 0 ) {
            Bd_Log::warning("param error, lessonId[$lessonId]");

            return false;
        }
        //发nmq请求
        $params['lessonId'] = $lessonId;
        $params['time'] = time();
        $params['groupKey'] = $lessonId;
//        $http = http_build_query($params);
        $arrNmqParams = $params;
        Bd_Log::notice("addtask-playback-start, lessonId[$lessonId]");
        $objNmq = new Zb_Service_Nmq();
        $ret = $objNmq->sendCommand(800012, $arrNmqParams);
        Bd_Log::notice("addtask-playback-end, ret".json_encode($ret));
        if ($ret === false) {
            //请求失败时，删除当前频率限制，方便重试
            return false;
        }


        return true;
    }

}
