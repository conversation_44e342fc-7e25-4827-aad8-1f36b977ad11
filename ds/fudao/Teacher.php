<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file Teacher.php
 * <AUTHOR>
 * @date 2015/11/17 14:13:18
 * @brief 老师
 *  
 **/

class Hkzb_Ds_Fudao_Teacher {

    private $cache;

    public function __construct($cache = null) {
        if (empty($cache)) {
            $cache = Hk_Service_RedisClient::getInstance("spdata");
        }
        $this->cache = $cache;
    }

    //老师职责
    const DUTY_TEACHER   = 1; //主讲老师
    const DUTY_ASSISTANT = 2; //辅导老师
    static $DUTY_ARRAY   = array(
        self::DUTY_TEACHER  => '主讲老师',
        self::DUTY_ASSISTANT=> '辅导老师',
    );

    const RECOMMEND_YES   = 1; //推荐老师
    const RECOMMEND_NO    = 0; //不是推荐


    //状态
    const STATUS_OK      = 0; //未删除
    const STATUS_DELETED = 1; //已删除
    static $STATUS_ARRAY = array(
        self::STATUS_OK     => '未删除',
        self::STATUS_DELETED=> '已删除',
    );
    static $ARRAY_STATUS = array(
        self::STATUS_OK     => '在职',
        self::STATUS_DELETED=> '离职',
    );
    const XINGZHI_FULL      = 0; //全职教研
    const XINGZHI_PART      = 1; //兼职
    const XINGZHI_INNER     = 2; //内部
    const XINGZHI_ZHUAN     = 3; //全职主讲
    const XINGZHI_IMC       = 100; //imc
    const XINGZHI_ZHENREN    = 99; //辅导老师真人账号
    static $XINGZHI_ARRAY = array(
        self::XINGZHI_FULL  => '全职教研',
        self::XINGZHI_PART  => '兼职老师',
        self::XINGZHI_INNER => '内部',
        self::XINGZHI_ZHUAN => '全职主讲',
        self::XINGZHI_IMC => 'imc',
    );

    //老师所属类型
    const TYPE_PRIVATE = 0;
    const TYPE_PUBLIC  = 1;
    const TYPE_PRIVATE_LONG  = 2;
    const TYPE_PARENT_COURSE = 3;
    const TYPE_NONU = 10;
    static $TYPE_ARRAY = array(
        self::TYPE_PRIVATE => '专题课',
        self::TYPE_PUBLIC  => '公开课',
        self::TYPE_PRIVATE_LONG  => '班课',
        self::TYPE_PARENT_COURSE => '家长课',
        self::TYPE_NONU          => '未分组',
    );

    /**
     * 教师保护期薪资策略
     */
    static $SBJ_PRO_SALARY = array(
        1 => array(
            'title' => '策略一',
            'role'  => array(
                array(
                    'salary' => 300,
                    'note'   => '每小时300元；',
                ),
            ),
        ),
        2 => array(
            'title' => '策略二',
            'role'  => array(
                array(
                    'salary' => 300,
                    'note'   => '≥45min，每章节300元；',
                ),
                array(
                    'salary' => 150,
                    'note'   => '<45min，每章节150元；',
                ),
            ),
        ),
    );

    //归属
    const BELONG_IN = 1;
    const BELONG_OUT = 2;
    static $BELONG_ARRAY = array(
        self::BELONG_IN => '自有',
        self::BELONG_OUT => '外部'
    );

    const TEACHER_CACHE_KEY = 'ds_teacher_info_';

    const ALL_FIELDS = 'teacherUid,teacherName,teacherAvatar,phone,duty,grade,subject,goodCnt,teachCnt,abstract,deleted,createTime,updateTime,operator_uid,operator,classTime,extData,type,isRecommend,weight,fansCnt,xingzhi,leaveTime,isInside,professionLevel,teacherType,professionLevelNew,channelId,frontShow';

    // 主讲类型
    const TEACHER_TYPE_REAL = 1;

    const TEACHER_TYPE_VIRTUAL = 2;

    public static $TEACHER_TYPE_DESC_MAP = array(
        self::TEACHER_TYPE_REAL => '真实主讲',
        self::TEACHER_TYPE_VIRTUAL => '虚拟主讲',
    );

    /**
     * 新增老师
     *
     * @param  array  $arrParams 老师属性
     * @return bool true/false
     */
    public function addTeacher($arrParams) {
        if(intval($arrParams['teacherUid']) <= 0 || strlen($arrParams['teacherName']) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[param: ". json_encode($arrParams). "]");
            return false;
        }

        $arrFields = array(
            'teacherUid'  => intval($arrParams['teacherUid']),
            'teacherName' => strval($arrParams['teacherName']),
            'teacherAvatar'=> isset($arrParams['teacherAvatar']) ? strval($arrParams['teacherAvatar']) : '',
            'phone'       => isset($arrParams['phone']) ? strval($arrParams['phone']) : '',
            'duty'        => isset($arrParams['duty']) ? strval($arrParams['duty']) : '',
            'grade'       => isset($arrParams['grade']) ? intval($arrParams['grade']) : 0,
            'xingzhi'     => isset($arrParams['xingzhi']) ? intval($arrParams['xingzhi']) : 0,
            'type'        => isset($arrParams['type']) ? intval($arrParams['type']) : 0,
            'subject'     => isset($arrParams['subject']) ? intval($arrParams['subject']) : 0,
            'goodCnt'     => isset($arrParams['goodCnt']) ? intval($arrParams['goodCnt']) : 0,
            'teachCnt'    => isset($arrParams['teachCnt']) ? intval($arrParams['teachCnt']) : 0,
            'abstract'    => isset($arrParams['abstract']) ? strval($arrParams['abstract']) : '',
            'deleted'     => self::STATUS_OK,
            'createTime'  => time(),
            'updateTime'  => time(),
            'operatorUid' => isset($arrParams['operatorUid']) ? intval($arrParams['operatorUid']) : 0,
            'operator'    => isset($arrParams['operator']) ? strval($arrParams['operator']) : '',
            'extData'     => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '',
            'isInside'    => isset($arrParams['isInside']) ? intval($arrParams['isInside']) : 1,
            'teacherType' => isset(self::$TEACHER_TYPE_DESC_MAP[$arrParams['teacherType']]) ? $arrParams['teacherType'] : self::TEACHER_TYPE_REAL,
            'professionLevelNew' => isset($arrParams['professionLevelNew']) ? strval($arrParams['professionLevelNew']) : 0,
            'channelId'   => isset($arrParams['channelId']) ? intval($arrParams['channelId']) : 0,
            'frontShow'    => isset($arrParams['frontShow']) ? intval($arrParams['frontShow']) : 1,
        );

        $objDaoTeacher = new Hkzb_Dao_Fudao_Teacher();
        $ret = $objDaoTeacher->insertRecords($arrFields);

        //清理缓存
        $cacheKey = self::TEACHER_CACHE_KEY . intval($arrParams['teacherUid']);
        $this->cache->del($cacheKey);

        //通知阿喀琉斯刷新teacherinfo
        $this->sendNmq(intval($arrParams['teacherUid']));
        return $ret;
    }

    /**
     * 更新老师
     *
     * @param  int  $teacherUid  老师uid
     * @param  array  $arrParams   老师属性
     * @return bool true/false
     */
    public function updateTeacher($teacherUid, $arrParams) {
        if(intval($teacherUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[teacherUid:$teacherUid]");
            return false;
        }

        $arrConds = array(
            'teacherUid' => intval($teacherUid),
        );

        $arrFields = array();
        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach($arrParams as $key => $value) {
            if(!in_array($key, $arrAllFields)) {
                continue;
            }

            $arrFields[$key] = $value;
        }

        //ext由ps补全，json处理在ds
        if(isset($arrFields['extData'])) {
            $arrFields['extData'] = json_encode($arrFields['extData']);
        }

        //补全更新时间
        $arrFields['updateTime'] = time();

        $objDaoTeacher = new Hkzb_Dao_Fudao_Teacher();
        $ret = $objDaoTeacher->updateByConds($arrConds, $arrFields);

        //清理缓存
        $cacheKey = self::TEACHER_CACHE_KEY . $teacherUid;
        $this->cache->del($cacheKey);

        //通知阿喀琉斯刷新teacherinfo
        $this->sendNmq($teacherUid);
        return $ret;
    }

    public function updateTeacherV2($teacherUid, $arrParams) {
        if(intval($teacherUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[teacherUid:$teacherUid]");
            return false;
        }

        $arrConds = array(
            'teacherUid' => intval($teacherUid),
        );

        $arrFields = array();
        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach($arrParams as $key => $value) {
            if(!in_array($key, $arrAllFields)) {
                continue;
            }

            $arrFields[$key] = $value;
        }

        //ext由ps补全，json处理在ds
        if(isset($arrFields['extData'])) {
            $arrFields['extData'] = json_encode($arrFields['extData']);
        }

        //补全更新时间
        $arrFields['updateTime'] = time();

        $objDaoTeacher = new Hkzb_Dao_Fudao_Teacher();
        $ret = $objDaoTeacher->updateByConds($arrConds, $arrFields);

        //通知阿喀琉斯刷新teacherinfo
        $this->sendNmq($teacherUid);
        return $ret;
    }

    /**
     * 删除老师
     *
     * @param  int  $teacherUid    老师uid
     * @param  int  $operatorUid    操作人id
     * @param  int  $operatorName  操作人
     * @return bool true/false
     */
    public function deleteTeacher($teacherUid, $operatorUid = 0, $operator = '') {
        if(intval($teacherUid) <= 0 || intval($operatorUid) <= 0 || strlen($operator) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[teacherUid:$teacherUid operatorUid:$operatorUid operatorName:$operator]");
            return false;
        }

        $arrConds = array(
            'teacherUid' => intval($teacherUid),
        );

        $arrFields = array(
            'deleted'       => self::STATUS_DELETED,
            'updateTime'    => time(),
            'operatorUid'   => intval($operatorUid),
            'operator'      => strval($operator),
        );

        $objDaoTeacher = new Hkzb_Dao_Fudao_Teacher();
        $ret = $objDaoTeacher->updateByConds($arrConds, $arrFields);

        //清理缓存
        $cacheKey = self::TEACHER_CACHE_KEY . $teacherUid;
        $this->cache->del($cacheKey);

        //通知阿喀琉斯刷新teacherinfo
        $this->sendNmq($teacherUid);
        return $ret;
    }
    
    /**
     * 获取老师详情
     *
     * @param  int  $teacherUid  老师uid
     * @param  mix  $arrFields   指定属性
     * @return mix
     */
    public function getTeacherInfo($teacherUid, $arrFields = array(), $cacheSwitch = false) {
        if(intval($teacherUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[teacherUid:$teacherUid]");
            return false;
        }

        //读缓存
        if($cacheSwitch) {
            $cacheKey = self::TEACHER_CACHE_KEY . $teacherUid;
            $cacheValue = $this->cache->get($cacheKey);
            if(!empty($cacheValue)) {
                $teacherInfo = json_decode(utf8_encode($cacheValue), true);
                $teacherInfo = $this->filter($teacherInfo, $arrFields);
                return $teacherInfo;
            }
        }

        $arrFields = explode(',', self::ALL_FIELDS);
        $arrConds = array(
            'teacherUid' => intval($teacherUid),
        );

        $objDaoTeacher = new Hkzb_Dao_Fudao_Teacher();
        $teacherInfo = $objDaoTeacher->getRecordByConds($arrConds, $arrFields);

        if(false === $teacherInfo) {
            Bd_Log::warning("Error:[getRecordByConds], Detail:[teacherUid:$teacherUid]");
            return false;
        }

        //写缓存
        if($cacheSwitch) {
            $cacheValue = json_encode($teacherInfo);
            $this->cache->set($cacheKey, $cacheValue);
        }

        $teacherInfo = $this->filter($teacherInfo, $arrFields);
        return $teacherInfo;
    }

    /**
     * 获取老师详情
     *
     * @param  int  $teacherUid  老师uid
     * @param  mix  $arrFields   指定属性
     * @return mix
     */
    public function getTeacherInfoArr($teacherUids, $arrFields = array(), $cacheSwitch = false) {
        $arrOutput = array();
        if(empty($teacherUids)){
            return $arrOutput;
        }
        $teacherUids = array_map('intval',$teacherUids);
        if(empty($teacherUids)) {
            return $arrOutput;
        }
        //先做去重过滤
        $teacherUids = array_unique($teacherUids);
        $arrOutput = [];
        //读缓存
        if($cacheSwitch) {
            $cacheKeys = array();
            $key2i = array();
            foreach($teacherUids as $teacherUid) {
                $key = self::TEACHER_CACHE_KEY . $teacherUid;
                $cacheKeys[] = $key;
                $key2i[$key] = count($cacheKeys) - 1;
            }

            $cacheValues = $this->cache->mget($cacheKeys);
            foreach($teacherUids as $key =>  $teacherUid){
                $cacheKey = self::TEACHER_CACHE_KEY . $teacherUid;
                $i = $key2i[$cacheKey];
                if(false === $cacheValues || empty($cacheValues[$i])) {
                    continue;
                }
                $cacheValue = $cacheValues[$i];
                $cacheValue = json_decode(utf8_encode($cacheValue),true);
                $cacheValue = $this->filter($cacheValue, $arrFields);
                $arrOutput[$teacherUid] = $cacheValue;
                unset($teacherUids[$key]);
            }
            if(empty($teacherUids)){
                //全部命中
                return $arrOutput;
            }
        }
        $arrFields = explode(',', self::ALL_FIELDS);
        $arrConds = array(
            0 => 'teacher_uid in ('.join(',',$teacherUids).')',
        );

        $objDaoTeacher = new Hkzb_Dao_Fudao_Teacher();
        $teacherInfoList = $objDaoTeacher->getListByConds($arrConds, $arrFields);


        foreach($teacherInfoList as $teacherInfo) {
            $teacherUid = $teacherInfo['teacherUid'];
            //写缓存
            if($cacheSwitch) {
                $cacheKey = self::TEACHER_CACHE_KEY . $teacherUid;
                $cacheValue = json_encode($teacherInfo);
                $this->cache->set($cacheKey, $cacheValue);
            }
            $teacherInfo = $this->filter($teacherInfo, $arrFields);
            $arrOutput[$teacherUid] = $teacherInfo;
        }

        return $arrOutput;
    }

    /**
     * 获取老师详情
     *
     * @param  int  $teacherUid  老师uid
     * @param  mix  $arrFields   指定属性
     * @return mix
     */
    public function getSignedTeacherList() {
        //读缓存
        $cacheKey = self::TEACHER_CACHE_KEY . 'getSignedTeacherList';
        $cacheValue = $this->cache->get($cacheKey);
        if(!empty($cacheValue)) {
            $teacherList = json_decode(utf8_encode($cacheValue), true);
            return $teacherList;
        }

        //读数据库
        $teacherList = array();
        $arrConds = array(
            'weight'  => 1,
            'deleted' => self::STATUS_OK,
        );
        $objDaoTeacher = new Hkzb_Dao_Fudao_Teacher();
        $arrTeacherInfo = $objDaoTeacher->getListByConds($arrConds, array('teacherUid'));
        foreach($arrTeacherInfo as $teacherInfo) {
            $teacherList[] = intval($teacherInfo['teacherUid']);
        }

        //写缓存
        $cacheValue = json_encode($teacherList);
        $this->cache->set($cacheKey, $cacheValue);

        return $teacherList;
    }

    /**
     * @param $arrConds
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @return array|false
     */
    public function getTeacherListByConds($arrConds, $arrFields = array(), $offset = 0, $limit = 20) {
        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrAppends = array(
            "order by create_time desc",
            "limit $offset, $limit",
        );

        $objDaoTeacher = new Hkzb_Dao_Fudao_Teacher();
        $ret = $objDaoTeacher->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

   /**
     * 获取数量
     * @param $arrConds
     * @return false|int
     */
    public function getTeacherCntByConds($arrConds) {
        $objDaoTeacher = new Hkzb_Dao_Fudao_Teacher();
        $ret = $objDaoTeacher->getCntByConds($arrConds);
        return $ret;
    }


    /**
     * 获取老师详情
     * @param int $phone
     * @param array $arrFields
     * @param int $status
     * @return array|bool|false
     */
    public function getTeacherInfoByPhone($phone, $arrFields = array(), $status = -1) {
        if(intval($phone) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[phone:$phone]");
            return false;
        }

        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'phone' => $phone,
            'deleted'=>self::STATUS_OK, 
        );
        if ($status == -1) {
            unset($arrConds['deleted']);
        }
        $objDaoTeacher = new Hkzb_Dao_Fudao_Teacher();
        $ret = $objDaoTeacher->getRecordByConds($arrConds, $arrFields);

        return $ret;
    }

    /**
     * 获取老师详情,兼容同一个手机号多条记录的情况
     * @param int $phone
     * @param array $arrFields
     * @param int $status
     * @return array|bool|false
     */
    public function getTeacherInfoListByPhone($phone, $arrFields = array(), $status = -1) {
        if(intval($phone) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[phone:$phone]");
            return false;
        }

        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'phone' => $phone,
            'deleted'=>self::STATUS_OK,
        );
        if ($status == -1) {
            unset($arrConds['deleted']);
        }
        $objDaoTeacher = new Hkzb_Dao_Fudao_Teacher();
        $ret = $objDaoTeacher->getListByConds($arrConds, $arrFields);

        return $ret;
    }

    /**
     * 获取老师总数
     *
     * @param  int  $grade      年级
     * @param  int  $subject    学科
     * @param  int  $duty       职务
     * @return int
     */
    public function getTeacherCnt($grade = 0, $subject = 0, $duty = 0) {
        $arrConds = array(
            'deleted' => self::STATUS_OK,
        );

        if(intval($grade) > 0) {
            $arrConds['grade'] = intval($grade);
        }

        if(intval($subject) > 0) {
            $arrConds['subject'] = intval($subject);
        }

        if(intval($duty) > 0) {
            $arrConds['duty'] = intval($duty);
        }

        $objDaoTeacher = new Hkzb_Dao_Fudao_Teacher();
        $ret = $objDaoTeacher->getCntByConds($arrConds);

        return $ret;
    }

    /**
     * 获取老师列表
     *
     * @param  int  $grade      年级
     * @param  int  $subject    学科
     * @param  int  $duty       职务
     * @param  mix  $arrFields  指定属性
     * @param  int  $offset     偏移
     * @param  int  $limit      限制
     * @return mix
     */
    public function getTeacherList($grade = 0, $subject = 0, $duty = 0, $arrFields = array(), $offset = 0, $limit = 20) {
        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'deleted' => self::STATUS_OK,
        );

        if(intval($grade) > 0) {
            $arrConds['grade'] = intval($grade);
        }

        if(intval($subject) > 0) {
            $arrConds['subject'] = intval($subject);
        }

        if(intval($duty) > 0) {
            $arrConds['duty'] = intval($duty);
        }

        $arrAppends = array(
            "order by create_time desc",
            "limit $offset, $limit",
        );

        $objDaoTeacher = new Hkzb_Dao_Fudao_Teacher();
        $ret = $objDaoTeacher->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    /**
     * 根据姓名获取老师数量
     *
     * @param  string  $teacherName  老师姓名
     * @param  mix     $arrFields    指定属性
     * @return mix
     */
    public function getTeacherCntByName($teacherName) {
        if(strlen($teacherName) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[teacherName:$teacherName]");
            return false;
        }

        $arrConds = array(
            'deleted' => self::STATUS_OK,
            'teacherName' => strval($teacherName),    
        );

        $objDaoTeacher = new Hkzb_Dao_Fudao_Teacher();
        $ret = $objDaoTeacher->getCntByConds($arrConds);

        return $ret;
    }

    /**
     * 根据姓名获取老师列表
     *
     * @param  string  $teacherName  老师姓名
     * @param  mix     $arrFields    指定属性
     * @param  int     $offset       偏移
     * @param  int     $limit        限制
     * @return mix
     */
    public function getTeacherListByName($teacherName, $arrFields = array(), $offset = 0, $limit = 20) {
        if(strlen($teacherName) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[teacherName:$teacherName]");
            return false;
        }

        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'deleted' => self::STATUS_OK,
            'teacherName' => strval($teacherName),    
        );

        $arrAppends = array(
            "order by create_time",
            "limit $offset, $limit",
        );

        $objDaoTeacher = new Hkzb_Dao_Fudao_Teacher();
        $ret = $objDaoTeacher->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }



    /**
     * 根据姓名获取老师数量（模糊）
     *
     * @param  string  $teacherName  老师姓名
     * @param  mix     $arrFields    指定属性
     * @return mix
     */
    public function getTeacherCntByKeyName($teacherName) {
        if(strlen($teacherName) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[teacherName:$teacherName]");
            return false;
        }

        $arrConds = array(
            'deleted' => self::STATUS_OK,
        );
        $binName = bin2hex("%$teacherName%");
        $arrConds[] = "teacher_name like unhex('$binName')";

        $objDaoTeacher = new Hkzb_Dao_Fudao_Teacher();
        $ret = $objDaoTeacher->getCntByConds($arrConds);

        return $ret;
    }



    /**
     * 根据姓名获取老师列表(模糊)
     *
     * @param  string  $teacherName  老师姓名
     * @param  mix     $arrFields    指定属性
     * @param  int     $offset       偏移
     * @param  int     $limit        限制
     * @return mix
     */
    public function getTeacherListByKeyName($teacherName, $arrFields = array(), $offset = 0, $limit = 20) {
        if(strlen($teacherName) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[teacherName:$teacherName]");
            return false;
        }

        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'deleted' => self::STATUS_OK,
        );
        
        $binName = bin2hex("%$teacherName%");
        $arrConds[] = "teacher_name like unhex('$binName')";


        $arrAppends = array(
            "order by create_time",
            "limit $offset, $limit",
        );

        $objDaoTeacher = new Hkzb_Dao_Fudao_Teacher();
        $ret = $objDaoTeacher->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }




    /**
     * 批量获取学生信息
     * @param       $arrTeacherUid
     * @param array $arrFields
     * @param int   $offset
     * @param int   $limit
     * @return array|bool|false
     */
    public function getTeacherListByIds($arrTeacherUid, $arrFields = array(), $offset = 0, $limit = 20)
    {
        if (empty($arrTeacherUid)) {
            Bd_Log::warning("Error:[param error], Detail:[arrTeacherUid:]");
            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $strTeacherUids = implode(',', $arrTeacherUid);
        $arrConds       = array("teacher_uid in ($strTeacherUids)");
        $arrAppends     = array("limit $offset, $limit");

        $objDaoTeacher = new Hkzb_Dao_Fudao_Teacher();
        $ret = $objDaoTeacher->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    /**
     * 校验辅导老师是否合法
     *
     * @param  int  $teacherUid  老师uid
     * @return bool
     */
    public function checkValidAssistantTeacher($teacherUid) {
        if(intval($teacherUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[teacherUid:$teacherUid]");

            return false;
        }

        $arrConds = array(
            'teacherUid' => intval($teacherUid),
            'duty'       => self::DUTY_ASSISTANT,
            'deleted'    => self::STATUS_OK,
        );

        $objDaoTeacher = new Hkzb_Dao_Fudao_Teacher();
        $ret = $objDaoTeacher->getRecordByConds($arrConds, array('teacherUid','phone'));
        if (empty($ret)) {
            return false;
        }
        //虚拟辅导老师需内网验证
        // 由于要支持班主任账号，并且班主任号段不规律且可能处于外网，所以暂时去掉下面的验证
        // $thirdPhone = substr($ret['phone'],0,3);
        // $virtualPhone = array('111');
        // if(in_array($thirdPhone,$virtualPhone) && !(Hk_Util_Ip::isInnerIp())){
        //     return false;
        // }

        return true;
    }
    
    /**
     * 增加老师粉丝数
     * @param integer $teacherUid
     */
    public function incrTeacherFansCnt($teacherUid) {
        $teacherUid = intval($teacherUid);
        if ($teacherUid <= 0) {
            return 0;
        }
        $arrConds = array(
            'teacherUid' => $teacherUid,
        );
        $arrFields = array(
            'fans_cnt = fans_cnt + 1',
        );
        $objDaoTeacher = new Hkzb_Dao_Fudao_Teacher();
        $ret = $objDaoTeacher->updateByConds($arrConds, $arrFields);
        
        //清理缓存
        $cacheKey = self::TEACHER_CACHE_KEY . intval($teacherUid);
        $this->cache->del($cacheKey);

        //通知阿喀琉斯刷新teacherinfo
        // $this->sendNmq($teacherUid);
        return $ret;
    }
    
    /**
     * 减少老师粉丝数
     * @param integer $teacherUid
     */
    public function decrTeacherFansCnt($teacherUid) {
        $teacherUid = intval($teacherUid);
        if ($teacherUid <= 0) {
            return 0;
        }
        $arrConds = array(
            'teacherUid' => $teacherUid,
            'fansCnt' => array(0, '>'),
        );
        $arrFields = array(
            'fans_cnt = fans_cnt - 1',
        );
        $objDaoTeacher = new Hkzb_Dao_Fudao_Teacher();
        $ret = $objDaoTeacher->updateByConds($arrConds, $arrFields);
        
        //清理缓存
        $cacheKey = self::TEACHER_CACHE_KEY . intval($teacherUid);
        $this->cache->del($cacheKey);

        //通知阿喀琉斯刷新teacherinfo
        $this->sendNmq($teacherUid);
        return $ret;
    }

    //字段过滤
    private function filter($teacherInfo, $arrFields) {
        if(empty($arrFields)) {
            return $teacherInfo;
        }

        $arrOutput = array();
        foreach($arrFields as $field) {
            if(isset($teacherInfo[$field])) {
                $arrOutput[$field] = $teacherInfo[$field];
            }
        }

        return $arrOutput;
    }

    /**
     * 通过教师uid获取教师海报及视频
     * @param $teacherUid
     * @return array
     */
    public function getTeacherStyleByTeacherUid($teacherUid){
        if(intval($teacherUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[teacherUid:$teacherUid]");
            return false;
        }

        // 通过老师uid获取教师信息
        $teacherInfo = $this->getTeacherInfo($teacherUid);

        $arrOutput = array();

        // 视频海报
        if ($teacherInfo['extData']['styleVideoCover']){
            $arrOutput['posterUrl'] = Hkzb_Util_FuDao::getFileUrl($teacherInfo['extData']['styleVideoCover']);
            $arrOutput['posterKey'] = $teacherInfo['extData']['styleVideoCover'];
        }else{
            $arrOutput['posterUrl'] = '';
            $arrOutput['posterKey'] = '';
        }

        // 视频地址
        if ($teacherInfo['extData']['styleVideo']){
            $arrOutput['videoUrl'] = Hkzb_Util_FuDao::getFileUrl(
                $teacherInfo['extData']['styleVideo']['res'],
                10800,
                'static'
            );
            if(!strpos($arrOutput['videoUrl'],'course_video')){
                $arrUrl = explode('/',$arrOutput['videoUrl']);
                $newHost = $arrUrl[2].'/course_video';
                $arrOutput['videoUrl'] = str_replace($arrUrl[2],$newHost,$arrOutput['videoUrl']);
            }
            $arrOutput['videoKey'] = $teacherInfo['extData']['styleVideo']['res'];
        }else{
            $arrOutput['videoUrl'] = '';
            $arrOutput['videoKey'] = '';
        }

        return $arrOutput;
    }

    public function getVirtualTeacherByRealTeacherUid($teacherUid) {
        $arrConds = array(
            "ext_data like \"%realTeacherUid\\\":{$teacherUid}%\"",
            'deleted' => 0,
        );

        $arrFields = explode(',', self::ALL_FIELDS);

        $objDaoTeacher = new Hkzb_Dao_Fudao_Teacher();
        $teacher = $objDaoTeacher->getRecordByConds($arrConds, $arrFields);
        if ($teacher === false) {
            Bd_Log::warning('getVirtualTeacherByRealTeacherUidFailed', 'dbError');
            return false;
        }
        return $teacher;
    }

    //通知阿喀琉斯更新teacherinfo的缓存
    public function sendNmq($teacherUid){
        $CommandNo = Zb_Const_Command::COMMAND_TEACHER_970001;
        $arrNmqParam = array(
            'teacherUid'    => $teacherUid,
        );
        $ret = Liveservice_Util_Nmq::sendCommandByRocketMQ($CommandNo, $arrNmqParam);
        if (false === $ret) {
            Bd_Log::warning('talkToQcm fail,Data:['.json_encode($arrNmqParam).']');
        }
        return true;
    }
}
