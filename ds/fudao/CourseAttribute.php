<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file Course.php
 * <AUTHOR>
 * @date 2015/11/17 14:13:18
 * @brief 批量报名记录
 *  
 **/

class Hkzb_Ds_Fudao_CourseAttribute {

    const ALL_FIELDS = 'courseId,learnSeason,seasonNum,teachMaterial,extData';
    const GIVE_COURSE_AWAY="Give_Course_Away_";
    private $objDaoCourseAttribute;
    private $objDsAdvancedCourse;

    /**
     * 构造函数
     *
     */
    public function __construct() {
        $this->objDaoCourseAttribute = new Hkzb_Dao_Fudao_CourseAttribute();
        $this->objDsAdvancedCourse = new Hkzb_Ds_Fudao_Advanced_Course();
    }

    /**
     * 新增课程
     *
     * @param  array  $arrParams 课程属性
     * @return bool true/false
     */
    public function addCourseAttribute($arrParams) {
        if(intval($arrParams['courseId']) <= 0 || strlen($arrParams['learnSeason'])<=0) {
            Bd_Log::warning("Error:[param error], Detail:[param: ". json_encode($arrParams). "]");
            return false;
        }

        $arrFields = array(
            'courseId'          => intval($arrParams['courseId']),
            'learnSeason'       => isset($arrParams['learnSeason']) ? strval($arrParams['learnSeason']) : '',
            'seasonNum'         => isset($arrParams['seasonNum']) ? intval($arrParams['seasonNum']) : '1',
            'teachMaterial'     => isset($arrParams['teachMaterial']) ? intval($arrParams['teachMaterial']) : '0',
            'extData'           => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '',
        );

        $ret = $this->objDaoCourseAttribute->insertRecords($arrFields);

        return $ret;
    }
    /**
     * 更新课程
     *
     * @param  int  $courseId  课程id
     * @param  array  $arrParams 课程属性
     * @return bool true/false
     */
    public function updateCourseAttribute($courseId, $arrParams) {
        if(intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId]");
            return false;
        }

        $arrConds = array(
            'courseId' => intval($courseId),
        );

        $arrFields = array();
        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach($arrParams as $key => $value) {
            if(!in_array($key, $arrAllFields)) {
                continue;
            }

            $arrFields[$key] = $value;
        }

        //ext由ps补全，json处理在ds
        if(isset($arrFields['extData'])) {
            $arrFields['extData'] = json_encode($arrFields['extData']);
        }

        //补全更新时间
        $ret = $this->objDaoCourseAttribute->updateByConds($arrConds, $arrFields);

        return $ret;
    }


    /**
     * 获取批量课程详情
     *
     * @param  int  $courseId  课程id
     * @param  mix  $arrFields 指定属性
     * @return mix
     */
    public function getCourseAttributeInfo($courseId, $arrFields = array()) {
        if(intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId]");
            return false;
        }

        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'courseId' => intval($courseId),    
        );

        $ret = $this->objDaoCourseAttribute->getRecordByConds($arrConds, $arrFields);
        return $ret;
    
    }
    
    /**
     * 获取批量课程属性
     *
     * @param  int  $arrCourseId  课程id
     * @param  mix  $arrFields 指定属性
     * @return mix
     */
    public function getCourseAttributeInfoArr($arrCourseId, $arrFields = array()) {
        if (empty($arrCourseId)) {
            return array();
        }
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        } elseif (!in_array('courseId', $arrFields)) {
            $arrFields[] = 'courseId';
        }
        $courseIds = implode(',', $arrCourseId);
        $arrConds = array(
            "course_id in ($courseIds)",
        );
        $ret = $this->objDaoCourseAttribute->getListByConds($arrConds, $arrFields);
        if (false === $ret) {
            return false;
        }
        $arrCourseAttr = array();
        foreach ($ret as $course) {
            $arrCourseAttr[$course['courseId']] = $course;
        }
        return $arrCourseAttr;
    }


    /**
     * @param $arrConds
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @return array|false
     */
    public function getCourseAttributeListByConds($arrConds, $arrFields = array(), $offset = 0, $limit = 20) {
        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrAppends = array(
            "order by course_id desc",
            "limit $offset, $limit",
        );

        $ret = $this->objDaoCourseAttribute->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    /**
     * 获取数量
     * @param $arrConds
     * @return false|int
     */
    public function getCourseAttributeCntByConds($arrConds) {
        $ret = $this->objDaoCourseAttribute->getCntByConds($arrConds);
        return $ret;
    }
     /**
     * 获取赠品
     * @param type $courseIds 
     * @param type $orderId
     * @return type
     */
    public function getGiveAway($courseId, $orderId,$parentOrderId) {
      $giveList=  self::getCourseGiveAway($courseId);
      return $giveList;
    }
    /***
     * 获取赠品数据
     */
    public function getCourseGiveAway($courseId)
    {
        $redis = new Hk_Service_Redis(NULL);
        $gives = Hkzb_Ds_Fudao_Advanced_Course::$attrServices;
        $services = Hkzb_Ds_Fudao_Advanced_Course::$services;
        $sign=  Hkzb_Ds_Fudao_Advanced_Course::$signServices;
      
            $rkey = self::GIVE_COURSE_AWAY . $courseId;
            $rList = $redis->hgetall($rkey);
            if ($rList) {
                foreach ($rList as $rk=>$rv){
                    $rArr[$rv['field']]=$rv['value'];
                }
                $ret[$courseId] = $rArr;
            } else {
                // 根据打包ID获取子课id;
               // $objDsCourseAttribute = new Hkzb_Ds_Fudao_CourseAttribute();
                //$objDsAdvancedCourse = new Hkzb_Ds_Fudao_Advanced_Course();
                $list = $this->objDsAdvancedCourse->getCourseInfoArr(array($courseId));
                $keys = $packCourseList = array();
                foreach ($list as $k => $packList) {
                    $coreCourseIds = isset($packList['extData']['coreCourseIds']) ? $packList['extData']['coreCourseIds'] : array();
                    $otherCourseIds = isset($packList['extData']['otherCourseIds']) ? $packList['extData']['otherCourseIds'] : array();
                    $packArr = array_merge($coreCourseIds, $otherCourseIds);
                    // $packCourseList[$packList['courseId']]=$packArr;
                    foreach ($packArr as $v) {
                        $packCourseList[$v['courseId']] = $packList['courseId'];
                    }
                }
                //获取赠品
                $keys = array_keys($packCourseList);
                $list = $this->getCourseAttributeInfoArr($keys);
                $giveArr = array_flip($gives);
                $new =$arr= $ret = array();
                foreach ($list as $k => $v) {
                    $teachMaterial = $v['teachMaterial'];
                     $mkey = self::GIVE_COURSE_AWAY . $packCourseList[$k];
                    foreach ($gives as $gk => $gv) {
                        if ($teachMaterial & $gv) {
                            $arr[$gv] = isset($services[$giveArr[$gv]]) ? $services[$giveArr[$gv]] : "未知";
                           $redis->hset($mkey, $sign[$giveArr[$gv]], $arr[$gv]); 
                        }
                    }
                    $redis->expire($mkey, 60 + $courseId % 60);
                    $ret[$packCourseList[$k]] = $arr;
                }
        }
        return $ret; 
    }
}
