<?php
/***************************************************************************
 * 
 * Copyright (c) 2016 Zuoyebang.com, Inc. All Rights Reserved
 * 
 **************************************************************************/

/**
 * @file StudentNetLog.php
 * <AUTHOR>
 * @date 2016/11/1
 * @brief 主讲端网络日志记录——前端主要写表，统计离线数据使用
 *  
 **/

class Hkzb_Ds_Fudao_StudentNetLog {
    private $_objDaoStudentNetLog;

    public function __construct() {
        $this->_objDaoStudentNetLog = new Hkzb_Dao_Fudao_StudentNetLog();
    }

    public function getLog($arrParams){
        if(!is_array($arrParams) || empty($arrParams['lessonId'])){
            Bd_Log::warning("Error[param error] Detail[".@json_encode($arrParams)."]");
            return false;
        }
        $arrConds=array("lessonId"=>$arrParams["lessonId"]);
        $arrFields=array("uid","ext_data","ctime");
        $arrAppends=array("ORDER BY ctime ASC");
        $data=$this->_objDaoStudentNetLog->getListByConds($arrConds,$arrFields,null,$arrAppends,null);
        $list=array();
        $startTime=0;
        $onlineUser=array();
        foreach($data as $item){
            $time=intval($item["ctime"]);
            $uid=$item["uid"];
            $extData=json_decode($item["ext_data"],true);
            
            if($startTime==0){
                $startTime=$time;
            }
            $buffers=0;
            if(isset($extData) && isset($extData["bufferTime"])){
                $bufferTime=intval($extData["bufferTime"]);
                if($bufferTime/1000>=1){
                    $buffers=1;
                } 
            }

            $duration=$time-$startTime;
            
            if($duration<=60){
                if(!$onlineUser[$uid]){
                    $onlineUser[$uid]=$buffers; 
                }else{
                    $onlineUser[$uid]=$onlineUser[$uid]+$buffers;
                }
            }
            if($duration>60){
                
                $bufferCount=0;
                $userCount=0;
                foreach($onlineUser as $key =>$item){
                    $bufferCount=$bufferCount+$item;
                   $userCount=$userCount+1; 
                }
                $avg=0;
                if($userCount>0){
                    $avg=round($bufferCount/$userCount,2);
                }
                    
                $list[]=array("ctime"=>$startTime,"userCount"=>$userCount,"bufferCount"=>$bufferCount,"count"=>$avg);
                $startTime=$time;
                $onlineUser=array();
                $onlineUser[$uid]=$buffers;
            }


        }
        return $list;
    }
}
