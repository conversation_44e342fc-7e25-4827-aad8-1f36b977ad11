<?php
/**
 * Created by PhpStorm.
 * User: <EMAIL>
 * Time: 2017/12/27 16:30
 */

class Hkzb_Ds_Fudao_LiveRoom
{
    const ALL_FIELDS = 'roomId,roomName,deviceName,deleted,isLock,createTime,extData';

    private $objDaoLiveRoom;

    public function __construct() {
        $this->objDaoLiveRoom = new Hkzb_Dao_Fudao_LiveRoom();
    }

    /**
     * 新增
     * @param $arrParams
     * @return bool
     */
    public function addLiveRoom($arrParams) {

        if (empty($arrParams['roomName'])) {
            Bd_Log::warning("Error:[param error], Detail:[roomName:'']");
            return false;
        }
        $arrFields = array(
            'roomName'      => isset($arrParams['roomName']) ? $arrParams['roomName'] : '',
            'deviceName'    => isset($arrParams['deviceName']) ? $arrParams['deviceName'] : '',
            'deleted'       => 0,
            'isLock'        => 0,
            'createTime'    => time(),
            'extData'       => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : array(),
        );
        $ret = $this->objDaoLiveRoom->insertRecords($arrFields);
        if ($ret) {
            $ret = $this->objDaoLiveRoom->getInsertId();
        }
        return $ret;
    }

    /**
     * 单个获取
     * @param $roomId
     * @param array $arrFields
     * @return bool
     */
    public function getLiveRoomInfo($roomId, $arrFields = array()) {
        if (intval($roomId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[roomId:$roomId]");
            return false;
        }
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrCond = array(
            'roomId'   => intval($roomId),
        );
        $ret = $this->objDaoLiveRoom->getRecordByConds($arrCond, $arrFields);
        return $ret;
    }

    /**
     * 数组获取
     * @param $arrRoomId
     * @param array $arrFields
     * @return bool
     */
    public function getArrLiveRoomInfo($arrRoomId, $arrFields = array()) {
        if (empty($arrRoomId)) {
            return false;
        }
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $str = 'room_id in (' . implode(',', $arrRoomId) . ')';
        $arrCond[] = $str;
        $ret = $this->objDaoLiveRoom->getListByConds($arrCond, $arrFields);
        $arr = array();
        foreach ($ret as $k=>$v) {
            $arr[$v['roomId']] = $v;
        }
        return $arr;
    }

    /**
     * 更新
     * @param $roomId
     * @param $arrParams
     * @return bool
     */
    public function updateLiveRoom($roomId, $arrParams) {
        if(intval($roomId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[roomId:$roomId]");
            return false;
        }
        $arrCond = array(
            'roomId' => intval($roomId),
        );
        $arrFields = array();
        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach ($arrParams as $key => $value) {
            if (!in_array($key, $arrAllFields)) {
                continue;
            }
            $arrFields[$key] = $value;
        }
        //ext由ps补全，json处理在ds
        if (isset($arrFields['extData'])) {
            $arrFields['extData'] = json_encode($arrFields['extData']);
        }
        $ret = $this->objDaoLiveRoom->updateByConds($arrCond, $arrFields);
        return $ret;
    }

    /**
     * 锁定直播间
     * @param $roomId
     * @return bool
     */
    public function lockLiveRoom($roomId) {
        if(intval($roomId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[roomId:$roomId]");
            return false;
        }
        $arrCond = array(
            'roomId' => intval($roomId),
        );
        $arrFields = array(
            'isLock' => 1,
        );
        $ret = $this->objDaoLiveRoom->updateByConds($arrCond, $arrFields);
        return $ret;
    }

    /**
     * 根据多条件获取数目
     * @param $arrCond
     * @return mixed
     */
    public function getLiveRoomCntByCond($arrCond) {

        $ret = $this->objDaoLiveRoom->getCntByConds($arrCond);
        return $ret;
    }

    /**
     * 根据多条件获取
     * @param $arrCond
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @return mixed
     */
    public function getLiveRoomListByCond($arrCond, $arrFields = array(), $offset = 0, $limit = 20) {

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrAppends = array(
            "order by create_time asc",
            "limit $offset, $limit",
        );

        $ret = $this->objDaoLiveRoom->getListByConds($arrCond, $arrFields, NULL, $arrAppends);
        return $ret;
    }

}