<?php
/**
 * Created by PhpStorm.
 * User: renka<PERSON><EMAIL>
 * Date: 2018/4/19
 * Time: 5:52
 */
class Hkzb_Ds_Fudao_LatestTeacherSays
{
    //动态类型
    const MESSAGE_PICTURE   = 0;
    const MESSAGE_COURSE    = 1;
    const MESSAGE_CONTENT   = 2;
    const MESSAGE_VIDEO     = 3;
    const MESSAGE_LINK      = 4;
    const MESSAGE_Card      = 5;
    static $MESSAGE_ARRAY = array(
        self::MESSAGE_PICTURE   => '图文动态',
        self::MESSAGE_COURSE    => '课程动态',
        self::MESSAGE_CONTENT   => '纯文字',
        self::MESSAGE_VIDEO     => '视频动态',
        self::MESSAGE_LINK      => '链接动态',
        self::MESSAGE_Card      => '直播卡片动态'
    );

    //删除状态
    const DELETED_NO  = 0;
    const DELETED_YES = 1;
    static $DELETED_ARRAY = array(
        self::DELETED_NO  => '未删除',
        self::DELETED_YES => '已删除',
    );


    const ALL_FIELDS = 'id,pubUid,content,contentType,deleted,createTime,updateTime';

    private $_objDaoLatestTeacherSays;

    /**
     * 构造函数
     *
     */
    public function __construct()
    {
        $this->_objDaoLatestTeacherSays      = new Hkzb_Dao_Fudao_LatestTeacherSays();
    }

    /**
     * 新增一个老师的最新动态信息
     *
     * @param  mix $arrParams 动态属性
     * @return bool true/false
     */
    public function addLatestTeacherSays($arrParams)
    {
        if (empty($arrParams) || intval($arrParams['pubUid']) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[param: " . json_encode($arrParams) . "]");
            return false;
        }

        $arrFields = array(
            'pubUid'      => isset($arrParams['pubUid']) ? intval($arrParams['pubUid']) : 0,
            'content'     => isset($arrParams['content']) ? json_encode($arrParams['content']) : '[]',
            'contentType' => isset($arrParams['contentType']) ? intval($arrParams['contentType']) : 0,
            'deleted'     => isset($arrParams['deleted']) ? intval($arrParams['deleted']) : 0,
            'createTime'  => time(),
            'updateTime'  => time(),
        );

        $ret = $this->_objDaoLatestTeacherSays->insertRecords($arrFields);
        if (!$ret) {
            Bd_Log::warning("Error[dbError] Detail[db insert failed]");
            return false;
        }

        return $ret;
    }


    /**
     * 更新老师最新发的说说内容
     * @param  int $id id   动态id
     * @param  array $arrParams  动态属性
     * @return bool true/false
     */
    public function updateLatestTeacherSays($pubUid, $arrParams)
    {
        if (empty($arrParams) || intval($pubUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[pubUid:$pubUid arrParams:$arrParams]");
            return false;
        }

        $arrConds = array(
            'pubUid' => intval($pubUid),
        );

        $arrFields = array();
        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach ($arrParams as $key => $value) {
            if (!in_array($key, $arrAllFields)) {
                continue;
            }

            $arrFields[$key] = $value;
        }

        //content由ps补全，json处理在ds
        if (isset($arrFields['content'])) {
            $arrFields['content'] = json_encode($arrFields['content']);
        }

        $arrFields['deleted']  = self::DELETED_NO;

        //补全更新时间
        $arrFields['updateTime'] = time();

        $ret = $this->_objDaoLatestTeacherSays->updateByConds($arrConds, $arrFields);
        return $ret;
    }


    /**
     * 获取指定动态
     *
     * @param  int $id id    动态id
     * @param  array $arrFields 动态属性
     * @return array|false
     */
    public function getLatestTeacherSaysInfo($id, $arrFields = array())
    {
        if (intval($id) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[id:$id]");
            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'id' => intval($id),
        );

        $ret = $this->_objDaoLatestTeacherSays->getRecordByConds($arrConds, $arrFields);

        return $ret;
    }

    /**
     * 获取指定条件的动态
     *
     * @param array $arrConds     条件参数
     * @param array $arrFields   访问的属性
     * @param  int  $limit      每次获取的个数
     * @return array|false
     */
    public function getTeacherSaysListByConds($arrConds, $arrFields = array())
    {
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrAppends = array(
            "order by id desc",
//            "limit $limit",
        );

        $ret = $this->_objDaoLatestTeacherSays->getListByConds($arrConds, $arrFields, NULL, $arrAppends);
        return $ret;
    }


    /**
     * 删除老师动态
     *
     * @param  int $id   动态id
     * @return bool true/false
     */
    public function deleteTeacherSays($id)
    {
        if (intval($id) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[id:$id]");
            return false;
        }

        $arrFields = array(
            'deleted'    => self::DELETED_YES,
            'updateTime' => time(),
        );

        $ret = self::updateLatestTeacherSays($id, $arrFields);

        return $ret;
    }

}
