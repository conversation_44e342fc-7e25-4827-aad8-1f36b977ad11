<?php

/**
 * @file        AssistantSmsRecord.php
 * <AUTHOR>
 * @create_date 2017-09-30
 * @brief       短信记录
 *
 **/
class Hkzb_Ds_Fudao_AssistantSmsRecord
{
    // 删除状态
    const DELETED_STATUS_NO  = 0;
    const DELETED_STATUS_YES = 1;
    static $deletedStatusMap = array(
        self::DELETED_STATUS_NO  => '未删',
        self::DELETED_STATUS_YES => '已删',
    );

    // 短信记录来源
    const SMS_SOURCE_TYPE_HOME_VISIT       = 1;
    const SMS_SOURCE_TYPE_CLASS_ATTENDANCE = 2;
    const SMS_SOURCE_TYPE_HOMEWORK         = 3;
    const SMS_SOURCE_TYPE_AGAIN_VISIT      = 4;
    const SMS_SOURCE_TYPE_CONTINUE_COURSE  = 5;
    const SMS_SOURCE_TYPE_CLASSING         = 6;
    const SMS_SOURCE_TYPE_OTHER            = 99;
    static $SmsSourceTypeMap = array(
        self::SMS_SOURCE_TYPE_HOME_VISIT       => '家访',
        self::SMS_SOURCE_TYPE_CLASS_ATTENDANCE => '到课',
        self::SMS_SOURCE_TYPE_HOMEWORK         => '作业',
        self::SMS_SOURCE_TYPE_AGAIN_VISIT      => '回访',
        self::SMS_SOURCE_TYPE_CONTINUE_COURSE  => '续报',
        self::SMS_SOURCE_TYPE_CLASSING         => '跟课',
        self::SMS_SOURCE_TYPE_OTHER            => '其他',
    );

    // 短信类型
    const SMS_TYPE_OUT = 1;
    const SMS_TYPE_IN  = 2;
    static $SmsTypeMap = array(
        self::SMS_TYPE_OUT => '发送',
        self::SMS_TYPE_IN  => '接收',
    );

    //短信发送结果类型
    const SMS_RESULT_SUCCESS = 1;
    const SMS_RESULT_FAIL    = 2;
    static $SmsResultTypeMap = array(
        self::SMS_RESULT_SUCCESS => '发送成功',
        self::SMS_RESULT_FAIL    => '发送失败',
    );

    // 触发类型triggerType
    const SMS_TYPE_TRIGGER_LEGAL = 1;
    const SMS_TYPE_TRIGGER_SELF  = 2;
    static $SmsTriggerTypeMap = array(
        self::SMS_TYPE_TRIGGER_LEGAL => '合法触发',
        self::SMS_TYPE_TRIGGER_SELF  => '自己触发',
    );

    private $_objDaoAssistantSmsRecord;

    /**
     * 构造函数
     *
     */
    public function __construct() {
        $this->_objDaoAssistantSmsRecord = new Hkzb_Dao_Fudao_AssistantSmsRecord();
    }

    /**
     * 新增记录
     *
     * @param  array $arrParams 通话属性
     * @return bool true/false
     */
    public function addAssistantSmsRecord($arrParams) {
        if ((trim($arrParams['fromPhone']) == '') || (trim($arrParams['toPhone']) == '')) {
            Bd_Log::warning("Error:[param error], Detail:[param: " . json_encode($arrParams) . "]");
            return false;
        }

        $arrFields = array(
            'sourceType' => isset($arrParams['sourceType']) ? intval($arrParams['sourceType']) : self::SMS_SOURCE_TYPE_OTHER,
            'fromUid'    => isset($arrParams['fromUid']) ? intval($arrParams['fromUid']) : 0,
            'fromPhone'  => strval($arrParams['fromPhone']),
            'toUid'      => isset($arrParams['toUid']) ? intval($arrParams['toUid']) : 0,
            'toPhone'    => strval($arrParams['toPhone']),
            'sendTime'   => isset($arrParams['sendTime']) ? intval($arrParams['sendTime']) : 0,
            'sendType'   => isset($arrParams['sendType']) ? intval($arrParams['sendType']) : self::SMS_TYPE_OUT,
            'sendResult' => isset($arrParams['sendResult']) ? intval($arrParams['sendResult']) : self::SMS_RESULT_FAIL,
            'messageData'=> isset($arrParams['messageData']) ? strval($arrParams['messageData']) : '',
            'createTime' => time(),
            'updateTime' => time(),
            'deleted'    => isset($arrParams['deleted']) ? intval($arrParams['deleted']) : self::DELETED_STATUS_NO,
            'triggerType'=> isset($arrParams['triggerType']) ? intval($arrParams['triggerType']) : self::SMS_TYPE_TRIGGER_SELF,
            'courseId'   => isset($arrParams['courseId']) ? intval($arrParams['courseId']) : 0,
            'learnSeason'=> isset($arrParams['learnSeason']) ? strval($arrParams['learnSeason']) : '',
            'lessonId'   => isset($arrParams['lessonId']) ? intval($arrParams['lessonId']) : 0,
            'classId'    => isset($arrParams['classId']) ? intval($arrParams['classId']) : 0,
            'extData'    => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '',
        );

        $ret = $this->_objDaoAssistantSmsRecord->insertRecords($arrFields);
        if ($ret === false) {
            return false;
        }

        return $this->_objDaoAssistantSmsRecord->getInsertId();
    }

    /**
     * 更新通话记录
     *
     * @param  array $arrConds  条件
     * @param  array $arrParams 电话属性
     * @return bool true/false
     */
    public function updateAssistantSmsRecord($arrConds, $arrParams) {
        if (empty($arrConds) || empty($arrParams)) {
            Bd_Log::warning("Error:[param error], Detail:[id:$arrConds arrParams:$arrParams]");

            return false;
        }

        do {
            $tmpArrFields = Hkzb_Dao_Fudao_AssistantSmsRecord::$allFields;
            $data         = $this->_objDaoAssistantSmsRecord->getRecordByConds($arrConds, $tmpArrFields);
            if (!$data) {
                Bd_Log::warning("Error:[param error], Detail:[" . json_encode($arrConds) . "]");

                return false;
            }

            //待更新的所有字段
            $arrFields = array();

            //3. 拿回id 和 extdata
            $extData = $data['extData'];
            //extData补全
            if (isset($arrParams['extData']) && is_array($arrParams['extData'])) {
                foreach ($arrParams['extData'] as $key => $value) {
                    $extData[$key] = $value;
                }
                $arrFields['extData'] = json_encode($extData);
                unset($arrParams['extData']);
            }

            //4. 其他字段补全
            $arrAllFields = Hkzb_Dao_Fudao_AssistantSmsRecord::$allFields;
            foreach ($arrParams as $key => $value) {
                //不允许把值置空
                if (!$value && !in_array($key, $arrAllFields)) {
                    continue;
                }
                $arrFields[$key] = $value;
            }

            //5. 更新数据库
            $ret = $this->_objDaoAssistantSmsRecord->updateByConds($arrConds, $arrFields);

            if ($ret === false) {
                //提交失败
                Bd_Log::warning("Error:[update SmsRecord error], Detail:[" . json_encode($arrConds) . "]");

                return false;
            }

        } while (0);

        return true;
    }

    /**
     * 获取通话详情
     *
     * @param  string $smsId sms_id
     * @param  array  $arrFields 指定属性
     * @return array
     */
    public function getAssistantSmsRecordInfo($smsId, $arrFields = array()) {
        if (intval($smsId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[sms_id:$smsId]");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = Hkzb_Dao_Fudao_AssistantSmsRecord::$allFields;
        }

        $arrConds = array(
            'smsId' => strval($smsId),
        );

        $ret = $this->_objDaoAssistantSmsRecord->getRecordByConds($arrConds, $arrFields);

        return $ret;
    }

    /**
     * 列表
     * @param       $arrConds
     * @param array $arrFields
     * @param int   $offset
     * @param int   $limit
     * @return array|false
     */
    public function getAssistantSmsRecordListByConds($arrConds, $arrFields = array(), $offset = 0, $limit = 20) {

        if (empty($arrFields)) {
            $arrFields = Hkzb_Dao_Fudao_AssistantSmsRecord::$allFields;
        }

        $arrAppends = array(
            "order by create_time desc ",
            "limit $offset, $limit",
        );

        $ret = $this->_objDaoAssistantSmsRecord->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    /**
     * 获取总记录数
     *
     * @param       $arrConds
     * @return array|false
     */
    public function getAssistantSmsRecordCnt($arrConds) {

        $ret = $this->_objDaoAssistantSmsRecord->getCntByConds($arrConds);

        return $ret;
    }
}
