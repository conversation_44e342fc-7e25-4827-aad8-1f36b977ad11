<?php
/***************************************************************************
 *
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/



/**
 * @file SubjectCard.php
 * <AUTHOR>
 * @date 2015/11/17 14:13:18
 * @brief 学科运营 标签 卡片
 *
 **/

class Hkzb_Ds_Fudao_SubjectCard {


    //================异步脚本缓存数据需要的key==========
    //年级科目卡片需要的数据的缓存key
    const GRADE_SUBJECT_CARD_DATA_KEY = 'ZHIBOKE_ASYNC_CALCULATE_GRADE_SUBJECT_CARD_DATA_V1_';
    //首页推广卡片和学科自定义卡片需要的缓存key
    const GRADE_INDEX_PROMOTE_SUBJECT_CARD_DATA_KEY = 'ZHIBOKE_ASYNC_CALCULATE_GRADE_INDEX_PROMOTE_SUBJECT_CARD_DATA_V1_';

    //学科卡片，子标签缓存key
    const GRADE_SUBJECT_CARD_SUB_TAG_KEY = 'GRADE_SUBJECT_CARD_SUB_TAG_KEY_';
    //=============异步脚本缓存数据需要的key==========

    //定义一个表示配置课程无效的value

    const INVALID_SKUID = 'invalidSkuId';

    //tagtype
    const TAG_TYPE_TAG = 0;
    const TAG_TYPE_CARD = 1;
    static $TAG_TYPE_ARRAY = array(
        self::TAG_TYPE_TAG  => '标签',
        self::TAG_TYPE_CARD => '卡片',
    );
    //coursetype
    const COURSE_TYPE_PU    = 0;
    const COURSE_TYPE_BAN   = 1;
    const COURSE_TYPE_VIDEO = 2;
    static $COURSE_TYPE_ARRAY = array(
        self::COURSE_TYPE_PU    => '系列课',
        self::COURSE_TYPE_BAN   => '长期班',
        self::COURSE_TYPE_VIDEO => '录播课',
    );
    //cardType
    const CARD_TYPE_KE = 0;
    const CARD_TYPE_YE = 1;
    const CARD_TYPE_INDEX_CUSTOM = 100; //首页课程分类自定义卡片入口

    static $CARD_TYPE_ARRAY = array(
        self::CARD_TYPE_KE => '课程',
        self::CARD_TYPE_YE => '网页',
    );

    const SELF_DEFINE_CARD = 100;

    //卡片的状态
    const CARD_ONLINE_STATUS = 1;
    const CARD_OFFLINE_STATUS = 0;

    //卡片班课与非班课的定义
    const CARD_FLAG_TYPE_BAN = 1;//班课类型
    const CARD_FLAG_TYPE_ZHUAN = 2;//专题课类型
    const CARD_FLAG_TYPE_TIYAN = 3;//试听课类型
    static $CARD_STATUS_ARRAY = array(
        self::CARD_ONLINE_STATUS => '已上线',
        self::CARD_OFFLINE_STATUS => '已下线',
    );

    const ALL_FIELDS = 'tagId,parentId,title,grade,subject,params,imgPid,tagType,cardType,courseType,startTime,stopTime,createTime,updateTime,status,deleted,operatorUid,operator,courseIds,season,extData,rank,targetUser';
    const CACHE_KEY_TGS_V1_PRE = 'ZHIBOKE_SUBJECT_TAG_CARD_CACHE_KEY_V1_PRE_';
    const CACHE_KEY_CGS_V1_PRE = 'ZHIBOKE_SUBJECT_CARD_CARD_CACHE_KEY_V1_PRE_';


    const INDEX_CARD_COURSE_DATA = 'index_card_course_data_';
    const INDEX_CARD_SUBJECT_DATA = 'index_card_subject_data_';

    //首页自定义课程分类数据缓存
    const INDEX_CUSTOM_COURSE_CARD_DATA = 'index_custom_course_card_data';
    //定义一个全部科目，用于首页推广卡片的标识
    const COURSE_INDEX_CARD_SUBJECT = 1000;
    /**
     * 新增数据
     *
     * @param  array  $arrParams 老师属性
     * @return bool true/false
     */

    //不用区分年级的自定义卡片类型
    static $SPECIALTYPEARR = array(
        self::CARD_TYPE_INDEX_CUSTOM
    );
    public function addSubjectCard($arrParams) {

        $arrFields = array(
            'parentId'     => isset($arrParams['parentId']) ? intval($arrParams['parentId']) : 0,
            'title'        => isset($arrParams['title']) ? strval($arrParams['title']) : '',
            'grade'        => isset($arrParams['grade']) ? intval($arrParams['grade']) : 0,
            'subject'      => isset($arrParams['subject']) ? intval($arrParams['subject']) : 0,
            'params'       => isset($arrParams['params']) ? strval($arrParams['params']) : '',
            'imgPid'       => isset($arrParams['imgPid']) ? strval($arrParams['imgPid']) : '',
            'tagType'      => isset($arrParams['tagType']) ? intval($arrParams['tagType']) : 0,
            'cardType'     => isset($arrParams['cardType']) ? intval($arrParams['cardType']) : 0,
            'courseType'   => isset($arrParams['courseType']) ? intval($arrParams['courseType']) : 0,
            'startTime'    => isset($arrParams['startTime']) ? intval($arrParams['startTime']) : 0,
            'stopTime'     => isset($arrParams['stopTime']) ? intval($arrParams['stopTime']) : 0,
            'createTime'   => isset($arrParams['createTime']) ? intval($arrParams['createTime']) : 0,
            'updateTime'   => isset($arrParams['updateTime']) ? intval($arrParams['updateTime']) : 0,
            'status'   => isset($arrParams['status']) ? intval($arrParams['status']) : 0,
            'deleted'      => 0,
            'operatorUid'  => isset($arrParams['operatorUid']) ? intval($arrParams['operatorUid']) : 0,
            'operator'     => isset($arrParams['operator']) ? strval($arrParams['operator']) : '',
            'rank'         => isset($arrParams['rank']) ? intval($arrParams['rank']) : 0,
            'courseIds'    => isset($arrParams['courseIds']) ? json_encode($arrParams['courseIds']) : '',
            'season'       => isset($arrParams['season']) ? json_encode($arrParams['season']) : '',
            'extData'      => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '',
            'targetUser'   => isset($arrParams['targetUser']) ? json_encode($arrParams['targetUser']) : 0,
        );

        $objDaoSubjectCard = new Hkzb_Dao_Fudao_SubjectCard();
        $ret = $objDaoSubjectCard->insertRecords($arrFields);
        if($ret) {
            $ret = $objDaoSubjectCard->getInsertId();
        }
        //清理缓存
        $grade = $arrParams['grade'];
        $subject = $arrParams['subject'];
        if($arrParams['tagType'] == 1) {
            $cacheKey = self::CACHE_KEY_CGS_V1_PRE . md5($grade . '_' . $subject);
        }else {
            $cacheKey = self::CACHE_KEY_TGS_V1_PRE . md5($grade.'_'.$subject);
        }
        $objMemcached = Hk_Service_Memcached::getInstance("zhiboke");
        $objMemcached->delete($cacheKey);

        $cacheCourseKey = self::INDEX_CARD_COURSE_DATA.$grade;
        $cacheSubjectKey = self::INDEX_CARD_SUBJECT_DATA.$grade;
        $objMemcachedCard = Hk_Service_Memcached::getInstance("zhiboke");
        $objMemcachedCard->delete($cacheCourseKey);
        $objMemcachedCard->delete($cacheSubjectKey);

        return $ret;
    }
    /**
     * 更新subjectcard
     *
     * @param  int  $banner  bannerid
     * @param  array  $arrParams subjectcar属性
     * @return bool true/false
     */
    public function updateSubjectCard($tagId, $arrParams) {
        if(intval($tagId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[tagId:$tagId]");
            return false;
        }

        $arrConds = array(
            'tagId' => intval($tagId),
        );

        $arrFields = array();
        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach($arrParams as $key => $value) {
            if(!in_array($key, $arrAllFields)) {
                continue;
            }

            $arrFields[$key] = $value;
        }

        //ext由ps补全，json处理在ds
        if(isset($arrFields['extData'])) {
            $arrFields['extData'] = json_encode($arrFields['extData']);
        }
        if(isset($arrFields['courseIds'])) {
            $arrFields['courseIds'] = json_encode($arrFields['courseIds']);
        }
        if(isset($arrFields['season'])) {
            $arrFields['season'] = json_encode($arrFields['season']);
        }
        //补全更新时间
        $arrFields['updateTime'] = time();
        $objDaoSubjectCard = new Hkzb_Dao_Fudao_SubjectCard();
        $ret = $objDaoSubjectCard->updateByConds($arrConds, $arrFields);
        //清理缓存
        $this->deleteCache($tagId);
        return $ret;
    }
    /**
     * 获取卡片列表
     * @param $arrConds
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @return array|false
     */
    public function getSubjectCardListByConds($arrConds, $arrFields = array(), $offset = 0, $limit = 20) {
        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrConds['deleted'] = 0;
        $arrAppends = array(
            "order by rank asc,tag_id asc",
        );
        if($limit > 0) {
            $arrAppends[] = "limit $offset, $limit";
        }
        $objDaoSubjectCard = new Hkzb_Dao_Fudao_SubjectCard();
        $ret = $objDaoSubjectCard->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }
    //删除卡片
    public function deletedCard($tagId){
        if(!$tagId){
            Bd_Log::warning("Error:[param error], Detail:[tagId:$tagId]");
            return false;
        }
        $arrConds = array(
            'tagId' => intval($tagId),
        );
        $arrFields['deleted'] = 1;
        $objDaoSubjectCard = new Hkzb_Dao_Fudao_SubjectCard();
        $ret = $objDaoSubjectCard->updateByConds($arrConds, $arrFields);
        $ret = $this->updateSubjectCard($tagId, $arrFields);
        $this->deleteCache($tagId);
        return $ret;
    }
    //获取卡片信息
    public function getSubjectCardInfo($tagId){
        if(!$tagId){
            Bd_Log::warning("Error:[param error], Detail:[tagId:$tagId]");
            return [];
        }
        $arrFields = explode(',', self::ALL_FIELDS);
        $arrConds = array(
            'tagId' => $tagId,
            'deleted' => 0,
        );
        $objDaoSubjectCard = new Hkzb_Dao_Fudao_SubjectCard();
        $ret = $objDaoSubjectCard->getRecordByConds($arrConds, $arrFields);
        return $ret;
    }
    //卡片添加课程id
    public function updateSubjectCourseIds($tagId = 0, $courseIds = []){
        if(!$tagId) {
            Bd_Log::warning("Error:[param error], Detail:[tagId:$tagId]");
            return false;
        }
        $arrConds = array(
            'tagId' => $tagId,
        );
        $arrFields['courseIds'] = json_encode($courseIds);
        $objDaoSubjectCard = new Hkzb_Dao_Fudao_SubjectCard();
        $ret = $objDaoSubjectCard->updateByConds($arrConds, $arrFields);
        $this->deleteCache($tagId);
        return $ret;
    }
    //获取年级 学科的tag
    public function getTagByGS($grade, $subject){
        if($grade <= 0 || $subject <= 0){
            Bd_Log::warning("Error:[param error], Detail:[grade:$grade subject:$subject]");
            return false;
        }
        $cacheKey = Hkzb_Ds_Fudao_SubjectCard::CACHE_KEY_TGS_V1_PRE . md5($grade.'_'.$subject);
        $objMemcached = Hk_Service_Memcached::getInstance("zhiboke");
        $cacheValue = $objMemcached->get($cacheKey);
        if (!empty($cacheValue)) {
            $arrOutput = json_decode($cacheValue, true);
            return $arrOutput;
        }

        $arrConds = array(
            'grade'     => $grade,
            'subject'   => $subject,
            'tagType'   => self::TAG_TYPE_TAG,
        );
        $arrFields = explode(',', self::ALL_FIELDS);
        $arrConds['deleted'] = 0;
        $arrAppends = array(
            "order by rank asc,tag_id desc",
        );
        $objDaoSubjectCard = new Hkzb_Dao_Fudao_SubjectCard();
        $ret = $objDaoSubjectCard->getListByConds($arrConds, $arrFields, NULL, $arrAppends);
        $cacheValue = json_encode($ret);
        $objMemcached->set($cacheKey, $cacheValue);
        return $ret;
    }

    /**
     * 获取有效的学科卡片
     * @param int $parentId
     * @param int $grade
     * @param int $subject
     * @return array|bool|false
     */
    public function getOnlineCardByPGS($grade = 0, $subject = 0){
        $nowTime = Hkzb_Util_FuDao::getCurrentTimeStamp();

        if($grade <=0 || $subject <= 0){
            Bd_Log::warning("Error:[param error], Detail: grade:$grade subject:$subject]");
            return false;
        }

        $arrConds = array(
            'grade' => $grade,
            'subject' => $subject,
            'tagType' => self::TAG_TYPE_CARD,
            'parentId' => 0,
        );

        $arrFields = explode(',', self::ALL_FIELDS);
        $arrConds['deleted'] = 0;

        //获取有效的时间的卡片
        $arrConds[] = "start_time < {$nowTime} and stop_time > {$nowTime}";

        $arrAppends = array(
            "order by rank asc,tag_id desc",
        );

        $objDaoSubjectCard = new Hkzb_Dao_Fudao_SubjectCard();
        $ret = $objDaoSubjectCard->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    /**
     * 获取某个年级，所有学科需要的数据
     * @param int $grade
     * @return array|bool|false
     */
    public function getOnlineAllSubjectCardInfoListByGrade($grade=0)
    {
        $nowTime = Hkzb_Util_FuDao::getCurrentTimeStamp();

        if($grade <=0){
            Bd_Log::warning("Error:[param error], Detail: grade:$grade]");
            return false;
        }

        $arrConds = array(
            'grade' => $grade,
            'tagType' => self::TAG_TYPE_CARD,
            'parentId' => 0,
        );

        //获取学科卡片需要数据
        $subjectMapInfo = Zb_Const_GradeSubject::$SUBJECT;
        $subjectArr = array_keys($subjectMapInfo);
        $subjectStr = join(',',$subjectArr);

        $arrConds[] = " subject in ({$subjectStr})";

        $arrFields = explode(',', self::ALL_FIELDS);
        $arrConds['deleted'] = 0;

        //获取有效的时间的卡片
        $arrConds[] = "start_time < {$nowTime} and stop_time > {$nowTime}";



        $arrAppends = array(
            "order by rank asc,tag_id desc",
        );

        $objDaoSubjectCard = new Hkzb_Dao_Fudao_SubjectCard();
        $ret = $objDaoSubjectCard->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        if($ret === false){
            Bd_Log::warning("db error");
            return false;
        }
        return $ret;
    }

    //获取某个年级下所有的子标签的数据
    public function getOnlineSubTagListByGrade($grade)
    {

        if($grade <=0){
            Bd_Log::warning("Error:[param error], Detail: grade:$grade]");
            return false;
        }

        $arrConds = array(
            'grade' => $grade,
            'tagType' => self::TAG_TYPE_CARD,
            'parentId' => array(0,'>'),
        );

        //获取学科卡片需要数据
        $subjectMapInfo = Zb_Const_GradeSubject::$SUBJECT;
        $subjectArr = array_keys($subjectMapInfo);
        $subjectStr = join(',',$subjectArr);

        $arrConds[] = " subject in ({$subjectStr})";

        $arrFields = explode(',', self::ALL_FIELDS);
        $arrConds['deleted'] = 0;

        $arrAppends = array(
            "order by rank asc,tag_id desc",
        );

        $objDaoSubjectCard = new Hkzb_Dao_Fudao_SubjectCard();
        $ret = $objDaoSubjectCard->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        if($ret === false){
            Bd_Log::warning("db error");
            return false;
        }

        return $ret;
    }

    //获取年级 学科的card
    public function getCardByPGS($parentId = 0, $grade = 0, $subject = 0){
      //  $nowTime = Hkzb_Util_FuDao::getCurrentTimeStamp();

        if($parentId <=0 && ($grade <=0 || $subject <= 0) ){
            Bd_Log::warning("Error:[param error], Detail:[parentId:$parentId grade:$grade subject:$subject]");
            return false;
        }
        if($parentId > 0){
            $arrConds = array(
                'tagType'   => self::TAG_TYPE_CARD,
                'parentId'  => $parentId,
            );
        } else {
            $arrConds = array(
                'grade' => $grade,
                'subject' => $subject,
                'tagType' => self::TAG_TYPE_CARD,
                'parentId' => 0,
            );
            if($parentId < 0){
                //查全部卡片信息
                unset($arrConds['parentId']);
            }
        }
        $arrFields = explode(',', self::ALL_FIELDS);
        $arrConds['deleted'] = 0;

        $arrAppends = array(
            "order by rank asc,tag_id desc",
        );
        if($parentId < 0) {
            $arrAppends = array(
                'order by parent_id asc,rank asc,tag_id desc'
            );
        }
        $objDaoSubjectCard = new Hkzb_Dao_Fudao_SubjectCard();
        $ret = $objDaoSubjectCard->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }
    //获取所有 年级 学科的tag
    public function getAllByGS(){

        $arrConds = array(
            'deleted' => 0,
            'parentId' => 0,
        );
        $arrConds[] = 'start_time < '.time().' and stop_time > '. time();
        $arrFields = explode(',', self::ALL_FIELDS);
        $arrConds['deleted'] = 0;
        $arrAppends = array(
            "group by grade,subject",
        );
        $objDaoSubjectCard = new Hkzb_Dao_Fudao_SubjectCard();
        $ret = $objDaoSubjectCard->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }
    public function deleteCache($tagId){
        $objDaoSubjectCard = new Hkzb_Dao_Fudao_SubjectCard();
        $arrFields = explode(',', self::ALL_FIELDS);
        $arrConds = array(
                         'tagId' => $tagId
        );
        $cardInfo = $objDaoSubjectCard->getRecordByConds($arrConds,$arrFields);
        $grade = $cardInfo['grade'];
        $subject = $cardInfo['subject'];
        if($cardInfo['tagType'] == 1) {
            $cacheKey = self::CACHE_KEY_CGS_V1_PRE . md5($grade . '_' . $subject);
        }else {
            $cacheKey = self::CACHE_KEY_TGS_V1_PRE . md5($grade.'_'.$subject);
        }
        $objMemcached = Hk_Service_Memcached::getInstance("zhiboke");
        $objMemcached->delete($cacheKey);
    }

    /**
     * 获取年级 学科获取首页卡片
     * @param int $grade
     * @param int $subject
     * @return array|bool|false
     */
    public function getIndexCardByPGS( $grade = 0, $subject = 0,$status=self::CARD_ONLINE_STATUS){
        if($grade <=0 || $subject <= 0 ){
            Bd_Log::warning("Error:[param error], Detail:[grade:$grade subject:$subject]");
            return false;
        }
            $arrConds = array(
                'grade' => $grade,
                'subject' => $subject,
                'tagType' => self::TAG_TYPE_CARD,
            );

        $arrFields = explode(',', self::ALL_FIELDS);
        $arrConds['deleted'] = 0;
        if(is_array($status)){
            $statusStr = implode(',',$status);
            $arrConds[] = "status in ({$statusStr})";
        }else {
            $arrConds['status'] = intval($status);
        }

        $arrAppends = array(
            "order by rank asc",
        );
        $objDaoSubjectCard = new Hkzb_Dao_Fudao_SubjectCard();
        $ret = $objDaoSubjectCard->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    /**
     * 获取首页推广卡片列表
     * @param array $params
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @return array|false
     */
    public function getIndexSubjectCardList($params=array(),$arrFields = array(), $offset = 0, $limit = 20)
    {
        if(empty($arrFields)){
            $arrFields =  explode(',',self::ALL_FIELDS);
        }
        $arrConds['deleted'] = 0;
        $allFiedsArr = explode(',',self::ALL_FIELDS);
        foreach ($params as $key=>$value){
            if(!in_array($key,$allFiedsArr)){
                continue;
            }
            $arrConds[$key] = $value;
        }

        $arrAppends = array(
            "order by rank asc",
        );
        if($limit > 0) {
            $arrAppends[] = "limit $offset, $limit";
        }
        $objDaoSubjectCard = new Hkzb_Dao_Fudao_SubjectCard();
        $ret = $objDaoSubjectCard->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        if($ret === false){
            Bd_Log::warning("Error::db error getIndexSubjectCardList detail".json_encode($params));
        }

        return $ret;
    }

    /**
     * 获取在线的学科卡片需要数据
     * @param $grade
     * @param int $subject
     * @return array|bool|false
     */
    public function getOnlineIndexCardList($grade,$subject = 0)
    {
        if($grade <=0 || $subject <= 0 ){
            Bd_Log::warning("Error:[param error], Detail:[grade:$grade subject:$subject]");
            return false;
        }
        $arrConds = array(
            'grade' => $grade,
            'subject' => $subject,
            'tagType' => self::TAG_TYPE_CARD,
        );

        $arrFields = explode(',', self::ALL_FIELDS);
        $arrConds['deleted'] = 0;

        $arrConds['status'] = self::CARD_ONLINE_STATUS;

        //时间范围是有效的
        $nowTime = time();
        $arrConds[] = " start_time < {$nowTime} and stop_time > {$nowTime} ";

        $arrAppends = array(
            "order by rank asc",
        );

        $objDaoSubjectCard = new Hkzb_Dao_Fudao_SubjectCard();
        $ret = $objDaoSubjectCard->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        if($ret ===  false){
            Bd_Log::warning("db error ");
            return false;
        }

        return $ret;
    }

    /**
     * 清除收页推广卡片的缓存
     * @param $tagId
     * @return bool
     */
    public function deleteIndexCardCache($tagId)
    {
        if(empty($tagId)){
            Bd_Log::warning("Error::tagId is empty".$tagId);
            return false;
        }
        $cardInfo = $this->getSubjectCardInfo($tagId);
        $grade = isset($cardInfo['grade']) ? $cardInfo['grade'] : 0;
        if(empty($grade)){
            Bd_Log::warning("Error::grade is empty".json_encode($cardInfo));
            return false;
        }

        $cacheCourseKey = self::INDEX_CARD_COURSE_DATA.$grade;
        $cacheSubjectKey = self::INDEX_CARD_SUBJECT_DATA.$grade;
        $objMemcached = Hk_Service_Memcached::getInstance("zhiboke");
        $objMemcached->delete($cacheCourseKey);
        $objMemcached->delete($cacheSubjectKey);

        return true;
    }


    public function getCardTagInfo($cardInfo,$cardFlagType=1, $appid = Hk_Ds_Publish_Const::APP_HOMEWORK)
    {
        $extData = isset($cardInfo['extData']) ? $cardInfo['extData'] : array();
        //长期班，专题课标签
        $cardTagInfo = isset($extData['cardTagInfo']) ? $extData['cardTagInfo'] : array();
        //长期班的子标题列表（赠送讲义，25人小班）
        $resTagList = isset($extData['subTitleList']) ? $extData['subTitleList'] : array();
        //专题课标签的副标题
        $cardSubTitle = isset($extData['cardSubTitle']) ? $extData['cardSubTitle'] : '';
        // 是否试听课 1:是
        $isTrialClass = !empty($extData['isTrialClass']) ? 1 : 0;

        $promoteType = isset($extData['promoteType']) ? $extData['promoteType'] : 0;
        
        if(!empty($cardTagInfo) || !empty($resTagList) || !empty($cardSubTitle)){
            $tagList = array();
            if(!empty($resTagList)){
                foreach ($resTagList as $tagName){
                    $tagList[] = array(
                        'tagName' =>$tagName
                    );
                }
            }

            if(!empty($cardTagInfo)){
                $cardTagInfo['cardTagUrl'] = Hkzb_Util_FuDao::getSalesImageUrl($cardTagInfo['cardTagUrl']);
            }

        }else{
            $tagList = array(
                array(
                    'tagName' => '25人小班',
                ),
                array(
                    'tagName' => '赠送讲义',
                ),
                array(
                    'tagName' => '名师讲解',
                ),
            );
        }

        if(empty($cardTagInfo)){
            //默认是班课
            $banFlagType = Hkzb_Ds_Fudao_SubjectCard::CARD_FLAG_TYPE_BAN;
            $cardTagInfo = $this->getCardNameAndCardUrl($banFlagType,$appid);

            //专题课没有子标题列表
            if($cardFlagType == Hkzb_Ds_Fudao_SubjectCard::CARD_FLAG_TYPE_ZHUAN){
                $zhuanFlagType = Hkzb_Ds_Fudao_SubjectCard::CARD_FLAG_TYPE_ZHUAN;
                $tagList = array();
                $cardTagInfo = $this->getCardNameAndCardUrl($zhuanFlagType,$appid);
            }

            //试听课的优先级最高
            if ($isTrialClass) {
                $tagList = array();
                $tiyanFlagType = Hkzb_Ds_Fudao_SubjectCard::CARD_FLAG_TYPE_TIYAN;

                $cardTagInfo = $this->getCardNameAndCardUrl($tiyanFlagType,$appid);
            }

            //处理新增的推广类型支持
            if(!empty($promoteType)){
                $tagList = array();
                if($promoteType == Hkzb_Ds_Fudao_SubjectCard::CARD_FLAG_TYPE_TIYAN){
                    $isTrialClass = 1;
                }

                $cardTagInfo = $this->getCardNameAndCardUrl($promoteType,$appid);
            }

        }

        if(empty($cardSubTitle)){
            if($cardFlagType == Hkzb_Ds_Fudao_SubjectCard::CARD_FLAG_TYPE_ZHUAN){
                $cardSubTitle = '';
            }

        }
        $arrRes = array(
            'cardSubTitle' => $cardSubTitle,
            'subTitleList' => $tagList,
            'cardTagInfo'  => $cardTagInfo,
            'isTrialClass' => $isTrialClass,
        );

        return $arrRes;
    }

    private function getCardNameAndCardUrl($cardFlagType = 1,$appid='homework')
    {
        switch ($cardFlagType){
            case Hkzb_Ds_Fudao_SubjectCard::CARD_FLAG_TYPE_ZHUAN ://专题课
                $cardTagName = '专题课';
                $cardTagUrl = 'http://img.zuoyebang.cc/zyb_b6409a2250b2fdbffb3f1c56aa29e918.jpg@f_jpg';

                //一课app的支持
                if ($appid == Hk_Ds_Publish_Const::APP_AIRCLASS) {
                    $cardTagUrl = "http://img.zuoyebang.cc/zyb_f5b6c6943fc61c71baeddaf22f9e2666.jpg";
                }

                break;
            case Hkzb_Ds_Fudao_SubjectCard::CARD_FLAG_TYPE_TIYAN ://试听课
                $cardTagName = '试听课';
                //$cardTagUrl = 'http://img.zuoyebang.cc/zyb_19cfb02c92b3262f38338a2c7e121e97.jpg@f_jpg';
                $cardTagUrl = 'http://img.zuoyebang.cc/zyb_8eb8b6c3d203369a7fdb397732eafe92.jpg@f_jpg';
                break;
            default:  //班课
                $cardTagName = '长期班';
                $cardTagUrl = 'http://img.zuoyebang.cc/zyb_2d194168df122bb6ff390afe8d9d7dbb.jpg@f_jpg';
                //增加对一课图片的支持
                if ($appid == Hk_Ds_Publish_Const::APP_AIRCLASS) {
                    $cardTagUrl = "http://img.zuoyebang.cc/zyb_b0ea076fba51f8f8a8d2ec2f4c595c40.jpg";
                }
        }

        $res = array(
            'cardTagName' => $cardTagName,
            'cardTagUrl'  => $cardTagUrl,
        );

        return $res;
    }

    //获取课程自定义分类信息
    public function getIndexCustomCardInfo($grade,$arrFields=array())
    {
        if (empty($grade)) {
            Bd_Log::warning('grade is empty');
            return array();
        }
        $grade = intval($grade);
        //读缓存
        $cacheKey = self::INDEX_CUSTOM_COURSE_CARD_DATA . $grade;
        $objMemecache = Hk_Service_Memcached::getInstance("zhiboke");
        $res = $objMemecache->get($cacheKey);
        if (!empty($res)) {
            $res = json_decode($res, true);
            return $res;
        }
        //读库
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds['grade'] = $grade;
        $arrConds['cardType'] = self::CARD_TYPE_INDEX_CUSTOM;
        $arrConds['status'] = self::CARD_ONLINE_STATUS;

        $objDaoSubjectCard = new Hkzb_Dao_Fudao_SubjectCard();
        $ret = $objDaoSubjectCard->getRecordByConds($arrConds, $arrFields);

        if ($ret === false) {
            Bd_Log::warning("db error 获取数据失败");
            return array();
        }
        if (!empty($ret)) {
            $cacheData = json_encode($ret);
            $objMemecache->set($cacheData, 10 * 60);
        }

        return $ret;
    }
    /**
     * 获取自定义卡片列表
     * @param array $params
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @return array|false
     */
    public function getSelfDefinedCardList($params=array(),$arrFields = array(), $offset = 0, $limit = 20)
    {
        if(empty($arrFields)){
            $arrFields =  explode(',',self::ALL_FIELDS);
        }
        $arrConds['deleted'] = 0;
        $allFiedsArr = explode(',',self::ALL_FIELDS);
        foreach ($params as $key=>$value){
            if(!in_array($key,$allFiedsArr)){
                continue;
            }
            $arrConds[$key] = $value;
        }

        $arrAppends = array(
            "order by rank asc",
        );
        if($limit > 0) {
            $arrAppends[] = "limit $offset, $limit";
        }
        $objDaoSubjectCard = new Hkzb_Dao_Fudao_SubjectCard();
        $ret = $objDaoSubjectCard->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        if($ret === false){
            Bd_Log::warning("Error::db error getIndexSubjectCardList detail".json_encode($params));
        }

        return $ret;
    }

    public function deleteSelfDefineCardCache($tagId)
    {
        if(empty($tagId)){
            Bd_Log::warning("Error::tagId is empty".$tagId);
            return false;
        }
        $cardInfo = $this->getSubjectCardInfo($tagId);
        $grade = isset($cardInfo['grade']) ? $cardInfo['grade'] : 0;
        if(empty($grade)){
            Bd_Log::warning("Error::grade is empty".json_encode($cardInfo));
            return false;
        }

        return true;
    }

    static public function getBanCacheKey($grade,$role = 0,$isInner = false)
    {
        $role = intval($role);

        if($role != 1){
            $role = 0;
        }

        $isIn = 0;
        if($isInner !==  false){
            $isIn =  $isInner;
        }

        $isIn = intval($isIn);
        $cacheKey = self::GRADE_SUBJECT_CARD_DATA_KEY . $grade . '_' . $isIn . '_' . $role;
        return $cacheKey;
    }


    static public function getDefineCardCacheKey($grade,$tagId,$subject,$courseIdArr=array())
    {
        $courseIdStr = '';
        if(!empty($courseIdArr)){
            //排序
            sort($courseIdArr);
            $courseIdStr = join('_',$courseIdArr);
        }
        $md5CourseIdStr = md5($courseIdStr);
        $cacheKey = self::GRADE_INDEX_PROMOTE_SUBJECT_CARD_DATA_KEY . $grade .'_'. $tagId .'_' . $subject . '_' . $md5CourseIdStr;

        return $cacheKey;
    }

    static public function getDefineSubTagCacheKey($grade,$tagId,$subject,$courseIdArr=array())
    {
        $courseIdStr = '';
        if(!empty($courseIdArr)){
            //排序
            sort($courseIdArr);
            $courseIdStr = join('_',$courseIdArr);
        }
        $md5CourseIdStr = md5($courseIdStr);
        $cacheKey = self::GRADE_SUBJECT_CARD_SUB_TAG_KEY . $grade .'_'. $tagId .'_' . $subject . '_' . $md5CourseIdStr;

        return $cacheKey;
    }

    static public function getDefineCardCacheKeyV2($grade,$tagId,$subject,$courseIdArr=array(),$role = 0)
    {

        $role = intval($role);

        if($role != 1){
            $role = 0;
        }

        $courseIdStr = '';
        if(!empty($courseIdArr)){
            //排序
            sort($courseIdArr);
            $courseIdStr = join('_',$courseIdArr);
        }
        $md5CourseIdStr = md5($courseIdStr);
        $cacheKey = self::GRADE_INDEX_PROMOTE_SUBJECT_CARD_DATA_KEY . $grade .'_'. $tagId .'_' . $subject . '_' .$role .'_'. $md5CourseIdStr;

        return $cacheKey;
    }

}

