<?php
/***************************************************************************
 *
 * Copyright (c) 2017 zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   Exercise.php
 * <AUTHOR>
 * @date   2017/3/13 14:13:18
 * @brief  习题
 *
 **/
class Hkzb_Ds_Fudao_Advanced_Exercise {
    /**
     * 获取习题列表
     *
     * @param  int    $exerciseId  习题id
     * @param  array  $arrFields 指定属性
     * @return array
     */
    public function getExerciseListByIds($lessonId, $arrExerciseIds, $arrFields = array(), $useCache = true) {
        //参数检查
        if($lessonId <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[lessonId:$lessonId]");
            return false;
        }
        
        /*
        //获取子课
        $objDsLesson = new Hkzb_Ds_Fudao_Lesson();
        $lessonInfo = $objDsLesson->getLessonInfo($lessonId, array('courseId'));
        if(empty($lessonInfo)) {
            Bd_Log::warning("Error:[getLessonInfo error], Detail:[lessonId:$lessonId]");
            return false;
        }

        $subCourseId = intval($lessonInfo['courseId']);
        */

        //代理接口
        $objExercise = new Hkzb_Ds_Fudao_Exercise();
        $ret = $objExercise->getExerciseListByIds(0 , $lessonId, $arrExerciseIds, $arrFields, $useCache);

        return $ret;
    }

    /**
     * 不依赖lessonId 获取exerciseId
     * @param $arrExerciseIds
     * @param $arrFields
     * @param $useCache
     */
    public function getExerciseListByExerciseIds($arrExerciseIds, $arrFields = [], $useCache = true) {
        Bd_Log::addNotice('getExerciseListByExerciseIds', 1);
        $objExercise = new Hkzb_Dao_Fudao_Exercise();

        if(empty($arrExerciseIds)) {
            Bd_Log::warning("Error:[param error], Detail:[$arrExerciseIds:".implode(',' , $arrExerciseIds)."]");
            return false;
        }

        $strExerciseIds = implode(',',$arrExerciseIds);
        if(!preg_match("/^[0-9,\\s]+$/",$strExerciseIds)){
            Bd_Log::warning("Error:[param error], Detail:[$strExerciseIds:$strExerciseIds]");
            return false;
        }
        /*读缓存*/
        if($useCache) {
            $objMemcached = Hk_Service_Memcached::getInstance('zhiboke');
            $cKey = json_encode($arrExerciseIds);
            $cacheKey = Hkzb_Ds_Fudao_Exercise::$FRE_KEY . md5($cKey);
            $cacheValue = $objMemcached->get($cacheKey);
            if (!empty($cacheValue)) {
                $ret = json_decode(utf8_encode($cacheValue), true);
                return $ret;
            }
        }
        if (empty($arrFields)) {
            $arrFields = explode(',', Hkzb_Ds_Fudao_Exercise::ALL_FIELDS);
        }

        $arrConds = array(
            "exercise_id in ($strExerciseIds)",
        );
        $ret = $objExercise->getListByConds($arrConds, $arrFields );
        if(!empty($ret) && $useCache){
            $cacheValue = json_encode($ret);
            $objMemcached->set($cacheKey, $cacheValue, 3600);
        }
        return $ret;

    }

    /**
     * 计算学生课前预习和课后复习的完成状态
     * @param $studentUid
     * @param $lessonId
     * @param $purpose
     * @return int|mixed
     */
    public function getStudentExerciseFinishStatus($studentUid,$lessonId,$purpose)
    {

        if(empty($studentUid) || empty($lessonId) || empty($purpose)){
            Bd_Log::warning("param error detail : studentUid:{$studentUid} lessonId:{$lessonId} purpose:{$purpose}");
            return false;
        }

        $objDsLesson = new Hkzb_Ds_Fudao_Lesson();
        $lessonInfo = $objDsLesson->getLessonInfo($lessonId,array('courseId','lessonId'));
        if(empty($lessonInfo)){
            Bd_Log::warning('lessonInfo is empty ');
            return false;
        }

        $subCourseId = $lessonInfo['courseId'];

        //获取学生领取学分情况
        $studentLessonInfo = Hkzb_Ds_Fudao_Advanced_StudentLesson::getStudentLesson($studentUid,$lessonId,array('lessonId','extData'));
        if(empty($studentLessonInfo)){
            Bd_Log::warning("studentLesson is empty");
            return false;
        }

        $starInfo = isset($studentLessonInfo['extData']['starInfo']) ? $studentLessonInfo['extData']['starInfo'] : array();

        $purposeToFinishTypeMap = Hkzb_Ds_Fudao_Exercise::$PURPOSE_TO_FINISHTYPEMAP;
        $finishType = isset($purposeToFinishTypeMap[$purpose]) ? $purposeToFinishTypeMap[$purpose] : 0;
        if(empty($finishType)){
            Bd_Log::warning("purpose is not map : {$purpose}");
            return false;
        }

        $typeConf = Hkzb_Ds_Fudao_Advanced_LessonMap::$typeConf;
        $finishTypeStr = $typeConf[$finishType];

        if(isset($starInfo[$finishTypeStr])){
           $finishStatus =  $starInfo[$finishTypeStr];
           return $finishStatus;
        }

        //如果没有领取记录，判断是否完成答题
        $objDsExercise = new Hkzb_Ds_Fudao_Advanced_Exercise();
        $objDsExerciseDetail = new Hkzb_Ds_Fudao_Advanced_StudentExercise();

        $exerciseCnt = $objDsExercise->getExerciseCnt( $lessonId, $purpose);

        $studentExerciseCnt = $objDsExerciseDetail->getExerciseDetailCntByStudentUid($studentUid, $lessonId, $purpose);

        if($studentExerciseCnt < $exerciseCnt){
            $finishStatus = 0;
        }else{
            $finishStatus = 1;
        }

        return $finishStatus;
    }

    /**
     * 获取小学英语某个章节某个purpose的题目列表
     * @param $lessonId
     * @param $purpose
     * @param int $type
     * @return array|false
     */
    public function getLessonExerciseList($lessonId, $purpose,$type=0)
    {
        //purpose和type的转换
        $purpose = $this->getTransPurpose($purpose);
        $type = $this->getTransType($type);
        $objExamPaper = new Hkzb_Ds_Fudao_Exam_ExamPaper();
        $exerciseList = $objExamPaper->getLessonExerciseList($lessonId, $purpose,$type);
        foreach ($exerciseList as $key=>$itemExercise){
            $exerciseType = $itemExercise['exerciseType'];
            //特殊处理questionList
            $senstenceType = Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_MAKE_SENTENCE;
            $wordType = Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_MAKE_WORD;
            if($exerciseType == $senstenceType || $exerciseType == $wordType){
                $questionList = $itemExercise['questionList'];
                $exerciseList[$key]['questionList'] = array_slice($questionList,0,1);
            }

            $exerciseList[$key]['exerciseType'] = $this->getTransType($exerciseType,true);

            //转换正确答案
            $answer = $itemExercise['answer'];
            $exerciseList[$key]['answer'] = $this->transAnswer($answer);
        }

        return $exerciseList;
    }

    /**
     * 获取小学英语练习题的答案
     * @param $lessonId
     * @param $exerciseIdArr
     * @return array|false
     */
    public function getExerciseListByExerciseIdArr($lessonId,$exerciseIdArr)
    {
        $objDsExamPaper = new Hkzb_Ds_Fudao_Exam_ExamPaper();
        $exerciseArr = $objDsExamPaper->getExerciseListByIds($lessonId,$exerciseIdArr);
        if(empty($exerciseArr)){
            return array();
        }
        foreach ($exerciseArr as $key=>$itemExercise){
            $type = $itemExercise['type'];
            $answer = $itemExercise['answer'];
            $exerciseArr[$key]['type'] = $this->getTransType($type,true);

            //转换正确答案
            $exerciseArr[$key]['answer'] = $this->transAnswer($answer);
            $exerciseArr[$key]['optionList'] = $itemExercise['optionList'];
        }

        return $exerciseArr;
    }

    /**
     * @param $lessonId
     * @param $purpose
     * @return array|false
     */
    public function getExerciseCnt($lessonId, $purpose)
    {
        $purpose = $this->getTransPurpose($purpose);
        $objDsExamPaper = new Hkzb_Ds_Fudao_Exam_ExamPaper();
        $exerciseCnt = $objDsExamPaper->getExerciseCnt($lessonId,$purpose);

        return $exerciseCnt;
    }

    public function getTransPurpose($purpose,$isReverse=false)
    {
        $purposeMap = Hkzb_Ds_Fudao_Exercise::$studentPurposeNumMap;
        if($isReverse){
            $purposeMap = array_flip($purposeMap);
        }
        $purpose = isset($purposeMap[$purpose]) ? $purposeMap[$purpose] : 0;

        return $purpose;
    }

    public function getTransType($type,$isReverse=false)
    {
        $typeMap = Hkzb_Ds_Fudao_Exercise::$studentTypeNumMap;
        if($isReverse){
            $typeMap = array_flip($typeMap);
        }
        $typeArr = array();
        if(is_array($type)){
            foreach ($type as $key=>$typeItem){
                $typeArr[] = isset($typeMap[$typeItem]) ? $typeMap[$typeItem] : 0;
            }
            return $typeArr;
        }

        $type = isset($typeMap[$type]) ? $typeMap[$type] : 0;

        return $type;
    }


    public function transAnswer($answer='')
    {
        if(empty($answer)){
            return '';
        }

        $answerArr = explode(',',$answer);

        $flipAnswer = array_flip($answerArr);
        ksort($flipAnswer);
        $answer = implode(',',$flipAnswer);
        return $answer;
    }


}
