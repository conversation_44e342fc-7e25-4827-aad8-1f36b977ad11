<?php
/***************************************************************************
 *
 * Copyright (c) 2017 zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   StudentLesson.php
 * <AUTHOR>
 * @date   2017/3/13 14:13:18
 * @brief  学生-章节
 *
 **/
class Hkzb_Ds_Fudao_Advanced_StudentLesson
{

    /**
     * 判定学生的章节权限
     *
     * @param  int  $studentUid  学生uid
     * @param  int  $lessonId    章节id
     * @param  int  $classId     班级id
     * @return true|false
     */
    public function checkStudentLesson($studentUid, $lessonId, $classId = 0) {
        //参数检查
        if (intval($studentUid) <= 0 || intval($lessonId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid lessonId:$lessonId]");

            return false;
        }

        //获取章节信息
        $objDsLesson = new Hkzb_Ds_Fudao_Lesson();
        $lessonInfo  = $objDsLesson->getLessonInfo($lessonId, array('courseId', 'startTime'));
        if (empty($lessonInfo)) {
            Bd_Log::warning("Error:[getLessonInfo error], Detail:[lessonId:$lessonId]");

            return false;
        }

        $startTime   = intval($lessonInfo['startTime']);
        $subCourseId = intval($lessonInfo['courseId']);

        //获取学生-子课信息
        $objStudentCourse = new Hkzb_Ds_Fudao_StudentCourse();
        $studentCourse = $objStudentCourse->getStudentCourseInfo($studentUid, $subCourseId, array('classId', 'deleted', 'extData'));
        if(empty($studentCourse)) {
            //Bd_Log::warning("Error:[getTeacherUidList error], Detail:[studentUid:$studentUid subCourseId:$subCourseId]");
            return false;
        }

        if($classId > 0) {
            if(intval($studentCourse['classId']) !== intval($classId)) {
                Bd_Log::warning("Error:[classId invalid], Detail:[studentUid:$studentUid subCourseId:$subCourseId classId:$classId]");
                return false;
            }
        }

        //子课冻结
        $deleted = intval($studentCourse['deleted']);
        if ($deleted === Hkzb_Ds_Fudao_StudentCourse::STATUS_DELETED) {
            $extData    = $studentCourse['extData'];
            $deleteTime = intval($extData['deleteTime']);
            if ($deleteTime < $startTime - Hkzb_Ds_Fudao_StudentCourse::TIME_BEFORE_LESSON) {
                return false;
            }
        }

        return true;
    }

    /**
     * 获取学生的章节列表
     * @param $studentUid
     * @return array|bool
     */
    public function getLessonIdList($studentUid)
    {
        //参数检查
        if (intval($studentUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid]");

            return false;
        }

        $arrFields           = array('lessonId');
        $arrConds            = array(
            'studentUid' => $studentUid,
        );
        $objDaoStudentLesson = new Hkzb_Dao_Fudao_LessonDetail();
        $lessonList          = $objDaoStudentLesson->getListByConds($studentUid, $arrConds, $arrFields);
        if (false === $lessonList) {
            Bd_Log::warning("Error:[getListByConds error], Detail:[studentUid:$studentUid]");

            return false;
        }

        $lessonIdList = array();
        foreach ($lessonList as $lesson) {
            $lessonId = $lesson['lessonId'];
            $ret      = $this->checkStudentLesson($studentUid, $lessonId);
            if ($ret) {
                $lessonIdList[] = $lessonId;
            }
        }

        return $lessonIdList;
    }

    /**
     * 获取班级信息
     *
     * @param  int $studentUid 学生uid
     * @param  int $lessonId 章节id
     * @return array|false
     */
    public function getClassInfo($studentUid, $lessonId)
    {
        //参数检查
        if (intval($studentUid) <= 0 || intval($lessonId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid lessonId:$lessonId]");

            return false;
        }

        //获取章节信息
        $objDsLesson = new Hkzb_Ds_Fudao_Lesson();
        $lessonInfo  = $objDsLesson->getLessonInfo($lessonId, array('courseId'));
        if (empty($lessonInfo)) {
            Bd_Log::warning("Error:[getLessonInfo error], Detail:[lessonId:$lessonId]");

            return false;
        }

        $subCourseId = intval($lessonInfo['courseId']);

        //获取学生-子课信息
        $objStudentCourse = new Hkzb_Ds_Fudao_StudentCourse();
        $studentCourse    = $objStudentCourse->getStudentCourseInfo($studentUid, $subCourseId, array('classId', 'assistantUid', 'teacherUid'));
        if (empty($studentCourse)) {
            Bd_Log::warning("Error:[getTeacherUidList error], Detail:[subCourseId:$subCourseId]");

            return false;
        }

        //子课全部冻结
        $classId      = intval($studentCourse['classId']);
        $assistantUid = intval($studentCourse['assistantUid']);
        $teacherUid   = intval($studentCourse['teacherUid']);

        $arrOutput = array(
            'classId'      => $classId,
            'assistantUid' => $assistantUid,
            'teacherUid'   => $teacherUid,
        );

        return $arrOutput;
    }

    /**
     * 获取用户章节信息
     * @param int $studentUid
     * @param int $lessonId
     * @return array|bool
     */
    public static function getStudentLesson($studentUid, $lessonId, $arrFields = array())
    {
        $studentUid = intval($studentUid);
        $lessonId   = intval($lessonId);
        if ($studentUid <= 0 || $lessonId <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid lessonId:$lessonId]");

            return false;
        }
        if (empty($arrFields)) {
            $arrFields = Hkzb_Dao_Fudao_LessonDetail::$allFields;
        }
        $arrConds = array(
            'studentUid' => $studentUid,
            'lessonId'   => $lessonId,
        );
        $objDao   = new Hkzb_Dao_Fudao_LessonDetail();

        return $objDao->getRecordByConds($studentUid, $arrConds, $arrFields);
    }

    /**
     * 获取用户章节信息
     * @param $studentUid
     * @param $arrLessonId
     * @param array $arrFields
     * @return array|bool|false
     */
    public function getStudentLessonList($studentUid, $arrLessonId, $arrFields = array())
    {
        $studentUid = intval($studentUid);
        if ($studentUid <= 0 || !is_array($arrLessonId) || empty($arrLessonId)) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid lessonIds:]" . json_encode($arrLessonId));

            return false;
        }
        if (empty($arrFields)) {
            $arrFields = Hkzb_Dao_Fudao_LessonDetail::$allFields;
        }
        $arrConds = array(
            'studentUid' => $studentUid,
            'lesson_id in (' . join(',', $arrLessonId) . ')',
            'status'     => array(Hkzb_Ds_Fudao_LessonDetail::STATUS_DELETED, '<'),
        );
        $objDao   = new Hkzb_Dao_Fudao_LessonDetail();

        $ret = $objDao->getListByConds($studentUid, $arrConds, $arrFields);

        return $ret;
    }

    /**
     * @param $lessonId
     * @param $classId
     * @return array|bool|false|mixed
     * @desc 获取班级的人，按报课时间排序，加缓存
     */
    public function getStudentUidsByClassId($lessonId, $classId)
    {
        $lessonId = intval($lessonId);
        $classId = intval($classId);

        //读缓存
        $objMemcached = Hk_Service_Memcached::getInstance("zhiboke");
        $cacheKey = 'zhiboke_ds_advanced_studentlesson_getstudentuidsbyclassid_' . $lessonId . '_' . $classId;
        $cacheValue = $objMemcached->get($cacheKey);
        if (!empty($cacheValue)) {
            Hk_Util_Log::incrKey("zhiboke_ds_advanced_studentlesson_getstudentuidsbyclassid_hit", 1);
            $studentUids = json_decode(utf8_encode($cacheValue), true);
            return $studentUids;
        }
        Hk_Util_Log::incrKey("zhiboke_ds_advanced_studentlesson_getstudentuidsbyclassid_miss", 1);

        //读数据库
        if ($lessonId <= 0 || $classId <= 0) {
            Bd_log::warning("Error:[param error], Detail:[lessonId:$lessonId classId:$classId]");
            return false;
        }

        $arrFields = array('studentUid', 'createTime');
        $arrConds = array(
            'lessonId' => $lessonId,
            'classId' => $classId,
        );
        $objDao = new Hkzb_Dao_Fudao_LessonDetail();

        $studentList = array();
        for ($index = 0; $index < 20; $index++) {
            $ret = $objDao->getListByConds($index, $arrConds, $arrFields);
            $studentList += array_merge($studentList, $ret);
        }

        //按报名时间排序
        $uidCreateTimeMap = array();
        foreach ($studentList as $student) {
            $uidCreateTimeMap[$student['studentUid']] = $student['createTime'];
        }
        arsort($uidCreateTimeMap);

        $studentUids = array_keys($uidCreateTimeMap);

        //写缓存
        $cacheValue = json_encode($studentUids);
        $objMemcached->set($cacheKey, $cacheValue, 60);

        return $studentUids;
    }
    
    /**
     * 更新用户章节视频回放记录
     * @param $intStudentUid
     * @param $intLessonId
     * @param $ext
     * @return bool
     */
    public function updateLessonVideoRecordTime($intStudentUid, $intLessonId, $ext = array())
    {
        if (empty($intStudentUid) || empty($intLessonId) || empty($ext)) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$intStudentUid lessonId:$intLessonId ext:" . json_encode($ext) . "]");

            return false;
        }
        $arrConds = array(
            'studentUid' => $intStudentUid,
            'lessonId'   => $intLessonId,
        );
        $arrFields['videoRecordTime'] = json_encode($ext);
        $arrFields['updateTime']      = time();
        $objDaoStudentLesson          = new Hkzb_Dao_Fudao_LessonDetail();
        $res                          = $objDaoStudentLesson->updateByConds($intStudentUid, $arrConds, $arrFields);

        return $res;
    }
    
    /**
     * 更新用户章节视频标记记录
     * @param $intStudentUid
     * @param $intLessonId
     * @param $ext
     * @return bool
     */
    public function updateLessonvideoSignNote($intStudentUid, $intLessonId, $ext = array())
    {
        if (empty($intStudentUid) || empty($intLessonId) || empty($ext)) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$intStudentUid lessonId:$intLessonId ext:" . json_encode($ext) . "]");

            return false;
        }
        $arrConds = array(
            'studentUid' => $intStudentUid,
            'lessonId'   => $intLessonId,
        );
        $arrFields['videoSignNote'] = json_encode($ext);
        $arrFields['updateTime']      = time();
        $objDaoStudentLesson          = new Hkzb_Dao_Fudao_LessonDetail();
        $res                          = $objDaoStudentLesson->updateByConds($intStudentUid, $arrConds, $arrFields);

        return $res;
    }

    /**
     * 检查用户是否报名参数中的章节，有一门报名就返回true
     * @param $studentUid
     * @param $lessonIds
     * @return bool
     */
    public function checkStudentLessonIds($studentUid, $lessonIds)
    {
        $studentUid = intval($studentUid);
        if ($studentUid <= 0 || !is_array($lessonIds) || !$lessonIds) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid lessonIds:" . json_encode($lessonIds) . "]");
            return false;
        }
        $arrConds = array(
            'studentUid' => $studentUid,
            'lesson_id in (' . implode(',', $lessonIds) . ')',
        );
        $arrFields = array('courseId', 'startTime');
        $objDaoStudentLesson = new Hkzb_Dao_Fudao_LessonDetail();
        $ret = $objDaoStudentLesson->getListByConds($studentUid, $arrConds, $arrFields);
        if ($ret === false) {
            Bd_Log::warning('Error:[db error] Detail:[studentUid:'.$studentUid.' lessonIds:'.json_encode($lessonIds).']');
            return false;
        }
        if (!$ret) {
            return false;
        }
        $courseStartTimeMap = array();
        foreach ($ret as $value) {
            $courseStartTimeMap[$value['courseId']] = $value['startTime'];
        }
        $courseIds = array_keys($courseStartTimeMap);
        $arrConds = array(
            'studentUid' => $studentUid,
            'course_id in (' . implode(',', $courseIds) . ')',
        );
        $arrFields = array('courseId', 'deleted', 'ext_data');
        $objDaoStudentCourse = new Hkzb_Dao_Fudao_StudentCourse();
        $ret = $objDaoStudentCourse->getListByConds($studentUid, $arrConds, $arrFields);
        if ($ret === false) {
            Bd_Log::warning('Error:[db error] Detail:[studentUid:'.$studentUid.' lessonIds:'.json_encode($lessonIds).']');
            return false;
        }
        if (!$ret) {
            return false;
        }
        foreach ($ret as $value) {
            $deleted = intval($value['deleted']);
            if ($deleted === Hkzb_Ds_Fudao_StudentCourse::STATUS_OK) {
                return true;
            } else {
                $deleteTime = intval($value['extData']['deleteTime']);
                $startTime = intval($courseStartTimeMap[$value['courseId']]);
                if ($deleteTime >= $startTime - Hkzb_Ds_Fudao_StudentCourse::TIME_BEFORE_LESSON) {
                    return true;
                }
            }
        }
        return false;
    }
}
