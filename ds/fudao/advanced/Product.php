<?php
/**
 * Copyright (c) 2017 zuoyebang.com, Inc. All Rights Reserved
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @file: Product.php
 * @date: 2017/12/22
 * @time: 11:16
 * @desc:
 */

class Hkzb_Ds_Fudao_Advanced_Product
{
    /**
     * @param $intProductId
     * @param array $options
     * @param bool $cacheSwith
     * @param bool $showOff   [是否显示下架产品]
     * @return array|bool|mixed
     * add by yuyongchao
     */
    public function getProductInfo($intProductId, $options = array(), $cacheSwith = true, $showOff = false)
    {
        if (intval($intProductId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[productId:$intProductId]");

            return false;
        }
        //读缓存
        if ($cacheSwith) {
            $objMemcached = Hk_Service_Memcached::getInstance("zhiboke");
            $cacheKey     = 'zhiboke_ds_advanced_productinfo_' . $intProductId . '_' . md5(json_encode($options));
            $cacheValue   = $objMemcached->get($cacheKey);
            if (!empty($cacheValue)) {
                Hk_Util_Log::incrKey("zhiboke_ds_advanced_prodcutinfo_hit", 1);
                $productInfo = json_decode(utf8_encode($cacheValue), true);

                return $productInfo;
            }
            Hk_Util_Log::incrKey("zhiboke_ds_advanced_productinfo_miss", 1);
        }
        //获取商品信息
        $objProduct  = new Hkzb_Ds_Fudao_Product();
        $productInfo = $objProduct->getProductInfo($intProductId, $showOff);
        if ($productInfo === false) {
            return false;
        }
        if (empty($productInfo)) {
            return array();
        }
        $courseIdList = $productInfo['courseIdList'];
        if (empty($courseIdList)) {
            return array();
        }
        if (!empty($productInfo)) {
            if ($cacheSwith) {
                $cacheValue = json_encode($productInfo);
                $objMemcached->set($cacheKey, $cacheValue, 60 + $intProductId % 60);
            }
        }

        return $productInfo;
    }
}