<?php
/***************************************************************************
 *
 * Copyright (c) 2017 zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   Rtmp.php
 * <AUTHOR>
 * @date   2017/3/13 14:13:18
 * @brief  CDN服务
 *
 **/
class Hkzb_Ds_Fudao_Advanced_Rtmp {
    
    /**
     * 获取CDN服务信息(单线)
     *
     * @param  int  $lessonId
     * @return false|string
     */
    public function getRtmpInfo($lessonId) {
        if($lessonId <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[lessonId:$lessonId]");
            return false;
        }

        $objDsLesson = new Hkzb_Ds_Fudao_Lesson();
        $lessonInfo = $objDsLesson->getLessonInfo($lessonId, array('courseId'));
        if(empty($lessonInfo)) {
            Bd_Log::warning("Error:[getLessonInfo error], Detail:[lessonId:$lessonId]");
            return false;
        }

        $subCourseId = intval($lessonInfo['courseId']);

        //默认服务商
        $rtmpInfo = Hkzb_Util_FuDao::getRtmpAddress($subCourseId, $lessonId);
        if(empty($rtmpInfo)) {
            Bd_Log::warning("Error:[getRtmpAddress error], Detail:[subCourseId:$subCourseId lessonId:$lessonId]");
            return false;
        }

        return strval($rtmpInfo);
    }

    /**
     * 获取CDN服务信息(多路)
     *
     * @param  int  $lessonId
     * @return false|array
     */
    public function getRtmpInfoList($lessonId) {
        if($lessonId <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[lessonId:$lessonId]");
            return false;
        }

        $objDsLesson = new Hkzb_Ds_Fudao_Lesson();
        $lessonInfo = $objDsLesson->getLessonInfo($lessonId, array('courseId'));
        if(empty($lessonInfo)) {
            Bd_Log::warning("Error:[getLessonInfo error], Detail:[lessonId:$lessonId]");
            return false;
        }

        $subCourseId = intval($lessonInfo['courseId']);

        //默认服务商
        $rtmpInfo = Hkzb_Util_FuDao::getRtmpAddress($subCourseId, $lessonId);
        if(empty($rtmpInfo)) {
            Bd_Log::warning("Error:[getRtmpAddress error], Detail:[subCourseId:$subCourseId lessonId:$lessonId]");
            return false;
        }

        $rtmpInfoList = array(
            array(
                'title' => '线路1',
                'url'   => strval($rtmpInfo),
            ),
        );

        //老师直推cdn的时候，默认推高清清晰度
        $defaultClarity = "hd";
        $clarityExt = "_l".Hkzb_Util_FuDao::$clarity2Level[$defaultClarity];

        $index = 2;
        //默认服务商
        $curServices = Hkzb_Util_FuDao::getChooseRtmpService($subCourseId, $lessonId);
        //获取合作的服务商
        $allServices = Hkzb_Util_FuDao::$RTMP_SERVICE;
        foreach ($allServices as $service) {
            if ($service != $curServices) {
                $rtmpInfo = Hkzb_Util_FuDao::getRtmpAddressByRtmpService($subCourseId, $lessonId, $service,$clarityExt);
                if(empty($rtmpInfo)) {
                    Bd_Log::warning("Error:[getRtmpAddressByRtmpService error], Detail:[subCourseId:$subCourseId lessonId:$lessonId service:$service]");
                    continue;
                }

                $rtmpInfoList[] = array(
                    'title' => '线路' . $index++,
                    'url'   => strval($rtmpInfo),
                );
            }
        }

        return $rtmpInfoList;
    }

    /**
     * 根据章节和课程获取CDN服务信息(单线)
     * 该方法与 getRtmpInfo 的区别在于减少一次对 tblLesson 表的查询 粒度更小
     *
     * @param  int  $intLessonId 章节id
     * @param  int  $intCourseId 课程id（子课id）
     *
     * @return false|string
     */
    public function getRtmpInfoByLessonAndCourse($intLessonId,$intCourseId) {
        $intCourseId = intval($intCourseId);
        $intLessonId = intval($intLessonId);

        if ( $intLessonId <= 0 || $intCourseId <= 0 ) {
            Bd_Log::warning("Error:[getRtmpAddress error], Detail:[subCourseId:$intCourseId lessonId:$intLessonId]");
            return false;
        }
        // 获取默认服务商
        $rtmpInfo = Hkzb_Util_FuDao::getRtmpAddress($intCourseId, $intLessonId);
        if(empty($rtmpInfo)) {
            Bd_Log::warning("Error:[getRtmpAddress error], Detail:[subCourseId:$intCourseId lessonId:$intLessonId]");
            return false;
        }

        return strval($rtmpInfo);
    }

    /**
     * 获取CDN服务信息(多路)
     * 该方法与 getRtmpInfoList 的区别在于减少一次对 tblLesson 表的查询 粒度更小
     *
     * @param  int  $intLessonId 章节id
     * @param  int  $intCourseId 课程id（子课id）
     *
     * @return false|array
     *
     * 返回值格式
     * array(
     *     array(
     *         'title' => string
     *         'url'   => string
     *     )
     * )
     */
    public function getRtmpInfoListByLessonAndCourse( $intLessonId, $intCourseId) {

        $intCourseId = intval($intCourseId);
        $intLessonId = intval($intLessonId);

        if ( $intLessonId <= 0 || $intCourseId <= 0 ) {
            Bd_Log::warning("Error:[getRtmpAddress error], Detail:[subCourseId:$intCourseId lessonId:$intLessonId]");
            return false;
        }

        // 默认服务商
        $rtmpInfo = Hkzb_Util_FuDao::getRtmpAddress($intCourseId, $intLessonId);
        if(empty($rtmpInfo)) {
            Bd_Log::warning("Error:[getRtmpAddress error], Detail:[subCourseId:$intCourseId lessonId:$intLessonId]");
            return false;
        }

        $rtmpInfoList = array(
            array(
                'title' => '线路1',
                'url'   => strval($rtmpInfo),
            ),
        );

        //老师直推cdn的时候，默认推高清清晰度
        $defaultClarity = "hd";
        $clarityExt = "_l".Hkzb_Util_FuDao::$clarity2Level[$defaultClarity];

        $index = 2;
        //默认服务商
        $curServices = Hkzb_Util_FuDao::getChooseRtmpService($intCourseId, $intLessonId);
        //获取合作的服务商
        $allServices = Hkzb_Util_FuDao::$RTMP_SERVICE;
        foreach ($allServices as $service) {
            if ($service != $curServices) {
                $rtmpInfo = Hkzb_Util_FuDao::getRtmpAddressByRtmpService($intCourseId, $intLessonId, $service,$clarityExt);
                if(empty($rtmpInfo)) {
                    Bd_Log::warning("Error:[getRtmpAddressByRtmpService error], Detail:[subCourseId:$intCourseId lessonId:$intLessonId service:$service]");
                    continue;
                }

                $rtmpInfoList[] = array(
                    'title' => '线路' . $index++,
                    'url'   => strval($rtmpInfo),
                );
            }
        }

        return $rtmpInfoList;
    }
}
