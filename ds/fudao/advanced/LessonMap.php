<?php

/**
 * @file LessonDetail.php
 * <AUTHOR>
 * @date 2017/7/7 17:18:28
 * @brief 章节详情
 *
 **/

class Hkzb_Ds_Fudao_Advanced_LessonMap{
    const FINISH_TYPE_LESSON    = 1;//完课
    const FINISH_TYPE_EXERCISE  = 2;//答题
    const FINISH_TYPE_EXAM      = 3;//测验
    const FINISH_TYPE_HOMEWORK  = 4;//课后作业
    const FINISH_TYPE_PREENGLISH  = 5;//英语课前预习
    const FINISH_TYPE_PORENGLISH  = 6;//英语课后复习
    const EXAM_MAX_SCORE        = 80;
    const FROM_STATUS_IN_CLASS = 1;//来源课中
    const FROM_STATUS_AFTER_CLASS = 2;//来源课后
    
    public static $typeConf = array(
        self::FINISH_TYPE_LESSON => 'finishLesson',
        self::FINISH_TYPE_EXERCISE => 'finishExercise',
        self::FINISH_TYPE_EXAM => 'finishExam',
        self::FINISH_TYPE_HOMEWORK => 'finishHomework',
        self::FINISH_TYPE_PREENGLISH => 'finishPreEnglish',
        self::FINISH_TYPE_PORENGLISH => 'finishPorEnglish',
    );
    public static $scoreTypeConf = array(
        self::FINISH_TYPE_LESSON => Hkzb_Ds_Fudao_Score::TYPE_FINISH_LESSON,
        self::FINISH_TYPE_EXERCISE => Hkzb_Ds_Fudao_Score::TYPE_FINISH_EXERCISE,
        self::FINISH_TYPE_EXAM => Hkzb_Ds_Fudao_Score::TYPE_FINISH_EXAM,
        self::FINISH_TYPE_PREENGLISH => Hkzb_Ds_Fudao_Score::TYPE_ENGLISH_PRECLASS_EXERCISE,
        self::FINISH_TYPE_PORENGLISH => Hkzb_Ds_Fudao_Score::TYPE_ENGLISH_POSCLASS_EXERCISE,
    );
    public static $mapTypeFunc = array(
        'finishLesson' => array(
            'check'     => 'checkFinishLesson',
        ),
        'finishExercise' => array(
            'check'     => 'checkFinishExercise',
        ),
        'finishExam'    => array(
            'check'     => 'checkFinishExam',
        ),
        'finishHomework'    => array(
            'check'     => 'checkFinishHomework',
        ),
    );

    // 测试课程id
    // 例子：465,1100,3300
    public static $courseIdsArray = array(
        27622,
        27834,
        27832,
        28234,
        23122,//打开章节地图的线上课程id
        23170,
        23329,
        23454,
        24608,
        23127,
    );

    /**
     * 获取学生课堂星星情况及各种状态
     * @param $lessonId
     * @param $uid
     * @param $from 1课中 2课后
     * @return mixed
     */
    public static function checkAllStarInfo($lessonId, $uid, $from = 1){
        //获取章节信息
        $objStudentLesson = new Hkzb_Ds_Fudao_Advanced_StudentLesson();
        $studentLesson = $objStudentLesson->getStudentLesson($uid, $lessonId);
        if(!$studentLesson){
            Bd_Log::warning("Error[db error---getstudentlessonerr] Detail[uid:{$uid} lessonid:{$lessonId}]");
            return false;
        }
        $nowStarInfo = (isset($studentLesson['extData']['starInfo']) && !empty($studentLesson['extData']['starInfo'])) ? $studentLesson['extData']['starInfo'] : array();
        //判断是否有星
        $res = array();
        foreach(self::$typeConf as $type => $mapType) {
            if(!isset(self::$mapTypeFunc[$mapType])){
                continue;
            }
            $func = self::$mapTypeFunc[$mapType]['check'];
            $arrParams = array(
                'lessonId'      => $lessonId,
                'uid'           => $uid,
                'nowStarInfo'   => $nowStarInfo,
                'from'          => $from,
            );
            $ret  = call_user_func_array(array(self, $func), array($arrParams));
            if($ret){
                $res[] = $ret;
            }
        }
        $needSendType = array();
        //判断是否需要写入星
        foreach($res as $starInfo){
            if($starInfo['needSend']){
                $needSendType[] = array('type' => $starInfo['type'], 'status' => $starInfo['hasStar']);
            }
        }
        //写入星 发nmq
        if(!empty($needSendType)) {
            $ret = self::sendAddStartMessage($lessonId, $uid, $needSendType);
            if(!$ret){
                Bd_Log::warning("Error[send nmq err] Detail[uid:{$uid} lessonid:{$lessonId}]");
            }
        }
        return $res;
    }

    /**
     * 检查星状态 确认是否有记录
     * @param $lessonId
     * @param $uid
     * @return array
     */
    public static function checkAllStarStatus($lessonId, $uid) {
        $objStudentLesson = new Hkzb_Ds_Fudao_Advanced_StudentLesson();
        $studentLesson = $objStudentLesson->getStudentLesson($uid, $lessonId);
        $nowStarInfo = !empty($studentLesson['extData']['starInfo']) ? $studentLesson['extData']['starInfo'] : array();
        $typeConf = self::$typeConf;
        unset($typeConf[4]);
        $statusList = array();
        foreach(self::$typeConf as $type => $typeName){
            list($status, $hasRecord) = self::judgeStarInfo($nowStarInfo, $type);
            $statusList[] = array(
                'status'    => $status,
                'hasRecord' => $hasRecord,
                'type'      => $type,
            );
        }
        return $statusList;
    }
    /**
     * 加分入口
     * @param $courseId
     * @param $lessonId
     * @param $uid
     * @param $type
     * @return array|bool
     */
    public static function addScore($courseId, $lessonId, $uid, $type){
        if(!isset(self::$scoreTypeConf[$type])) {
            return false;
        }
        $objStudentLesson = new Hkzb_Ds_Fudao_Advanced_StudentLesson();
        $studentLesson = $objStudentLesson->getStudentLesson($uid, $lessonId);
        if(!$studentLesson) {
            Bd_Log::warning("Error[db error---getstudentlessonerr] Detail[uid:{$uid} lessonid:{$lessonId}]");
            return false;
        }
        $nowStarInfo = !empty($studentLesson['extData']['starInfo']) ? $studentLesson['extData']['starInfo'] : array();
        list($status, $hasRecord) = self::judgeStarInfo($nowStarInfo, $type);
        if($status == 2){
            return true;
        }
        //写入领取记录
        $starInfo = array(
            0 => array(
                'type' => $type,
                'status' => 2,
            ),
        );
        $extData         = self::getExtDataParams($studentLesson, $starInfo);
        $objLessonDetail = new Hkzb_Ds_Fudao_LessonDetail();
        $ret = $objLessonDetail->updateLessonExt($uid, $lessonId, $extData);
        if($ret === false){
            return false;
            Bd_Log::warning('db error update studentlesson ext_data');
        }
        $arrScoreParams                = array(
            'uid'      => $uid,
            'courseId' => $courseId,
            'lessonId' => $lessonId,
            'type'     => self::$scoreTypeConf[$type],
        );
        $res = Hkzb_Ds_Fudao_Score::addScore($arrScoreParams);
        if(!$res){
            return false;
        }
        return $res;
    }
    /**
     * 异步写入请求
     * @param $lessonId
     * @param $uid
     * @param $needSendType
     * array(
        0=>array(
            'type'=>1,
            'status'=>1,
            ),
        );
     * @return bool
     */
    public static function sendAddStartMessage($lessonId, $uid, $needSendTypeList)
    {

        $uid      = intval($uid); //必须
        $lessonId = intval($lessonId); //必须
        $typeList     = $needSendTypeList; //必须

        if ($uid <= 0 || $lessonId <= 0 || empty($typeList)) {
            Bd_Log::warning("param error, uid[$uid] lessonId[$lessonId] type[$typeList]");

            return false;
        }
        //发nmq请求
        $objNmq            = new Hk_Service_Nmq();
        $arrParams = array(
            'lessonId' => $lessonId,
            'uid'      => $uid,
            'starInfo' => $typeList,
        );
        $ret               = $objNmq->talkToQcm(Hk_Const_Command::CMD_FUDAO_ADD_STAR, $arrParams);
        if ($ret === false) {
            return false;
        }
        return true;
    }
    //判断状态
    private static function judgeStarInfo($nowStarInfo, $type){
        /*$nowStarInfo = array(
            'finishLesson' => 0,没完成,1已完成，2领取积分
            'finishExercise' => 0,
            'finishExam'    =>0,
            'finishExercise' => 0,
        );*/
        $status = 0;
        $hasRecord = 0;
        if(isset($nowStarInfo[self::$typeConf[$type]])){
            $status = $nowStarInfo[self::$typeConf[$type]];
            $hasRecord = 1;
        }
        return array($status, $hasRecord);
    }
    //获取完课 星状态
    private static function checkFinishLesson($arrParams){
        $needSend = 0;
        $hasStar = 0;
        $lessonId = $arrParams['lessonId'];
        $uid = $arrParams['uid'];
        $nowStarInfo = $arrParams['nowStarInfo'];
        $from = $arrParams['from'];
        list($status, $hasRecord) = self::judgeStarInfo($nowStarInfo, self::FINISH_TYPE_LESSON);
        if($hasRecord ==0){
            //需要写入
            $needSend = 1;
        }
        $hasStar = $status;
        if($from == 1){
            $hasStar = 1;
        }
        return array(
            'needSend' => $needSend,
            'hasStar'  => $hasStar,
            'type'     => self::FINISH_TYPE_LESSON,
        );
    }
    //获取互动题
    private static function checkFinishExercise($arrParams){
        $lessonId = $arrParams['lessonId'];
        $uid = $arrParams['uid'];
        $nowStarInfo = $arrParams['nowStarInfo'];
        $needSend = 0;
        list($status, $hasRecord) = self::judgeStarInfo($nowStarInfo, self::FINISH_TYPE_EXERCISE);
        if($hasRecord == 0){
            $needSend = 1;
        }
        $hasStar = $status;
        if($needSend == 1 && $status == 0) {
            $objAdvancedStudentExercise = new Hkzb_Ds_Fudao_Advanced_StudentExercise();
            $total = $objAdvancedStudentExercise->getExerciseDetailCntByStudentUid($uid, $lessonId, 1);
            if(0 === $total) {
                $hasStar = -1;
            } else {
                $list = $objAdvancedStudentExercise->getExerciseDetailListByStudentUid($uid, $lessonId, 1, array(), 0, $total);
                //判断是否有错题
                foreach ($list as $exerciseDetail) {
                    $hasStar = 1;
                    $remark = $exerciseDetail['remark'];
                    if (empty($remark) || $remark['result'] != 'y') {
                        $hasStar = 0;
                        break;
                    }
                }
            }
        }
        return array(
            'needSend' => $needSend,
            'hasStar'  => $hasStar,
            'type'     => self::FINISH_TYPE_EXERCISE
        );
    }
    //获取课中测试
    private static function checkFinishExam($arrParams){
        $lessonId = $arrParams['lessonId'];
        $uid = $arrParams['uid'];
        $nowStarInfo = $arrParams['nowStarInfo'];
        $needSend = 0;
        list($status, $hasRecord) = self::judgeStarInfo($nowStarInfo, self::FINISH_TYPE_EXAM);
        if($hasRecord == 0){
            $needSend = 1;
        }
        $hasStar = $status;

        //获取一次分数
        $score = 0;
        $objLesson = new Hkzb_Ds_Fudao_Lesson();
        $lessonInfo = $objLesson->getLessonInfo($lessonId);
        if (!isset($lessonInfo['extData']['examInfo'])) {
            $hasStar  = -1;
        } else {
            $examId = $lessonInfo['extData']['examInfo']['examId'];
            $examId = Hk_Util_IdCrypt::decodeQid($examId);
            $objDsUserExam = new Hk_Ds_Practice_UserExam();
            $userInfo = $objDsUserExam->getExamInfoByExamId($uid, '', $examId);
            if(!empty($userInfo)) {
                $userInfo = $userInfo[0];
                $score = strval(round(intval($userInfo['score']) / 10, 1));
                if ($score > self::EXAM_MAX_SCORE && $hasStar == 0) {
                    $hasStar = 1;
                    $needSend = 1;
                }
            } else {
                $score = 0;
            }
        }
        return array(
            'needSend' => $needSend,
            'hasStar'  => $hasStar,
            'type'     => self::FINISH_TYPE_EXAM,
            'score'    => $score,
        );
    }
    //获取课后作业勋章
    private static function checkFinishHomework($arrParams){
        $lessonId = $arrParams['lessonId'];
        $uid = $arrParams['uid'];
        $nowStarInfo = $arrParams['nowStarInfo'];
        $from = $arrParams['from'];
        $needSend = 0;
        list($status, $hasRecord) = self::judgeStarInfo($nowStarInfo, self::FINISH_TYPE_HOMEWORK);
        if($hasRecord == 0){
            $needSend = 1;
        }
        if($needSend == 1){
            $objExerciseDetail = new Hkzb_Ds_Fudao_Advanced_StudentExercise();
            $list = $objExerciseDetail->getExerciseDetailListByStudentUid($uid, $lessonId, 2, array(), 0, 2);
            if(!empty($list)) {
                foreach($list as $info){
                    if(!empty($info['answer'])){
                        $status = 1;
                        break;
                    }
                }
            } else {
                if($from == self::FROM_STATUS_IN_CLASS){//课中请求
                    $objExercise = new Hkzb_Ds_Fudao_Exercise();
                    $cnt = $objExercise->getExerciseCnt(0, $lessonId, Hkzb_Ds_Fudao_Exercise::PURPOSE_SUFCLASS);
                    if($cnt == 0) {
                        $status = -1;
                    }
                }else{
                    $status = -1;
                }
            }
        }

        return array(
            'needSend' => $needSend,
            'hasStar'  => $status,
            'type'     => self::FINISH_TYPE_HOMEWORK
        );
    }
    // 拼装扩展字段的参数 
    public static function getExtDataParams($studentLesson, $starInfo){
        //获取已经点亮的星星
		$nowStarInfo = !empty($studentLesson['extData']['starInfo']) ? $studentLesson['extData']['starInfo'] : array();
        //遍历要更新的参数
        foreach($starInfo as $key => $value){
			$type   = $value['type'];	
            $status = $value['status'];
            if(empty(self::$typeConf[$type])){
                continue;
            }
            $tName  = self::$typeConf[$type];
            if(isset($nowStarInfo[$tName])){
                $nStatus = $nowStarInfo[$tName];
                //当前存储状态和变更的状态做对比
                if($nStatus > $status){
                    $status = $nStatus;
                }
            }
            //存在更新，不存在添加
            $nowStarInfo[$tName] = $status;
        }
        $extData = $studentLesson['extData'];
        $extData['starInfo'] = $nowStarInfo;
        return $extData; 
    }


    /**
     * 通过课程id学生uid获取学生章节信息
     * @param int $studentUid
     * @param int $courseId
     * @param array $arrFields
     * @return array|bool
     */
    public static function getStudentCourseLessonInfo($studentUid=0,$courseId=0,$arrFields=array())
    {
        $studentUid = intval($studentUid);
        $courseId = intval($courseId);

        if(empty($studentUid) || empty($courseId)){
            Bd_Log::warning("param is empty [courseId:$courseId studentUid:{$studentUid}");
            return false;
        }

        //获取章节信息
        $objDsAdvStudentCourse = new Hkzb_Ds_Fudao_Advanced_StudentCourse();
        $studentCourseInfo = $objDsAdvStudentCourse->getCourseInfo($studentUid,$courseId, 1, false);
        if(empty($studentCourseInfo)){
            Bd_Log::warning("studentCourseInfo is empty [courseId:$courseId studentUid:{$studentUid}");
            return false;
        }

        $lessonInfoArr = $studentCourseInfo['allLesson'];
        $lessonIdMap = $lessonInfoArr;
        if(empty($lessonInfoArr)){
            Bd_Log::warning("lessonInfoArr is empty [courseId:$courseId studentUid:{$studentUid}");
            return false;
        }
        $lessonIdArr = array();
        foreach ($lessonInfoArr as $lesson){
            $lessonId = $lesson['lessonId'];
            $lessonIdArr[] = $lessonId;
        }

        if (empty($arrFields)) {
            $arrFields = Hkzb_Dao_Fudao_LessonDetail::$allFields;
        }
        $arrConds = array(
            'studentUid' => $studentUid,
            'lesson_id in (' . join(',', $lessonIdArr) . ')',
            'status'     => array(Hkzb_Ds_Fudao_LessonDetail::STATUS_DELETED, '<'),
        );
        $objDao   = new Hkzb_Dao_Fudao_LessonDetail();

        $stuLessonList = $objDao->getListByConds($studentUid, $arrConds, $arrFields);

        if($stuLessonList === false){
            Bd_Log::warning("getListByConds is false [courseId:$courseId studentUid:{$studentUid}");
            return false;
        }

        $sortMap = array();
        foreach ($stuLessonList as &$lesson){
            $lessonId = $lesson['lessonId'];
            $lesson['lessonInfo'] = $lessonIdMap[$lessonId];
            $sortMap[] = $lesson['startTime'];
        }
        //按时间正序
        array_multisort($sortMap,SORT_ASC,$stuLessonList);

        $arrOutput = array(
            'studentLessonList'=>$stuLessonList,
            'lastLessonId'=>$studentCourseInfo['lastLessonId'],
            'teacherUid'=>$studentCourseInfo['teacherUid'],
            'createTime'=>$studentCourseInfo['createTime'],//报名开始时间
            'report'=>$studentCourseInfo['report'],
            'assistantUid'=>$studentCourseInfo['assistantUid'],
            'curLessonId'=>$studentCourseInfo['curLessonId'],//即将要上的章节id
            'lessonCnt'=>$studentCourseInfo['lessonCnt'],//刚刚结束的章节id
            'courseInfo'=>$studentCourseInfo['courseInfo'],
        );

        return $arrOutput;
    }

    // 判断入口开关 
    // return true or false  true开 false关
    public static function getSplitSwitch($courseId, $courseType){
        $bool = FALSE; 
        if($courseType != Hkzb_Ds_Fudao_Advanced_Course::TYPE_PRIVATE_LONG){
            return $bool; 
        }
        //return TRUE;
        if(in_array($courseId, self::$courseIdsArray)){
            $bool = TRUE; 
        }
        return $bool;
    }

    /**
     * 提交互动题全对，更新答题星
     * Hkzb_Ds_Fudao_Advanced_LessonMap::addStarBySubmitExercise($arrInput);
     */

    static public function addStarBySubmitExercise($arrInput=array())
    {
        if(empty($arrInput)){
            Bd_Log::warning("param error ".json_encode($arrInput));
            return false;
        }

        $exerceList = isset($arrInput['exerciseList']) ? $arrInput['exerciseList'] : array();
        $purpose = isset($arrInput['purpose']) ? $arrInput['purpose'] : 0;
        $courseId = isset($arrInput['courseId']) ? $arrInput['courseId'] : 0;
        $studentUid = isset($arrInput['studentUid']) ? $arrInput['studentUid'] : 0;
        $lessonId = isset($arrInput['lessonId']) ? $arrInput['lessonId'] : 0;

        if(empty($exerceList) || empty($purpose) || empty($courseId) || empty($lessonId) || empty($studentUid)){
            Bd_Log::warning("param error ".var_export($arrInput,true));
            return false;
        }

        //互动题如果全部答对的话，或者提交课后作业的话，就去更新星星状态
        $needSendTypeList = array();
        if($arrInput['purpose'] == Hkzb_Ds_Fudao_Exercise::PURPOSE_INCLASS && self::checkAllExerciseIsPass($arrInput)){
            $needSendTypeList = array(
                0=>array(
                    'type'=>Hkzb_Ds_Fudao_Advanced_LessonMap::FINISH_TYPE_EXERCISE,
                    'status'=>1,
                ),
            );
        }else if($arrInput['purpose'] == Hkzb_Ds_Fudao_Exercise::PURPOSE_SUFCLASS){
            $needSendTypeList = array(
                0=>array(
                    'type'=>Hkzb_Ds_Fudao_Advanced_LessonMap::FINISH_TYPE_HOMEWORK,
                    'status'=>1,
                ),
            );

        }
        //加上开关策略
        $objCourse = new Hkzb_Ds_Fudao_Course();
        $courseInfo = $objCourse->getCourseInfo($courseId,array('type'));
        $lessonMapSwitch = self::getSplitSwitch($courseId,$courseInfo['type']);
        if(!empty($needSendTypeList) && $lessonMapSwitch){
            self::sendAddStartMessage($lessonId, $studentUid, $needSendTypeList);
        }

    }

    static public function checkAllExerciseIsPass($arrInput)
    {
        if(empty($arrInput)){
            return false;
        }
        $lessonId = $arrInput['lessonId'];
        //如果章节不是课后的状态，返回false
        $objDsLesson = new Hkzb_Ds_Fudao_Lesson();
        $lessonInfo = $objDsLesson->getLessonInfo($lessonId);
        if(empty($lessonInfo)){
            Bd_Log::warning("param err:".var_export($arrInput,true));
            return false;
        }
        if($lessonInfo['status'] != Hkzb_Ds_Fudao_Lesson::STATUS_CLASS_STOPED){
            return false;
        }


        $idList = array();
        foreach($arrInput['exerciseList'] as $v){
            $idList[] = $v['exerciseId'];
        }
        $exerciseList = $arrInput['exerciseList'];
        $rightAnswerArr = self::getRightAnswer($lessonId,$idList);
        $isPass = 1;
        foreach ($exerciseList as $exercise){
            $exerciseId = $exercise['exerciseId'];
            $result = $exercise['result'];
            $rightAnswer = $rightAnswerArr[$exerciseId]['answer'];
            $type = $rightAnswerArr[$exerciseId]['type'];
            //单选题
            if($type == Hkzb_Ds_Fudao_Exercise::TYPE_SINGLE){
                if($result != $rightAnswer){
                    $isPass = 0;
                    break;
                }
            }
            //多选题
            if($type == Hkzb_Ds_Fudao_Exercise::TYPE_MULTI){
                $arrLeft = explode(',', $result);
                $arrRight = explode(',', $rightAnswer);
                sort($arrLeft);
                sort($arrRight);
                if($arrLeft != $arrRight){
                    $isPass = 0;
                    break;
                }
            }

        }

        if(!$isPass){
            Hk_Util_Log::setLog('userNotIsPass',"not is pass :" .var_export($rightAnswerArr,true)."use answer:" .var_export($exerciseList,true));
            return false;
        }

        return true;
    }


    static private function getRightAnswer($lessonId, $idList){
        $rightAnswers = array();
        $objAdvancedExercise = new Hkzb_Ds_Fudao_Advanced_Exercise();
        $res = $objAdvancedExercise->getExerciseListByIds($lessonId, $idList, array('exerciseId', 'type', 'answer'));
        if (!$res || count($res) <> count($idList)) {
            Bd_Log::warning("param error " . implode(',', $idList));
        }
        foreach($res as $v){
            $rightAnswers[$v['exerciseId']] = array(
                'exerciseId' => $v['exerciseId'],
                'type'       => $v['type'],
                'answer'     => trim($v['answer']),
            );
        }

        return $rightAnswers;
    }

}

