<?php
/***************************************************************************
 *
 * Copyright (c) 2015 zybang.com, Inc. All Rights Reserved
 *
 **************************************************************************/



/**
 * @file AppleCourse.php
 * <AUTHOR>
 * @date 2015/12/14 20:30:15
 * @brief 课程检查
 *
 **/

class Hkzb_Ds_Fudao_CourseCheck {
      //状态
      const STATUS_OK              = 0; // 已生效 (管理员复核同意，组长复核同意)
      const STATUS_REVOKE          = 1; // 已撤销
      const STATUS_ADMIN_UN_AGREE  = 2; // 管理员复核不同意
      const STATUS_LEADER_CHECKING = 3; // 组长复核中
      const STATUS_LEADER_UN_AGREE = 4; // 组长复核不同意
      const STATUS_CHECK           = 5; // 违规添加
      const STATUS_ADMIN_UN_EDIT   = 6;  //管理员修改
      const CHECK_TYPE_CHAPTER=1;//章节
      const CHECK_TYPE_HANDOUT=2;//专题讲义


            static $STATUS_ARRAY   = array(
          self::STATUS_OK              => '同意',
          self::STATUS_REVOKE          => '已撤销',
          self::STATUS_ADMIN_UN_AGREE  => '不同意',
          self::STATUS_LEADER_CHECKING => '组长处理中',
          self::STATUS_LEADER_UN_AGREE => '组长不同意',
          self::STATUS_CHECK           => '已检查',
          self::STATUS_ADMIN_UN_EDIT   => '规则修改'
      );

    const ALL_FIELDS = 'id,courseId,type,lessonId,teacherUid,rule,score,status,createTime,updateTime,operatorUid,operator,extData,ruleOld,checkType,checkorName,comfirmTime';
    private $objDaoCourseCheck;
    //排序参数
    private $orderBy = array('create_time');

    //临时存储,扣分规则,之后存db
    public static $ruleCategory = array (
        array (
            'catId'   => '1',
            'catName' => '讲义'
        ),
        array (
            'catId'   => '2',
            'catName' => '课程内容'
        ),
        array (
            'catId'   => '3',
            'catName' => '老师行为'
        ),
        array (
            'catId'   => '4',
            'catName' => '其他'
        ),
    );

    //临时存储,扣分规则,之后存db
    public static $ruleSubject = array (
        array (
            'subjectId'   => '1',
            'name'        => '理科'
        ),
        array (
            'subjectId'   => '2',
            'name'        => '语文'
        ),
        array (
            'subjectId'   => '3',
            'name'        => '英语'
        ),
    );


    /**
     * 每次规则变动，需在现有规则后新增，旧版对应规则delete, 注意修改sort
     * @var array
     *  2017.6.2 新增规则 [40]
     *  2017.7.4 新增规则 [41-48]
     *  2017.7.13 废弃之前所有规则，本次规则[49-82]
     *  2017.9.14 废弃之前规则 本次规则[83-202] 加入学科分类 subject 1 理科 2 语文 3 英语
     */
    public static $ruleDetail = array (
        0 =>
            array (
                'ruleId' => '1',
                'catId' => '1',
                'ruleName' => '讲义未及时上传',
                'score' => '0.05',
                'remark' => '',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        1 =>
            array (
                'ruleId' => '2',
                'catId' => '1',
                'ruleName' => '上传白板讲义',
                'score' => '0.05',
                'remark' => '',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        2 =>
            array (
                'ruleId' => '3',
                'catId' => '1',
                'ruleName' => '上传讲义错误（不是本次课讲义）',
                'score' => '0.05',
                'remark' => '',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        3 =>
            array (
                'ruleId' => '4',
                'catId' => '2',
                'ruleName' => '讲义中有错别字/单词/符号',
                'score' => '0.1',
                'remark' => '',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        4 =>
            array (
                'ruleId' => '5',
                'catId' => '2',
                'ruleName' => '内容或排版错误',
                'score' => '0.05',
                'remark' => '1、内容错误：缺少文字、数学符号错误、缺少配图等影响到教学效果的；2、排版出错：排版乱、字号过小、页面文字过多、字体模糊、动态内容展现错位等影响学生体验的。',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        5 =>
            array (
                'ruleId' => '6',
                'catId' => '1',
                'ruleName' => '讲义尺寸不符合规范',
                'score' => '0.05',
                'remark' => '讲义合格尺寸：16:9',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        6 =>
            array (
                'ruleId' => '7',
                'catId' => '2',
                'ruleName' => '讲义格式错误',
                'score' => '0.05',
                'remark' => '讲义正确格式：ppt或pptx',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        7 =>
            array (
                'ruleId' => '8',
                'catId' => '2',
                'ruleName' => '课堂出现知识性错误（讲错知识点、讲错习题等）',
                'score' => '0.5',
                'remark' => '老师出现严重错误将被解约',
                'sort' => '0',
                'checkStatus' => '4',
                'delete' => '1',
                'subject' => '0',
            ),
        8 =>
            array (
                'ruleId' => '9',
                'catId' => '2',
                'ruleName' => '课堂内容出现暴力、恐怖、色情等违反法律法规的内容',
                'score' => '1',
                'remark' => '',
                'sort' => '0',
                'checkStatus' => '4',
                'delete' => '1',
                'subject' => '0',
            ),
        9 =>
            array (
                'ruleId' => '10',
                'catId' => '2',
                'ruleName' => '板书字迹不整齐或不清晰，板书布局不合理或不美观',
                'score' => '0.1',
                'remark' => '',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        10 =>
            array (
                'ruleId' => '11',
                'catId' => '3',
                'ruleName' => '未悬挂作业帮背景布或不开视频',
                'score' => '0.05',
                'remark' => '背景布必须干净、整齐、美观',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        11 =>
            array (
                'ruleId' => '12',
                'catId' => '3',
                'ruleName' => '迟到',
                'score' => '0.2',
                'remark' => '扣减系数0.2',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        12 =>
            array (
                'ruleId' => '13',
                'catId' => '3',
                'ruleName' => '早退',
                'score' => '0.2',
                'remark' => '提前下课一律视为早退',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        13 =>
            array (
                'ruleId' => '14',
                'catId' => '3',
                'ruleName' => '课堂抽烟、吃东西、打电话等做与上课无关的事情',
                'score' => '1',
                'remark' => '喝水属于正常行为',
                'sort' => '0',
                'checkStatus' => '4',
                'delete' => '1',
                'subject' => '0',
            ),
        14 =>
            array (
                'ruleId' => '15',
                'catId' => '3',
                'ruleName' => '在课堂或官方Q群辱骂、讽刺学生、家长和老师',
                'score' => '1',
                'remark' => '',
                'sort' => '0',
                'checkStatus' => '4',
                'delete' => '1',
                'subject' => '0',
            ),
        15 =>
            array (
                'ruleId' => '16',
                'catId' => '3',
                'ruleName' => '在课堂或官方Q群宣传个人或其他机构招生信息',
                'score' => '0.5',
                'remark' => '在课堂或官方Q群对学生宣传个人手机号、QQ号，微信、微博等个人或其他机构招生信息',
                'sort' => '0',
                'checkStatus' => '4',
                'delete' => '1',
                'subject' => '0',
            ),
        16 =>
            array (
                'ruleId' => '17',
                'catId' => '3',
                'ruleName' => '课堂出现抱怨、情绪失控等负能量的行为举止',
                'score' => '1',
                'remark' => '',
                'sort' => '0',
                'checkStatus' => '4',
                'delete' => '1',
                'subject' => '0',
            ),
        17 =>
            array (
                'ruleId' => '18',
                'catId' => '3',
                'ruleName' => '课堂或官方Q群等地方出现有明显导流倾向的行为',
                'score' => '1',
                'remark' => '',
                'sort' => '0',
                'checkStatus' => '4',
                'delete' => '1',
                'subject' => '0',
            ),
        18 =>
            array (
                'ruleId' => '19',
                'catId' => '3',
                'ruleName' => '拖堂',
                'score' => '0.1',
                'remark' => '拖延≥15分钟为拖堂',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        19 =>
            array (
                'ruleId' => '20',
                'catId' => '3',
                'ruleName' => '旷课',
                'score' => '0.5',
                'remark' => '因为老师个人原因没有上课',
                'sort' => '0',
                'checkStatus' => '4',
                'delete' => '1',
                'subject' => '0',
            ),
        20 =>
            array (
                'ruleId' => '21',
                'catId' => '4',
                'ruleName' => '因老师问题导致的课程重开',
                'score' => '0.1',
                'remark' => '老师原因导致的课程需要重开',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        21 =>
            array (
                'ruleId' => '22',
                'catId' => '4',
                'ruleName' => '学员或家长投诉',
                'score' => '0',
                'remark' => '按上列标准对应扣减或视投诉情况而定',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        22 =>
            array (
                'ruleId' => '23',
                'catId' => '1',
                'ruleName' => '专题课教师版讲义未提前24小时上传',
                'score' => '0.05',
                'remark' => '应在开课前24小时上传教师版讲义',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        23 =>
            array (
                'ruleId' => '24',
                'catId' => '1',
                'ruleName' => '讲义不合格（讲义应包含主题逻辑、知识框架）',
                'score' => '0.1',
                'remark' => '',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        24 =>
            array (
                'ruleId' => '25',
                'catId' => '1',
                'ruleName' => '授课内容与讲义内容不符',
                'score' => '0.1',
                'remark' => '',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        25 =>
            array (
                'ruleId' => '26',
                'catId' => '2',
                'ruleName' => '讲义存在缺字、多字、缺图等影响教学效果的内容错误',
                'score' => '0.05',
                'remark' => '',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        26 =>
            array (
                'ruleId' => '27',
                'catId' => '2',
                'ruleName' => '讲义排版混乱：排版乱、页面文字过多、动态内容展现错位等影响学生体验的',
                'score' => '0.05',
                'remark' => '',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        27 =>
            array (
                'ruleId' => '28',
                'catId' => '2',
                'ruleName' => '讲义中图片模糊、字体过小，影响学生体验',
                'score' => '0.05',
                'remark' => '',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        28 =>
            array (
                'ruleId' => '29',
                'catId' => '2',
                'ruleName' => '课堂内容出现暴力、恐怖、色情、与社会主流观点严重不符或不当政治倾向言论等违反法律法规的内容',
                'score' => '1',
                'remark' => '',
                'sort' => '0',
                'checkStatus' => '4',
                'delete' => '1',
                'subject' => '0',
            ),
        29 =>
            array (
                'ruleId' => '30',
                'catId' => '3',
                'ruleName' => '未悬挂作业帮背景布（已向领导或师资备案）',
                'score' => '0',
                'remark' => '',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        30 =>
            array (
                'ruleId' => '31',
                'catId' => '3',
                'ruleName' => '未悬挂作业帮背景布或背景布不整洁，展现不清楚',
                'score' => '0.05',
                'remark' => '上课时应在头像后方悬挂背景布，背景布应干净、整齐，展现清晰。',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        31 =>
            array (
                'ruleId' => '32',
                'catId' => '3',
                'ruleName' => '不开摄像头（已向领导或师资备案）',
                'score' => '0',
                'remark' => '',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        32 =>
            array (
                'ruleId' => '33',
                'catId' => '3',
                'ruleName' => '不开摄像头',
                'score' => '0.05',
                'remark' => '摄像头应在上课过程中打开，且开启时长不应低于整体时长的50%',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        33 =>
            array (
                'ruleId' => '34',
                'catId' => '3',
                'ruleName' => '在课堂或官方群宣传个人信息',
                'score' => '0.5',
                'remark' => '按上列标准对应扣减或视投诉情况而定',
                'sort' => '0',
                'checkStatus' => '4',
                'delete' => '1',
                'subject' => '0',
            ),
        34 =>
            array (
                'ruleId' => '35',
                'catId' => '3',
                'ruleName' => '在课堂或官方Q群宣传其他机构招生信息',
                'score' => '0.5',
                'remark' => '在课堂和官方q群仅允许宣传作业帮课程',
                'sort' => '0',
                'checkStatus' => '4',
                'delete' => '1',
                'subject' => '0',
            ),
        35 =>
            array (
                'ruleId' => '36',
                'catId' => '3',
                'ruleName' => '拖堂超过15分钟',
                'score' => '0.1',
                'remark' => '',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        36 =>
            array (
                'ruleId' => '37',
                'catId' => '4',
                'ruleName' => '其他影响学生学习体验或不符合作业帮规定的行为',
                'score' => '0',
                'remark' => '',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        37 =>
            array (
                'ruleId' => '38',
                'catId' => '1',
                'ruleName' => '专题课教师版讲义未提前12小时上传',
                'score' => '0.05',
                'remark' => '1.应在开课前12小时上传教师版讲义,2.以最后一次上传时间为准',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        38 =>
            array (
                'ruleId' => '39',
                'catId' => '1',
                'ruleName' => '讲义页面未放置作业帮一课官方logo',
                'score' => '0.05',
                'remark' => '每一页讲义应在明显位置放置作业帮一课官方logo，logo可清晰识别。',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        39 =>
            array (
                'ruleId' => '40',
                'catId' => '3',
                'ruleName' => '未使用新版背景布',
                'score' => '0.05',
                'remark' => '应悬挂最新版背景布',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        40 =>
            array (
                'ruleId' => '41',
                'catId' => '1',
                'ruleName' => '专题课教师版讲义未提前12小时上传（班课讲义由教务组上传）',
                'score' => '0.05',
                'remark' => '1.应在开课前12小时上传教师版讲义。2.以最后一次上传时间为准。3.每月可免责一次。',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        41 =>
            array (
                'ruleId' => '42',
                'catId' => '2',
                'ruleName' => '讲义中有错别字/单词/符号，但不影响阅读及题意理解',
                'score' => '0.02',
                'remark' => '',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        42 =>
            array (
                'ruleId' => '43',
                'catId' => '2',
                'ruleName' => '讲义中有错别字/单词/符号，影响阅读及题意理解',
                'score' => '0.1',
                'remark' => '',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        43 =>
            array (
                'ruleId' => '44',
                'catId' => '2',
                'ruleName' => '讲义存在缺字、多字、缺图等影响教学效果的内容错误',
                'score' => '0.05',
                'remark' => '每月可免责一次',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        44 =>
            array (
                'ruleId' => '45',
                'catId' => '3',
                'ruleName' => '未悬挂背景布或悬挂旧版背景布',
                'score' => '0.05',
                'remark' => '每月可免责一次',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        45 =>
            array (
                'ruleId' => '46',
                'catId' => '3',
                'ruleName' => '悬挂背景布但展现效果不好或光线差',
                'score' => '0.02',
                'remark' => '1.上课时应在头像后方悬挂背景布，背景布应干净、整齐，展现清晰。2.每月可免责一次。',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        46 =>
            array (
                'ruleId' => '47',
                'catId' => '3',
                'ruleName' => '授课语言不文明',
                'score' => '0.05',
                'remark' => '教师课上出现不雅口头语如“我靠”“干死她”“傻逼了吧”等，本身无恶意',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        47 =>
            array (
                'ruleId' => '48',
                'catId' => '3',
                'ruleName' => '拖堂超过15分钟',
                'score' => '0.1',
                'remark' => '1.拖堂时长=实际下课时间-应下课时间。2.每月可免责一次。',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        48 =>
            array (
                'ruleId' => '49',
                'catId' => '1',
                'ruleName' => '讲义页面未放置作业帮一课官方logo',
                'score' => '5',
                'remark' => '每一页讲义应在明显位置放置作业帮一课官方logo，logo可清晰识别。',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        49 =>
            array (
                'ruleId' => '50',
                'catId' => '1',
                'ruleName' => '上传讲义错误（不是本次课讲义）',
                'score' => '5',
                'remark' => '',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        50 =>
            array (
                'ruleId' => '51',
                'catId' => '1',
                'ruleName' => '讲义不合格（讲义应包含主题逻辑、知识框架）',
                'score' => '10',
                'remark' => '',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        51 =>
            array (
                'ruleId' => '52',
                'catId' => '1',
                'ruleName' => '授课内容与讲义内容不符',
                'score' => '10',
                'remark' => '',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        52 =>
            array (
                'ruleId' => '53',
                'catId' => '1',
                'ruleName' => '讲义尺寸不符合规范',
                'score' => '5',
                'remark' => '讲义合格尺寸：16:9',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        53 =>
            array (
                'ruleId' => '54',
                'catId' => '1',
                'ruleName' => '专题课教师版讲义未提前12小时上传（班课讲义由教务组上传）',
                'score' => '5',
                'remark' => '1.应在开课前12小时上传教师版讲义。2.以最后一次上传时间为准。3.每月可免责一次。',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        54 =>
            array (
                'ruleId' => '55',
                'catId' => '2',
                'ruleName' => '讲义中有错别字/单词/符号，但不影响阅读及题意理解',
                'score' => '2',
                'remark' => '',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        55 =>
            array (
                'ruleId' => '56',
                'catId' => '2',
                'ruleName' => '讲义中有错别字/单词/符号，影响阅读及题意理解',
                'score' => '10',
                'remark' => '',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        56 =>
            array (
                'ruleId' => '57',
                'catId' => '2',
                'ruleName' => '讲义排版混乱：排版乱、页面文字过多、动态内容展现错位等影响学生体验的',
                'score' => '5',
                'remark' => '',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        57 =>
            array (
                'ruleId' => '58',
                'catId' => '2',
                'ruleName' => '讲义中图片模糊、字体过小，影响学生体验',
                'score' => '5',
                'remark' => '',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        58 =>
            array (
                'ruleId' => '59',
                'catId' => '2',
                'ruleName' => '讲义存在缺字、多字、缺图等影响教学效果的内容错误',
                'score' => '5',
                'remark' => '每月可免责一次',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        59 =>
            array (
                'ruleId' => '60',
                'catId' => '2',
                'ruleName' => '板书字迹不整齐或不清晰，板书布局不合理或不美观',
                'score' => '10',
                'remark' => '',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        60 =>
            array (
                'ruleId' => '61',
                'catId' => '2',
                'ruleName' => '课堂出现知识性错误（讲错知识点、讲错习题等）',
                'score' => '50',
                'remark' => '老师出现严重错误将被解约',
                'sort' => '0',
                'checkStatus' => '4',
                'delete' => '1',
                'subject' => '0',
            ),
        61 =>
            array (
                'ruleId' => '62',
                'catId' => '3',
                'ruleName' => '未悬挂背景布或悬挂旧版背景布',
                'score' => '5',
                'remark' => '每月可免责一次',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        62 =>
            array (
                'ruleId' => '63',
                'catId' => '3',
                'ruleName' => '悬挂背景布但展现效果不好或光线差',
                'score' => '2',
                'remark' => '1.上课时应在头像后方悬挂背景布，背景布应干净、整齐，展现清晰。2.每月可免责一次。',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        63 =>
            array (
                'ruleId' => '64',
                'catId' => '3',
                'ruleName' => '不开摄像头',
                'score' => '5',
                'remark' => '摄像头应在上课过程中打开，且开启时长不应低于整体时长的50%',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        64 =>
            array (
                'ruleId' => '65',
                'catId' => '3',
                'ruleName' => '不开摄像头（已向领导或师资备案）',
                'score' => '0',
                'remark' => '',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        65 =>
            array (
                'ruleId' => '66',
                'catId' => '3',
                'ruleName' => '未悬挂作业帮背景布（已向领导或师资备案）',
                'score' => '0',
                'remark' => '',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        66 =>
            array (
                'ruleId' => '67',
                'catId' => '3',
                'ruleName' => '授课语言不文明',
                'score' => '5',
                'remark' => '教师课上出现不雅口头语如“我靠”“干死她”“傻逼了吧”等，本身无恶意',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        67 =>
            array (
                'ruleId' => '68',
                'catId' => '3',
                'ruleName' => '课堂出现抱怨、情绪失控等负能量的行为举止',
                'score' => '100',
                'remark' => '',
                'sort' => '0',
                'checkStatus' => '4',
                'delete' => '1',
                'subject' => '0',
            ),
        68 =>
            array (
                'ruleId' => '69',
                'catId' => '2',
                'ruleName' => '课堂内容出现暴力、恐怖、色情、与社会主流观点严重不符或不当政治倾向言论等违反法律法规的内容',
                'score' => '100',
                'remark' => '',
                'sort' => '0',
                'checkStatus' => '4',
                'delete' => '1',
                'subject' => '0',
            ),
        69 =>
            array (
                'ruleId' => '70',
                'catId' => '3',
                'ruleName' => '在课堂或官方Q群辱骂、讽刺学生、家长和老师',
                'score' => '100',
                'remark' => '',
                'sort' => '0',
                'checkStatus' => '4',
                'delete' => '1',
                'subject' => '0',
            ),
        70 =>
            array (
                'ruleId' => '71',
                'catId' => '3',
                'ruleName' => '课堂抽烟、吃东西、打电话等做与上课无关的事情',
                'score' => '100',
                'remark' => '喝水属于正常行为',
                'sort' => '0',
                'checkStatus' => '4',
                'delete' => '1',
                'subject' => '0',
            ),
        71 =>
            array (
                'ruleId' => '72',
                'catId' => '3',
                'ruleName' => '在课堂或官方群宣传个人信息',
                'score' => '50',
                'remark' => '按上列标准对应扣减或视投诉情况而定',
                'sort' => '0',
                'checkStatus' => '4',
                'delete' => '1',
                'subject' => '0',
            ),
        72 =>
            array (
                'ruleId' => '73',
                'catId' => '3',
                'ruleName' => '在课堂或官方Q群宣传其他机构招生信息',
                'score' => '50',
                'remark' => '在课堂和官方q群仅允许宣传作业帮课程',
                'sort' => '0',
                'checkStatus' => '4',
                'delete' => '1',
                'subject' => '0',
            ),
        73 =>
            array (
                'ruleId' => '74',
                'catId' => '3',
                'ruleName' => '课堂或官方Q群等地方出现有明显导流倾向的行为',
                'score' => '100',
                'remark' => '',
                'sort' => '0',
                'checkStatus' => '4',
                'delete' => '1',
                'subject' => '0',
            ),
        74 =>
            array (
                'ruleId' => '75',
                'catId' => '3',
                'ruleName' => '迟到',
                'score' => '20',
                'remark' => '扣减系数0.2',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        75 =>
            array (
                'ruleId' => '76',
                'catId' => '3',
                'ruleName' => '早退',
                'score' => '20',
                'remark' => '提前下课一律视为早退',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        76 =>
            array (
                'ruleId' => '77',
                'catId' => '3',
                'ruleName' => '拖堂超过15分钟',
                'score' => '10',
                'remark' => '1.拖堂时长=实际下课时间-应下课时间。2.每月可免责一次。',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        77 =>
            array (
                'ruleId' => '78',
                'catId' => '3',
                'ruleName' => '拖堂超过30分钟',
                'score' => '100',
                'remark' => '',
                'sort' => '0',
                'checkStatus' => '4',
                'delete' => '1',
                'subject' => '0',
            ),
        78 =>
            array (
                'ruleId' => '79',
                'catId' => '3',
                'ruleName' => '旷课',
                'score' => '50',
                'remark' => '因为老师个人原因没有上课',
                'sort' => '0',
                'checkStatus' => '4',
                'delete' => '1',
                'subject' => '0',
            ),
        79 =>
            array (
                'ruleId' => '80',
                'catId' => '4',
                'ruleName' => '因老师问题导致的课程重开',
                'score' => '10',
                'remark' => '老师原因导致的课程需要重开',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        80 =>
            array (
                'ruleId' => '81',
                'catId' => '4',
                'ruleName' => '学员或家长投诉',
                'score' => '0',
                'remark' => '按上列标准对应扣减或视投诉情况而定',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        81 =>
            array (
                'ruleId' => '82',
                'catId' => '4',
                'ruleName' => '其他影响学生学习体验或不符合作业帮规定的行为',
                'score' => '0',
                'remark' => '',
                'sort' => '0',
                'checkStatus' => '3',
                'delete' => '1',
                'subject' => '0',
            ),
        82 =>
            array (
                'ruleId' => '83',
                'catId' => '1',
                'ruleName' => '讲义中有错别字/符号，多字，少字，不影响阅读及题意理解',
                'score' => '2',
                'remark' => '',
                'sort' => '1',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '1',
            ),
        83 =>
            array (
                'ruleId' => '84',
                'catId' => '1',
                'ruleName' => '原始讲义有错误，但是直播前手工修改',
                'score' => '2',
                'remark' => '',
                'sort' => '2',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '1',
            ),
        84 =>
            array (
                'ruleId' => '85',
                'catId' => '1',
                'ruleName' => '讲义排版乱、页面文字过多、动态内容展现错位等影响学生体验的情况',
                'score' => '5',
                'remark' => '',
                'sort' => '3',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '1',
            ),
        85 =>
            array (
                'ruleId' => '86',
                'catId' => '1',
                'ruleName' => '讲义中图片模糊、字体过小，影响学生体验',
                'score' => '5',
                'remark' => '',
                'sort' => '4',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '1',
            ),
        86 =>
            array (
                'ruleId' => '87',
                'catId' => '1',
                'ruleName' => '讲义中有错别字/错误符号影响题目理解或影响正常解答',
                'score' => '10',
                'remark' => '',
                'sort' => '5',
                'checkStatus' => '3',
                'delete' => '0',
                'subject' => '1',
            ),
        87 =>
            array (
                'ruleId' => '88',
                'catId' => '1',
                'ruleName' => '讲义不合格（讲义应包含主题逻辑、知识框架）',
                'score' => '10',
                'remark' => '',
                'sort' => '6',
                'checkStatus' => '3',
                'delete' => '0',
                'subject' => '1',
            ),
        88 =>
            array (
                'ruleId' => '89',
                'catId' => '1',
                'ruleName' => '讲义页面未放置作业帮一课官方logo或logo错误',
                'score' => '5',
                'remark' => '',
                'sort' => '7',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '1',
            ),
        89 =>
            array (
                'ruleId' => '90',
                'catId' => '1',
                'ruleName' => '上传讲义错误（不是本次课讲义）',
                'score' => '5',
                'remark' => '',
                'sort' => '8',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '1',
            ),
        90 =>
            array (
                'ruleId' => '91',
                'catId' => '1',
                'ruleName' => '讲义尺寸不符合规范',
                'score' => '5',
                'remark' => '',
                'sort' => '9',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '1',
            ),
        91 =>
            array (
                'ruleId' => '92',
                'catId' => '1',
                'ruleName' => '讲义格式错误（不是PPT或PPTx）',
                'score' => '5',
                'remark' => '',
                'sort' => '10',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '1',
            ),
        92 =>
            array (
                'ruleId' => '93',
                'catId' => '1',
                'ruleName' => '教师版讲义未提前12小时(工作时间）上传合格讲义',
                'score' => '5',
                'remark' => '',
                'sort' => '11',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '1',
            ),
        93 =>
            array (
                'ruleId' => '94',
                'catId' => '2',
                'ruleName' => '授课内容与讲义内容不符',
                'score' => '10',
                'remark' => '',
                'sort' => '12',
                'checkStatus' => '3',
                'delete' => '0',
                'subject' => '1',
            ),
        94 =>
            array (
                'ruleId' => '95',
                'catId' => '2',
                'ruleName' => '板书字迹不整齐或不清晰，板书布局不合理或不美观',
                'score' => '10',
                'remark' => '',
                'sort' => '13',
                'checkStatus' => '3',
                'delete' => '0',
                'subject' => '1',
            ),
        95 =>
            array (
                'ruleId' => '96',
                'catId' => '2',
                'ruleName' => '出现板书上的笔误（讲对，写错了）',
                'score' => '2',
                'remark' => '',
                'sort' => '14',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '1',
            ),
        96 =>
            array (
                'ruleId' => '97',
                'catId' => '2',
                'ruleName' => '课上试题/知识讲解错误,后修正',
                'score' => '10',
                'remark' => '',
                'sort' => '15',
                'checkStatus' => '3',
                'delete' => '0',
                'subject' => '1',
            ),
        97 =>
            array (
                'ruleId' => '98',
                'catId' => '2',
                'ruleName' => '出现单纯的计算错误（讲解方法无错误）',
                'score' => '10',
                'remark' => '',
                'sort' => '16',
                'checkStatus' => '3',
                'delete' => '0',
                'subject' => '1',
            ),
        98 =>
            array (
                'ruleId' => '99',
                'catId' => '2',
                'ruleName' => '课堂出现知识性错误（讲错知识点、习题等未能当堂改正）',
                'score' => '50',
                'remark' => '',
                'sort' => '17',
                'checkStatus' => '4',
                'delete' => '0',
                'subject' => '1',
            ),
        99 =>
            array (
                'ruleId' => '100',
                'catId' => '3',
                'ruleName' => '悬挂背景布但展现效果不好或光线差',
                'score' => '2',
                'remark' => '',
                'sort' => '18',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '1',
            ),
        100 =>
            array (
                'ruleId' => '101',
                'catId' => '3',
                'ruleName' => '未悬挂作业帮背景布',
                'score' => '5',
                'remark' => '',
                'sort' => '19',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '1',
            ),
        101 =>
            array (
                'ruleId' => '102',
                'catId' => '3',
                'ruleName' => '教师不文明用语',
                'score' => '5',
                'remark' => '',
                'sort' => '20',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '1',
            ),
        102 =>
            array (
                'ruleId' => '103',
                'catId' => '3',
                'ruleName' => '不开摄像头',
                'score' => '5',
                'remark' => '',
                'sort' => '21',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '1',
            ),
        103 =>
            array (
                'ruleId' => '104',
                'catId' => '3',
                'ruleName' => '拖堂超过15分钟',
                'score' => '10',
                'remark' => '',
                'sort' => '22',
                'checkStatus' => '3',
                'delete' => '0',
                'subject' => '1',
            ),
        104 =>
            array (
                'ruleId' => '105',
                'catId' => '3',
                'ruleName' => '拖堂超过30分钟',
                'score' => '100',
                'remark' => '',
                'sort' => '23',
                'checkStatus' => '4',
                'delete' => '0',
                'subject' => '1',
            ),
        105 =>
            array (
                'ruleId' => '106',
                'catId' => '3',
                'ruleName' => '因老师问题导致的课程重开',
                'score' => '10',
                'remark' => '',
                'sort' => '24',
                'checkStatus' => '3',
                'delete' => '0',
                'subject' => '1',
            ),
        106 =>
            array (
                'ruleId' => '107',
                'catId' => '3',
                'ruleName' => '迟到',
                'score' => '20',
                'remark' => '',
                'sort' => '25',
                'checkStatus' => '3',
                'delete' => '0',
                'subject' => '1',
            ),
        107 =>
            array (
                'ruleId' => '108',
                'catId' => '3',
                'ruleName' => '早退',
                'score' => '20',
                'remark' => '',
                'sort' => '26',
                'checkStatus' => '3',
                'delete' => '0',
                'subject' => '1',
            ),
        108 =>
            array (
                'ruleId' => '109',
                'catId' => '3',
                'ruleName' => '旷课',
                'score' => '50',
                'remark' => '',
                'sort' => '27',
                'checkStatus' => '4',
                'delete' => '0',
                'subject' => '1',
            ),
        109 =>
            array (
                'ruleId' => '110',
                'catId' => '3',
                'ruleName' => '在课堂或官方群宣传个人信息',
                'score' => '50',
                'remark' => '',
                'sort' => '28',
                'checkStatus' => '4',
                'delete' => '0',
                'subject' => '1',
            ),
        110 =>
            array (
                'ruleId' => '111',
                'catId' => '3',
                'ruleName' => '在课堂或官方Q群宣传其他机构招生信息',
                'score' => '50',
                'remark' => '',
                'sort' => '29',
                'checkStatus' => '4',
                'delete' => '0',
                'subject' => '1',
            ),
        111 =>
            array (
                'ruleId' => '112',
                'catId' => '3',
                'ruleName' => '课堂出现抱怨、情绪失控等负能量的行为举止',
                'score' => '100',
                'remark' => '',
                'sort' => '30',
                'checkStatus' => '4',
                'delete' => '0',
                'subject' => '1',
            ),
        112 =>
            array (
                'ruleId' => '113',
                'catId' => '3',
                'ruleName' => '课堂内容出现暴力、恐怖、色情、与社会主流观点严重不符或不当政治倾向言论等违反法律法规的内容',
                'score' => '100',
                'remark' => '',
                'sort' => '31',
                'checkStatus' => '4',
                'delete' => '0',
                'subject' => '1',
            ),
        113 =>
            array (
                'ruleId' => '114',
                'catId' => '3',
                'ruleName' => '在课堂或官方Q群辱骂、讽刺学生、家长和老师',
                'score' => '100',
                'remark' => '',
                'sort' => '32',
                'checkStatus' => '4',
                'delete' => '0',
                'subject' => '1',
            ),
        114 =>
            array (
                'ruleId' => '115',
                'catId' => '3',
                'ruleName' => '课堂或官方Q群等地方出现有明显导流倾向的行为',
                'score' => '100',
                'remark' => '',
                'sort' => '33',
                'checkStatus' => '4',
                'delete' => '0',
                'subject' => '1',
            ),
        115 =>
            array (
                'ruleId' => '116',
                'catId' => '3',
                'ruleName' => '课堂抽烟、吃东西、打电话等做与上课无关的事情',
                'score' => '100',
                'remark' => '',
                'sort' => '34',
                'checkStatus' => '4',
                'delete' => '0',
                'subject' => '1',
            ),
        116 =>
            array (
                'ruleId' => '117',
                'catId' => '4',
                'ruleName' => '学员或家长投诉，经核实按照上述规则惩罚',
                'score' => '0',
                'remark' => '',
                'sort' => '35',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '1',
            ),
        117 =>
            array (
                'ruleId' => '118',
                'catId' => '4',
                'ruleName' => '其他影响学生学习体验或不符合作业帮规定的行为',
                'score' => '0',
                'remark' => '',
                'sort' => '36',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '1',
            ),
        118 =>
            array (
                'ruleId' => '119',
                'catId' => '1',
                'ruleName' => '教师课件中原始文案出现错字、别字、多字、少字现象。（不影响正常阅读理解或题目解答）',
                'score' => '2',
                'remark' => '',
                'sort' => '37',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '2',
            ),
        119 =>
            array (
                'ruleId' => '120',
                'catId' => '1',
                'ruleName' => '原始讲义有错误，但是直播前手工修改',
                'score' => '2',
                'remark' => '',
                'sort' => '38',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '2',
            ),
        120 =>
            array (
                'ruleId' => '121',
                'catId' => '1',
                'ruleName' => '教师课件中原始文案出现标点符号使用错误',
                'score' => '2',
                'remark' => '',
                'sort' => '39',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '2',
            ),
        121 =>
            array (
                'ruleId' => '122',
                'catId' => '1',
                'ruleName' => '教师课件中原始文案存在语序、语病问题；（不影响影响正常阅读理解或题目解答）',
                'score' => '2',
                'remark' => '',
                'sort' => '40',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '2',
            ),
        122 =>
            array (
                'ruleId' => '123',
                'catId' => '1',
                'ruleName' => '讲义中图片模糊、字体过小，影响学生体验',
                'score' => '5',
                'remark' => '',
                'sort' => '41',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '2',
            ),
        123 =>
            array (
                'ruleId' => '124',
                'catId' => '1',
                'ruleName' => '教师课件中原始文案出现出现错字、别字、多字、少字现象。（影响正常阅读理解或题目解答）',
                'score' => '5',
                'remark' => '',
                'sort' => '42',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '2',
            ),
        124 =>
            array (
                'ruleId' => '125',
                'catId' => '1',
                'ruleName' => '讲义排版乱、页面文字过多、动态内容展现错位等影响学生体验',
                'score' => '5',
                'remark' => '',
                'sort' => '43',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '2',
            ),
        125 =>
            array (
                'ruleId' => '126',
                'catId' => '1',
                'ruleName' => '教师课件中原始文案存在语序、语病问题；（影响正常阅读理解或题目解答）',
                'score' => '10',
                'remark' => '',
                'sort' => '44',
                'checkStatus' => '3',
                'delete' => '0',
                'subject' => '2',
            ),
        126 =>
            array (
                'ruleId' => '127',
                'catId' => '1',
                'ruleName' => '教师课件中原始文案中引用知识不正确、或为非主流用法；',
                'score' => '10',
                'remark' => '',
                'sort' => '45',
                'checkStatus' => '3',
                'delete' => '0',
                'subject' => '2',
            ),
        127 =>
            array (
                'ruleId' => '128',
                'catId' => '1',
                'ruleName' => '讲义不合格（讲义应包含主题逻辑、知识框架）',
                'score' => '10',
                'remark' => '',
                'sort' => '46',
                'checkStatus' => '3',
                'delete' => '0',
                'subject' => '2',
            ),
        128 =>
            array (
                'ruleId' => '129',
                'catId' => '1',
                'ruleName' => '讲义页面未放置作业帮一课官方logo或logo错误',
                'score' => '5',
                'remark' => '',
                'sort' => '47',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '2',
            ),
        129 =>
            array (
                'ruleId' => '130',
                'catId' => '1',
                'ruleName' => '上传讲义错误（不是本次课讲义）',
                'score' => '5',
                'remark' => '',
                'sort' => '48',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '2',
            ),
        130 =>
            array (
                'ruleId' => '131',
                'catId' => '1',
                'ruleName' => '讲义尺寸不符合规范',
                'score' => '5',
                'remark' => '',
                'sort' => '49',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '2',
            ),
        131 =>
            array (
                'ruleId' => '132',
                'catId' => '1',
                'ruleName' => '教师版讲义未提前12小时(工作时间）上传合格讲义',
                'score' => '5',
                'remark' => '',
                'sort' => '50',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '2',
            ),
        132 =>
            array (
                'ruleId' => '133',
                'catId' => '1',
                'ruleName' => '讲义格式错误',
                'score' => '5',
                'remark' => '',
                'sort' => '51',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '2',
            ),
        133 =>
            array (
                'ruleId' => '134',
                'catId' => '2',
                'ruleName' => '教师讲解过程中，板书文案书写出现错字、别字',
                'score' => '10',
                'remark' => '',
                'sort' => '52',
                'checkStatus' => '3',
                'delete' => '0',
                'subject' => '2',
            ),
        134 =>
            array (
                'ruleId' => '135',
                'catId' => '2',
                'ruleName' => '板书字迹不整齐或不清晰，板书布局不合理或不美观',
                'score' => '10',
                'remark' => '',
                'sort' => '53',
                'checkStatus' => '3',
                'delete' => '0',
                'subject' => '2',
            ),
        135 =>
            array (
                'ruleId' => '136',
                'catId' => '2',
                'ruleName' => '授课内容与讲义内容不符',
                'score' => '50',
                'remark' => '',
                'sort' => '54',
                'checkStatus' => '3',
                'delete' => '0',
                'subject' => '2',
            ),
        136 =>
            array (
                'ruleId' => '137',
                'catId' => '2',
                'ruleName' => '课堂出现知识性错误（讲错知识点、习题等）',
                'score' => '50',
                'remark' => '',
                'sort' => '55',
                'checkStatus' => '4',
                'delete' => '0',
                'subject' => '2',
            ),
        137 =>
            array (
                'ruleId' => '138',
                'catId' => '2',
                'ruleName' => '教师讲解过程中，词语读音错误或多音字读音使用不当（语音类知识讲解、及考试应知必会词语正音）',
                'score' => '50',
                'remark' => '',
                'sort' => '56',
                'checkStatus' => '4',
                'delete' => '0',
                'subject' => '2',
            ),
        138 =>
            array (
                'ruleId' => '139',
                'catId' => '2',
                'ruleName' => '教师讲解过程中，词语释义、古文中文字释义、诗词翻译等知识出现错误',
                'score' => '50',
                'remark' => '',
                'sort' => '57',
                'checkStatus' => '4',
                'delete' => '0',
                'subject' => '2',
            ),
        139 =>
            array (
                'ruleId' => '140',
                'catId' => '2',
                'ruleName' => '教师讲解过程中，书写文字存在语法问题、或病句现象；或病句类问题分析讲解错误',
                'score' => '50',
                'remark' => '',
                'sort' => '58',
                'checkStatus' => '4',
                'delete' => '0',
                'subject' => '2',
            ),
        140 =>
            array (
                'ruleId' => '141',
                'catId' => '2',
                'ruleName' => '教师讲解过程中，出现基本语法知识错误；',
                'score' => '50',
                'remark' => '',
                'sort' => '59',
                'checkStatus' => '4',
                'delete' => '0',
                'subject' => '2',
            ),
        141 =>
            array (
                'ruleId' => '142',
                'catId' => '3',
                'ruleName' => '悬挂背景布但展现效果不好或光线差',
                'score' => '2',
                'remark' => '',
                'sort' => '60',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '2',
            ),
        142 =>
            array (
                'ruleId' => '143',
                'catId' => '3',
                'ruleName' => '未悬挂作业帮背景布',
                'score' => '5',
                'remark' => '',
                'sort' => '61',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '2',
            ),
        143 =>
            array (
                'ruleId' => '144',
                'catId' => '3',
                'ruleName' => '教师文明用语',
                'score' => '5',
                'remark' => '',
                'sort' => '62',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '2',
            ),
        144 =>
            array (
                'ruleId' => '145',
                'catId' => '3',
                'ruleName' => '不开摄像头',
                'score' => '5',
                'remark' => '',
                'sort' => '63',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '2',
            ),
        145 =>
            array (
                'ruleId' => '146',
                'catId' => '3',
                'ruleName' => '拖堂超过15分钟',
                'score' => '10',
                'remark' => '',
                'sort' => '64',
                'checkStatus' => '3',
                'delete' => '0',
                'subject' => '2',
            ),
        146 =>
            array (
                'ruleId' => '147',
                'catId' => '3',
                'ruleName' => '拖堂超过30分钟',
                'score' => '100',
                'remark' => '',
                'sort' => '65',
                'checkStatus' => '4',
                'delete' => '0',
                'subject' => '2',
            ),
        147 =>
            array (
                'ruleId' => '148',
                'catId' => '3',
                'ruleName' => '因老师问题导致的课程重开',
                'score' => '10',
                'remark' => '',
                'sort' => '66',
                'checkStatus' => '3',
                'delete' => '0',
                'subject' => '2',
            ),
        148 =>
            array (
                'ruleId' => '149',
                'catId' => '3',
                'ruleName' => '早退',
                'score' => '20',
                'remark' => '',
                'sort' => '67',
                'checkStatus' => '3',
                'delete' => '0',
                'subject' => '2',
            ),
        149 =>
            array (
                'ruleId' => '150',
                'catId' => '3',
                'ruleName' => '迟到',
                'score' => '20',
                'remark' => '',
                'sort' => '68',
                'checkStatus' => '3',
                'delete' => '0',
                'subject' => '2',
            ),
        150 =>
            array (
                'ruleId' => '151',
                'catId' => '3',
                'ruleName' => '旷课',
                'score' => '50',
                'remark' => '',
                'sort' => '69',
                'checkStatus' => '4',
                'delete' => '0',
                'subject' => '2',
            ),
        151 =>
            array (
                'ruleId' => '152',
                'catId' => '3',
                'ruleName' => '在课堂或官方群宣传个人信息',
                'score' => '50',
                'remark' => '',
                'sort' => '70',
                'checkStatus' => '4',
                'delete' => '0',
                'subject' => '2',
            ),
        152 =>
            array (
                'ruleId' => '153',
                'catId' => '3',
                'ruleName' => '在课堂或官方Q群宣传其他机构招生信息',
                'score' => '50',
                'remark' => '',
                'sort' => '71',
                'checkStatus' => '4',
                'delete' => '0',
                'subject' => '2',
            ),
        153 =>
            array (
                'ruleId' => '154',
                'catId' => '3',
                'ruleName' => '课堂或官方Q群等地方出现有明显导流倾向的行为',
                'score' => '100',
                'remark' => '',
                'sort' => '72',
                'checkStatus' => '4',
                'delete' => '0',
                'subject' => '2',
            ),
        154 =>
            array (
                'ruleId' => '155',
                'catId' => '3',
                'ruleName' => '课堂出现抱怨、情绪失控等负能量的行为举止',
                'score' => '100',
                'remark' => '',
                'sort' => '73',
                'checkStatus' => '4',
                'delete' => '0',
                'subject' => '2',
            ),
        155 =>
            array (
                'ruleId' => '156',
                'catId' => '3',
                'ruleName' => '课堂内容出现暴力、恐怖、色情、与社会主流观点严重不符或不当政治倾向言论等违反法律法规的内容',
                'score' => '100',
                'remark' => '',
                'sort' => '74',
                'checkStatus' => '4',
                'delete' => '0',
                'subject' => '2',
            ),
        156 =>
            array (
                'ruleId' => '157',
                'catId' => '3',
                'ruleName' => '在课堂或官方Q群辱骂、讽刺学生、家长和老师',
                'score' => '100',
                'remark' => '',
                'sort' => '75',
                'checkStatus' => '4',
                'delete' => '0',
                'subject' => '2',
            ),
        157 =>
            array (
                'ruleId' => '158',
                'catId' => '3',
                'ruleName' => '课堂抽烟、吃东西、打电话等做与上课无关的事情',
                'score' => '100',
                'remark' => '',
                'sort' => '76',
                'checkStatus' => '4',
                'delete' => '0',
                'subject' => '2',
            ),
        158 =>
            array (
                'ruleId' => '159',
                'catId' => '4',
                'ruleName' => '学员或家长投诉，经核实按照上述规则惩罚',
                'score' => '0',
                'remark' => '',
                'sort' => '77',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '2',
            ),
        159 =>
            array (
                'ruleId' => '160',
                'catId' => '4',
                'ruleName' => '其他影响学生学习体验或不符合作业帮规定的行为',
                'score' => '0',
                'remark' => '',
                'sort' => '78',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '2',
            ),
        160 =>
            array (
                'ruleId' => '161',
                'catId' => '1',
                'ruleName' => '原始讲义有错误，但是直播前手工修改',
                'score' => '2',
                'remark' => '',
                'sort' => '79',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '3',
            ),
        161 =>
            array (
                'ruleId' => '162',
                'catId' => '1',
                'ruleName' => '教师课件中原始文案出现单词拼写错误、多单词、少单词；（不影响正常阅读理解或题目解答）',
                'score' => '2',
                'remark' => '',
                'sort' => '80',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '3',
            ),
        162 =>
            array (
                'ruleId' => '163',
                'catId' => '1',
                'ruleName' => '教师课件中原始文案出现标点符号使用错误；',
                'score' => '2',
                'remark' => '',
                'sort' => '81',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '3',
            ),
        163 =>
            array (
                'ruleId' => '164',
                'catId' => '1',
                'ruleName' => '教师课件中原始文案存在语法错误；（不影响影响正常阅读理解或题目解答）',
                'score' => '2',
                'remark' => '',
                'sort' => '82',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '3',
            ),
        164 =>
            array (
                'ruleId' => '165',
                'catId' => '1',
                'ruleName' => '讲义排版乱、页面文字过多、动态内容展现错位等影响学生体验的；',
                'score' => '5',
                'remark' => '',
                'sort' => '83',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '3',
            ),
        165 =>
            array (
                'ruleId' => '166',
                'catId' => '1',
                'ruleName' => '讲义中图片模糊、字体过小，影响学生体验；',
                'score' => '5',
                'remark' => '',
                'sort' => '84',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '3',
            ),
        166 =>
            array (
                'ruleId' => '167',
                'catId' => '1',
                'ruleName' => '教师课件中原始文案出现单词拼写错误、多单词、少单词；（影响正常阅读理解或题目解答）',
                'score' => '10',
                'remark' => '',
                'sort' => '85',
                'checkStatus' => '3',
                'delete' => '0',
                'subject' => '3',
            ),
        167 =>
            array (
                'ruleId' => '168',
                'catId' => '1',
                'ruleName' => '教师课件中原始文案存在语法错误；（影响正常阅读理解或题目解答）',
                'score' => '10',
                'remark' => '',
                'sort' => '86',
                'checkStatus' => '3',
                'delete' => '0',
                'subject' => '3',
            ),
        168 =>
            array (
                'ruleId' => '169',
                'catId' => '1',
                'ruleName' => '教师课件中原始文案中引用知识不正确、或为非主流用法；',
                'score' => '10',
                'remark' => '',
                'sort' => '87',
                'checkStatus' => '3',
                'delete' => '0',
                'subject' => '3',
            ),
        169 =>
            array (
                'ruleId' => '170',
                'catId' => '1',
                'ruleName' => '讲义不合格；（讲义应包含主题逻辑、知识框架）',
                'score' => '10',
                'remark' => '',
                'sort' => '88',
                'checkStatus' => '3',
                'delete' => '0',
                'subject' => '3',
            ),
        170 =>
            array (
                'ruleId' => '171',
                'catId' => '1',
                'ruleName' => '讲义页面未放置作业帮一课官方logo或logo错误',
                'score' => '5',
                'remark' => '',
                'sort' => '89',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '3',
            ),
        171 =>
            array (
                'ruleId' => '172',
                'catId' => '1',
                'ruleName' => '上传讲义错误（不是本次课讲义）',
                'score' => '5',
                'remark' => '',
                'sort' => '90',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '3',
            ),
        172 =>
            array (
                'ruleId' => '173',
                'catId' => '1',
                'ruleName' => '讲义尺寸不符合规范',
                'score' => '5',
                'remark' => '',
                'sort' => '91',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '3',
            ),
        173 =>
            array (
                'ruleId' => '174',
                'catId' => '1',
                'ruleName' => '教师版讲义未提前12小时(工作时间）上传合格讲义',
                'score' => '5',
                'remark' => '',
                'sort' => '92',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '3',
            ),
        174 =>
            array (
                'ruleId' => '175',
                'catId' => '1',
                'ruleName' => '讲义格式错误',
                'score' => '5',
                'remark' => '',
                'sort' => '93',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '3',
            ),
        175 =>
            array (
                'ruleId' => '176',
                'catId' => '2',
                'ruleName' => '教师讲解过程中，有发音错误；（非语音类知识讲解）',
                'score' => '2',
                'remark' => '',
                'sort' => '94',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '3',
            ),
        176 =>
            array (
                'ruleId' => '177',
                'catId' => '2',
                'ruleName' => '板书字迹不整齐或不清晰，板书布局不合理或不美观',
                'score' => '10',
                'remark' => '',
                'sort' => '95',
                'checkStatus' => '3',
                'delete' => '0',
                'subject' => '3',
            ),
        177 =>
            array (
                'ruleId' => '178',
                'catId' => '2',
                'ruleName' => '教师讲解过程中，板书文案书写出现单词拼写错误',
                'score' => '10',
                'remark' => '',
                'sort' => '96',
                'checkStatus' => '3',
                'delete' => '0',
                'subject' => '3',
            ),
        178 =>
            array (
                'ruleId' => '179',
                'catId' => '2',
                'ruleName' => '教师讲解过程中，有发音错误；（语音类知识讲解）',
                'score' => '20',
                'remark' => '',
                'sort' => '97',
                'checkStatus' => '3',
                'delete' => '0',
                'subject' => '3',
            ),
        179 =>
            array (
                'ruleId' => '180',
                'catId' => '2',
                'ruleName' => '教师讲解过程中，单词释义、语句翻译等知识出现错误',
                'score' => '20',
                'remark' => '',
                'sort' => '98',
                'checkStatus' => '3',
                'delete' => '0',
                'subject' => '3',
            ),
        180 =>
            array (
                'ruleId' => '181',
                'catId' => '2',
                'ruleName' => '教师讲解过程中，书写文字存在语法问题、或病句现象；   或病句类问题分析讲解错误；',
                'score' => '50',
                'remark' => '',
                'sort' => '99',
                'checkStatus' => '4',
                'delete' => '0',
                'subject' => '3',
            ),
        181 =>
            array (
                'ruleId' => '182',
                'catId' => '2',
                'ruleName' => '教师讲解过程中，教师提供的答案不正确；',
                'score' => '50',
                'remark' => '',
                'sort' => '100',
                'checkStatus' => '4',
                'delete' => '0',
                'subject' => '3',
            ),
        182 =>
            array (
                'ruleId' => '183',
                'catId' => '2',
                'ruleName' => '教师讲解过程中，出现基本语法知识错误；',
                'score' => '20',
                'remark' => '',
                'sort' => '101',
                'checkStatus' => '3',
                'delete' => '0',
                'subject' => '3',
            ),
        183 =>
            array (
                'ruleId' => '184',
                'catId' => '3',
                'ruleName' => '悬挂背景布但展现效果不好或光线差',
                'score' => '2',
                'remark' => '',
                'sort' => '102',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '3',
            ),
        184 =>
            array (
                'ruleId' => '185',
                'catId' => '3',
                'ruleName' => '未悬挂作业帮背景布',
                'score' => '5',
                'remark' => '',
                'sort' => '103',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '3',
            ),
        185 =>
            array (
                'ruleId' => '186',
                'catId' => '3',
                'ruleName' => '不开摄像头',
                'score' => '5',
                'remark' => '',
                'sort' => '104',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '3',
            ),
        186 =>
            array (
                'ruleId' => '187',
                'catId' => '3',
                'ruleName' => '教师不文明用语',
                'score' => '5',
                'remark' => '',
                'sort' => '105',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '3',
            ),
        187 =>
            array (
                'ruleId' => '188',
                'catId' => '3',
                'ruleName' => '拖堂超过15分钟',
                'score' => '10',
                'remark' => '',
                'sort' => '106',
                'checkStatus' => '3',
                'delete' => '0',
                'subject' => '3',
            ),
        188 =>
            array (
                'ruleId' => '189',
                'catId' => '3',
                'ruleName' => '拖堂超过30分钟',
                'score' => '100',
                'remark' => '',
                'sort' => '107',
                'checkStatus' => '4',
                'delete' => '0',
                'subject' => '3',
            ),
        189 =>
            array (
                'ruleId' => '190',
                'catId' => '3',
                'ruleName' => '因老师问题导致的课程重开',
                'score' => '10',
                'remark' => '',
                'sort' => '108',
                'checkStatus' => '3',
                'delete' => '0',
                'subject' => '3',
            ),
        190 =>
            array (
                'ruleId' => '191',
                'catId' => '3',
                'ruleName' => '迟到',
                'score' => '20',
                'remark' => '',
                'sort' => '109',
                'checkStatus' => '3',
                'delete' => '0',
                'subject' => '3',
            ),
        191 =>
            array (
                'ruleId' => '192',
                'catId' => '3',
                'ruleName' => '早退',
                'score' => '20',
                'remark' => '',
                'sort' => '110',
                'checkStatus' => '3',
                'delete' => '0',
                'subject' => '3',
            ),
        192 =>
            array (
                'ruleId' => '193',
                'catId' => '3',
                'ruleName' => '旷课',
                'score' => '50',
                'remark' => '',
                'sort' => '111',
                'checkStatus' => '4',
                'delete' => '0',
                'subject' => '3',
            ),
        193 =>
            array (
                'ruleId' => '194',
                'catId' => '3',
                'ruleName' => '在课堂或官方群宣传个人信息',
                'score' => '50',
                'remark' => '',
                'sort' => '112',
                'checkStatus' => '4',
                'delete' => '0',
                'subject' => '3',
            ),
        194 =>
            array (
                'ruleId' => '195',
                'catId' => '3',
                'ruleName' => '在课堂或官方Q群宣传其他机构招生信息',
                'score' => '50',
                'remark' => '',
                'sort' => '113',
                'checkStatus' => '4',
                'delete' => '0',
                'subject' => '3',
            ),
        195 =>
            array (
                'ruleId' => '196',
                'catId' => '3',
                'ruleName' => '课堂出现抱怨、情绪失控等负能量的行为举止',
                'score' => '100',
                'remark' => '',
                'sort' => '114',
                'checkStatus' => '4',
                'delete' => '0',
                'subject' => '3',
            ),
        196 =>
            array (
                'ruleId' => '197',
                'catId' => '3',
                'ruleName' => '课堂内容出现暴力、恐怖、色情、与社会主流观点严重不符或不当政治倾向言论等违反法律法规的内容',
                'score' => '100',
                'remark' => '',
                'sort' => '115',
                'checkStatus' => '4',
                'delete' => '0',
                'subject' => '3',
            ),
        197 =>
            array (
                'ruleId' => '198',
                'catId' => '3',
                'ruleName' => '在课堂或官方Q群辱骂、讽刺学生、家长和老师',
                'score' => '100',
                'remark' => '',
                'sort' => '116',
                'checkStatus' => '4',
                'delete' => '0',
                'subject' => '3',
            ),
        198 =>
            array (
                'ruleId' => '199',
                'catId' => '3',
                'ruleName' => '课堂或官方Q群等地方出现有明显导流倾向的行为',
                'score' => '100',
                'remark' => '',
                'sort' => '117',
                'checkStatus' => '4',
                'delete' => '0',
                'subject' => '3',
            ),
        199 =>
            array (
                'ruleId' => '200',
                'catId' => '3',
                'ruleName' => '课堂抽烟、吃东西、打电话等做与上课无关的事情',
                'score' => '100',
                'remark' => '',
                'sort' => '118',
                'checkStatus' => '4',
                'delete' => '0',
                'subject' => '3',
            ),
        200 =>
            array (
                'ruleId' => '201',
                'catId' => '4',
                'ruleName' => '学员或家长投诉，经核实按照上述规则惩罚',
                'score' => '0',
                'remark' => '',
                'sort' => '119',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '3',
            ),
        201 =>
            array (
                'ruleId' => '202',
                'catId' => '4',
                'ruleName' => '其他影响学生学习体验或不符合作业帮规定的行为',
                'score' => '0',
                'remark' => '',
                'sort' => '120',
                'checkStatus' => '2',
                'delete' => '0',
                'subject' => '3',
            ),

        202=>array(
                'ruleId'=>'203',
                'catId'=>'1',
                'ruleName'=>'讲义中有错别字/符号,多字,少字,但不影响阅读及题意理解',
                'score'=>'2',
                'remark'=>'',
                'sort'=>'0',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'4'),
        203=>array(
                'ruleId'=>'204',
                'catId'=>'1',
                'ruleName'=>'讲义排版乱、页面文字过多、动态内容展现错位等影响学生体验的情况',
                'score'=>'5',
                'remark'=>'',
                'sort'=>'0',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'4'),
        204=>array(
                'ruleId'=>'205',
                'catId'=>'1',
                'ruleName'=>'讲义中图片模糊、字体过小,影响学生体验',
                'score'=>'5',
                'remark'=>'',
                'sort'=>'0',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'4'),
        205=>array(
                'ruleId'=>'206',
                'catId'=>'1',
                'ruleName'=>'讲义中有错别字/错误符号影响题目理解或影响正常解答',
                'score'=>'10',
                'remark'=>'',
                'sort'=>'0',
                'checkStatus'=>'3',
                'delete'=>'0',
                'subject'=>'4'),
        206=>array(
                'ruleId'=>'207',
                'catId'=>'1',
                'ruleName'=>'讲义不合格（讲义应包含主题逻辑、知识框架',
                'score'=>'10',
                'remark'=>'',
                'sort'=>'0',
                'checkStatus'=>'3',
                'delete'=>'0',
                'subject'=>'4'),
        207=>array(
                'ruleId'=>'208',
                'catId'=>'1',
                'ruleName'=>'讲义页面未放置作业帮一课官方logo或logo错误',
                'score'=>'5',
                'remark'=>'',
                'sort'=>'0',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'4'),
        208=>array(
                'ruleId'=>'209',
                'catId'=>'5',
                'ruleName'=>'讲义中图片模糊、字体过小,影响学生体验',
                'score'=>'5',
                'remark'=>'',
                'sort'=>'0',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'4'),
        209=>array(
                'ruleId'=>'210',
                'catId'=>'5',
                'ruleName'=>'上传讲义错误（不是本次课讲义）',
                'score'=>'10',
                'remark'=>'',
                'sort'=>'0',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'4'),
        210=>array(
                'ruleId'=>'211',
                'catId'=>'5',
                'ruleName'=>'讲义尺寸不符合规范',
                'score'=>'5',
                'remark'=>'',
                'sort'=>'0',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'4'),
        211=>array(
                'ruleId'=>'212',
                'catId'=>'5',
                'ruleName'=>'讲义格式错误（不是PPT或PPTx）',
                'score'=>'5',
                'remark'=>'',
                'sort'=>'0',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'4'),
        212=>array(
                'ruleId'=>'213',
                'catId'=>'5',
                'ruleName'=>'教师版讲义未提前12小时上传合格讲义',
                'score'=>'5',
                'remark'=>'',
                'sort'=>'0',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'4'),
        213=>array(
                'ruleId'=>'214',
                'catId'=>'3',
                'ruleName'=>'小学课上未按要求进行点名',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'35',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'1'),
        214=>array(
                'ruleId'=>'215',
                'catId'=>'3',
                'ruleName'=>'初中课上未按要求进行点名',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'36',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'1'),
        215=>array(
                'ruleId'=>'216',
                'catId'=>'3',
                'ruleName'=>'高中课上未按要求进行点名',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'37',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'1'),
        216=>array(
                'ruleId'=>'217',
                'catId'=>'3',
                'ruleName'=>'高中课上未按要求进行点名(0基础班)',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'38',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'1'),
        217=>array(
                'ruleId'=>'218',
                'catId'=>'3',
                'ruleName'=>'小学课上未按要求进行表扬',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'39',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'1'),
        218=>array(
                'ruleId'=>'219',
                'catId'=>'3',
                'ruleName'=>'初中课上未按要求进行表扬',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'40',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'1'),
        219=>array(
                'ruleId'=>'220',
                'catId'=>'3',
                'ruleName'=>'高中课上未按要求进行表扬',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'41',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'1'),
        220=>array(
                'ruleId'=>'221',
                'catId'=>'3',
                'ruleName'=>'高中课上未按要求进行表扬（0基础班）',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'42',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'1'),
        221=>array(
                'ruleId'=>'222',
                'catId'=>'3',
                'ruleName'=>'小学课上未按要求进行抢麦',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'43',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'1'),
        222=>array(
                'ruleId'=>'223',
                'catId'=>'3',
                'ruleName'=>'初中课上未按要求进行抢麦',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'44',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'1'),
        223=>array(
                'ruleId'=>'224',
                'catId'=>'3',
                'ruleName'=>'小学课上未按要求进行发红包',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'45',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'1'),
        224=>array(
                'ruleId'=>'225',
                'catId'=>'3',
                'ruleName'=>'初中课上未按要求进行发红包',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'46',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'1'),
        225=>array(
                'ruleId'=>'226',
                'catId'=>'3',
                'ruleName'=>'高中课上未按要求进行发红包',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'47',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'1'),
        226=>array(
                'ruleId'=>'227',
                'catId'=>'3',
                'ruleName'=>'高中课上未按要求进行发红包（0基础班）',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'48',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'1'),
        227=>array(
                'ruleId'=>'228',
                'catId'=>'3',
                'ruleName'=>'小学课上未按要求进行发互动题',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'49',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'1'),
        228=>array(
                'ruleId'=>'229',
                'catId'=>'3',
                'ruleName'=>'初中课上未按要求进行发互动题',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'50',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'1'),
        229=>array(
                'ruleId'=>'230',
                'catId'=>'3',
                'ruleName'=>'高中课上未按要求进行发互动题',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'51',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'1'),
        230=>array(
                'ruleId'=>'231',
                'catId'=>'3',
                'ruleName'=>'高中课上未按要求进行发互动题（0基础班）',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'52',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'1'),
        231=>array(
                'ruleId'=>'232',
                'catId'=>'3',
                'ruleName'=>'小学课上未按要求进行讲笑话',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'53',
                'checkStatus'=>'2',
                'delete'=>'1',
                'subject'=>'1'),
        232=>array(
                'ruleId'=>'233',
                'catId'=>'3',
                'ruleName'=>'初中课上未按要求进行发是否卡',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'54',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'1'),
        233=>array(
                'ruleId'=>'234',
                'catId'=>'3',
                'ruleName'=>'小学课上未按要求进行点名',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'77',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'2'),
        234=>array(
                'ruleId'=>'235',
                'catId'=>'3',
                'ruleName'=>'初中课上未按要求进行点名',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'78',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'2'),
        235=>array(
                'ruleId'=>'236',
                'catId'=>'3',
                'ruleName'=>'高中课上未按要求进行点名',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'79',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'2'),
        236=>array(
                'ruleId'=>'237',
                'catId'=>'3',
                'ruleName'=>'高中课上未按要求进行点名(0基础班)',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'80',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'2'),
        237=>array(
                'ruleId'=>'238',
                'catId'=>'3',
                'ruleName'=>'小学课上未按要求进行表扬',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'81',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'2'),
        238=>array(
                'ruleId'=>'239',
                'catId'=>'3',
                'ruleName'=>'初中课上未按要求进行表扬',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'82',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'2'),
        239=>array(
                'ruleId'=>'240',
                'catId'=>'3',
                'ruleName'=>'高中课上未按要求进行表扬',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'83',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'2'),
        240=>array(
                'ruleId'=>'241',
                'catId'=>'3',
                'ruleName'=>'高中课上未按要求进行表扬（0基础班）',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'84',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'2'),
        241=>array(
                'ruleId'=>'242',
                'catId'=>'3',
                'ruleName'=>'小学课上未按要求进行抢麦',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'85',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'2'),
        242=>array(
                'ruleId'=>'243',
                'catId'=>'3',
                'ruleName'=>'初中课上未按要求进行抢麦',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'86',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'2'),
        243=>array(
                'ruleId'=>'244',
                'catId'=>'3',
                'ruleName'=>'小学课上未按要求进行发红包',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'87',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'2'),
        244=>array(
                'ruleId'=>'245',
                'catId'=>'3',
                'ruleName'=>'初中课上未按要求进行发红包',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'88',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'2'),
        245=>array(
                'ruleId'=>'246',
                'catId'=>'3',
                'ruleName'=>'高中课上未按要求进行发红包',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'89',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'2'),
        246=>array(
                'ruleId'=>'247',
                'catId'=>'3',
                'ruleName'=>'高中课上未按要求进行发红包（0基础班）',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'90',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'2'),
        247=>array(
                'ruleId'=>'248',
                'catId'=>'3',
                'ruleName'=>'小学课上未按要求进行发互动题',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'91',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'2'),
        248=>array(
                'ruleId'=>'249',
                'catId'=>'3',
                'ruleName'=>'初中课上未按要求进行发互动题',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'92',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'2'),
        249=>array(
                'ruleId'=>'250',
                'catId'=>'3',
                'ruleName'=>'高中课上未按要求进行发互动题',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'93',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'2'),
        250=>array(
                'ruleId'=>'251',
                'catId'=>'3',
                'ruleName'=>'高中课上未按要求进行发互动题（0基础班）',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'94',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'2'),
        251=>array(
                'ruleId'=>'252',
                'catId'=>'3',
                'ruleName'=>'小学课上未按要求进行讲笑话',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'95',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'2'),
        252=>array(
                'ruleId'=>'253',
                'catId'=>'3',
                'ruleName'=>'初中课上未按要求进行发是否卡',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'96',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'2'),
        253=>array(
                'ruleId'=>'254',
                'catId'=>'3',
                'ruleName'=>'小学课上未按要求进行点名',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'120',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'3'),
        254=>array(
                'ruleId'=>'255',
                'catId'=>'3',
                'ruleName'=>'初中课上未按要求进行点名',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'121',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'3'),
        255=>array(
                'ruleId'=>'256',
                'catId'=>'3',
                'ruleName'=>'高中课上未按要求进行点名',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'122',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'3'),
        256=>array(
                'ruleId'=>'257',
                'catId'=>'3',
                'ruleName'=>'高中课上未按要求进行点名(0基础班)',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'123',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'3'),
        257=>array(
                'ruleId'=>'258',
                'catId'=>'3',
                'ruleName'=>'小学课上未按要求进行表扬',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'124',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'3'),
        258=>array(
                'ruleId'=>'259',
                'catId'=>'3',
                'ruleName'=>'初中课上未按要求进行表扬',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'125',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'3'),
        259=>array(
                'ruleId'=>'260',
                'catId'=>'3',
                'ruleName'=>'高中课上未按要求进行表扬',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'126',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'3'),
        260=>array(
                'ruleId'=>'261',
                'catId'=>'3',
                'ruleName'=>'高中课上未按要求进行表扬（0基础班）',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'127',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'3'),
        261=>array(
                'ruleId'=>'262',
                'catId'=>'3',
                'ruleName'=>'小学课上未按要求进行抢麦',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'128',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'3'),
        262=>array(
                'ruleId'=>'263',
                'catId'=>'3',
                'ruleName'=>'初中课上未按要求进行抢麦',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'129',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'3'),
        263=>array(
                'ruleId'=>'264',
                'catId'=>'3',
                'ruleName'=>'小学课上未按要求进行发红包',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'130',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'3'),
        264=>array(
                'ruleId'=>'265',
                'catId'=>'3',
                'ruleName'=>'初中课上未按要求进行发红包',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'131',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'3'),
        265=>array(
                'ruleId'=>'266',
                'catId'=>'3',
                'ruleName'=>'高中课上未按要求进行发红包',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'132',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'3'),
        266=>array(
                'ruleId'=>'267',
                'catId'=>'3',
                'ruleName'=>'高中课上未按要求进行发红包（0基础班）',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'133',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'3'),
        267=>array(
                'ruleId'=>'268',
                'catId'=>'3',
                'ruleName'=>'小学课上未按要求进行发互动题',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'134',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'3'),
        268=>array(
                'ruleId'=>'269',
                'catId'=>'3',
                'ruleName'=>'初中课上未按要求进行发互动题',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'135',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'3'),
        269=>array(
                'ruleId'=>'270',
                'catId'=>'3',
                'ruleName'=>'高中课上未按要求进行发互动题',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'136',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'3'),
        270=>array(
                'ruleId'=>'271',
                'catId'=>'3',
                'ruleName'=>'高中课上未按要求进行发互动题（0基础班）',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'137',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'3'),
        271=>array(
                'ruleId'=>'272',
                'catId'=>'3',
                'ruleName'=>'小学课上未按要求进行讲笑话',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'138',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'3'),
        272=>array(
                'ruleId'=>'273',
                'catId'=>'3',
                'ruleName'=>'初中课上未按要求进行发是否卡',
                'score'=>'1',
                'remark'=>'',
                'sort'=>'139',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'3'),
        273=>array(
                'ruleId'=>'274',
                'catId'=>'2',
                'ruleName'=>'授课内容与讲义内容不符',
                'score'=>'10',
                'remark'=>'',
                'sort'=>'55',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'2'),
        274=>array(
                'ruleId'=>'275',
                'catId'=>'3',
                'ruleName'=>'违规使用无线网络上课',
                'score'=>'5',
                'remark'=>'',
                'sort'=>'121',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'3'),
        275=>array(
                'ruleId'=>'276',
                'catId'=>'3',
                'ruleName'=>'违规使用无线网络上课',
                'score'=>'5',
                'remark'=>'',
                'sort'=>'76',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'2'),
        276=>array(
                'ruleId'=>'277',
                'catId'=>'3',
                'ruleName'=>'违规使用无线网络上课',
                'score'=>'5',
                'remark'=>'',
                'sort'=>'34',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'1'),
        277 => array(
                'ruleId'=>'278',
                'catId'=>'3',
                'ruleName'=>'直播课教师未按要求穿着“一课”标准服装',
                'score'=>'5',
                'remark'=>'',
                'sort'=>'201',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'3'
                ),
        278 => array(
                'ruleId'=>'279',
                'catId'=>'3',
                'ruleName'=>'直播课教师未按要求穿着“一课”标准服装',
                'score'=>'5',
                'remark'=>'',
                'sort'=>'159',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'2'
                ),
        279 => array(
                'ruleId'=>'280',
                'catId'=>'3',
                'ruleName'=>'直播课教师未按要求穿着“一课”标准服装',
                'score'=>'5',
                'remark'=>'',
                'sort'=>'117',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'1'
        ),
        280 => array(
                'ruleId'=>'281',
                'catId'=>'3',
                'ruleName'=>'直播课教师未按要求穿着“一课”标准服装',
                'score'=>'5',
                'remark'=>'',
                'sort'=>'119',
                'checkStatus'=>'2',
                'delete'=>'0',
                'subject'=>'1'
        ),
        281 => array(
            'ruleId'=>'282',
            'catId'=>'4',
            'ruleName'=>'违反开课规范要求（调整时间、重开、更换教师等）',
            'score'=>'0',
            'remark'=>'',
            'sort'=>'161',
            'checkStatus'=>'2',
            'delete'=>'0',
            'subject'=>'2'
        ),
        282 => array(
            'ruleId'=>'283',
            'catId'=>'4',
            'ruleName'=>'违反开课规范要求（调整时间、重开、更换教师等）',
            'score'=>'0',
            'remark'=>'',
            'sort'=>'121',
            'checkStatus'=>'2',
            'delete'=>'0',
            'subject'=>'3'
        ),
        283 => array(
            'ruleId'=>'284',
            'catId'=>'4',
            'ruleName'=>'小学课中未按要求进行眼保健操',
            'score'=>'0',
            'remark'=>'',
            'sort'=>'119',
            'checkStatus'=>'2',
            'delete'=>'0',
            'subject'=>'1'
        ),
        284 => array(
            'ruleId'=>'285',
            'catId'=>'4',
            'ruleName'=>'小学课中未按要求进行眼保健操',
            'score'=>'5',
            'remark'=>'',
            'sort'=>'161',
            'checkStatus'=>'2',
            'delete'=>'0',
            'subject'=>'2'
        ),
        285 => array(
            'ruleId'=>'286',
            'catId'=>'4',
            'ruleName'=>'小学课中未按要求进行眼保健操',
            'score'=>'5',
            'remark'=>'',
            'sort'=>'161',
            'checkStatus'=>'2',
            'delete'=>'0',
            'subject'=>'3'
        ),
        286 => array(
            'ruleId'=>'287',
            'catId'=>'3',
            'ruleName'=>'小学：班课未提前5分钟进教室',
            'score'=>'5',
            'remark'=>'',
            'sort'=>'35',
            'checkStatus'=>'2',
            'delete'=>'0',
            'subject'=>'1'
        ),
        287 => array(
            'ruleId'=>'288',
            'catId'=>'3',
            'ruleName'=>'小学：班课未提前5分钟进教室',
            'score'=>'2',
            'remark'=>'',
            'sort'=>'35',
            'checkStatus'=>'2',
            'delete'=>'0',
            'subject'=>'2'
        ),
        288 => array(
            'ruleId'=>'289',
            'catId'=>'3',
            'ruleName'=>'小学：班课未提前5分钟进教室',
            'score'=>'2',
            'remark'=>'',
            'sort'=>'119',
            'checkStatus'=>'2',
            'delete'=>'0',
            'subject'=>'3'
        ),
        289 => array(
            'ruleId'=>'290',
            'catId'=>'3',
            'ruleName'=>'初中：班课未提前5分钟进教室',
            'score'=>'2',
            'remark'=>'',
            'sort'=>'35',
            'checkStatus'=>'2',
            'delete'=>'0',
            'subject'=>'1'
        ),
        290 => array(
            'ruleId'=>'291',
            'catId'=>'3',
            'ruleName'=>'初中：班课未提前5分钟进教室',
            'score'=>'2',
            'remark'=>'',
            'sort'=>'77',
            'checkStatus'=>'2',
            'delete'=>'0',
            'subject'=>'2'
        ),
        291 => array(
            'ruleId'=>'292',
            'catId'=>'3',
            'ruleName'=>'初中：班课未提前5分钟进教室',
            'score'=>'2',
            'remark'=>'',
            'sort'=>'119',
            'checkStatus'=>'2',
            'delete'=>'0',
            'subject'=>'3'
        ),
        292 => array(
            'ruleId'=>'293',
            'catId'=>'3',
            'ruleName'=>'高中：班课未提前5分钟进教室',
            'score'=>'2',
            'remark'=>'',
            'sort'=>'35',
            'checkStatus'=>'2',
            'delete'=>'0',
            'subject'=>'1'
        ),
        293=> array(
            'ruleId'=>'294',
            'catId'=>'3',
            'ruleName'=>'高中：班课未提前5分钟进教室',
            'score'=>'2',
            'remark'=>'',
            'sort'=>'76',
            'checkStatus'=>'2',
            'delete'=>'0',
            'subject'=>'2'
        ),
        294 => array(
            'ruleId'=>'295',
            'catId'=>'3',
            'ruleName'=>'高中：班课未提前5分钟进教室',
            'score'=>'2',
            'remark'=>'',
            'sort'=>'119',
            'checkStatus'=>'2',
            'delete'=>'0',
            'subject'=>'3'
        ),
        295 => array(
            'ruleId'=>'296',
            'catId'=>'3',
            'ruleName'=>'未提前24小时上传合格班课讲义',
            'score'=>'5',
            'remark'=>'',
            'sort'=>'35',
            'checkStatus'=>'2',
            'delete'=>'0',
            'subject'=>'1'
        ),
        296 => array(
            'ruleId'=>'297',
            'catId'=>'3',
            'ruleName'=>'未提前24小时上传合格班课讲义',
            'score'=>'5',
            'remark'=>'',
            'sort'=>'77',
            'checkStatus'=>'2',
            'delete'=>'0',
            'subject'=>'2'
        ),
        297 => array(
            'ruleId'=>'298',
            'catId'=>'3',
            'ruleName'=>'未提前24小时上传合格班课讲义',
            'score'=>'5',
            'remark'=>'',
            'sort'=>'119',
            'checkStatus'=>'2',
            'delete'=>'0',
            'subject'=>'3'
        ),
        298 => array(
            'ruleId'=>'299',
            'catId'=>'3',
            'ruleName'=>'课前12小时内违规修改讲义',
            'score'=>'5',
            'remark'=>'',
            'sort'=>'35',
            'checkStatus'=>'2',
            'delete'=>'0',
            'subject'=>'1'
        ),
        299 => array(
            'ruleId'=>'300',
            'catId'=>'3',
            'ruleName'=>'课前12小时内违规修改讲义',
            'score'=>'0',
            'remark'=>'',
            'sort'=>'77',
            'checkStatus'=>'2',
            'delete'=>'0',
            'subject'=>'2'
        ),
        300 => array(
            'ruleId'=>'301',
            'catId'=>'3',
            'ruleName'=>'课前12小时内违规修改讲义',
            'score'=>'0',
            'remark'=>'',
            'sort'=>'119',
            'checkStatus'=>'2',
            'delete'=>'0',
            'subject'=>'3'
        ),
        301 => array(
            'ruleId'=>'302',
            'catId'=>'3',
            'ruleName'=>'教师版讲义未提前12小时上传合格讲义（班课、体验课）',
            'score'=>'5',
            'remark'=>'',
            'sort'=>'119',
            'checkStatus'=>'2',
            'delete'=>'0',
            'subject'=>'1'
        ),
        302 => array(
            'ruleId'=>'303',
            'catId'=>'3',
            'ruleName'=>'教师版讲义未提前12小时上传合格讲义（班课、体验课）',
            'score'=>'5',
            'remark'=>'',
            'sort'=>'77',
            'checkStatus'=>'2',
            'delete'=>'0',
            'subject'=>'2'
        ),
        303 => array(
            'ruleId'=>'304',
            'catId'=>'3',
            'ruleName'=>'教师版讲义未提前12小时上传合格讲义（班课、体验课）',
            'score'=>'5',
            'remark'=>'',
            'sort'=>'119',
            'checkStatus'=>'2',
            'delete'=>'0',
            'subject'=>'3'
        ),

    );


    /**
     * 构造函数
     *
     */
    public function __construct() {
        $this->objDaoCourseCheck = new Hkzb_Dao_Fudao_CourseCheck();
    }

    /**新增记录
     * @param $arrParams
     * @return bool|int
     */
    public function addCourseCheck($arrParams) {
        if (intval($arrParams['courseId']) <= 0 || intval($arrParams['lessonId']) <= 0 ||
          intval($arrParams['teacherUid']) <= 0 || intval($arrParams['rule']) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[". json_encode($arrParams) . "]");
            return false;
        }

        $arrFields = array(
            'courseId'     => isset($arrParams['courseId']) ? intval($arrParams['courseId']) : 0,
            'type'         => isset($arrParams['type']) ? intval($arrParams['type']) : 0,
            'lessonId'     => isset($arrParams['lessonId']) ? intval($arrParams['lessonId']) : 0,
            'teacherUid'   => isset($arrParams['teacherUid']) ? intval($arrParams['teacherUid']) : 0,
            'rule'         => isset($arrParams['rule']) ? intval($arrParams['rule']) : 0,
            'score'        => isset($arrParams['score']) ? $arrParams['score'] : 0,
            'status'       => isset($arrParams['status']) ? $arrParams['status'] : self::STATUS_CHECK,
            'createTime'   => time(),
            'updateTime'   => time(),
            'operatorUid'  => isset($arrParams['operatorUid']) ? intval($arrParams['operatorUid']) : 0,
            'operator'     => isset($arrParams['operator']) ? strval($arrParams['operator']) : '',
            'extData'      => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '',
            'checkType'      => isset($arrParams['checkType']) ? json_encode($arrParams['checkType']) : '1',
            'checkorName'    => isset($arrParams['checkorName']) ? trim($arrParams['checkorName']) : ''
        );

        $ret = $this->objDaoCourseCheck->insertRecords($arrFields);
        if ($ret) {
            $ret = $this->objDaoCourseCheck->getInsertId();
        }
        return $ret;
    }

    /**
     * 更新课程
     *
     * @param  int   $id  课程id
     * @param  array $arrParams 课程属性
     * @return bool true/false
     */
    public function updateCourseCheck($id, $arrParams) {
        if (intval($id) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[id:$id]");
            return false;
        }
        $arrConds = array(
            'id' => intval($id),
        );
        $arrFields = array();
        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach ($arrParams as $key => $value) {
            if (!in_array($key, $arrAllFields)) {
                continue;
            }
            $arrFields[$key] = $value;
        }

        //ext由ps补全，json处理在ds
        if (isset($arrFields['extData'])) {
            $arrFields['extData'] = json_encode($arrFields['extData']);
        }

        //补全更新时间
        $arrFields['updateTime'] = time();
        $ret = $this->objDaoCourseCheck->updateByConds($arrConds, $arrFields);

        return $ret;
    }

    /**撤销
     * @param $id
     * @param $reason
     * @param int $operatorUid
     * @param string $operator
     * @return bool
     */
    public function CourseCheckRevoke($id, $reason, $operatorUid = 0, $operator = '') {
        if (intval($id) <= 0 || intval($operatorUid) <= 0 || strlen($operator) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[id:$id operatorUid:$operatorUid operator:$operator]");
            return false;
        }

        $arrConds = array(
            'id' => intval($id),
        );
        $arrFields = array(
            'status'        => self::STATUS_REVOKE,
            'updateTime'    => time(),
        );
        $courseCheckInfo = $this->getCourseCheckInfo($id, array('extData'));
        $extData = $courseCheckInfo['extData'];
        $extData['revokeLog'] = array(
            'reason'      =>$reason,
            'operateTime' =>time(),
            'operator'    =>$operator,
            'operatorUid' =>$operatorUid,
        );
        $arrFields['extData'] = json_encode($extData);
        $ret = $this->objDaoCourseCheck->updateByConds($arrConds, $arrFields);

        return $ret;
    }

    /**获取记录
     * @param $id
     * @param array $arrFields
     * @return array|bool|false
     */
    public function getCourseCheckInfo($id,$arrFields = array()) {
        if (intval($id) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[$id]");
            return false;
        }
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds  = array(
            'id' => $id,
        );
        $ret = $this->objDaoCourseCheck->getRecordByConds($arrConds, $arrFields);
        return $ret;
    }

    /**通过章节Id获取所有的列表
     * @param int $lessonId
     * @param array $arrFields
     * @param string $order
     * @param string $by
     * @param int $offset
     * @param int $limit
     * @return array|false
     */
    public function getCourseCheckListByLessonId($lessonId=0,$arrFields = array(),$order = '',$by = '',$offset = 0,$limit = 0) {
        $arrConds = array(
            'lessonId'    => $lessonId,
        );
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        //排序
        $orderBy = '';
        if ($order != '' && in_array($order,$this->orderBy)) {
            $orderBy .= 'order by ' .$order . ' ';
            $orderBy .= ($by == 'desc') ? 'desc' : 'asc';
        }
        //limit限制
        if ($offset >= 0 && $limit > 0) {
            $orderBy .= " limit $offset,$limit";
        } else if ($limit > 0) {
            $orderBy .= " limit $limit";
        }
        $arrAppends = ($orderBy != '') ? array($orderBy) : NULL;
        $ret = $this->objDaoCourseCheck->getListByConds($arrConds,$arrFields, NULL, $arrAppends);
        return $ret;
    }

    /**通过章节Id获取所有的数量
     * @param int $lessonId
     * @return false|int
     */
    public function getCourseCheckCntByLessonId($lessonId=0) {
        $arrConds = array(
            'lessonId'    => $lessonId,
        );
        $ret = $this->objDaoCourseCheck->getCntByConds($arrConds);
        return $ret;
    }

    /**通过条件查询违规详情
     * @param $arrConds
     * @param array $arrFields
     * @param null $arrAppendExt
     * @return array|false
     */
    public function getCheckCourseListByConds($arrConds, $arrFields = array(), $arrAppendExt = NULL) {
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrAppends = array(
            "order by create_time desc",
        );

        if (!empty($arrAppendExt)) {
            $arrAppends = $arrAppendExt;
        }
        $ret = $this->objDaoCourseCheck->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    /** 通过条件获取所有的数量
     * @param $arrConds
     * @return false|int
     */
    public function getCourseCheckCntByArrConds($arrConds) {
        $ret = $this->objDaoCourseCheck->getCntByConds($arrConds);
        return $ret;
    }

}
