<?php
/***************************************************************************
 *
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/



/**
 * @file StudentAttention.php
 * <AUTHOR>
 * @date 2015/11/17 14:13:18
 * @brief 学生关注老师
 *
 **/

class Hkzb_Ds_Fudao_StudentAttention {

    private $cache;

    public function __construct($cache = null) {
        if ($cache === null) {
            $cache = Hk_Service_Memcached::getInstance('zhiboke');
        }
        $this->cache = $cache;
    }

    //状态
    const STATUS_OK      = 0;
    const STATUS_DELETED = 1;

    const ALL_FIELDS = 'studentUid,teacherUid,deleted,createTime,updateTime,extData';

    const STUDENT_CACHE_KEY = 'zhiboke_ds_studentattention_studentuid_';

    /**
     * 新增学生老师
     *
     * @param  mix  $arrParams 班级属性
     * @return bool true/false
     */
    public function addStudentAttention($arrParams) {
        if(intval($arrParams['studentUid']) <= 0 || intval($arrParams['teacherUid']) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[param: ". json_encode($arrParams). "]");
            return false;
        }

        $arrFields = array(
            'studentUid'   => intval($arrParams['studentUid']),
            'teacherUid'   => intval($arrParams['teacherUid']),
            'deleted'      => self::STATUS_OK,
            'createTime'   => time(),
            'updateTime'   => time(),
            'extData'      => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '',
        );

        $objDaoStudentAttention = new Hkzb_Dao_Fudao_StudentAttention();
        $ret = $objDaoStudentAttention->insertRecords($arrFields);

        //清理缓存
        $cacheKey = self::STUDENT_CACHE_KEY . intval($arrParams['studentUid']);
        $this->cache->delete($cacheKey);

        return $ret;
    }

    /**
     * 更新学生收集课程
     *
     * @param  int  $studentUid 学生uid
     * @param  int  $teacherUid 教师uid
     * @return bool true/false
     */
    public function updateStudentAttention($studentUid, $teacherUid) {
        if(intval($studentUid) <= 0 || intval($teacherUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid teacherUid:$teacherUid]");
            return false;
        }

        $arrConds = array(
            'studentUid' => intval($studentUid),
            'teacherUid'   => intval($teacherUid),
        );

        $arrFields = array(
            'deleted'    => self::STATUS_OK,
            'updateTime' => time(),
        );

        $objDaoStudentAttention = new Hkzb_Dao_Fudao_StudentAttention();
        $ret = $objDaoStudentAttention->updateByConds($arrConds, $arrFields);

        //清理缓存
        $cacheKey = self::STUDENT_CACHE_KEY . $studentUid;
        $this->cache->delete($cacheKey);

        return $ret;
    }


    /**
     * 删除学生收集课程
     *
     * @param  int  $studentUid 学生uid
     * @param  int  $teacherUid   课程id
     * @return bool true/false
     */
    public function deleteStudentAttention($studentUid, $teacherUid) {
        if(intval($studentUid) <= 0 || intval($teacherUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid teacherUid:$teacherUid]");
            return false;
        }

        $arrConds = array(
            'studentUid' => intval($studentUid),
            'teacherUid'   => intval($teacherUid),
        );

        $arrFields = array(
            'deleted'    => self::STATUS_DELETED,
            'updateTime' => time(),
        );

        $objDaoStudentAttention = new Hkzb_Dao_Fudao_StudentAttention();
        $ret = $objDaoStudentAttention->updateByConds($arrConds, $arrFields);

        //清理缓存
        $cacheKey = self::STUDENT_CACHE_KEY . $studentUid;
        $this->cache->delete($cacheKey);

        return $ret;
    }


    /**
     * 获取学生关注老师详情
     *
     * @param  int  $studentUid 学生uid
     * @param  int  $teacherUid   课程id
     * @param  mix  $arrFields  指定属性
     * @return mix
     */
    public function getStudentAttentionInfo($studentUid, $teacherUid, $arrFields = array()) {
        if(intval($studentUid) <= 0 || intval($teacherUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid teacherUid:$teacherUid]");
            return false;
        }

        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'studentUid' => intval($studentUid),
            'teacherUid'   => intval($teacherUid),
        );

        $objDaoStudentAttention = new Hkzb_Dao_Fudao_StudentAttention();
        $ret = $objDaoStudentAttention->getRecordByConds($arrConds, $arrFields);

        return $ret;
    }


    /**
     * 获取数量
     * @param $arrConds
     * @return false|int
     */
    public function getCntByConds($arrConds) {
        $arrConds['deleted'] = self::STATUS_OK;
        $objDaoStudentAttention = new Hkzb_Dao_Fudao_StudentAttention();
        $ret = $objDaoStudentAttention->getCntByConds($arrConds);
        return $ret;
    }


    /**
     * 获取指定学生关注教师
     *
     * @param  int  $studentUid  学生uid
     * @return mix
     */
    public function getTeacherListByStudentUid($studentUid) {
        if(intval($studentUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid]");
            return false;
        }

        //读缓存
        $cacheKey = self::STUDENT_CACHE_KEY . $studentUid;
        $cacheValue = $this->cache->get($cacheKey);
        if(!empty($cacheValue)) {
            $teacherUidList = json_decode(utf8_encode($cacheValue), true);
            return $teacherUidList;
        }

        //读数据库
        $arrConds = array(
            'studentUid' => intval($studentUid),
            'deleted'    => self::STATUS_OK,
        );

        $objDaoStudentAttention = new Hkzb_Dao_Fudao_StudentAttention();
        $teacherList = $objDaoStudentAttention->getListByConds($arrConds, array('teacherUid'));
        if(false === $teacherList) {
            Bd_Log::warning("Error:[getListByConds], Detail:[studentUid:$studentUid]");
            return false;
        }

        $teacherUidList = array();
        foreach($teacherList as $teacher) {
            if($teacher['teacherUid'] != 2144805210) {
                $teacherUidList[] = intval($teacher['teacherUid']);
            }
        }

        //写缓存
        $cacheValue = json_encode($teacherUidList);
        $this->cache->set($cacheKey, $cacheValue);
        //去除特殊老师
        $teacherUidsList =[];
        foreach($teacherUidList as $uid){
            if($uid != 2144805210){
                $teacherUidsList[] = intval($uid);
            }
        }
        return $teacherUidsList;
    }

    /**
     * 获取指定老师粉丝
     *
     * @param  int  $studentUid  学生uid
     * @param  mix  $arrFields   指定属性
     * @return mix
     */
    public function getStudentListByTeacherUid($teacherUid, $arrFields = array(),$order = 'create_time',$sort = 'desc',$offset = 0, $limit = 0) {
        if(intval($teacherUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[teacherUid:$teacherUid]");
            return false;
        }

        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'teacherUid' => intval($teacherUid),
            'deleted'    => self::STATUS_OK,
        );

        $arrAppends = array(
            "order by $order $sort",
            "limit $offset, $limit",
        );

        $objDaoStudentAttention = new Hkzb_Dao_Fudao_StudentAttention();
        $ret = $objDaoStudentAttention->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }
    /**
     * 获取数量
     * @param $arrConds
     * @return false|int
     */
    public function getTeacherCntByStudentUid($studentUid) {
        $arrConds = array(
            'studentUid' => intval($studentUid),
            'deleted'    => self::STATUS_OK,
        );
        $objDaoStudentAttention = new Hkzb_Dao_Fudao_StudentAttention();
        $ret = $objDaoStudentAttention->getCntByConds($arrConds);
        return $ret;
    }
    /**
     * 获取粉丝数量
     * @param $arrConds
     * @return false|int
     */
    public function getStudentCntByTeacherUid($teacherUid) {
        $arrConds = array(
            'teacherUid' => intval($teacherUid),
            'deleted'    => self::STATUS_OK,
        );
        $objDaoStudentAttention = new Hkzb_Dao_Fudao_StudentAttention();
        $ret = $objDaoStudentAttention->getCntByConds($arrConds);

        return $ret;
    }

}
