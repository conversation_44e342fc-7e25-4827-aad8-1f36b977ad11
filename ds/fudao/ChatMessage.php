<?php
/***************************************************************************
 *
 * Copyright (c) 2015 Zybang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file   ChatMessage.php
 * <AUTHOR>
 * @date   2015-11-19
 * @brief  聊天信息
 *
 **/
class Hkzb_Ds_Fudao_ChatMessage
{
    const UTYPE_STUDENT                 = 1; //学生
    const UTYPE_TEACHER                 = 2; //老师
    const UTYPE_ASSISTANT               = 3; //助教
    const MESSAGETYPE_STUDENT_ASSISTANT = 1; //学生-辅导老师之间
    const MESSAGETYPE_TEACHER_ASSISTANT = 2; //老师-辅导老师之间

    const MESSAGESTATUS_OK = 0;  //消息状态

    const ALL_FIELDS = 'id,rid,lessonId,classId,uid,touid,uname,utype,type,content,status,createTime,extData';

    private $_objDaoChatMessage;

    public function __construct() {
        $this->_objDaoChatMessage = new Hkzb_Dao_Fudao_ChatMessage();
    }


    /**
     * 新增聊天信息
     * @param integer $courseId
     * @param array   $arrParams
     * @return array('errno'=>0)
     */
    public function addChatMessage($arrParams) {
        //校验参数合法
        if (0 >= $arrParams['lessonId'] || 0 >= $arrParams['uid'] || strlen($arrParams['content']) <= 0 || !isset($arrParams['uname']) || !isset($arrParams['utype']) || !isset($arrParams['type'])
        ) {
            Bd_Log::warning("Error[paramError] Detail[lessonId:{$arrParams['lesson_id']} uid:{$arrParams['uid']}]");

            return false;
        }

        //获取子课id
        $lessonId = intval($arrParams['lessonId']);
        $objLesson = new Hkzb_Ds_Fudao_Lesson();
        $lessonInfo = $objLesson->getLessonInfo($lessonId, array('courseId'));
        if(empty($lessonInfo)) {
            Bd_Log::warning("Error:[get getLessonInfo error], Detail:[lessonId:$lessonId]");
            return false;
        }

        $subCourseId = intval($lessonInfo['courseId']);

        $objIdAlloc = new Hk_Service_IdAlloc(Hk_Service_IdAlloc::NAME_FUDAO_CHAT);
        $chatId = $objIdAlloc->getIdAlloc();
        if(false === $chatId) {
            Bd_Log::warning("Error:[service idalloc error], Detail:[name:fudao_chat_id]");
            return false;
        }

        //处理数据的存储格式细节
        $arrCommand = array(
            'rid'        => $chatId,
            'courseId'   => $subCourseId,
            'lessonId'   => $lessonId,
            'classId'    => isset($arrParams['classId']) ? intval($arrParams['classId']) : 0,
            'uid'        => intval($arrParams['uid']),
            'touid'      => isset($arrParams['touid']) ? intval($arrParams['touid']) : 0,
            'uname'      => strval($arrParams['uname']),
            'utype'      => intval($arrParams['utype']),
            'type'       => intval($arrParams['type']),
            'content'    => strval($arrParams['content']),
            'status'     => isset($arrParams['status']) ? intval($arrParams['status']) : 0,
            'createTime' => isset($arrParams['createTime']) ? intval($arrParams['createTime']) : time(),
            'extData'    => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '{}',
        );

        //[BDY] 临时屏蔽聊天消息入库
        if(true) {
            $objNmq = new Hk_Service_Nmq();
            $objNmq->talkToQcm(Hk_Const_Command::CMD_FUDAO_CHATMESSAGE, $arrCommand);
        }

        return $chatId;
    }

    /**
     * 异步写聊天记录
     * @param array   $arrParams
     * @return bool
     */
    public function addChatMessageCommit($arrParams) {
        $arrFields = array(
            'rid'        => intval($arrParams['rid']),
            'courseId'   => intval($arrParams['courseId']),
            'lessonId'   => intval($arrParams['lessonId']),
            'classId'    => isset($arrParams['classId']) ? intval($arrParams['classId']) : 0,
            'uid'        => intval($arrParams['uid']),
            'touid'      => isset($arrParams['touid']) ? intval($arrParams['touid']) : 0,
            'uname'      => strval($arrParams['uname']),
            'utype'      => intval($arrParams['utype']),
            'type'       => intval($arrParams['type']),
            'content'    => strval($arrParams['content']),
            'status'     => isset($arrParams['status']) ? intval($arrParams['status']) : 0,
            'createTime' => isset($arrParams['createTime']) ? intval($arrParams['createTime']) : time(),
            'extData'    => isset($arrParams['extData']) ? $arrParams['extData'] : '',
        );
        $ret = $this->_objDaoChatMessage->insertRecords(intval($arrParams['courseId']), $arrFields);
        return $ret;
    }

    /**
     * 单人拉取消息  id范围为[index , infinite)
     * @param int   $lessonId
     * @param int   $classId
     * @param int   $type
     * @param int   $startIndex
     * @param int   $touid
     * @param int   $uid
     * @param array $arrFields
     * @param int   $offset 页码，从0开始
     * @param int   $limit  返回记录数
     * @return bool
     */
    public function pullMessageByIndex($lessonId, $classId, $type, $startIndex, $touid = 0, $uid = 0,
                                       $arrFields = array(), $offset = 0, $limit = 200
    ) {
        if (0 >= $lessonId || !isset($type) || 0 >= $startIndex) {
            Bd_Log::warning("Error[paramError] Detail[lessonId:{$lessonId} type:{$type} startIndex:{$startIndex} touid:{$touid}]");

            return false;
        }

        //获取子课id
        $objLesson = new Hkzb_Ds_Fudao_Lesson();
        $lessonInfo = $objLesson->getLessonInfo($lessonId, array('courseId'));
        if(empty($lessonInfo)) {
            Bd_Log::warning("Error:[get getLessonInfo error], Detail:[lessonId:$lessonId]");
            return false;
        }

        $subCourseId = intval($lessonInfo['courseId']);

        //学生和辅导老师之间的聊天 必须传入班级id
        if (($type == Service_Data_Chat_ChatMessage::MESSAGETYPE_STUDENT_ASSISTANT)
            && 0 >= intval($classId)
        ) {
            Bd_Log::warning("Error[paramError] Detail[student get message need classId]");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrConds = array(
            'courseId' => $subCourseId,
            'lessonId' => intval($lessonId),
            'classId'  => intval($classId),
            'type'     => intval($type),
            'status'   => self::MESSAGESTATUS_OK,
        );

        //高中实验章节不需要按照班级搜索【硬伤】：命中不了索引了
        if(Chat_Util::isLessonInLab($lessonId)){
            unset($arrConds['classId']);
        }

        $touid = intval($touid);
        $uid = intval($uid);
        if ($touid > 0) {
            //拉取私聊消息
            $arrConds[] = "touid in ($touid,$uid)";
            $arrConds[] = "uid in ($touid,$uid)";
        }else{
            $arrConds['touid'] = 0;
        }
        $arrConds[] = "id >=" . intval($startIndex);
        $arrAppends = array("limit $offset, $limit");
        $ret        = $this->_objDaoChatMessage->getListByConds($subCourseId, $arrConds, $arrFields, NULL, $arrAppends);
        if ($ret === false) {
            Bd_Log::warning("Error[dbError] Detail[db select message failed]");

            return false;
        }

        // 封装权限信息
        $ret = $this->enclosurePrivilegeInfo($ret);

        return $ret;
    }

    /**
     * 单人拉取消息 按时间拉取  时间范围为[start,end]
     * @param int   $lessonId
     * @param int   $classId
     * @param int   $type
     * @param int   $startTime
     * @param int   $endTime
     * @param int   $touid
     * @param int   $uid
     * @param array $arrFields
     * @param int   $offset 页码，从0开始
     * @param int   $limit  返回记录数
     * @return bool
     */
    public function pullMessageByTime($lessonId, $classId, $type, $startTime, $endTime, $touid = 0, $uid = 0,
                                      $arrFields = array(), $offset = 0, $limit = 200
    ) {
        if (0 >= $lessonId || !isset($type) || 0 >= $startTime) {
            Bd_Log::warning("Error[paramError] Detail[lessonId:{$lessonId} type:{$type} startTime:{$startTime} touid:{$touid}]");

            return false;
        }

        //获取子课id
        $objLesson = new Hkzb_Ds_Fudao_Lesson();
        $lessonInfo = $objLesson->getLessonInfo($lessonId, array('courseId'));
        if(empty($lessonInfo)) {
            Bd_Log::warning("Error:[get getLessonInfo error], Detail:[lessonId:$lessonId]");
            return false;
        }

        $subCourseId = intval($lessonInfo['courseId']);

        //学生和辅导老师之间的聊天 必须传入班级id
        if (($type == Service_Data_Chat_ChatMessage::MESSAGETYPE_STUDENT_ASSISTANT)
            && 0 >= intval($classId)
        ) {
            Bd_Log::warning("Error[paramError] Detail[student get message need classId]");

            return false;
        }

        if (0 >= $endTime) {
            $endTime = time();
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrConds = array(
            'courseId' => $subCourseId,
            'lessonId' => intval($lessonId),
            'classId'  => intval($classId),
            'type'     => intval($type),
            'status'   => self::MESSAGESTATUS_OK,
        );

        //高中实验章节不需要按照班级搜索【硬伤】：命中不了索引了
        if(Chat_Util::isLessonInLab($lessonId)){
            unset($arrConds['classId']);
        }

        $touid = intval($touid);
        $uid = intval($uid);
        if ($touid > 0) {
            //拉取私聊消息
            $arrConds[] = "touid in ($touid,$uid)";
            $arrConds[] = "uid in ($touid,$uid)";
        }else{
            $arrConds['touid'] = 0;
        }
        $arrConds[] = "create_time >=" . intval($startTime);
        $arrConds[] = "create_time <=" . intval($endTime);

        $arrAppends = array("limit $offset, $limit");
        $ret        = $this->_objDaoChatMessage->getListByConds($subCourseId, $arrConds, $arrFields, NULL, $arrAppends);
        if ($ret === false) {
            Bd_Log::warning("Error[dbError] Detail[db select message failed]");

            return false;
        }

        // 封装权限信息
        $ret = $this->enclosurePrivilegeInfo($ret);

        return $ret;
    }

    /**
     * 单人拉取最近的消息
     * @param int   $lessonId
     * @param int   $classId
     * @param int   $type
     * @param int   $touid
     * @param int   $uid
     * @param array $arrFields
     * @param int   $offset 页码，从0开始
     * @param int   $limit  返回记录数
     * @return bool
     */
    public function pullLatestMessage($lessonId, $classId, $type, $touid = 0, $uid = 0,
                                      $arrFields = array(),
                                      $offset = 0, $limit = 200
    ) {
        if (0 >= $lessonId || !isset($type)) {
            Bd_Log::warning("Error[paramError] Detail[lessonId:{$lessonId} type:{$type} touid:{$touid}]");

            return false;
        }

        //获取子课id
        $objLesson = new Hkzb_Ds_Fudao_Lesson();
        $lessonInfo = $objLesson->getLessonInfo($lessonId, array('courseId'));
        if(empty($lessonInfo)) {
            Bd_Log::warning("Error:[get getLessonInfo error], Detail:[lessonId:$lessonId]");
            return false;
        }

        $subCourseId = intval($lessonInfo['courseId']);

        //学生和辅导老师之间的聊天 必须传入班级id
        if (($type == Service_Data_Chat_ChatMessage::MESSAGETYPE_STUDENT_ASSISTANT)
            && 0 >= intval($classId)
        ) {
            Bd_Log::warning("Error[paramError] Detail[student get message need classId]");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrConds = array(
            'courseId' => $subCourseId,
            'lessonId' => intval($lessonId),
            'classId'  => intval($classId),
            'type'     => intval($type),
            'status'   => self::MESSAGESTATUS_OK,
        );

        //高中实验章节不需要按照班级搜索【硬伤】：命中不了索引了
        if(Chat_Util::isLessonInLab($lessonId)){
            unset($arrConds['classId']);
        }

        $touid = intval($touid);
        $uid = intval($uid);
        if ($touid > 0) {
            //拉取私聊消息
            $arrConds[] = "touid in ($touid,$uid)";
            $arrConds[] = "uid in ($touid,$uid)";
        }else{
            $arrConds['touid'] = 0;
        }
        $arrAppends = array(
            "order by id desc ",
            "limit $offset, $limit"
        );
        $ret        = $this->_objDaoChatMessage->getListByConds($subCourseId, $arrConds, $arrFields, NULL, $arrAppends);
        if ($ret === false) {
            Bd_Log::warning("Error[dbError] Detail[db select message failed]");

            return false;
        }

        // 封装权限信息
        $ret = $this->enclosurePrivilegeInfo($ret);

        return $ret;
    }

    /**
     * 单人拉取消息  id范围为(0,endIndex)
     * @param int   $lessonId
     * @param int   $classId
     * @param int   $type
     * @param int   $endIndex
     * @param int   $touid
     * @param int   $uid
     * @param array $arrFields
     * @param int   $offset 页码，从0开始
     * @param int   $limit  返回记录数
     * @return bool
     */
    public function pullMessageByEndIndex($lessonId, $classId, $type, $endIndex, $touid = 0, $uid = 0,
                                          $arrFields = array(), $offset = 0, $limit = 200
    ) {
        if (0 >= $lessonId || !isset($type) || 0 >= $endIndex) {
            Bd_Log::warning("Error[paramError] Detail[lessonId:{$lessonId} type:{$type} endIndex:{$endIndex} touid:{$touid}]");

            return false;
        }

        //获取子课id
        $objLesson = new Hkzb_Ds_Fudao_Lesson();
        $lessonInfo = $objLesson->getLessonInfo($lessonId, array('courseId'));
        if(empty($lessonInfo)) {
            Bd_Log::warning("Error:[get getLessonInfo error], Detail:[lessonId:$lessonId]");
            return false;
        }

        $subCourseId = intval($lessonInfo['courseId']);

        //学生和辅导老师之间的聊天 必须传入班级id
        if (($type == Service_Data_Chat_ChatMessage::MESSAGETYPE_STUDENT_ASSISTANT)
            && 0 >= intval($classId)
        ) {
            Bd_Log::warning("Error[paramError] Detail[student get message need classId]");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrConds = array(
            'courseId' => $subCourseId,
            'lessonId' => intval($lessonId),
            'classId'  => intval($classId),
            'type'     => intval($type),
            'status'   => self::MESSAGESTATUS_OK,
        );

        //高中实验章节不需要按照班级搜索【硬伤】：命中不了索引了
        if(Chat_Util::isLessonInLab($lessonId)){
            unset($arrConds['classId']);
        }

        $touid = intval($touid);
        $uid = intval($uid);
        if ($touid > 0) {
            //拉取私聊消息
            $arrConds[] = "touid in ($touid,$uid)";
            $arrConds[] = "uid in ($touid,$uid)";
        }else{
            $arrConds['touid'] = 0;
        }
        //$arrConds[] = "id < " . intval($endIndex);
        //更改为rid
        $arrConds[] = "rid < " . intval($endIndex);
        $arrAppends = array(
            "order by rid desc ",
            "limit $offset, $limit",
        );
        $ret        = $this->_objDaoChatMessage->getListByConds($subCourseId, $arrConds, $arrFields, NULL, $arrAppends);
        if ($ret === false) {
            Bd_Log::warning("Error[dbError] Detail[db select message failed]");

            return false;
        }

        // 封装权限信息
        $ret = $this->enclosurePrivilegeInfo($ret);

        return $ret;
    }

    // 封装权限信息
    protected function enclosurePrivilegeInfo($arrChatList) {
        $intPrivilege = Hkzb_Ds_Fudao_ScoreGoods::SCORE_GOODS_COLORFUL_WORD_PRIVILEGE;
        foreach ( $arrChatList as $key=> $value) {
            if (!isset($value['extData'])) {
                continue;
            }
            $content = strval($value['content']);
            $arrExtData = $value['extData'];
            if (isset($arrExtData['privilege'][$intPrivilege]) && !empty($arrExtData['privilege'][$intPrivilege]) ) {
                // 聊天特权
                $arrChatList[ $key][ 'privilegeType' ]    = Hkzb_Ds_Fudao_ScoreGoods::SCORE_GOODS_TYPE_PRIVILEGE;
                $arrChatList[ $key][ 'privilegeSubtype' ] = Hkzb_Ds_Fudao_ScoreGoods::SCORE_GOODS_COLORFUL_WORD_PRIVILEGE;
                $arrChatList[ $key][ 'hasChatColorPrivilege' ] = 1;

                // 添加富文本，用于主讲端和web端展现
                $arrChatList[ $key][ 'rtfContent' ] = Chat_Util::getRtfContent($content);

            } else {
                // 没有权限
                $arrChatList[ $key][ 'privilegeType' ]    = 0;
                $arrChatList[ $key][ 'privilegeSubtype' ] = 0;
                $arrChatList[ $key][ 'hasChatColorPrivilege' ] = 0;
                $arrChatList[ $key][ 'rtfContent' ]       = $content;
            }
        }

        return $arrChatList;
    }
}
