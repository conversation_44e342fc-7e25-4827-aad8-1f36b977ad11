<?php
/***************************************************************************
 * Copyright (c) 2016 Zuoyebang.com, Inc. All Rights Reserved
 **************************************************************************/


/**
 * @file IMUser.php
 * <AUTHOR>
 * @date 2016/11/17
 * @brief 从IM模块收敛User DS层
 **/

class Hkzb_Ds_Fudao_IMUser
{
    const STATUS_DELETE    = 0;
    const STATUS_NORMAL    = 1;
    const STATUS_FROBIDDEN = 2;

    const TYPE_STUDENT    = 1;
    const TYPE_TEACHER    = 2;
    const TYPE_ASSISTANT  = 3;
    const TYPE_DISCIPLINE = 4;

    const ALL_FIELDS       = 'uid,nickName,userInfo,type,status,friendCount,friendGroup,groupCount,ctime,operator,utime,extData';
    const SINGLE_CACHE_KEY = 'IM_USER_DATA_UID_';
    const SINGLE_CHECK_KEY = 'IM_USER_CHECK_UID_';
    const IM_USER_KEY      = 'IM_USER_UID_';
    const CACHE_EXPIRE     = 300;

    //特权类型 -- 挂件
    const PRIVILEGE_TYPE_PENDANT = 'P_10';
    const PRIVILEGE_TYPE_BUBBLE = 'P_11';

    private $_objDaoUser;
    private $_objCache = null;

    private $_cacheSwitch = 0;

    function __construct() {
        $this->_objDaoUser = new Hkzb_Dao_Fudao_IMUser();
        if ($this->_cacheSwitch === 1) {
            $this->_objCache = Hk_Service_Memcached::getInstance('platactivity');
        }
    }

    /**
     * @brief 是否为IM用户
     * @param int  $intUid
     * @param bool $force
     * @return bool
     */
    public function checkIMUser($intUid, $force = false) {
        if (!is_numeric($intUid) || intval($intUid) <= 0) {
            Bd_Log::warning("Check IMUser detail error! Data: no uid!");

            return false;
        }

        if ($force !== true && $this->_cacheSwitch === 1 && !empty($this->_objCache)) {
            $strCacheKey = self::SINGLE_CHECK_KEY . intval($intUid);
            $res         = $this->_objCache->get($strCacheKey);
            if (!empty($res) && intval($res) === 1) {
                return true;
            }
        }
        $arrFields = array( 'uid', 'status' );

        $res = $this->_objDaoUser->getRecordByConds(array( 'uid' => intval($intUid) ), $arrFields);
        if (empty($res) || !is_array($res) || empty($res['uid']) || intval($res['status']) != self::STATUS_NORMAL) {
            return false;
        }

        if ($this->_cacheSwitch === 1 && !empty($this->_objCache)) {
            $strCacheKey = self::SINGLE_CHECK_KEY . intval($intUid);
            $this->_objCache->set($strCacheKey, 1, self::CACHE_EXPIRE);
        }

        return true;
    }

    /**
     * 获取用户特权信息列表
     *
     * @param $uids
     * @return array|bool
     */
    public function getUserPrivilegeMapByUids($uids){

        if(empty($uids) || !is_array($uids)){
            return false;
        }

        $uidChunkedList = array_chunk($uids, 500);
        $ret = array();
        foreach($uidChunkedList as $uidList) {
            $tmpRet = $this->_objDaoUser->getListByConds(array('uid in (' . implode(',', $uidList) . ')'), array('uid','extData'));
            $ret    = array_merge($ret, $tmpRet);
        }

        $result = array();
        foreach($ret as $userInfo){
            if(empty($userInfo['extData']['privilege'])){
                continue;
            }
            $result[intval($userInfo['uid'])] = array_values($userInfo['extData']['privilege']);
        }

        return $result;
    }

    /**
     * @brief 获取用户详情
     * @param int  $intUid
     * @param bool $force
     * @return array|bool|false|mixed
     */
    public function getIMUserDetail($intUid, $force = false) {
        if (!is_numeric($intUid) || intval($intUid) <= 0) {
            Bd_Log::warning("Get IMUser detail error! Data: no uid!");

            return false;
        }

        if ($force !== true && $this->_cacheSwitch === 1 && !empty($this->_objCache)) {
            $strCacheKey     = self::SINGLE_CACHE_KEY . intval($intUid);
            $strIMUserDetail = $this->_objCache->get($strCacheKey);
            $arrIMUserDetail = json_decode($strIMUserDetail, true);
            if (!empty($arrIMUserDetail) && is_array($arrIMUserDetail) && !empty($arrIMUserDetail['uid'])) {
                return $arrIMUserDetail;
            }
        }
        $arrFields = explode(',', self::ALL_FIELDS);

        $arrIMUserDetail = $this->_objDaoUser->getRecordByConds(array( 'uid' => intval($intUid) ), $arrFields);
        if (empty($arrIMUserDetail) || !is_array($arrIMUserDetail) || empty($arrIMUserDetail['uid'])) {
            Bd_Log::warning("Get IMUser detail DB error!");

            return false;
        }

        if ($this->_cacheSwitch === 1 && !empty($this->_objCache)) {
            $strCacheKey = self::SINGLE_CACHE_KEY . intval($intUid);
            $this->_objCache->set($strCacheKey, json_encode($arrIMUserDetail), self::CACHE_EXPIRE);
        }

        return $arrIMUserDetail;
    }

    public function addIMUser($arrOrigFields) {
        if (intval($arrOrigFields['uid']) <= 0) {
            Bd_Log::warning("Insert IMUser params error! Data:" . json_encode($arrOrigFields));

            return false;
        }

        $arrFields = array(
            'uid'         => intval($arrOrigFields['uid']),
            'nickName'    => isset($arrOrigFields['nickName']) ? strval($arrOrigFields['nickName']) : '',
            'userInfo'    => (isset($arrOrigFields['userInfo']) && is_array($arrOrigFields['userInfo'])) ? json_encode($arrOrigFields['userInfo']) : '[]',
            'type'        => intval($arrOrigFields['type']),
            'status'      => intval($arrOrigFields['status']),
            'friendCount' => intval($arrOrigFields['friendCount']),
            'friendGroup' => (isset($arrOrigFields['friendGroup']) && is_array($arrOrigFields['friendGroup'])) ? json_encode($arrOrigFields['friendGroup']) : '[]',
            'groupCount'  => intval($arrOrigFields['groupCount']),
            'ctime'       => time(),
            'operator'    => 0,
            'utime'       => 0,
            'extData'     => (isset($arrOrigFields['extData']) && is_array($arrOrigFields['extData'])) ? json_encode($arrOrigFields['extData']) : '[]',
        );

        $ret = $this->_objDaoUser->insertRecords($arrFields);
        if ($ret === false) {
            Bd_Log::warning("Insert IMUser DB error!");

            return false;
        }

        return true;
    }

    public function getIMUserDetailForUpdate($intUid, $arrFields = array()) {
        if (!is_numeric($intUid) || intval($intUid) <= 0) {
            Bd_Log::warning("Get IMUser detail for update error! Data: no uid!");

            return false;
        }

        if (empty($arrFields) || !is_array($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrIMUserDetail = $this->_objDaoUser->getRecordByConds(array( 'uid' => intval($intUid) ), $arrFields, null, array( 'FOR UPDATE' ));
        if (empty($arrIMUserDetail) || !is_array($arrIMUserDetail)) {
            Bd_Log::warning("Get IMUser detail for update DB error!");

            return false;
        }

        return $arrIMUserDetail;
    }

    public function updateIMUser($intUid, $arrFields) {
        if (!is_numeric($intUid) || intval($intUid) <= 0) {
            Bd_Log::warning("Update IMUser params error! Data: no uid!");

            return false;
        }

        $arrOrigData = $this->getIMUserDetail(intval($intUid));
        if (empty($arrOrigData['uid'])) {
            Bd_Log::warning("Update IMUser get orig data error!");

            return false;
        }
        if (isset($arrFields['extData']) && !empty($arrFields['extData']) && is_array($arrFields['extData'])) {
            $tmpExtData = array();
            if (!empty($arrOrigData['extData']) && is_array($arrOrigData['extData'])) {
                $tmpExtData = $arrOrigData['extData'];
            }
            foreach ($arrFields['extData'] as $key => $value) {
                $tmpExtData[$key] = $value;
            }
            $arrFields['extData'] = json_encode($tmpExtData);
        } else {
            unset($arrFields['extData']);
        }
        if (isset($arrFields['userInfo']) && !empty($arrFields['userInfo']) && is_array($arrFields['userInfo'])) {
            $tmpUserInfo = array();
            if (!empty($arrOrigData['userInfo']) && is_array($arrOrigData['userInfo'])) {
                $tmpUserInfo = $arrOrigData['userInfo'];
            }
            foreach ($arrFields['userInfo'] as $key => $value) {
                $tmpUserInfo[$key] = $value;
            }
            $arrFields['userInfo'] = json_encode($tmpUserInfo);
        } else {
            unset($arrFields['userInfo']);
        }

        $arrFields['operator'] = isset($arrFields['operator']) ? intval($arrFields['operator']) : 0;
        $arrFields['utime']    = time();

        $ret = $this->_objDaoUser->updateByConds(array( 'uid' => intval($intUid) ), $arrFields);
        if ($ret === false) {
            Bd_Log::warning("Update IMUser DB error!");

            return false;
        }

        //目前仅关注sql执行是否成功，并未返回影响行数，后续如需要自己增加
        if ($this->_cacheSwitch === 1 && !empty($this->_objCache)) {
            $strCacheKey = self::SINGLE_CACHE_KEY . intval($intUid);
            $this->_objCache->delete($strCacheKey);
        }

        return true;
    }

    /**
     * @brief 用户加入群
     * @param array $groupInfo
     * @param int   $uid
     * @return bool|int
     * @throws Hk_Util_Exception
     */
    public function joinGroup($groupInfo, $uid) {
        if (empty($groupInfo) || intval($uid) == 0) {
            return false;
        }

        $this->_objDaoUser->startTransaction();

        $now = time();
        $objDsUserGroup = new Hkzb_Ds_Fudao_IMUserGroup();
        $res            = $objDsUserGroup->isInGroup($groupInfo['groupId'], $uid);
        if (!$res) {
            $arrFields = array(
                'groupId'     => $groupInfo['groupId'],
                'uid'         => $uid,
                'status'      => Hkzb_Ds_Fudao_IMUserGroup::STATUS_ATTEND,
                'innerStatus' => Hkzb_Ds_Fudao_IMUserGroup::INNER_STATUS_NORMAL,
                'pushStatus'  => Hkzb_Ds_Fudao_IMUserGroup::PUSH_STATUS_TEACHER,
                'roleId'      => Hkzb_Ds_Fudao_IMUserGroup::ROLE_STUDENT,
                'attendTime'  => $now,
                'utime'       => $now,
            );

            if(!$objDsUserGroup->addIMGroupUser($arrFields)){
                return false;
            }
        } else {
            $arrInput = array(
                'status' => Hkzb_Ds_Fudao_IMUserGroup::STATUS_ATTEND,
                'utime'  => $now,
            );
            if (!$objDsUserGroup->updateIMGroupUserDetail($groupInfo['groupId'], $uid, $arrInput)) {
                return false;
            }
        }

        $resUser = 1;
        if (!$this->checkIMUser($uid)) {
            $arrFields = array(
                'uid'      => $uid,
                'nickname' => '',
                'userInfo' => array(),
                'type'     => self::TYPE_STUDENT,
                'status'   => self::STATUS_NORMAL,
                'ctime'    => time(),
            );
            if (!$resUser = $this->addIMUser($arrFields)) {
                $this->_objDaoUser->rollback();
            }
        }

        $this->_objDaoUser->commit();
        return ($resUser) ? 1 : 0;
    }

    /**
     * 向指定的用户添加已经购买成功的挂件
     *
     * @param array $arrParams 积分商城支付成功后回调参数
     * @return boolean
     */
    public function addPendant($arrParams)
    {

        if(empty($arrParams) || !is_array($arrParams) || !isset($arrParams['inputExt'])){
            Bd_Log::warning("Error:[param error], Detail:[params: ".var_export($arrParams, true)."]");
            return false;
        }
        $url = isset($arrParams['inputExt']['goodsExtInfo']['ornament']) ? $arrParams['inputExt']['goodsExtInfo']['ornament'] : '';
        $uid = intval($arrParams['uid']);
        $goodsId = intval($arrParams['goodsId']);
        $expTime = intval($arrParams['inputExt']['endTime']) * 1000;

        if($uid <= 0 || $goodsId <= 0){
            Bd_Log::warning("Error:[param error], Detail:[uid]:{$uid} [goodsId]:{$goodsId}");
            return false;
        }

        $type = self::PRIVILEGE_TYPE_PENDANT;
        $imUserInfo = $this->_objDaoUser->getRecordByConds(array('uid' => $uid), array('extData'));

        if(empty($imUserInfo)){
            Bd_Log::warning("Error:[addPendant]:get IMUserInfo failed, Detail:[uid]:{$uid}");
            return false;
        }

        if(!isset($imUserInfo['extData']['privilege'])) {
            $imUserInfo['extData']['privilege'] = array(
                $type => array('ext' => array(),),
            );
        }
        if(!isset($imUserInfo['extData']['privilege'][$type])) {
            $imUserInfo['extData']['privilege'][$type] = array(
                'ext' => array(),
            );
        }

        if(is_array($imUserInfo['extData']['privilege'][$type]['ext'])){
            $imUserInfo['extData']['privilege'][$type]['ext'] = json_encode($imUserInfo['extData']['privilege'][$type]['ext']);
        }
        $imUserInfo['extData']['privilege'][$type]['type'] = $type;
        $imUserInfo['extData']['privilege'][$type]['goodsId'] = $goodsId;
        $imUserInfo['extData']['privilege'][$type]['url'] = $url;
        $imUserInfo['extData']['privilege'][$type]['expTime'] = $expTime;
        $ret = $this->_objDaoUser->updateByConds(array('uid' => $uid), array('extData' => json_encode($imUserInfo['extData'])));

        if($ret){
            $objUserGroup = new Hkzb_Dao_Fudao_IMUserGroup();
            $ret = $objUserGroup->updateByConds(array('uid' => $uid), array('utime' => time()));
        }
        if(!$ret) {
            Bd_Log::warning("Error:[addPendant]:update privilege failed, Detail:[uid]:{$uid} [type]:{$type} [extData]:" . json_encode($imUserInfo['extData']));
        }

        Hkzb_Util_Fudao_IMActQueue::add($uid, Hkzb_Util_Fudao_IMActQueue::ACT_NO_USER_PRIVILEGE);

        return $ret;
    }

    /**
     * 向指定的用户添加已经购买成功的气泡
     *
     * @param array $arrParams 积分商城支付成功后回调参数
     * @return boolean
     */
    public function addBubble($arrParams)
    {

        if(empty($arrParams) || !is_array($arrParams) || !isset($arrParams['inputExt'])){
            Bd_Log::warning("Error:[param error], Detail:[params: ".var_export($arrParams, true)."]");
            return false;
        }
        $url = isset($arrParams['inputExt']['goodsExtInfo']['ornament']) ? $arrParams['inputExt']['goodsExtInfo']['ornament'] : '';
        $uid = intval($arrParams['uid']);
        $goodsId = intval($arrParams['goodsId']);
        $expTime = intval($arrParams['inputExt']['endTime']) * 1000;
        $fontColor = isset($arrParams['inputExt']['goodsExtInfo']['fontColor']) ? $arrParams['inputExt']['goodsExtInfo']['fontColor'] : '';

        if($uid <= 0 || $goodsId <= 0){
            Bd_Log::warning("Error:[param error], Detail:[uid]:{$uid} [goodsId]:{$goodsId}");
            return false;
        }

        $type = self::PRIVILEGE_TYPE_BUBBLE;
        $imUserInfo = $this->_objDaoUser->getRecordByConds(array('uid' => $uid), array('extData'));

        if(empty($imUserInfo)){
            Bd_Log::warning("Error:[addBubble]:get IMUserInfo failed, Detail:[uid]:{$uid}");
            return false;
        }

        if(!isset($imUserInfo['extData']['privilege'])) {
            $imUserInfo['extData']['privilege'] = array(
                $type => array('ext' => array(),),
            );
        }
        if(!isset($imUserInfo['extData']['privilege'][$type])) {
            $imUserInfo['extData']['privilege'][$type] = array(
                'ext' => array(),
            );
        }
        $imUserInfo['extData']['privilege'][$type]['type'] = $type;
        $imUserInfo['extData']['privilege'][$type]['goodsId'] = $goodsId;
        $imUserInfo['extData']['privilege'][$type]['url'] = $url;
        $imUserInfo['extData']['privilege'][$type]['ext'] = array("fontColor"=>$fontColor);
        $imUserInfo['extData']['privilege'][$type]['expTime'] = $expTime;

        $ret = $this->_objDaoUser->updateByConds(array('uid' => $uid), array('extData' => json_encode($imUserInfo['extData'])));

        if($ret){
            $objUserGroup = new Hkzb_Dao_Fudao_IMUserGroup();
            $ret = $objUserGroup->updateByConds(array('uid' => $uid), array('utime' => time()));
        }
        if(!$ret) {
            Bd_Log::warning("Error:[addBubble]:update privilege failed, Detail:[uid]:{$uid} [type]:{$type} [extData]:" . json_encode($imUserInfo['extData']));
        }

        Hkzb_Util_Fudao_IMActQueue::add($uid, Hkzb_Util_Fudao_IMActQueue::ACT_NO_USER_PRIVILEGE);

        return $ret;
    }


    /**
     * @brief 是否为群用户
     * @param int  $uid
     * @param null $objCache
     * @return bool
     */
    public static function isIMUser($uid, $objCache = null, $fromCache = false)
    {
        //从缓存是否在直播课学生中取
        if ($fromCache) {
            if (!$objCache) {
                $objCache = Hk_Service_RedisClient::getInstance("zbcourse");
            }
            $arrRedisConf = Bd_Conf::getConf("/hk/redis/fudao");
            $objRedis     = new Hk_Service_Redis($arrRedisConf['service']);

            $cacheKey = 'ZHIBOKE_STUDENT_' . $uid;
            if ($objRedis->get($cacheKey)) {
                return 1;
            }
        }

        //从老师列表中取
        $objDsTeacher = new Hkzb_Ds_Fudao_Teacher();
        if ($objDsTeacher->getTeacherInfo($uid, array(), true)) {
            return 1;
        }

        return 0;
    }

    /**
     * 记录用户最后使用IM功能的zbkv或者ykvc
     *
     * @param integer $intUid 用户id
     * @param integer $intZbkvc 直播课版本号
     * @param integer $intYkvc 一课版本号
     */
    public static function recordLastUsedVersionCode($intUid, $intZbkvc, $intYkvc, $intVc = 0){

        if($intUid <= 0 || ($intYkvc <= 0 && $intZbkvc <= 0) ){
            return;
        }

        $objCache = Hk_Service_RedisClient::getInstance("zhibo");
        $cacheKey    = 'IM_LAST_USE_APP_VER_' . $intUid;

        if($intYkvc > 0){
            $objCache->set($cacheKey, 80000 + $intYkvc);
        }else if($intZbkvc > 0){
            $objCache->set($cacheKey, 90000 + $intZbkvc);
        }else if($intVc > 0){
            $objCache->set($cacheKey, 70000 + $intVc);
        }
    }


    /**
     * 获得用户最后使用IM功能的zbkv或者ykvc
     *
     * 【注意】：一课版本号为：80000+真实版本号  主APP版本号为：90000+真实版本号
     *
     * @param integer $intUid 用户id
     * @return integer
     */
    public static function getLastUsedVersionCode($intUid){

        if($intUid <= 0){
            return 0;
        }

        $objCache = Hk_Service_RedisClient::getInstance("zhibo");
        $cacheKey    = 'IM_LAST_USE_APP_VER_' . $intUid;
        $lastUseAppVer = intval($objCache->get($cacheKey));

        return $lastUseAppVer;

    }

}