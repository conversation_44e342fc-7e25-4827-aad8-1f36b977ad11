<?php
/**
 * Created by PhpStorm.
 * User: <PERSON><PERSON><PERSON><PERSON>_niux<PERSON>gwen
 * Date: 2018/5/9
 * Time: 13:27
 */

class Hkzb_Ds_Fudao_ActivityPageCourse
{
    public static $ACTIVITY_COURSE_NORMAL = 0;
    public static $ACTIVITY_COURSE_DELETED = 1;

    public function __construct()
    {
        $this->_objDao = new Hkzb_Dao_Fudao_ActivityPageCourse();
    }

    /**
     * 获取活动课程
     * @param $intActivityId
     * @param bool $intDeleted
     * @param array $arrFields
     * @return array|bool|false
     */
    public function getCourseListByActivityId($intActivityId, $intDeleted = false, $arrFields = array(), $pn = false, $rn = false)
    {
        if (empty($intActivityId)) {
            return false;
        }
        $arrConds = array(
            'actId'   => intval($intActivityId),
            'deleted' => self::$ACTIVITY_COURSE_NORMAL,
        );
        if ($intDeleted !== false) {
            $arrConds['deleted'] = intval($intDeleted);
        }
        if (empty($arrFields)) {
            $arrFields = Hkzb_Dao_Fudao_ActivityPageCourse::$arrFields;
        }
        if ($pn !== false && $rn !== false) {
            $arrAppends = array(
                "order by create_time desc limit $pn, $rn",
            );
        } else {
            $arrAppends = array(
                "order by create_time desc",
            );
        }
        $res = $this->_objDao->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $res;
    }

    /**
     * 获取活动课程
     * @param $intActivityId
     * @param $intCourseId
     * @param array $arrFields
     * @return array|bool|false
     */
    public function getActivityCourse($intActivityId, $intCourseId, $arrFields = array())
    {
        if (empty($intActivityId) || empty($intCourseId)) {
            return false;
        }
        $arrConds = array(
            'actId'    => intval($intActivityId),
            'courseId' => intval($intCourseId),
        );
        if (empty($arrFields)) {
            $arrFields = Hkzb_Dao_Fudao_ActivityCourse::$arrFields;
        }
        $res = $this->_objDao->getRecordByConds($arrConds, $arrFields);

        return $res;
    }

    /**
     * 更新课程
     * @param $intActivityId
     * @param $intCourseId
     * @param $arrFields
     * @return bool
     */
    public function updateActivityCourse($intActivityId, $intCourseId, $arrFields)
    {
        if (empty($intActivityId) || empty($intCourseId) || empty($arrFields)) {
            return false;
        }
        $arrConds                = array(
            'actId'    => intval($intActivityId),
            'courseId' => intval($intCourseId),
        );
        $arrFields['updateTime'] = time();
        $res         = $this->_objDao->updateByConds($arrConds, $arrFields);

        return $res;
    }

    /**
     * 添加课程
     * @param $arrFields
     * @return bool
     */
    public function addActivityCourse($arrFields)
    {
        if (empty($arrFields)) {
            return false;
        }
        $arrInsert = array(
            'actId'      => isset($arrFields['actId']) ? intval($arrFields['actId']) : 0,
            'courseId'   => isset($arrFields['courseId']) ? intval($arrFields['courseId']) : 0,
            'createTime' => time(),
        );
        if (empty($arrInsert['actId']) || empty($arrInsert['courseId'])) {
            return false;
        }
        $res = $this->_objDao->insertRecords($arrInsert);

        return $res;
    }

    /**
     * 获取活动课程数量
     * @param $actId
     * @return false|int
     */
    public function getActivityCourseCnt($actId)
    {
        if (empty($actId)) {
            return 0;
        }
        $arrConds = array(
            'actId'   => intval($actId),
            'deleted' => self::$ACTIVITY_COURSE_NORMAL,
        );
        $cnt      = $this->_objDao->getCntByConds($arrConds);

        return $cnt;
    }

    /**
     * @param $intActivityId
     * @return bool
     * 删除活动时，标志性删除课程
     */

    public function updateActivityCourseByActivity($intActivityId){
        if (empty($intActivityId) ) {
            return false;
        }
        $arrConds                = array(
            'actId'    => intval($intActivityId),
           // 'courseId' => intval($intCourseId),
        );
        $arrFields['updateTime'] = time();
        $arrFields['deleted'] = 1;
        $res         = $this->_objDao->updateByConds($arrConds, $arrFields);

        return $res;
    }
}