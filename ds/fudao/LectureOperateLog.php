<?php
/**
 * File: LectureOperateLog.php
 * User: y<PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * Date: 2018/5/9 19:50
 * Brief: 章节讲义操作记录
 */

class Hkzb_Ds_Fudao_LectureOperateLog
{
    //讲义操作记录类型
    const ACTION_UPLOAD  = 1;  //上传
    const ACTION_MODIFIY = 2;  //修改 （二次上传）
    const ACTION_REVIEW  = 3;  //审核

    static $LECTURE_ACTION_ARRAY = array(
        self::ACTION_UPLOAD  => '上传',
        self::ACTION_MODIFIY => '修改',
        self::ACTION_REVIEW  => '审核',
    );

    //操作记录明细
    const DETAIL_REVIEW_PASS         = 1; //审核通过
    const DETAIL_REVIEW_NOPASSS      = 2; //审核驳回
    const DETAIL_WORKBENCH_UPLOAD    = 3; //工作台上传
    const DETAIL_TUTORMIS_UPLOAD     = 4; //辅导后台上传

    static $LECTURE_DETAIL_ARRAY = array(
        self::DETAIL_REVIEW_PASS      => '审核通过',
        self::DETAIL_REVIEW_NOPASSS   => '审核驳回',
        self::DETAIL_WORKBENCH_UPLOAD => '上传',
        self::DETAIL_TUTORMIS_UPLOAD  => '排课组上传',
    );


    //lecture_type讲义类型
    const LECTURE_TYPE_LECTURE        =1;//课件
    const LECTURE_TYPE_ATTACHED       =2;//学生资料or学生讲义
    const LECTURE_TYPE_PRELECTURE     =3;//教师讲义
    const LECTURE_TYPE_VIDEO_LIST     =4;//视频
    const LECTURE_TYPE_AUDIO_LIST     =5;//音频
    const LECTURE_TYPE_LONG_FILE_LIST =6;//长文章
    const LECTURE_TYPE_GRADUATION     =7;//结业典礼
    static $LECTURE_TYPE_ARRAY = array(
        self::LECTURE_TYPE_LECTURE        => '课件',
        self::LECTURE_TYPE_ATTACHED       => '学生资料',
        self::LECTURE_TYPE_PRELECTURE     => '教师讲义',
        self::LECTURE_TYPE_VIDEO_LIST     => '视频',
        self::LECTURE_TYPE_AUDIO_LIST     => '音频',
        self::LECTURE_TYPE_LONG_FILE_LIST => '长文章',
        self::LECTURE_TYPE_GRADUATION     => '结业典礼',
    );

    const  ALL_FIELDS = 'id,lessonId,operatorUid,roleType,action,detail,createTime,updateTime';

    private $_objLectureOperateLog;


    public function __construct() {
        $this->_objLectureOperateLog = new Hkzb_Dao_Fudao_LectureOperateLog();

    }

    /**
     * 格式化处理讲义相关操作记录insert操作
     * @param $inputParams
     * @param $detailAction
     * @param mixed $extdata  [数组：代表上传的格式， 字符串：代表驳回信息]
     * @return bool
     */
    public function addOperateLogFormat($inputParams, $detailAction, $extdata = null) {
        $strOperateDetail = self::$LECTURE_DETAIL_ARRAY[$detailAction];
        $arrLectureTypeMap = array_flip(Hkzb_Ds_Fudao_LectureReview::$LECTURE_TYPE_ARRAY);
        //获取提交所有不为空的资料
        $strDetailSuffix = '';
        if (is_array($extdata)) {
            foreach ($extdata as $key => $item) {
                if ((isset($item['data']) && $item['data'])) {
                    if (isset($arrLectureTypeMap[$key])) {
                        $type = $arrLectureTypeMap[$key];
                        $arrUpload[] = self::$LECTURE_TYPE_ARRAY[$type];
                    }
                }

                if ($item && !isset($item['data'])) {
                    if (isset($arrLectureTypeMap[$key])) {
                        $type = $arrLectureTypeMap[$key];
                        $arrUpload[] = self::$LECTURE_TYPE_ARRAY[$type];
                    }
                }
            }
            if (empty($arrUpload)) {
                return false;
            }
            $strDetailSuffix = '(' . implode('、', $arrUpload) . ')';
        }


        if (is_string($extdata) && $extdata) {
            $strDetailSuffix = '(意见:' . $extdata . ')';
        }

        $strOperateDetail .= $strDetailSuffix;

        $inputParams = array_merge($inputParams, array('detail' => $strOperateDetail));

        $intOperateLogId = $this->addOperateLog($inputParams);

        return $intOperateLogId;
    }

    /**
     * 新增操作记录
     * @param $arrParams
     * @return bool
     */
    public function addOperateLog($arrParams) {

        if (empty($arrParams) || intval($arrParams['lessonId']) <= 0 || intval($arrParams['operatorUid']) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[param: " . json_encode($arrParams) . "]");
            return false;
        }

        $arrFields = array(
            'lessonId'    => intval($arrParams['lessonId']),
            'operatorUid' => intval($arrParams['operatorUid']),
            'roleType'    => intval($arrParams['roleType']),
            'action'      => intval($arrParams['action']),
            'detail'      => strval($arrParams['detail']),
            'createTime'  => time(),
            'updateTime'  => time(),
        );
        $ret = $this->_objLectureOperateLog->insertRecords($arrFields);
        if ($ret) {
            $ret = $this->_objLectureOperateLog->getInsertId();
        }
        return $ret;
    }

    /**
     * 根据多条件获取
     * @param $arrCond
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @return mixed
     */
    public function getLectureListByCond($arrCond, $arrFields = array(), $offset = 0, $limit = 30) {

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrAppends = array(
            "order by id desc",
            "limit $offset, $limit",
        );

        $ret = $this->_objLectureOperateLog->getListByConds($arrCond, $arrFields, NULL, $arrAppends);
        return $ret;
    }

}