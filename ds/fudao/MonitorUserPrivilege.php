<?php
/***************************************************************************
 *
 * Copyright (c) 2016 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file MonitorPrivilege.php
 * <AUTHOR>
 * @date 2017/11/27 15:45
 * @brief 权限列表
 *
 **/
class Hkzb_Ds_Fudao_MonitorUserPrivilege
{

	private $_objDaoMonitor2Privilege = null;

	public function __construct()
	{
		$this->_objDaoMonitor2Privilege = new Hkzb_Dao_Fudao_MonitorUserPrivilege();
	}

	public function getMonitor2Privileges($arrConds = array())
	{
		if (empty($arrConds) || !$arrConds) {
			$arrConds = array("1=1");
		}
		$arrFields = array('*');
		$monitor2privileges = $this->_objDaoMonitor2Privilege->getListByConds($arrConds, $arrFields);
		return $monitor2privileges;
	}

	public function privilegeBind($arrInput)
	{
		$uid = $arrInput["uid"];
		unset($arrInput['uid']);
		$this->_objDaoMonitor2Privilege->deleteByConds(array("uid" => $uid));
		foreach ($arrInput['id'] as $id) {
			$per = array();
			$per['privilege_id'] = $id;
			$per['uid'] = $uid;
			$per['creator_uid'] = $arrInput['creator_uid'];
			$per['create_time'] = time();
			$per['update_time'] = time();
			$this->_objDaoMonitor2Privilege->insertRecords($per);
		}
		return true;
	}
}
