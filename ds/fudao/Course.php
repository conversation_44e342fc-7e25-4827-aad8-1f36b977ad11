<?php
/***************************************************************************
 *
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/



/**
 * @file Course.php
 * <AUTHOR>
 * @date 2015/11/17 14:13:18
 * @brief 课程
 *
 **/

class Hkzb_Ds_Fudao_Course
{

    //类型
    const TYPE_PRIVATE       = 0;
    const TYPE_PUBLIC        = 1;
    const TYPE_PRIVATE_LONG  = 2;
    const TYPE_PARENT_COURSE = 3;
    const TYPE_PRE_LONG      = 4;
    const STATUS_LESSON = 10;//备课
    const STATUS_NO_LESSON = 20;//未备课
    const STATUS_CLASS_ROOM = 30;//进入教室
    const STATUS_CLASS_ROOM_READY = 40;//灰色进入教室


    static $TYPE_ARRAY = array(
        self::TYPE_PRIVATE       => '专题课',
        self::TYPE_PUBLIC        => '公开课',
        self::TYPE_PRIVATE_LONG  => '班课',
        self::TYPE_PARENT_COURSE => '家长课',
        self::TYPE_PRE_LONG      => '试听课',
    );

    const PACK_NO   = 0;
    const PACK_YES  = 1;
    const PACK_YESD = 2;
    static $PACK_ARRAY = array(
        self::PACK_NO   => '非打包课',
        self::PACK_YES  => '打包课',
        self::PACK_YESD => '被打包课',
    );

    // 内外部
    const INNER_COURSE    = 1;
    const OUTER_COURSE    = 0;
    static $NATURE_ARRAY = array(
        self::OUTER_COURSE   => '外部',
        self::INNER_COURSE   => '内部',
    );

    //类型
    const DEGREE_LOWER  = 0;
    const DEGREE_HIGHER = 1;
    static $DEGREE_ARRAY = array(
        self::DEGREE_LOWER  => '神虎班',
        self::DEGREE_HIGHER => '神龙班',
    );
    //课程来源
    const FROM_MIS   = 1;
    const FROM_APPLY = 2;
    static $FROM_ARRAY = array(
        self::FROM_MIS   => '后台创建',
        self::FROM_APPLY => '老师申请',
    );

    //状态
    const STATUS_TOPREPARE = 0; //待备课
    const STATUS_TOAUDIT   = 1; //待审核
    const STATUS_TOONLINE  = 2; //待上线
    const STATUS_ONLINE    = 3; //已上线
    const STATUS_FINISHED  = 4; //已结束
    const STATUS_STOP      = 5; //已中止
    const STATUS_DELETED   = 6; //已删除
    static $STATUS_ARRAY = array(
        self::STATUS_TOPREPARE => '待备课',
        self::STATUS_TOAUDIT   => '待审核',
        self::STATUS_TOONLINE  => '待上线',
        self::STATUS_ONLINE    => '已上线',
        self::STATUS_FINISHED  => '已结束',
        self::STATUS_STOP      => '已中止',
        self::STATUS_DELETED   => '已删除',
    );
    //18项服务
    static $services = array(
        1  => '直播课',
        2  => '课程回放',
        3  => '周学习报告',
        4  => '学期学习报告',
        5  => '期中复习计划',
        6  => '期末复习计划',
        7  => '作业讲解',
        8  => '一对一批改',
        9  => '7×24答疑',
        10 => '错题本',
        11 => '个性化作业',
        12 => '天天练',
        13 => '试卷分析',
        14 => '短信提醒',
        15 => '电话提醒',
        16 => '家长课',
        17 => '升学指导',
        18 => '专属辅导老师',
        19 => '纸质教材',
        20 => '课程缓存',
        21 => '天天练',
        22 => '结课典礼',
        23 => '书包',
        24 => '文具',
    );
     static $giveServices =array(
        19 => '纸质教材',
        23 => '书包',
        24 => '文具',
     );

    //班课课程标签
    static $bkTag = array(
        '0' => array(
            'title'     => '辅',
            'desc'      => '专属辅导老师，作业批改',
            'color'     => 'FFFFFF',
            'backColor' => 'f6ab6a',
        ),
        '1' => array(
            'title'     => '书',
            'desc'      => '配套纸质版教材辅导',
            'color'     => 'FFFFFF',
            'backColor' => 'a4e748',
        ),
        '2' => array(
            'title'     => '赠',
            'desc'      => '专属精美赠品',
            'color'     => 'FFFFFF',
            'backColor' => 'ef77c7',
        ),
        '3' => array(
            'title'     => '限',
            'desc'      => '限量购买',
            'color'     => 'FFFFFF',
            'backColor' => 'c38ef4',
        ),
        '4' => array(
            'title'     => '服',
            'desc'      => '课程配套服务',
            'color'     => 'FFFFFF',
            'backColor' => 'f6699b',
        ),
        '5' => array(
            'title'     => '研',
            'desc'      => '科学研究的专属课程',
            'color'     => 'FFFFFF',
            'backColor' => 'f9a867',
        ),
        '6' => array(
            'title'     => '惠',
            'desc'      => '特惠课程',
            'color'     => 'FFFFFF',
            'backColor' => 'ff635b',
        ),
        '7' => array(
            'title'     => '热',
            'desc'      => '热课加开',
            'color'     => 'FFFFFF',
            'backColor' => 'f6699b',
        ),
        '8' => array(
            'title'     => '荐',
            'desc'      => '命中率超高的押题课',
            'color'     => 'FFFFFF',
            'backColor' => 'f6699b',
        ),
    );
    //专题课标签
    static $ztkTag = array(
        '0' => array(
            'title'     => '惠',
            'desc'      => '',
            'color'     => 'FFFFFF',
            'backColor' => 'ff635b',
        ),
        '1' => array(
            'title'     => '赠',
            'desc'      => '',
            'color'     => 'FFFFFF',
            'backColor' => 'ef77c7',
        ),
        '2' => array(
            'title'     => '研',
            'desc'      => '科学研究的专属课程',
            'color'     => 'FFFFFF',
            'backColor' => 'f9a867',
        ),
        '3' => array(
            'title'     => '热',
            'desc'      => '热课加开',
            'color'     => 'FFFFFF',
            'backColor' => 'f6699b',
        ),
        '4' => array(
            'title'     => '荐',
            'desc'      => '命中率超高的押题课',
            'color'     => 'FFFFFF',
            'backColor' => 'f6699b',
        ),
    );

    //标签信息
    static $tagList = array(
        self::TYPE_PRIVATE      => array(
            '0' => array(
                'title'     => '惠',
                'desc'      => '',
                'color'     => 'FFFFFF',
                'backColor' => 'ff635b',
            ),
            '1' => array(
                'title'     => '赠',
                'desc'      => '',
                'color'     => 'FFFFFF',
                'backColor' => 'ef77c7',
            ),
            '2' => array(
                'title'     => '研',
                'desc'      => '科学研究的专属课程',
                'color'     => 'FFFFFF',
                'backColor' => 'f9a867',
            ),
            '3' => array(
                'title'     => '热',
                'desc'      => '热课加开',
                'color'     => 'FFFFFF',
                'backColor' => 'f6699b',
            ),
            '4' => array(
                'title'     => '荐',
                'desc'      => '命中率超高的押题课',
                'color'     => 'FFFFFF',
                'backColor' => 'f6699b',
            ),
        ),
        self::TYPE_PRIVATE_LONG => array(
            '0' => array(
                'title'     => '辅',
                'desc'      => '专属辅导老师，作业批改',
                'color'     => 'FFFFFF',
                'backColor' => 'f6ab6a',
            ),
            '1' => array(
                'title'     => '书',
                'desc'      => '配套纸质版教材辅导',
                'color'     => 'FFFFFF',
                'backColor' => 'a4e748',
            ),
            '2' => array(
                'title'     => '赠',
                'desc'      => '专属精美赠品',
                'color'     => 'FFFFFF',
                'backColor' => 'ef77c7',
            ),
            '3' => array(
                'title'     => '限',
                'desc'      => '限量购买',
                'color'     => 'FFFFFF',
                'backColor' => 'c38ef4',
            ),
            '4' => array(
                'title'     => '服',
                'desc'      => '课程配套服务',
                'color'     => 'FFFFFF',
                'backColor' => 'f6699b',
            ),
            '5' => array(
                'title'     => '研',
                'desc'      => '科学研究的专属课程',
                'color'     => 'FFFFFF',
                'backColor' => 'f9a867',
            ),
            '6' => array(
                'title'     => '惠',
                'desc'      => '特惠课程',
                'color'     => 'FFFFFF',
                'backColor' => 'ff635b',
            ),
            '7' => array(
                'title'     => '热',
                'desc'      => '热课加开',
                'color'     => 'FFFFFF',
                'backColor' => 'f6699b',
            ),
            '8' => array(
                'title'     => '荐',
                'desc'      => '命中率超高的押题课',
                'color'     => 'FFFFFF',
                'backColor' => 'f6699b',
            ),
        ),
        self::TYPE_PRE_LONG     => array(
            '0' => array(
                'title'     => '辅',
                'desc'      => '专属辅导老师，作业批改',
                'color'     => 'FFFFFF',
                'backColor' => 'f6ab6a',
            ),
            '1' => array(
                'title'     => '书',
                'desc'      => '配套纸质版教材辅导',
                'color'     => 'FFFFFF',
                'backColor' => 'a4e748',
            ),
            '2' => array(
                'title'     => '赠',
                'desc'      => '专属精美赠品',
                'color'     => 'FFFFFF',
                'backColor' => 'ef77c7',
            ),
            '3' => array(
                'title'     => '限',
                'desc'      => '限量购买',
                'color'     => 'FFFFFF',
                'backColor' => 'c38ef4',
            ),
            '4' => array(
                'title'     => '服',
                'desc'      => '课程配套服务',
                'color'     => 'FFFFFF',
                'backColor' => 'f6699b',
            ),
            '5' => array(
                'title'     => '研',
                'desc'      => '科学研究的专属课程',
                'color'     => 'FFFFFF',
                'backColor' => 'f9a867',
            ),
            '6' => array(
                'title'     => '惠',
                'desc'      => '特惠课程',
                'color'     => 'FFFFFF',
                'backColor' => 'ff635b',
            ),
            '7' => array(
                'title'     => '热',
                'desc'      => '热课加开',
                'color'     => 'FFFFFF',
                'backColor' => 'f6699b',
            ),
            '8' => array(
                'title'     => '荐',
                'desc'      => '命中率超高的押题课',
                'color'     => 'FFFFFF',
                'backColor' => 'f6699b',
            ),
        ),
    );


    //课程属性
    static $attribute = array(
        1 => '运营活动',
    );

    static $seasonConf = array(
        '春_1' => 1,
        '春_2' => 1,
        '春_3' => 1,
        '春_4' => 1,
        '暑_0' => 2,
        '暑_1' => 2,
        '暑_2' => 2,
        '暑_3' => 2,
        '暑_4' => 2,
        '暑_5' => 2,
        '暑_6' => 2,
        '秋_1' => 3,
        '秋_2' => 3,
        '秋_3' => 3,
        '秋_4' => 3,
        '寒_0' => 4,
        '寒_1' => 4,
        '寒_2' => 4,
        '寒_3' => 4,
        '寒_4' => 4,
        '寒_5' => 4,
        '寒_6' => 4,
    );

    static $learnSeasonMap = array(
        '春季班' => array('春_1', '春_2', '春_3', '春_4'),
        '暑期班' => array('暑_0','暑_1', '暑_2', '暑_3', '暑_4', '暑_5', '暑_6'),
        '秋季班' => array('秋_1', '秋_2', '秋_3', '秋_4'),
        '寒假班' => array('寒_0','寒_1', '寒_2', '寒_3', '寒_4', '寒_5', '寒_6'),
    );

    static $seasonNameMap = array(
        '春_1' => '春一期',
        '春_2' => '春二期',
        '春_3' => '春三期',
        '春_4' => '春四期',
        '暑_0' => '暑',
        '暑_1' => '暑一期',
        '暑_2' => '暑二期',
        '暑_3' => '暑三期',
        '暑_4' => '暑四期',
        '暑_5' => '暑五期',
        '暑_6' => '暑六期',
        '秋_1' => '秋一期',
        '秋_2' => '秋二期',
        '秋_3' => '秋三期',
        '秋_4' => '秋四期',
        '寒_0' => '寒',
        '寒_1' => '寒一期',
        '寒_2' => '寒二期',
        '寒_3' => '寒三期',
        '寒_4' => '寒四期',
        '寒_5' => '寒五期',
        '寒_6' => '寒六期',
    );
    const ALL_FIELDS = 'teacherUid,teacherName,courseId,courseName,grade,subject,type,degree,price,registerStartTime,registerStopTime,studentCnt,studentMaxCnt,status,createTime,updateTime,startTime,operatorUid,operator,extData,`inner`,learnSeason,onlineStart,currentLessonTime,onlineStop,isShow,pack,content,tags,moreGrade,courseFrom';
    private $objDaoCourse;

    /**
     * 构造函数
     *
     */
    public function __construct()
    {
        $this->objDaoCourse = new Hkzb_Dao_Fudao_Course();
    }

    /**
     * 新增课程
     *
     * @param  array $arrParams 课程属性
     * @return bool true/false
     */
    public function addCourse($arrParams)
    {
        if (intval($arrParams['courseId']) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[param: " . json_encode($arrParams) . "]");

            return false;
        }

        $arrFields = array(
            'teacherUid'        => isset($arrParams['teacherUid']) ? intval($arrParams['teacherUid']) : 0,
            'teacherName'       => isset($arrParams['teacherName']) ? strval($arrParams['teacherName']) : '',
            'courseId'          => intval($arrParams['courseId']),
            'courseName'        => isset($arrParams['courseName']) ? strval($arrParams['courseName']) : '',
            'grade'             => isset($arrParams['grade']) ? intval($arrParams['grade']) : 0,
            'subject'           => isset($arrParams['subject']) ? intval($arrParams['subject']) : 0,
            'type'              => isset($arrParams['type']) ? intval($arrParams['type']) : self::TYPE_PRIVATE,
            'degree'            => isset($arrParams['degree']) ? intval($arrParams['degree']) : self::DEGREE_LOWER,
            'price'             => isset($arrParams['price']) ? intval($arrParams['price']) : 0,
            'startTime'         => isset($arrParams['startTime']) ? intval($arrParams['startTime']) : 0,
            'registerStartTime' => isset($arrParams['registerStartTime']) ? intval($arrParams['registerStartTime']) : 0,
            'registerStopTime'  => isset($arrParams['registerStopTime']) ? intval($arrParams['registerStopTime']) : 0,
            'studentCnt'        => isset($arrParams['studentCnt']) ? intval($arrParams['studentCnt']) : 0,
            'studentMaxCnt'     => isset($arrParams['studentMaxCnt']) ? intval($arrParams['studentMaxCnt']) : 0,
            'status'            => self::STATUS_TOONLINE, //初始状态为待上线
            'createTime'        => time(),
            'updateTime'        => time(),
            'operatorUid'       => isset($arrParams['operatorUid']) ? intval($arrParams['operatorUid']) : 0,
            'operator'          => isset($arrParams['operator']) ? strval($arrParams['operator']) : '',
            'extData'           => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '',
            '`inner`'           => intval($arrParams['inner']),
            'learnSeason'       => isset($arrParams['learnSeason']) ? strval($arrParams['learnSeason']) : '',
            'onlineStart'       => isset($arrParams['onlineStart']) ? intval($arrParams['onlineStart']) : 0,
            'onlineStop'        => isset($arrParams['onlineStop']) ? intval($arrParams['onlineStop']) : 0,
            'pack'              => isset($arrParams['pack']) ? intval($arrParams['pack']) : 0,
            'isShow'            => isset($arrParams['isShow']) ? intval($arrParams['isShow']) : 1,
            'content'           => isset($arrParams['content']) ? $arrParams['content'] : '',
            'tags'              => isset($arrParams['tags']) ? json_encode($arrParams['tags']) : '[]',
            'moreGrade'         => isset($arrParams['moreGrade']) ? intval($arrParams['moreGrade']) : 0,
            'courseFrom'        => isset($arrParams['courseFrom']) ? intval($arrParams['courseFrom']) : 1,
        );

        $ret = $this->objDaoCourse->insertRecords($arrFields);

        return $ret;
    }

    /**
     * 更新课程
     *
     * @param  int   $courseId 课程id
     * @param  array $arrParams 课程属性
     * @return bool true/false
     */
    public function updateCourse($courseId, $arrParams)
    {
        if (intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId]");

            return false;
        }

        $arrConds = array(
            'courseId' => intval($courseId),
        );

        $arrFields    = array();
        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach ($arrParams as $key => $value) {
            if (!in_array($key, $arrAllFields)) {
                continue;
            }

            $arrFields[$key] = $value;
        }

        //ext由ps补全，json处理在ds
        if (isset($arrFields['extData'])) {
            $arrFields['extData'] = json_encode($arrFields['extData']);
        }
        //tags由ps补全，json处理在ds
        //ext由ps补全，json处理在ds
        if (isset($arrFields['tags'])) {
            $arrFields['tags'] = json_encode($arrFields['tags']);
        }
        //补全更新时间
        $arrFields['updateTime'] = time();
        $ret                     = $this->objDaoCourse->updateByConds($arrConds, $arrFields);
        if (false === $ret) {
            return false;
        }

        //仅子课双写打包课
        $courseInfo = $this->getCourseInfo($courseId, array('pack', 'extData'));
        if ($courseInfo['pack'] != self::PACK_YESD) {
            return $ret;
        }

        //仅双写课程信息字段
        if (!isset($arrFields['content']) || !isset($arrFields['extData'])) {
            return $ret;
        }

        $packId = $courseInfo['extData']['packId'];
        foreach ($packId as $pCourseId) {
            $pCourseInfo     = $this->getCourseInfo($pCourseId);
            $coreSubCourseId = 0;
            $coreCourseIds   = $pCourseInfo['extData']['coreCourseIds'];
            foreach ($coreCourseIds as $coreCourseId) {
                if ($coreCourseId['type'] == 1) {
                    $coreSubCourseId = intval($coreCourseId['courseId']);
                    break;
                }
            }

            //仅主子课双写打包课
            if ($coreSubCourseId <= 0 || $coreSubCourseId != $courseId) {
                continue;
            }

            $pConds  = array(
                'courseId' => $pCourseId,
            );
            $pFields = array();

            if (isset($arrFields['content'])) {
                $pFields['content'] = $arrFields['content'];
            }

            if (isset($arrFields['extData'])) {
                //过滤不需要双写的扩展字段
                $extData   = json_decode($arrFields['extData'], true);
                $arrFilter = array('coreCourseIds', 'otherCourseIds', 'originalPrice', 'courseHour', 'lessonCnt', 'packId');
                foreach ($arrFilter as $filter) {
                    if (isset($extData[$filter])) {
                        unset($extData[$filter]);
                    }
                }

                $pExtData = $pCourseInfo['extData'];
                foreach ($extData as $field => $value) {
                    $pExtData[$field] = $value;
                }

                $pFields['extData'] = json_encode($pExtData);
            }

            $ret = $this->objDaoCourse->updateByConds($pConds, $pFields);
        }

        return $ret;
    }

    /**
     * 更新课程扩展信息
     *
     * @param  int   $courseId 课节id
     * @param  array $arrExt 课节属性
     * @return bool true/false
     */
    public function updateCourseExt($courseId, $arrExt)
    {
        if (intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId]");

            return false;
        }

        $this->objDaoCourse->startTransaction();

        $courseInfo = $this->getCourseInfo($courseId, array('extData'));
        if (empty($courseInfo)) {
            Bd_Log::warning("Error:[course empty], Detail:[courseId:$courseId]");
            $this->objDaoCourse->rollback();

            return false;
        }

        $arrCourseExt = $courseInfo['extData'];
        foreach ($arrExt as $key => $value) {
            $arrCourseExt[$key] = $value;
        }

        $arrConds = array(
            'courseId' => intval($courseId),
        );

        $arrFields = array(
            'extData' => json_encode($arrCourseExt),
        );

        $ret = $this->objDaoCourse->updateByConds($arrConds, $arrFields);

        $this->objDaoCourse->commit();

        return $ret;
    }

    /**
     * 更新课程tags信息
     *
     * @param  int   $courseId 课节id
     * @param  array $arrExt 课节属性
     * @return bool true/false
     */
    public function updateCourseTags($courseId, $arrExt)
    {
        if (intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId]");

            return false;
        }

        $this->objDaoCourse->startTransaction();

        $courseInfo = $this->getCourseInfo($courseId, array('tags'));
        if (empty($courseInfo)) {
            Bd_Log::warning("Error:[course empty], Detail:[courseId:$courseId]");
            $this->objDaoCourse->rollback();

            return false;
        }

        $arrCourseExt = $courseInfo['tags'];
        foreach ($arrExt as $key => $value) {
            $arrCourseExt[$key] = $value;
        }

        $arrConds = array(
            'courseId' => intval($courseId),
        );

        $arrFields = array(
            'tags' => json_encode($arrCourseExt),
        );

        $ret = $this->objDaoCourse->updateByConds($arrConds, $arrFields);

        $this->objDaoCourse->commit();

        return $ret;
    }

    /**
     * 更新课程第一次上线时间，及课程开始报名时间
     *
     * @param  int $courseId 课程id
     * @return bool true/false
     */
    public function updateCourseRegisterTime($courseId)
    {
        if (intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId]");

            return false;
        }

        $arrConds = array(
            'courseId'          => intval($courseId),
            'registerStartTime' => 0,
            'type'              => 0,
            'status'            => self::STATUS_ONLINE,
        );

        //补全更新时间
        $arrFields['registerStartTime'] = time();
        $arrFields['updateTime']        = time();
        $ret                            = $this->objDaoCourse->updateByConds($arrConds, $arrFields);

        return $ret;
    }

    /**
     * 更新课程报名人数
     *
     * @param  int   $courseId 课程id
     * @return bool true/false
     */
    public function updateCourseStudentCnt($courseId)
    {
        if (intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId]");

            return false;
        }
        $sql = 'update tblCourse set student_cnt = student_cnt + 1 where course_id = ' . $courseId;
        $ret = $this->objDaoCourse->query($sql);
        if ($ret) {
            $this->deleteCourseCacheByCourseId($courseId,'updateCourseStudentCnt');
        }
        return $ret;
    }

    /**
     * 退课后更新课程报名人数
     *
     * @param  int   $courseId 课程id
     * @return bool true/false
     */
    public function changeCourseStudentCnt($courseId)
    {
        if (intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId]");

            return false;
        }
        $sql = 'update tblCourse set student_cnt = student_cnt - 1 where course_id = ' . $courseId . ' and student_cnt > 0';
        $ret = $this->objDaoCourse->query($sql);
        if ($ret) {
            $this->deleteCourseCacheByCourseId($courseId,'changeCourseStudentCnt');
        }
        return $ret;
    }

    /**
     * 上线课程
     *
     * @param  int $courseId 课程id
     * @param  int $operatorUid 操作人id
     * @param  int $operator 操作人
     * @return bool true/false
     */
    public function onlineCourse($courseId, $operatorUid, $operator)
    {
        if (intval($courseId) <= 0 || intval($operatorUid) <= 0 || strlen($operator) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId operatorUid:$operatorUid operator:$operator]");

            return false;
        }

        $arrConds = array(
            'courseId' => intval($courseId),
        );

        $arrFields = array(
            'status'      => self::STATUS_ONLINE,
            'updateTime'  => time(),
            'operatorUid' => intval($operatorUid),
            'operator'    => strval($operator),
        );

        $ret = $this->objDaoCourse->updateByConds($arrConds, $arrFields);

        return $ret;
    }

    /**
     * 下线课程
     *
     * @param  int $courseId 课程id
     * @param  int $operatorUid 操作人id
     * @param  int $operator 操作人
     * @return bool true/false
     */
    public function offlineCourse($courseId, $operatorUid, $operator)
    {
        if (intval($courseId) <= 0 || intval($operatorUid) <= 0 || strlen($operator) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId operatorUid:$operatorUid operator:$operator]");

            return false;
        }

        $arrConds = array(
            'courseId' => intval($courseId),
        );

        $arrFields = array(
            'status'      => self::STATUS_TOONLINE,
            'updateTime'  => time(),
            'operatorUid' => intval($operatorUid),
            'operator'    => strval($operator),
        );

        $ret = $this->objDaoCourse->updateByConds($arrConds, $arrFields);
        
        return $ret;
    }

    /**
     * 获取在线课程类型
     * @return array|false
     */
    public function getCourseTypeOnline()
    {
        $arrFields = array(
            "type",
            "grade",
            "subject",
            "count(*) as cnt",
        );

        $arrConds   = array(
            'status' => self::STATUS_ONLINE,
        );
        $arrAppends = array(
            "group by type, grade, subject",
        );
        $ret        = $this->objDaoCourse->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    public function getCourseTypeOnlineNew()
    {
        $arrFields = array(
            "type",
            "grade",
            "subject",
            "count(*) as cnt",
        );

        $arrConds = array(
            'status' => self::STATUS_ONLINE,
        );
        if (!(Hk_Util_Ip::isInnerIp())) {
            $arrConds['`inner`'] = 0;
        }
        $arrAppends = array(
            "group by type, grade, subject",
        );
        $ret        = $this->objDaoCourse->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    public function getCourseTypeOnlineForMoreGrade()
    {
        $arrFields = array(
            "type",
            "more_grade",
            "subject",
            "count(*) as cnt",
        );

        $arrConds   = array(
            'status' => self::STATUS_ONLINE,
            'isShow' => 1,
            'registerStopTime' => array(time(),'>'),
        );
        $arrConds[] = 'pack <> 2';
        if (!(Hk_Util_Ip::isInnerIp())) {
            $arrConds['`inner`'] = 0;
        }
        $arrAppends = array(
            "group by type, more_grade, subject",
        );
        $ret        = $this->objDaoCourse->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    //获取学季信息
    public function getCourseSeason($arrConds)
    {
        $arrFields  = array(
            "learnSeason",
        );
        $arrAppends = array(
            "group by learn_season order by start_time desc",
        );
        $ret        = $this->objDaoCourse->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    /**
     * 删除课程
     *
     * @param  int $courseId 课程id
     * @param  int $operatorUid 操作人id
     * @param  string $operator 操作人
     * @return bool true/false
     */
    public function deleteCourse($courseId, $operatorUid = 0, $operator = '')
    {
        if (intval($courseId) <= 0 || intval($operatorUid) <= 0 || strlen($operator) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId operatorUid:$operatorUid operator:$operator]");

            return false;
        }

        $arrConds = array(
            'courseId' => intval($courseId),
        );

        $arrFields = array(
            'status'      => self::STATUS_DELETED,
            'updateTime'  => time(),
            'operatorUid' => intval($operatorUid),
            'operator'    => strval($operator),
        );

        $ret = $this->objDaoCourse->updateByConds($arrConds, $arrFields);

        return $ret;
    }

    /**
     * 获取课程详情
     * @param       $courseId
     * @param array $arrFields
     * @return array|bool|false
     */
    public function getCourseInfo($courseId, $arrFields = array())
    {
        if (intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId]");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'courseId' => intval($courseId),
        );

        $ret = $this->objDaoCourse->getRecordByConds($arrConds, $arrFields);

        return $ret;

    }

    /**
     * 批量获取课程详情(不包含已删除)
     * @param       $arrCourseId
     * @param array $arrFields
     * @return array|bool|false
     */
    public function getCourseInfoArray($arrCourseId, $arrFields = array())
    {
        if (empty($arrCourseId)) {
            Bd_Log::warning("Error:[param error], Detail:[arrCourseId empty]");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds     = array(
            'status' => array(self::STATUS_DELETED, '<>'),
        );
        $strCourseIds = implode(',', $arrCourseId);
        $arrConds[]   = " course_id in ($strCourseIds)";


        $ret = $this->objDaoCourse->getListByConds($arrConds, $arrFields, NULL, NULL);

        return $ret;
    }

    /**
     * 批量获取课程信息
     * @param array $aCourseId
     * @param array $aField
     * @param bool $isCache
     * @return array|bool|false
     */
    public function getCourseListByCourseIdList($aCourseId, $aField = array(),$isCache = true)
    {
        if (empty($aCourseId) || !is_array($aCourseId)) {
            Bd_Log::addNotice("Error:[param error]","Detail:[aCourseId empty]");
            return array();
        }

        $aAllField              = explode(',', self::ALL_FIELDS);
        $aFormatCourseList      = array();
        $aNotCacheCourseId      = $aCourseId;
        $oMemcached             = null;
        if ($isCache) {
            $aNotCacheCourseId  = array();
            $oMemcached         = Hk_Service_Memcached::getInstance('zhiboke');
            foreach ($aCourseId as $courseId) {
                $cacheKey       = 'zhiboke_hkzb_advanced_course_'.$courseId;
                $oResult        = $oMemcached->get($cacheKey);
                if (!empty($oResult)) {
                    Hk_Util_Log::incrKey('zhiboke_hkzb_advanced_course_hit', 1);
//                    $aCourse    = json_decode($oResult, true);
                    $aCourse    = json_decode(htmlspecialchars_decode($oResult), true);
//                    Bd_Log::warning('getCourseListByCourseIdList_cache  courseId_'.$courseId.'****oResult'.$oResult.'*****aCourse'.$aCourse.'error'.json_last_error());
                    $aFormatCourseList[$courseId] = $aCourse;
                } else {
                    $aNotCacheCourseId[]    = $courseId;
                }
            }
        }

        if (!empty($aNotCacheCourseId)) {
            $aCourseId      = array_unique($aNotCacheCourseId);
            //根据courseId 批量获取course信息
            $count = count($aCourseId);
            $baseNum = 60;
            $aCourseLists = [];
            if($count > $baseNum){  //切割查询
                $num = ceil($count/$baseNum);
                for($i=0;$i<$num;$i++){
                    $cIdArr = array_slice($aCourseId,$i*$baseNum,$baseNum);
                    $aWhere         = array(sprintf('course_id in (%s)',implode(',',$cIdArr)));
                    $lists   = $this->objDaoCourse->getListByConds($aWhere, $aAllField);
                    $aCourseLists = array_merge($aCourseLists,$lists);
                }
            }else{
                $aWhere         = array(sprintf('course_id in (%s)',implode(',',$aCourseId)));
                $aCourseLists   = $this->objDaoCourse->getListByConds($aWhere, $aAllField);
                if ($aCourseLists === false) {
                    Bd_Log::warning('Db error,tblCourse getListByConds falied in getCourseListByCourseIdList');
                    return false;
                }
            }

            if (empty($aCourseLists)) {
                return array();
            }

            //保存到缓存
            foreach ($aCourseLists as $aCourse) {
                $courseId           = intval($aCourse['courseId']);
                $aFormatCourseList[$courseId]   = $aCourse;
                if ($isCache) {
                    $cacheKey   = 'zhiboke_hkzb_advanced_course_'.$courseId;
                    $oMemcached->set($cacheKey,json_encode($aCourse),3600);
                }
            }
        }

        if (empty($aField)) {
            return $aFormatCourseList;
        }

        $aCourseLists   = array();
        foreach ($aFormatCourseList as $courseId => $aCourse) {
            foreach ($aField as $field) {
                $aCourseLists[$courseId]["$field"]  = isset($aCourse["$field"]) ? $aCourse["$field"] : array();
            }
        }
        unset($aFormatCourseList);
        return $aCourseLists;
    }

    /**
     * 删除缓存
     * @param $courseId
     * @param $from
     */
    public function deleteCourseCacheByCourseId($courseId,$from)
    {
        if (!empty($courseId)) {
            $oMemcached = Hk_Service_Memcached::getInstance('zhiboke');
            $cacheKey   = 'zhiboke_hkzb_advanced_course_'.$courseId;
            $oMemcached->delete($cacheKey);
            $logText    = sprintf('zhiboke_hkzb_advanced_course_clear_cache [From:%s] [courseId:%d]',$from,$courseId);
            Bd_Log::notice($logText);
        }
    }

    /**
     * 获取后台课程数量
     * @param  int $type 类型
     * @param  int $status 状态
     * @param  int $grade 年级
     * @param  int $subject 学科
     * @return int
     */
    public function getCourseCntForMis($type, $status, $grade = 0, $subject = 0)
    {
        $arrConds = array(
            'type'   => $type,
            'status' => array(self::STATUS_DELETED, "<>"),
        );
        if ($status >= 0) {
            $arrConds['status'] = intval($status);
        }

        if (intval($grade) > 0) {
            $arrConds['grade'] = intval($grade);
        }

        if (intval($subject) > 0) {
            $arrConds['subject'] = intval($subject);
        }

        $ret = $this->objDaoCourse->getCntByConds($arrConds);

        return $ret;
    }

    /**
     * @param       $arrConds
     * @param array $arrFields
     * @param int   $offset
     * @param int   $limit
     * @return array|false
     */
    public function getCourseListByConds($arrConds, $arrFields = array(), $offset = 0, $limit = 20)
    {
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrAppends = array(
            "order by create_time desc",
            "limit $offset, $limit",
        );

        $ret = $this->objDaoCourse->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    /**
     * @param       $arrConds
     * @param array $arrFields
     * @param int   $offset
     * @param int   $limit
     * @return array|false
     */
    public function getsortCourseListByConds($arrConds, $arrFields = array(), $offset = 0, $limit = 20)
    {
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrAppends = array(
            "limit $offset, $limit",
        );

        $ret = $this->objDaoCourse->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }
    /**
     * @param       $arrConds
     * @param array $arrFields
     * @param int   $offset
     * @param int   $limit
     * @param array $orderList
     * @param array $sortList
     * @return array|false
     */
    public function getCourseListByCondsNew($arrConds, $arrFields = array(), $offset = 0, $limit = 20, $orderList = array('create_time'), $sortList = array('desc'))
    {
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $orderString = ' order by ';
        foreach ($orderList as $key => $order) {
            $sort        = $sortList[$key];
            $orderString .= $order . ' ' . $sort . ',';
        }
        $orderString = rtrim($orderString, ',');
        $arrAppends  = array(
            $orderString,
            "limit $offset, $limit",
        );

        $ret = $this->objDaoCourse->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }


    /**
     * @param        $arrConds
     * @param array  $arrFields
     * @param int    $offset
     * @param int    $limit
     * @param string $order
     * @param string $sort
     * @return array|false
     */
    public function getOnLineCourseListByConds($arrConds, $arrFields = array(), $offset = 0, $limit = 20, $order = 'create_time', $sort = 'desc')
    {
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds['status'] = self::STATUS_ONLINE;
        $arrAppends         = array(
            "order by $order $sort",
            "limit $offset, $limit",
        );
        $ret                = $this->objDaoCourse->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    //新版na端使用
    public function getOnLineCourseListByCondsV2($arrConds, $arrFields = array(), $offset = 0, $limit = 20)
    {
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds['status'] = self::STATUS_ONLINE;
        $arrAppends         = array(
            "order by student_cnt desc, price asc, create_time desc ",
            "limit $offset, $limit",
        );
        $ret                = $this->objDaoCourse->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    //临时使用寒春过后删掉
    public function getOnLineCourseListByCondsV3($arrConds, $arrFields = array(), $offset = 0, $limit = 20)
    {
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds['status'] = self::STATUS_ONLINE;
        $arrAppends         = array(
            "order by online_start asc ",
            "limit $offset, $limit",
        );
        $ret                = $this->objDaoCourse->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    /**
     * 获取数量
     * @param $arrConds
     * @return false|int
     */
    public function getOnLineCourseCntByConds($arrConds)
    {
        $arrConds['status'] = self::STATUS_ONLINE;
        $ret                = $this->objDaoCourse->getCntByConds($arrConds);

        return $ret;
    }

    /**
     * @param        $arrConds
     * @param array  $arrFields
     * @param string $arrOptions
     * @return array|false
     */
    public function getCourseGradeSubject($arrConds, $arrFields = array(), $arrOptions = NULL)
    {
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrConds['status'] = self::STATUS_ONLINE;
        $ret                = $this->objDaoCourse->getListByConds($arrConds, $arrFields, $arrOptions);

        return $ret;
    }

    /**
     * 获取后台课程列表
     * @param  int   $type 类型
     * @param  int   $status 状态
     * @param  int   $grade 年级
     * @param  int   $subject 学科
     * @param  array $arrFields 指定属性
     * @param  int   $offset 偏移
     * @param  int   $limit 限制
     * @return array|bool
     */
    public function getCourseListForMis($type, $status, $grade = 0, $subject = 0, $arrFields = array(), $offset = 0, $limit = 20)
    {
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'type'   => $type,
            'status' => array(self::STATUS_DELETED, "<>"),
        );
        if ($status >= 0) {
            $arrConds['status'] = intval($status);
        }

        if (intval($grade) > 0) {
            $arrConds['grade'] = intval($grade);
        }

        if (intval($subject) > 0) {
            $arrConds['subject'] = intval($subject);
        }

        $arrAppends = array(
            "order by create_time desc",
            "limit $offset, $limit",
        );

        $ret = $this->objDaoCourse->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    /**
     * 获取数量
     * @param $arrConds
     * @return false|int
     */
    public function getCourseCntByConds($arrConds)
    {
        $ret = $this->objDaoCourse->getCntByConds($arrConds);

        return $ret;
    }

    /**
     * 获取前台课程数量
     * @param int $grade
     * @param int $subject
     * @return false|int
     */
    public function getCourseCnt($grade = 0, $subject = 0)
    {
        $arrConds = array(
            'status' => self::STATUS_ONLINE,
        );

        if (intval($grade) > 0) {
            $arrConds['grade'] = intval($grade);
        }

        if (intval($subject) > 0) {
            $arrConds['subject'] = intval($subject);
        }

        $ret = $this->objDaoCourse->getCntByConds($arrConds);

        return $ret;
    }

    /**
     * 获取前台课程列表
     * @param int   $grade
     * @param int   $subject
     * @param array $arrFields
     * @param int   $offset
     * @param int   $limit
     * @return array|false
     */
    public function getCourseList($grade = 0, $subject = 0, $arrFields = array(), $offset = 0, $limit = 20)
    {
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'status' => self::STATUS_ONLINE,
        );

        if (intval($grade) > 0) {
            $arrConds['grade'] = intval($grade);
        }

        if (intval($subject) > 0) {
            $arrConds['subject'] = intval($subject);
        }

        $arrAppends = array(
            "order by start_time desc",
            "limit $offset, $limit",
        );

        $ret = $this->objDaoCourse->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    /**
     * 获取课程详情 for update
     * 内置for update参数 慎用
     * @param  int   $courseId 课程id
     * @param  array $arrFields 指定属性
     * @return array|bool
     */
    public function getCourseInfoForUpdate($courseId, $arrFields = array())
    {
        if (intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId]");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'courseId' => intval($courseId),
        );
        $appends  = array('for update');

        $ret = $this->objDaoCourse->getRecordByConds($arrConds, $arrFields, null, $appends);

        return $ret;

    }

    /**
     * 根据课程名查课程，除后台外慎用
     * @param       $courseName
     * @param array $arrFields
     * @return array|false
     */
    public function getCourseListByName($courseName, $arrFields = array())
    {
        $arrOutput = array();
        if (strlen($courseName) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseName:$courseName]");

            return $arrOutput;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'status' => array(self::STATUS_DELETED, '<>'),
        );

        $binName    = bin2hex("%$courseName%");
        $arrConds[] = "course_name like unhex('$binName')";

        $ret = $this->objDaoCourse->getListByConds($arrConds, $arrFields);

        return $ret;
    }


    /**
     * 获取打包课程信息（未打包课程，也返回打包格式，以方便上层业务逻辑开发）
     * @param int $courseId
     * @throws Hk_Util_Exception
     * @return array|boolean
     */
    public function getPackCourseInfo($courseId)
    {
        if ($courseId <= 0) {
            return array();
        }
        $courseInfo = $this->getCourseInfo($courseId);
        if (false === $courseInfo) {
            Bd_Log::warning("db error, get course fail, courseId[$courseId]");

            return false;
        }
        if (empty($courseInfo)) {
            return array();
        }
        $courseInfo = $this->formatCourseInfo($courseInfo);
        if ($courseInfo['pack'] == self::PACK_YES) { //打包课
            $coreCourseIds                     = self::getCoreCourseIds($courseInfo);
            $otherCourseIds                    = self::getOtherCourseIds($courseInfo);
            $courseInfo['defaultCoreCourseId'] = intval($coreCourseIds[0]);
            $courseIds                         = array_merge($coreCourseIds, $otherCourseIds);
            $arrCourseInfoTmp                  = $this->getCourseInfoArray($courseIds);
            if (false === $arrCourseInfoTmp) {
                Bd_Log::warning("db error, get course fail");

                return false;
            }
            $arrCourseInfo = array();
            foreach ($arrCourseInfoTmp as $info) {
                $arrCourseInfo[$info['courseId']] = $info;
            }
            $courseInfo['coreCourse'] = array();
            foreach ($coreCourseIds as $courseId) {
                $courseInfo['coreCourse'][$courseId] = $this->formatCourseInfo($arrCourseInfo[$courseId]);
            }
            $courseInfo['otherCourse'] = array();
            foreach ($otherCourseIds as $courseId) {
                $courseInfo['otherCourse'][$courseId] = $this->formatCourseInfo($arrCourseInfo[$courseId]);
            }
        } else { //非打包课，结构上也认为是打包课，打包内容为自身，便于上层业务逻辑开发
            $courseInfo['coreCourse'][$courseInfo['courseId']] = $courseInfo;
            $courseInfo['defaultCoreCourseId']                 = $courseInfo['courseId'];
            $courseInfo['otherCourse']                         = array();
        }

        return $courseInfo;
    }

    /**
     * 课程格式化
     * @param array $courseInfo
     * @return array
     */
    public function formatCourseInfo($courseInfo)
    {
        if (empty($courseInfo)) {
            return $courseInfo;
        }
        if ($courseInfo['pack'] == self::PACK_YES) {
            $courseInfo['onlineTime'] = date('m月d日', $courseInfo['onlineStart']) . '-' . date('m月d日', $courseInfo['onlineStop']);
        } else {
            $courseInfo['onlineTime'] = Zhibo_Util::formatToday(strval($courseInfo['extData']['onlineTime']));
        }
        $lessonCnt = intval($courseInfo['extData']['lessonCnt']);
        if ($lessonCnt > 1) {
            $onlineList               = explode(' ', $courseInfo['onlineTime']);
            $courseInfo['onlineTime'] = $onlineList[0] . ' ' . $lessonCnt . '次课';
        }
        $qqList = $courseInfo['extData']['qqList'];
        if (!empty($qqList)) {
            $lastQQInfo = end($qqList);
        } else {
            $lastQQInfo = array();
        }
        //$courseInfo['qqKey']  = isset($lastQQInfo['key']) ? $lastQQInfo['key'] : '';
        //$courseInfo['qqKeyI'] = isset($lastQQInfo['qqKeyI']) ? $lastQQInfo['qqKeyI'] : '';
        //$courseInfo['uin']    = isset($lastQQInfo['uin']) ? $lastQQInfo['uin'] : '';

        return $courseInfo;
    }

    /**
     * 获取打包课的核心课id
     * @param array $courseInfo
     * @return array
     */
    public static function getCoreCourseIds($courseInfo)
    {
        if ($courseInfo['pack'] == self::PACK_YES) { //打包课
            $arrCourseIds = array();
            //$defaultCoreCourseId = 0;
            foreach ($courseInfo['extData']['coreCourseIds'] as $course) {
                /*
                if ($course['type'] == 1) {
                    $defaultCoreCourseId = $course['courseId'];
                } else {
                    $arrCourseIds[] = $course['courseId'];
                }
                */
                $arrCourseIds[] = $course['courseId'];
            }
            //array_unshift($arrCourseIds, $defaultCoreCourseId); //把默认核心课放到最前面
        } else { //非打包课
            $arrCourseIds[] = $courseInfo['courseId'];
        }

        return $arrCourseIds;
    }

    /**
     * 获取打包课的非核心课列表
     * @param array $courseInfo
     * @return array
     */
    public static function getOtherCourseIds($courseInfo)
    {
        if ($courseInfo['pack'] == self::PACK_YES) {
            $arrCourseIds = array();
            foreach ($courseInfo['extData']['otherCourseIds'] as $course) {
                $arrCourseIds[] = $course['courseId'];
            }
        } else {
            $arrCourseIds = array();
        }

        return $arrCourseIds;
    }

    /**
     * 打包课中所有课程id
     * @param array $courseInfo
     * @return array
     */
    public static function getAllCourseIds($courseInfo)
    {
        $coreCourseIds  = self::getCoreCourseIds($courseInfo);
        $otherCourseIds = self::getOtherCourseIds($courseInfo);

        return array_merge($coreCourseIds, $otherCourseIds);
    }

    /**
     * 打包课核心课默认id
     * @param array $courseInfo
     * @return array
     */
    public static function getDefaultCoreCourseId($courseInfo)
    {
        $coreCourseIds = self::getCoreCourseIds($courseInfo);

        return $coreCourseIds[0];
    }

    /**
     * 获取子课详情
     *
     * @param  int  $courseId 仅支持子课id
     * @param  bool $isCache 缓存开关
     * @return bool|array
     *
     */
    public function getSubCourseInfo($courseId, $isCache = false)
    {
        if (intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId]");

            return false;
        }

        $oMemcached             = null;
        $aCourse                = array();
        if ($isCache) {
            $oMemcached         = Hk_Service_Memcached::getInstance('zhiboke');

            $cacheKey       = 'zhiboke_hkzb_advanced_course_'.$courseId;
            $oResult        = $oMemcached->get($cacheKey);
            if (!empty($oResult)) {
                Hk_Util_Log::incrKey('zhiboke_hkzb_advanced_course_hit', 1);
                $aCourse    = json_decode(utf8_encode($oResult), true);
            }
        }

        if (!empty($aCourse)) {
            $aAllField      = explode(',', self::ALL_FIELDS);
            $aWhere         = array('courseId' => $courseId);
            $aCourse        = $this->objDaoCourse->getRecordByConds($aWhere, $aAllField);
            if ($aCourse === false) {
                Bd_Log::warning('Db error,tblCourse getRecordByConds falied in getSubCourseInfo');
                return false;
            }

            if (empty($aCourse)) {
                return array();
            }

            if ($isCache) {
                $cacheKey   = 'zhiboke_hkzb_advanced_course_'.$courseId;
                $oMemcached->set($cacheKey,json_encode($aCourse),3600);
            }
        }

        if (false === $aCourse) {
            Bd_Log::warning("Error:[getRecordByConds], Detail:[courseId:$courseId]");

            return false;
        }

        //不支持打包课
        if ($aCourse['pack'] == self::PACK_YES) {
            Bd_Log::warning("Error:[subCourse invalid], Detail:[courseId:$courseId]");

            return false;
        }

        $curLessonFind = 0;
        $arrOutput     = array(
            'curLesson'  => array(),
            'lastLesson' => array(),
            'allLesson'  => array(),
        );
        $objLesson     = new Hkzb_Ds_Fudao_Lesson();
        $lessonList = $objLesson->getLessonListByCourseId($courseId, array());
        if (false === $lessonList) {
            Bd_Log::warning("Error:[getLessonListByCourseId error], Detail:[courseId:$courseId]");

            return false;
        }
        $lessonCnt = count($lessonList);
        foreach ($lessonList as $lesson) {
            $lessonId  = $lesson['lessonId'];
            $startTime = $lesson['startTime'];
            $stopTime  = $lesson['stopTime'];
            $status    = $lesson['status'];
            $curTime   = time();

            //提前30分钟上课
            if ($curTime >= $startTime - 1800 && $status == Hkzb_Ds_Fudao_Lesson::STATUS_CLASS_TOSTART) {
                $lesson['status'] = Hkzb_Ds_Fudao_Lesson::STATUS_CLASS_IN;
            }

            //计算即将要上的章节
            if ($curLessonFind == 0 && $status < Hkzb_Ds_Fudao_Lesson::STATUS_CLASS_STOPED) {
                $arrOutput['curLesson'] = $lesson;
                $curLessonFind          = 1;
            }

            //计算刚刚结束的章节
            if ($status == Hkzb_Ds_Fudao_Lesson::STATUS_CLASS_STOPED) {
                $arrOutput['lastLesson'] = $lesson;
            }

            $arrOutput['allLesson'][$lessonId] = $lesson;
        }

        $arrOutput['lessonCnt']  = $lessonCnt;
        $arrOutput['courseInfo'] = $aCourse;

        return $arrOutput;
    }

    /**
     * 获取子课数组列表详情
     *
     * @param  array $courseIdsArr 仅支持子课id数组
     * @param  bool  $cacheSwitch 缓存开关
     * @return array|bool
     */
    public function getSubCourseInfoArray($courseIdArr, $cacheSwitch = false)
    {
        $arrOutput      = array();
        $courseInfoArr  = array();
        $packedIds      = array();
        $lessonInfoArr  = array();
        $objLesson      = new Hkzb_Ds_Fudao_Lesson();

        $courseInfoList = $this->getCourseListByCourseIdList($courseIdArr,array(),$cacheSwitch);
//        Bd_Log::warning('********'.json_encode($courseIdArr).'************'.json_encode($courseInfoList));
        //过滤掉打包课跟按照子课维度聚合子课课程信息
        if(empty($courseInfoList)){
            return $arrOutput;
        }

        foreach ($courseInfoList as $courseInfo){
            if($courseInfo['pack'] == self::PACK_YES){
                continue;
            }
            $courseId                 = $courseInfo['courseId'];
            $packedIds[]              = $courseId;
            $courseInfoArr[$courseId] = $courseInfo;
        }
        //批量获取子课程全部章节的信息
        $allLessonList = $objLesson->getLessonListByCourseArr($packedIds, array(), 0, -1);

        if (false === $allLessonList) {
            Bd_Log::warning("Error:[getLessonListByCourseId error]".json_encode($packedIds));

            return false;
        }

        //按照子课维度收敛章节信息
        if(empty($allLessonList)){
            return $arrOutput;
        }

        foreach ($allLessonList as $lessonInfo){
            $courseId                   = $lessonInfo['courseId'];
            $lessonInfoArr[$courseId][] = $lessonInfo;
        }
        //获取每个子课的章节详情
        foreach ($lessonInfoArr as $courseId => $lessonInfoList) {
            if(empty($lessonInfoList)){
                continue;
            }
            $curLessonFind = 0;
            $lessonCnt     = count($lessonInfoList);
            foreach ($lessonInfoList as $lesson){
                $lessonId  = $lesson['lessonId'];
                $startTime = $lesson['startTime'];
                $status    = $lesson['status'];
                $curTime   = Hkzb_Util_FuDao::getCurrentTimeStamp();

                //提前30分钟上课
                if ($curTime >= $startTime - 1800 && $status == Hkzb_Ds_Fudao_Lesson::STATUS_CLASS_TOSTART) {
                    $lesson['status'] = Hkzb_Ds_Fudao_Lesson::STATUS_CLASS_IN;
                }

                //计算即将要上的章节
                if ($curLessonFind == 0 && $status < Hkzb_Ds_Fudao_Lesson::STATUS_CLASS_STOPED) {
                    $arrOutput[$courseId]['curLesson'] = $lesson;
                    $curLessonFind    = 1;
                }

                //计算刚刚结束的章节
                if ($status == Hkzb_Ds_Fudao_Lesson::STATUS_CLASS_STOPED) {
                    $arrOutput[$courseId]['lastLesson'] = $lesson;
                }

                $arrOutput[$courseId]['allLesson'][$lessonId] = $lesson;
            }

            $arrOutput[$courseId]['lessonCnt']  = $lessonCnt;
            $arrOutput[$courseId]['courseInfo'] = $courseInfoArr[$courseId];
        }

        return $arrOutput;
    }

    /**
     * 获取子课详情
     *
     * @param  array $courseIds 仅支持子课id
     * @param  bool  $cacheSwitch 缓存开关
     * @return array|bool
     */
    public function getSubCourseInfoArr($courseIds, $cacheSwitch = false)
    {

        if (!is_array($courseIds) || empty($courseIds)) {
            return array();
        }
        return $this->getSubCourseInfoArray($courseIds,$cacheSwitch);

        $arrOutput = array();
        //获取课程&章节基础信息
        foreach ($courseIds as $courseId) {

            //读数据库
            $subCourseInfo = $this->getSubCourseInfo($courseId,$cacheSwitch);
            if (false === $subCourseInfo) {
                Bd_Log::warning("Error:[getSubCourseInfo error], Detail:[courseId:$courseId]");
                continue;
            }

            $arrOutput[$courseId] = $subCourseInfo;
        }
        return $arrOutput;
    }
    /**
     * 获取所有进行中的非打包课
     * @return array|bool
     */
    public function getAllSubCourseIdList()
    {
        //基本条件
        $arrConds = array(
            'status' => self::STATUS_ONLINE,
            'pack'   => array(self::PACK_YES, '<>'),
        );

        //访问Dao
        $objDaoCourse  = new Hkzb_Dao_Fudao_Course();
        $arrCourseInfo = $objDaoCourse->getListByConds($arrConds, array('courseId'));
        if (false === $arrCourseInfo) {
            Bd_Log::warning("Error:[getListByConds], Detail:[]");

            return false;
        }

        $courseList = array();
        foreach ($arrCourseInfo as $courseInfo) {
            $courseList[] = intval($courseInfo['courseId']);
        }

        return $courseList;
    }

    //获取辅导课程数量
    public function getMixStudentCnt($courseId)
    {
        $objTeacherCourse = new Hkzb_Ds_Fudao_TeacherCourse();
        $teacherCourseCnt = $objTeacherCourse->getTeacherCnt($courseId);
        if (false === $teacherCourseCnt) {
            Bd_Log::warning("Error:[getTeacherCnt error], Detail:[courseId:$courseId]");

            return false;
        }

        $teacherCourseList = $objTeacherCourse->getTeacherList($courseId, array(), 0, $teacherCourseCnt);
        if (false === $teacherCourseList) {
            Bd_Log::warning("Error:[getTeacherList error], Detail:[courseId:$courseId]");

            return false;
        }

        //虚报策略
        $sumStudentLeftCnt     = 0; //剩余名额
        $sumStudentRegisterCnt = 0;
        $sumStudentMaxCnt      = 0;
        foreach ($teacherCourseList as $teacherCourse) {
            if ($teacherCourse['duty'] !== Hkzb_Ds_Fudao_TeacherCourse::DUTY_ASSISTANT) {
                continue;
            }

            $studentCnt            = intval($teacherCourse['studentCnt']);//实际报名人数
            $studentMaxCnt         = intval($teacherCourse['extData']['studentMaxCnt']); //最大报名人数
            $sumStudentMaxCnt      += $studentMaxCnt;
            $sumStudentLeftCnt     += $studentMaxCnt - $studentCnt;
            $sumStudentRegisterCnt += $studentCnt;
        }
        $arrOutput = array(
            'studentLeftCnt'     => $sumStudentLeftCnt,
            'studentRegisterCnt' => $sumStudentRegisterCnt,
            'studentMaxCnt'      => $sumStudentMaxCnt,
        );

        return $arrOutput;
    }

    /**
     * 获取某个学季在售的班课程id列表，通过季节和报名时间
     * @param string $season
     * @param int    $registerStartTime
     * @param int    $type
     * @return array
     */
    public function getCourseIdListBylearnSeason($season = '', $registerStartTime = 0, $type = 2)
    {
        if (empty($season)) {
            return array();
        }
        if (empty($registerStartTime)) {
            $registerStartTime = time();
        }
        if (is_array($type)) {
            $typeStr    = join(',', $type);
            $arrConds[] = "type in ({$typeStr})";
        } else {
            $arrConds['type'] = intval($type);
        }
        $statusStr  = self::STATUS_ONLINE . ',' . self::STATUS_FINISHED;
        $arrConds[] = "status in ({$statusStr})";

        if (is_array($season)) {
            $seasonStr  = join(',', $season);
            $arrConds[] = "learn_season in ({$seasonStr})";
        } else {
            $arrConds['learnSeason'] = strval($season);
        }

        $arrConds['registerStartTime'] = array($registerStartTime, '>');
        //过滤掉被打包课
        $arrConds['pack'] = array(self::PACK_YESD, '<>');
        $arrFields        = array('courseId');
        $ret              = $this->objDaoCourse->getListByConds($arrConds, $arrFields, NULL);
        if (empty($ret)) {
            return array();
        }
        $courseIdList = array();
        foreach ($ret as $value) {
            $courseIdList[] = $value['courseId'];
        }

        return $courseIdList;
    }

    //临时处理更新课程teachername
    public function updateTeacherNameByTeacherUid($teacherUid, $teacherName)
    {
        $arrConds = array(
            'teacherUid' => intval($teacherUid),
        );
        $fields   = array(
            'teacherName' => $teacherName,
        );
        $ret      = $this->objDaoCourse->updateByConds($arrConds, $fields);

        return $ret;
    }

    /**
     * 设置最近的一个章节的上课时间
     * @param $courseId
     * @param $currentLessonTime
     * @return bool
     */
    public function setCurrentLessonTime($courseId, $currentLessonTime)
    {
        if (empty($courseId) || empty($currentLessonTime)) {
            Bd_Log::warning("Error:[setCurrentLessonTime error ] detail:[courseId or currentLessonTime is empty]");

            return false;
        }

        $arrConds = array(
            'courseId' => intval($courseId),
        );

        $fields = array(
            'currentLessonTime' => intval($currentLessonTime)
        );

        $ret = $this->objDaoCourse->updateByConds($arrConds, $fields);

        if ($ret === false) {
            Bd_Log::warning("Error:[db error ] detail:" . json_encode($arrConds));
        }

        return $ret;
    }

    /**
     * 获取mord_grade和grade
     * @param array $courseIds
     * @param string $learnSeason
     * @param int $grade
     * @param bool $cacheSwith
     * @param int $isShow
     * @return array|bool|false
     */
    public function getGradeAndMoreGradeByCourseIdsLearnSeason($courseIds=array(),$learnSeason='',$grade=0,$type=false,$cacheSwith=true,$isShow=1)
    {

        //读缓存
        $objMemcached = Hk_Service_Memcached::getInstance("zhiboke");
        if ($cacheSwith && !(Hk_Util_Ip::isInnerIp())) {
            if (empty($courseIds)) {
                $condStr = md5($learnSeason . $grade . $type);
            } else {
                $condStr = md5(join('_', $courseIds) . $learnSeason . $grade . $type);
            }
            $cacheKey   = 'zhiboke_ds_course_getGradeAndMoreGradeByCourseIdsLearnSeason_' . $condStr;
            $cacheValue = $objMemcached->get($cacheKey);
            if (!empty($cacheValue)) {
                Hk_Util_Log::incrKey("zhiboke_ds_course_getGradeAndMoreGradeByCourseIdsLearnSeason_hit", 1);
                $arrOutput = json_decode(utf8_encode($cacheValue), true);
                if(!empty($arrOutput)){
                    return $arrOutput;
                }

            }
            Hk_Util_Log::incrKey("zhiboke_ds_course_getGradeAndMoreGradeByCourseIdsLearnSeason_miss", 1);
        }

        $arrFields = array(
            "moreGrade",
            "subject",
            "learnSeason",
        );

        $arrConds = array(
            'status'           => self::STATUS_ONLINE,
            'registerStopTime' => array(time(), '>'),
        );
        //是否展示隐藏的课程
        if ($isShow) {
            $arrConds['isShow'] = 1;
        }

        $validType = array(
            Hkzb_Ds_Fudao_Course::TYPE_PRIVATE_LONG,
            Hkzb_Ds_Fudao_Course::TYPE_PRIVATE,
            Hkzb_Ds_Fudao_Course::TYPE_PRE_LONG,
        );

        if (in_array($type, $validType)) {
            $arrConds['type'] = $type;
        }

        if (!empty($courseIds)) {
            $strCourseIds = implode(',', $courseIds);
            $arrConds[]   = "course_id in ({$strCourseIds})";
        }

        //特殊类型年级处理
        if (in_array($type, Hkzb_Ds_Fudao_SubjectCard::$SPECIALTYPEARR)) {
            $grade = 0;
        }

        if ($grade > 0) {
            $gradeInt   = Hkzb_Const_FudaoGradeMap::$GradeMap[$grade];
            $arrConds[] = " more_grade & {$gradeInt} = {$gradeInt} ";
        }

        if (!empty($learnSeason)) {
            $arrConds['type'] = 2;
            $arrConds[]       = "learn_season in (" . $learnSeason . ")";
        }
        $arrConds[] = 'pack in (0,1) ';

        $isInner = Hk_Util_Ip::isInnerIp() ? 1 : 0;
        if (!$isInner) {
            $arrConds['`inner`'] = 0;
        }
        $arrAppends = array(
            "group by more_grade,learn_season, subject",
        );
        if ($type == Hkzb_Ds_Fudao_Course::TYPE_PRIVATE_LONG) {
            //走张骁的接口获取
            $flipSeasonMap       = array_flip(Hkzb_Const_FudaoGradeMap::$seasonIdNameMap);
            $seasonId            = $flipSeasonMap[$learnSeason];
            $zbAdvancedInterface = new Zb_Core_Ds_Dak_Interface();
            $ret                 = $zbAdvancedInterface->getBCourseSujectSeasonGroup($grade, $seasonId, true, $isInner);
        } else {
            $objDaoCourse = new Hkzb_Dao_Fudao_Course();
            $ret          = $objDaoCourse->getListByConds($arrConds, $arrFields, NULL, $arrAppends);
        }

        if ($ret === false) {
            Bd_Log::warning("db error detail:" . json_encode($arrAppends));

            return false;
        }
        //写缓存
        if ($cacheSwith) {
            $cacheValue = json_encode($ret);
            $objMemcached->set($cacheKey, $cacheValue, 60 * 2);
        }

        return $ret;
    }

    /**
     * 获取一个打包课的章节列表
     * @param int $courseId
     * @return array|bool|false
     */
    public function getLessonListByPackCourseId($courseId){
        if (intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId]");

            return false;
        }
        $arrFields = explode(',', self::ALL_FIELDS);
        $arrConds = array(
            'courseId' => intval($courseId),
        );

        $courseInfo = $this->objDaoCourse->getRecordByConds($arrConds, $arrFields);
        if(false === $courseInfo){
            Bd_Log::warning("Error:[getRecordByConds error], Detail:[courseId:$courseId]");

            return false;
        }
        if(empty($courseInfo)){
           return $courseInfo;
        }
        if($courseInfo['pack'] != self::PACK_YES){
            return false;
        }


        //获取打包课下的所有子课
        $arrCourseId = array();
        $otherCourseId = array();
        foreach($courseInfo['extData']['coreCourseIds'] as $val){
            $arrCourseId[] = $val['courseId'];
        }
        if(!empty($courseInfo['extData']['otherCourseIds'])){
            foreach($courseInfo['extData']['otherCourseIds'] as $val){
                $arrCourseId[] = $val['courseId'];
                $otherCourseId[] = $val['courseId'];
            }
        }

        //获取章节列表
        $objLesson = new Hkzb_Ds_Fudao_Lesson();
        $lessonList = $objLesson->getLessonListByArrCourseId($arrCourseId);
        if(false === $lessonList){
            Bd_Log::warning("Error:[getLessonListByArrCourseId error], Detail:[courseId:$courseId]");
            return false;
        }
        if(empty($lessonList)){
            return $lessonList;
        }
        $res = array();
        foreach($lessonList as $v){
            $v['otherCourseIdSign'] = 0;
            if(!empty($otherCourseId) && in_array($v['courseId'], $otherCourseId)){
                $v['otherCourseIdSign'] = 1;
            }
            $res[] = $v;
        }
        return $res;

    }

    /**
     * 某个老师所有的非被打包课
     * @param $teacherUid
     * @param $courseStatus
     * @param string $courseType
     * @return array|bool
     */
    public function getPackCourseList($teacherUid, $courseStatus, $courseType = '') {
        if(intval($teacherUid) <= 0 || $courseStatus < 0 || $courseStatus > 2 ) {
            Bd_Log::warning("Error:[param error], Detail:[teacherUid:$teacherUid status:$courseStatus]");
            return false;
        }

        // 获取老师课程列表
        $arrConds = array(
            'teacherUid' => $teacherUid,
            'create_time > 1493568000',
            'status in (3,4)',
            'pack != 1',
        );
        $arrFields = array('courseId','courseName','subject','learnSeason','onlineStart','onlineStop','studentCnt','studentMaxCnt','type','pack','extData');
        $teacherCourseList = $this->objDaoCourse->getListByConds($arrConds, $arrFields);
        // 所有打包课Id
        $arrCoursePack = array(); // 打包课的子课
        $packLessonList = array();
        foreach ($teacherCourseList as $k=>$v) {
            if(isset($v['extData']['stopReason']) && $v['extData']['stopReason'] == 'closecourse'){
                continue;
            }
            $packId = !empty($v['extData']['packId'][0]) ? $v['extData']['packId'][0] : $v['courseId'];
            $arrCoursePack[$packId][] = $v['courseId'];
            $packLessonList[$packId]['courseId'] = $packId;
        }
        $objCourse = new Hkzb_Ds_Fudao_Course();
        $objLesson = new Hkzb_Ds_Fudao_Lesson();

        // 打包课下所有章节
        foreach ($arrCoursePack as $packId=>$arrSubCourseId) {

            $allCourseInfo = $objCourse->getArrCourseInfo($arrSubCourseId, true);

            // 老师课程信息格式化
            $courseAllList = array();
            $courseTypeList = array();
            foreach ($arrSubCourseId as $subCourseId) {
                if ($courseType > 0 && $courseType == $allCourseInfo[$subCourseId]['type']) { // 当前需要的子课Id
                    $courseTypeList[] = $subCourseId;
                } else {
                    $courseAllList[] = $subCourseId;
                }
            }
            $courseListTmp = $courseType > 0 ? $courseTypeList : $courseAllList;
            foreach ($courseListTmp as $courseId) {
                $lessonList = $objLesson->getLessonListByCourseId($courseId, array('lessonId','lessonName','courseId','status','startTime','stopTime','extData'));
                foreach ($lessonList as $lesson) {
                    $packLessonList[$packId]['allLesson'][] = $lesson;
                }
            }
        }
        // 封装
        $arrOutput = array();
        foreach ($packLessonList as $packId=>$lesson) {

            // 打包课信息
            $arrOutput[$packId]['courseId']   = $packLessonList[$packId]['courseId'];
            $packInfo = $objCourse->getCourseInfo($packId, $arrFields);
            $arrOutput[$packId]['courseInfo'] = $packInfo;
            $allLesson = $lesson['allLesson'];
            if(empty($allLesson)) {
                continue;
            }
            $sort = array();
            foreach ($allLesson as $lessonInfo) {
                $sort[] = $lessonInfo['startTime'];
            }
            array_multisort($sort, SORT_ASC, $allLesson);
            $curLessonFind = 0;
            $curTime   = time();
            foreach ($allLesson as $lessonInfo) {
                $lessonId  = $lessonInfo['lessonId'];
                $startTime = $lessonInfo['startTime'];
                $stopTime  = $lessonInfo['stopTime'];
                $status    = $lessonInfo['status'];

                // 所有章节
                $arrOutput[$packId]['allLesson'][$lessonId] = $lessonInfo;

                //提前30分钟上课
                if ($curTime >= $startTime - 1800 && $status == Hkzb_Ds_Fudao_Lesson::STATUS_CLASS_TOSTART) {
                    $lessonInfo['status'] = Hkzb_Ds_Fudao_Lesson::STATUS_CLASS_IN;
                }

                //计算即将要上的章节
                if($curLessonFind == 0 && $curTime <= $stopTime) {
                    $curLessonId = $lessonId;
                    $arrOutput[$packId]['curLessonId'] = $curLessonId;
                    $curLessonFind = 1;
                }

                //计算刚刚结束的章节
                if($curTime > $stopTime) {
                    $lastLessonId = $lessonId;
                    $arrOutput[$packId]['lastLessonId'] = $lastLessonId;
                }

            }
            $arrOutput[$packId]['lessonCnt'] = count($arrOutput[$packId]['allLesson']);
        }

        //老师课程列表排序&分页
        $arrOutput = $this->sortByType($arrOutput, $courseStatus);
        return $arrOutput;
    }

    //老师课程列表排序
    private function sortByType($courseList, $type) {
        $arrRank = array();
        $arrOutput = array();
        switch($type) {
            case 0:
                //待上课列表，按即将开始的章节上课时间顺序排列
                foreach($courseList as $packId => $course) {
                    $curLessonId = intval($course['curLessonId']);
                    if($curLessonId <= 0) {
                        continue;
                    }
                    $arrRank[] = intval($course['allLesson'][$curLessonId]['startTime']);
                    $arrOutput[] = $course;
                }
                if(!empty($arrRank)) {
                    array_multisort($arrRank, SORT_ASC, $arrOutput);
                }
                break;
            case 1:
                //已上课列表，按刚刚结束的章节下课时间倒序排列
                foreach($courseList as $packId => $course) {
                    $lastLessonId = intval($course['lastLessonId']);
                    if($lastLessonId <= 0) {
                        continue;
                    }

                    $arrRank[] = intval($course['allLesson'][$lastLessonId]['stopTime']);
                    $arrOutput[] = $course;
                }
                if(!empty($arrRank)) {
                    array_multisort($arrRank, SORT_DESC, $arrOutput);
                }
                break;
            case 2:
                $arrRank1 = array();
                $arrRank2 = array();
                $arrOutput1 = array();
                $arrOutput2 = array();
                //全部列表，待上课程在前，已上课程在后
                foreach($courseList as $packId => $course) {
                    $curLessonId = intval($course['curLessonId']);
                    $lastLessonId = intval($course['lastLessonId']);
                    if($curLessonId > 0) {
                        $arrRank1[] = intval($course['allLesson'][$curLessonId]['startTime']);
                        $arrOutput1[] = $course;
                    }

                    if($curLessonId <= 0 && $lastLessonId > 0) {
                        $arrRank2[] = intval($course['allLesson'][$lastLessonId]['stopTime']);
                        $arrOutput2[] = $course;
                    }
                }
                //待上课列表，按即将开始的章节上课时间顺序排列
                if(!empty($arrRank1)) {
                    array_multisort($arrRank1, SORT_ASC, $arrOutput1);
                }
                //已上课列表，按刚刚结束的章节下课时间倒序排列
                if(!empty($arrRank2)) {
                    array_multisort($arrRank2, SORT_DESC, $arrOutput2);
                }

                $arrOutput = array_merge($arrOutput1, $arrOutput2);
                break;
        }
        return $arrOutput;
    }

    /**
     * 批量获取详情
     * @param  array $courseIds
     * @param  bool  $cacheSwitch 缓存开关
     * @return array|bool
     */
    public function getArrCourseInfo($courseIds, $cacheSwitch = false)
    {
        $arrOutput = array();

        //批量读缓存
        $cacheValues = array();
        if ($cacheSwitch) {
            $objMemcached = Hk_Service_Memcached::getInstance("zhiboke");
            $cacheKeys    = array();
            foreach ($courseIds as $courseId) {
                $cacheKeys[] = 'zhiboke_ds_course_subcourseinfo_' . $courseId;
            }

            $cacheValues = $objMemcached->mget($cacheKeys);
        }

        //获取课程&章节基础信息
        foreach ($courseIds as $courseId) {
            //读缓存
            if ($cacheSwitch) {
                $cacheKey = 'zhiboke_ds_course_subcourseinfo_' . $courseId;
                if (!empty($cacheValues[$cacheKey])) {
                    Hk_Util_Log::incrKey("zhiboke_ds_course_subcourseinfo_hit", 1);
                    $arrOutput[$courseId] = json_decode($cacheValues[$cacheKey], true);
                    continue;
                }
                Hk_Util_Log::incrKey("zhiboke_ds_course_subcourseinfo_miss", 1);
            }

            //读数据库
            $subCourseInfo = $this->getCourseInfo($courseId, array('courseId','courseName','type'));
            if (false === $subCourseInfo) {
                Bd_Log::warning("Error:[getSubCourseInfo error], Detail:[courseId:$courseId]");
                continue;
            }

            $arrOutput[$courseId] = $subCourseInfo;

            //写缓存
            if ($cacheSwitch) {
                $cacheValue = json_encode($subCourseInfo);
                $objMemcached->set($cacheKey, $cacheValue, 60 + $courseId % 60);
            }
        }

        return $arrOutput;
    }

    /**
     * 某个老师所有的非打包课
     * @param $teacherUid
     * @return array|bool
     */
    public function getArrPackCourseList($teacherUid) {
        if(intval($teacherUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[teacherUid:$teacherUid ]");
            return false;
        }

        // 获取老师课程列表
        $arrConds = array(
            'teacherUid' => $teacherUid,
            'create_time > 1488297600',
            'status in (3,4)',
            'pack != 1',
        );
        $arrFields = array('courseId','courseName','subject','learnSeason','createTime','onlineStart','onlineStop','studentCnt','studentMaxCnt','type','pack','extData');
        $teacherCourseList = $this->objDaoCourse->getListByConds($arrConds, $arrFields);
        // 所有打包课Id
        $arrCoursePack = array(); // 打包课的子课
        $packLessonList = array();
        foreach ($teacherCourseList as $k=>$v) {
            $packId = !empty($v['extData']['packId'][0]) ? $v['extData']['packId'][0] : $v['courseId'];
            $arrCoursePack[$packId][] = $v['courseId'];
            $packLessonList[$packId]['courseId'] = $packId;
        }
        $objCourse = new Hkzb_Ds_Fudao_Course();
        $objLesson = new Hkzb_Ds_Fudao_Lesson();

        // 打包课下所有章节
        foreach ($arrCoursePack as $packId=>$arrSubCourseId) {

//            $allCourseInfo = $objCourse->getArrCourseInfo($arrSubCourseId, true);

            // 老师课程信息格式化
            $courseAllList = array();
            foreach ($arrSubCourseId as $subCourseId) {
                $courseAllList[] = $subCourseId;
            }
            foreach ($courseAllList as $courseId) {
                $lessonList = $objLesson->getLessonListByCourseId($courseId, array('lessonId','lessonName','status','startTime','stopTime','extData'));
                foreach ($lessonList as $lesson) {
                    $packLessonList[$packId]['allLesson'][] = $lesson;
                }
            }
        }

        // 封装
        $arrOutput = array();
        foreach ($packLessonList as $packId=>$lesson) {

            // 打包课信息
            $arrOutput[$packId]['courseId']   = $packLessonList[$packId]['courseId'];
            $packInfo = $objCourse->getCourseInfo($packId, $arrFields);
            $arrOutput[$packId]['courseInfo'] = $packInfo;
            $allLesson = $lesson['allLesson'];
            $sort = array();
            /**
             * @des 统一处理线上报警 start
             * <AUTHOR>
             * @time 2018/04/09
             */
            if(is_array($allLesson) && !empty($allLesson)){
                foreach ($allLesson as $lessonInfo) {
                    $sort[] = $lessonInfo['startTime'];
                }
            }
            /**
             * end
             */
            array_multisort($sort, SORT_ASC, $allLesson);
            $curLessonFind = 0;
            $curTime   = time();
            /**
             * @des 统一处理线上报警 start
             * <AUTHOR>
             * @time 2018/04/09
             */
            if(is_array($allLesson) && !empty($allLesson)) {
                foreach ($allLesson as $lessonInfo) {
                    $lessonId = $lessonInfo['lessonId'];
                    $startTime = $lessonInfo['startTime'];
                    $stopTime = $lessonInfo['stopTime'];
                    $status = $lessonInfo['status'];

                    // 所有章节
                    $arrOutput[$packId]['allLesson'][$lessonId] = $lessonInfo;

                    //提前30分钟上课
                    if ($curTime >= $startTime - 1800 && $status == Hkzb_Ds_Fudao_Lesson::STATUS_CLASS_TOSTART) {
                        $lessonInfo['status'] = Hkzb_Ds_Fudao_Lesson::STATUS_CLASS_IN;
                    }

                    //计算即将要上的章节
                    if ($curLessonFind == 0 && $curTime <= $stopTime) {
                        $curLessonId = $lessonId;
                        $arrOutput[$packId]['curLessonId'] = $curLessonId;
                        $curLessonFind = 1;
                    }

                    //计算刚刚结束的章节
                    if ($curTime > $stopTime) {
                        $lastLessonId = $lessonId;
                        $arrOutput[$packId]['lastLessonId'] = $lastLessonId;
                    }
                }
            }
            /**
             * end
             */
            $arrOutput[$packId]['lessonCnt'] = count($arrOutput[$packId]['allLesson']);
        }
        //老师课程列表排序&分页
        $arrOutput = $this->sortByType($arrOutput, 2);
        return $arrOutput;
    }

    /**
     * 通过条件获取某个老师所有的非被打包课
     * @param $teacherUid
     * @return array|bool
     */
    public function getArrPackCourseListByConds($arrConds) {
        // 获取老师课程列表
        $arrFields = array('courseId','courseName','subject','status','learnSeason','createTime','onlineStart','onlineStop','studentCnt','studentMaxCnt','type','pack','extData');
        $arrAppends = array();
        if (3 == $arrConds['status']) {
            $arrAppends = array(
                'order by online_start ASC',
            );
        }
        else {
            $arrAppends = array(
                'order by online_start DESC',
            );
        }
        $teacherCourseList = $this->objDaoCourse->getListByConds($arrConds, $arrFields, $arrOptions = NULL, $arrAppends);
        // 所有打包课Id
        $arrCoursePack = array();
        $packLessonList = array();
        foreach ($teacherCourseList as $k=>$v) {
            $packId = !empty($v['extData']['packId'][0]) ? $v['extData']['packId'][0] : $v['courseId'];
            $arrCoursePack[$packId][] = $v['courseId'];
            $packLessonList[$packId]['courseId'] = $packId;
        }
        $objCourse = new Hkzb_Ds_Fudao_Course();
        $objLesson = new Hkzb_Ds_Fudao_Lesson();

        // 打包课下所有章节
        foreach ($arrCoursePack as $packId=>$arrSubCourseId) {

//            $allCourseInfo = $objCourse->getArrCourseInfo($arrSubCourseId, true);

            // 老师课程信息格式化
            $courseAllList = array();
            foreach ($arrSubCourseId as $subCourseId) {
                $courseAllList[] = $subCourseId;
            }
            foreach ($courseAllList as $courseId) {
                $lessonList = $objLesson->getLessonListByCourseId($courseId, array('lessonId','lessonName','status','startTime','stopTime','extData'));
                foreach ($lessonList as $lesson) {
                    $packLessonList[$packId]['allLesson'][] = $lesson;
                }
            }
        }

        // 封装
        $arrOutput = array();
        foreach ($packLessonList as $packId=>$lesson) {

            // 打包课信息
            $packInfo = $objCourse->getCourseInfo($packId, $arrFields);
            if (3 == $arrConds['status'] && 3 != $packInfo['status']) {
                continue;
            }
            if (4 == $arrConds['status'] && 4 != $packInfo['status']) {
                continue;
            }
            $arrOutput[$packId]['courseId']   = $packLessonList[$packId]['courseId'];
            $arrOutput[$packId]['courseInfo'] = $packInfo;
            $allLesson = $lesson['allLesson'];
            $sort = array();
            foreach ($allLesson as $lessonInfo) {
                $sort[] = $lessonInfo['startTime'];
            }
            array_multisort($sort, SORT_ASC, $allLesson);
            $curLessonFind = 0;
            $curTime   = time();
            foreach ($allLesson as $lessonInfo) {
                $lessonId  = $lessonInfo['lessonId'];
                $startTime = $lessonInfo['startTime'];
                $stopTime  = $lessonInfo['stopTime'];
                $status    = $lessonInfo['status'];

                // 所有章节
                $arrOutput[$packId]['allLesson'][$lessonId] = $lessonInfo;

                //提前30分钟上课
                if ($curTime >= $startTime - 1800 && $status == Hkzb_Ds_Fudao_Lesson::STATUS_CLASS_TOSTART) {
                    $lessonInfo['status'] = Hkzb_Ds_Fudao_Lesson::STATUS_CLASS_IN;
                }

                //计算即将要上的章节
                if($curLessonFind == 0 && $curTime <= $stopTime) {
                    $curLessonId = $lessonId;
                    $arrOutput[$packId]['curLessonId'] = $curLessonId;
                    $curLessonFind = 1;
                }

                //计算刚刚结束的章节
                if($curTime > $stopTime) {
                    $lastLessonId = $lessonId;
                    $arrOutput[$packId]['lastLessonId'] = $lastLessonId;
                }
            }
            $arrOutput[$packId]['lessonCnt'] = count($arrOutput[$packId]['allLesson']);
        }

        //$arrOutput = $this->sortByType($arrOutput, 2);
        $tempRes = array();
        foreach ($arrOutput as $k => $v){
            $tempRes[] = $v;
        }
        $arrOutput = $tempRes;

        return $arrOutput;
    }

    /**
     * 根据课程属性获取课程列表，用于策略后台课程搜索
     */

    public function getCourseListForSaleStrategy($offset = 0, $limit = 0,$arrFields=array(),$grade=0,$subject=0,$courseIds=array(),$courseType=-1,$courseName='',$season=0)
    {
        $grade = intval($grade);
        $subject = intval($subject);
        $courseType = intval($courseType);
        $courseName = strval($courseName);

        if(empty($arrFields)){
            $arrFields = explode(',',self::ALL_FIELDS);
        }

        if ($grade > 0) {
            $gradeInt   = Hkzb_Const_FudaoGradeMap::$GradeMap[$grade];
            $arrConds[] = " more_grade & {$gradeInt} = {$gradeInt} ";
        }

        if($subject > 0){
            $arrConds['subject'] = $subject;
        }

        //添加学季条件
        if(! empty($season)){
            $arrConds[] = $this->getLearnSeasonCondStr($season);
        }


        if(!empty($courseIds) && is_array($courseIds)){
            $courseIdStr = join(',',$courseIds);
            $arrConds[] = " course_id in ({$courseIdStr})";
        }

        if($courseType >=0){
            $arrConds['type'] = $courseType;
        }

        if(!empty($courseName)){
            $arrConds['courseName'] = $courseName;
        }

        //获取非子课
        $arrConds[] = "pack != " . self::PACK_YESD;

        //过滤掉已经结束的课程
        $unValidArr = array(
            self::STATUS_FINISHED,
            self::STATUS_STOP,
            self::STATUS_DELETED,
        );
        $unValidStr = implode(',',$unValidArr);
        $arrConds[] = " status not in ({$unValidStr})";


        if($limit > 0){
            $arrAppends = array(
                "order by start_time desc",
                "limit $offset, $limit",
            );
        }

        $res = $this->objDaoCourse->getListByConds($arrConds,$arrFields,null,$arrAppends);

        if($res ===  false){
            Bd_Log::warning("db error ");
            return false;
        }

        return $res;
    }

    public function getCourseCntForSaleStrategy($grade=0,$subject=0,$courseIds=array(),$courseType=-1,$courseName='',$season=0)
    {
        $grade = intval($grade);
        $subject = intval($subject);
        $courseType = intval($courseType);
        $courseName = strval($courseName);

        if ($grade > 0) {
            $gradeInt   = Hkzb_Const_FudaoGradeMap::$GradeMap[$grade];
            $arrConds[] = " more_grade & {$gradeInt} = {$gradeInt} ";
        }

        if($subject > 0){
            $arrConds['subject'] = $subject;
        }

        //添加学季条件
        if(!empty($season)){
            $arrConds[] = $this->getLearnSeasonCondStr($season);
        }

        if(!empty($courseIds) && is_array($courseIds)){
            $courseIdStr = join(',',$courseIds);
            $arrConds[] = " course_id in ({$courseIdStr})";
        }

        if($courseType >=0){
            $arrConds['type'] = $courseType;
        }

        if(!empty($courseName)){
            $arrConds['courseName'] = $courseName;
        }

        //获取非子课
        $arrConds[] = "pack != " . self::PACK_YESD;

        //过滤掉已经结束的课程
        $unValidArr = array(
            self::STATUS_FINISHED,
            self::STATUS_STOP,
            self::STATUS_DELETED,
        );
        $unValidStr = implode(',',$unValidArr);
        $arrConds[] = " status not in ({$unValidStr})";


        $res = $this->objDaoCourse->getCntByConds($arrConds);

        if($res ===  false){
            Bd_Log::warning("db error ");
            return false;
        }

        return $res;
    }


    //根据学季id获取获取学季条件字符串

    public function getLearnSeasonCondStr($seasonId=0)
    {
        $condStr = '';
        if(empty($seasonId)){
            return $condStr;
        }

        $learnSeasonRangeMap = Zb_Const_LearnSeason::$learnSeasonRangeMap;
        //指定学期
        if (!empty($seasonId)) {
            //获取学期检索条件
            $seasonConf = Hkzb_Const_FudaoGradeMap::$seasonIdNameMap;
            $seasonIds = isset($learnSeasonRangeMap[$seasonId]) ? $learnSeasonRangeMap[$seasonId] : array();
            $seasonList = [];
            foreach ($seasonIds as $itemSeasonId) {
                //过滤掉空的
                if(empty($seasonConf[$itemSeasonId])){
                    continue;
                }

                $seasonList[] = $seasonConf[$itemSeasonId];
            }
            $seasonConds = join(',', $seasonList);
            $condStr  = 'learn_season in (' . $seasonConds . ')';
        }

        return $condStr;
    }



    /**
     * 获取学生课中跟读相关的统计
     *
     * @param $intLessonId
     * @param $intStudentUid
     *
     * @return array
     * array(
     *     "five_star_rpepeat_words_num" => "4", #5星跟读单词数量
     *     "rpepeat_words_num"           => "6", #总跟读单词数量
     *     "less_three_star_words"       => array( 'aa', 'bb', 'cc' ),  #三星以下单词：
     *     "all_points"                  => array( '11', '22', '33' ), #知识点（所有）
     *     "error_points"                => array( '11', '22', '33' ), #易错点
     *     "rpepeat_list"                => array(
     *         array(
     *             'rpepeat_words' => "跟读单词",
     *             'word_record'   => '单词发音',
     *             'word_means'    => '单词释义',
     *             'star_num'      => '星星数量',
     *         ),
     *         array(
     *             'rpepeat_words' => "跟读单词",
     *             'word_record'   => '单词发音',
     *             'word_means'    => '单词释义',
     *             'star_num'      => '星星数量',
     *         ),
     *     ),
     * );
     *
     */
    public function getWxStuInclassStatistics( $intLessonId, $intStudentUid ) {

        // 返回值
        // 知识点（所有）
        $arrAllPointsList = array();
        // 总跟读单词数量
        $intRepeatWordNum = 0;
        // 5星跟读单词数量
        $intFiveStarNum = 0;
        // 三星以下单词
        $arrLessThreeStarWordsList = array();
        // 易错点
        $arrErrorPointsList = array();
        // 跟读情况情况
        $arrRepeatList = array();

        $objDsExercise       = new Hkzb_Ds_Fudao_Exercise();
        $objDsExerciseDetail = new Hkzb_Ds_Fudao_ExerciseDetail();
        $objDsHomework       = new Hkzb_Ds_Homework_Homework();

        $arrLessonInfo = (new Hkzb_Ds_Fudao_Lesson())->getLessonInfo($intLessonId);
        $intCourseId   = intval($arrLessonInfo[ 'courseId' ]);

        // 获取章节课中跟读的信息
        $arrConds        = array(
            'lessonId' => $intLessonId,
            'purpose'  => Hkzb_Ds_Fudao_Exercise::PURPOSE_INCLASS,
            //        'type'     => Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_IN_LESSON_READ,
        );
        $intCnt          = $objDsExercise->getExerciseCntByConds($arrConds);
        $arrExerciseList = $objDsExercise->getExerciseListByConds($arrConds, array(), 0, $intCnt);

        if ( intval($intCnt)>0 ) {
            $intRepeatWordNum = intval( $intCnt );
        }

        // 获取学生在该章节下的课中答题情况
        $intStuAnswerCnt     = $objDsExerciseDetail->getExerciseDetailCntByStudentUid(
            $intStudentUid, $intCourseId, $intLessonId, Hkzb_Ds_Fudao_Exercise::PURPOSE_INCLASS
        );
        $arrStuAnswerListRet = $objDsExerciseDetail->getExerciseDetailListByStudentUid(
            $intStudentUid, $intCourseId, $intLessonId, Hkzb_Ds_Fudao_Exercise::PURPOSE_INCLASS, array(), 0, $intStuAnswerCnt
        );
        $arrStuAnswerList    = array();
        foreach ($arrStuAnswerListRet as $value) {
            $intExerciseId                      = intval($value[ 'exerciseId' ]);
            $arrStuAnswerList[ $intExerciseId ] = $value;
        }

        foreach ($arrExerciseList as $value) {
            $intTid          = intval($value[ 'question' ][ 'tid' ]);
            $intExerciseId   = intval($value[ 'exerciseId' ]);
            $intQuestionType = intval($value[ 'type' ]);

            // 如果tid小于0 跳过
            if ($intTid <= 0) {
                continue;
            }

            // 获取题目详情
            $arrSubjectInfo = $objDsHomework->getHomeworkDetailByTid($intTid);
            // 如果获取题目详情失败
            if (empty( $arrSubjectInfo )) {
                continue;
            }

            // 跟读单词文本
            $strRepeatWord = $arrSubjectInfo[ 'question' ][ 'title' ];
            // 单词释义
            $strTranslation = $arrSubjectInfo[ 'question' ][ 'translation' ];
            // 知识点
            $arrPointListRet = $arrSubjectInfo[ 'extContent' ][ 'pointsList' ];
            if (!is_array($arrPointListRet)) {
                $arrPointListRet = array();
            }

            // 学生作答情况
            $arrAnswerInfo = $arrStuAnswerList[ $intExerciseId ];

            // 所有知识点
            $arrPointList = array();
            foreach ($arrPointListRet as $point) {
                if (!in_array($point[ 'title' ], $arrAllPointsList)) {
                    $arrAllPointsList[] = $point[ 'title' ];
                }
                $arrPointList[] = $point[ 'title' ];

                // 易错知识点
                if (!in_array($point[ 'title' ], $arrErrorPointsList)) {
                    $arrAllPointsList[] = $point[ 'title' ];
                }
                if ('y' != $arrAnswerInfo[ 'remark' ][ 'result' ]) {
                    $arrErrorPointsList[] = $point[ 'title' ];
                }
            }

            // 单词
            if (Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_IN_LESSON_READ == $intQuestionType) {
                // 单词发音
                $strRepeatUrl = $arrAnswerInfo[ 'answer' ][ 'result' ];

                if (false === strpos($strRepeatUrl, "http")) {
                    $strRepeatUrl = Hkzb_Util_FuDao::getFileUrl($strRepeatUrl);
                }

                $intScore = intval($arrAnswerInfo[ 'score' ]);

                $intStarNum = $this->getStarNumByScore($intScore);

                // 获取 小于三星单词集合 和 五星单词集合
                if ($intStarNum <= 3) {
                    // 小于三星单词集合
                    $arrLessThreeStarWordsList[] = $strRepeatWord;
                    // 易错知识点
                    foreach ($arrPointList as $point) {
                        if (!in_array($point[ 'title' ], $arrErrorPointsList)) {
                            $arrErrorPointsList[] = $point[ 'title' ];
                        }
                    }

                } else if (5 == $intStarNum) {
                    // 五星单词集合
                    $intFiveStarNum++;
                }

                $arrRepeatList[] = array(
                    'rpepeat_words' => $strRepeatWord,
                    'word_record'   => $strRepeatUrl,
                    'word_means'    => $strTranslation,
                    'star_num'      => $intStarNum,
                );
            }
        }

        $arrOutput = array(
            "five_star_rpepeat_words_num" => $intFiveStarNum,
            "rpepeat_words_num"           => $intRepeatWordNum,
            "less_three_star_words"       => $arrLessThreeStarWordsList,
            "all_points"                  => $arrAllPointsList,
            "error_points"                => $arrErrorPointsList,
            "rpepeat_list"                => $arrRepeatList,
        );

        return $arrOutput;
    }

    /**
     * 获取星星数量
     * @param  int $score
     * @return int
     */
    private function getStarNumByScore( $score ) {

        $scoreRate = array(
            20  => 1,
            40  => 2,
            60  => 3,
            80  => 4,
            100 => 5,
        );

        foreach ($scoreRate as $limitScore => $starNum) {
            if ($score <= $limitScore) {
                return $starNum;
            }
        }

        return 0;

    }

    /**
     * @param $key int
     * @param $value
     * @param $lessonId int
     * @param $studentUid int
     * @return bool
     * @desc 学生需要存入缓存的一些扩展字段
     */
    public function setStudentExtDataRedis($key, $value, $lessonId, $studentUid) {
        $objCodis =  Hk_Service_RedisClient::getInstance("zbcourse");
        $redisKey    = "FUDAO_ELIVE_USER_EXT_DATA_{$lessonId}_{$studentUid}";
        $redis_res = $objCodis->get($redisKey);
        $arrRes = array();
        if ($redis_res){
            $arrRes = json_decode($redis_res, true);
        }
        $arrRes[$key] = $value;
        $res = $objCodis->set($redisKey, json_encode($arrRes));
        if ($res == false) {
            Bd_Log::warning("set ext data error arrRes:".json_encode($arrRes));
        }
        return $res;
    }

    /**
     * @param $arrStudentUid array
     * @param $lessonId int
     * @return array|bool
     * @desc 获取学生存入缓存的一些扩展字段
     */
    public function getStudentExtDataRedis($arrStudentUid, $lessonId) {
        $objCodis =  Hk_Service_RedisClient::getInstance("zbcourse");
        $arrRedisKey = array();
        foreach ($arrStudentUid as $studentUid) {
            $arrRedisKey[] = "FUDAO_ELIVE_USER_EXT_DATA_{$lessonId}_{$studentUid}";
        }
        $res = $objCodis->mget($arrRedisKey);
        if ($res == false) {
            Bd_Log::warning("get ext data error arrRedisKey:".json_encode($arrRedisKey));
            return $res;
        }
        $result = array();
        foreach ($arrRedisKey as $key => $redisKey) {
            if(empty($res[$key])) {
                continue;
            }
            $result[$redisKey] = $res[$key];
        }

        return $result;
    }

}
