<?php

/**
 * @file   NewCallRecord.php
 * <AUTHOR>
 * @date   2015-05-04
 * @brief  呼叫记录
 *
 **/
class Hkzb_Ds_Fudao_NewCallRecord
{

    const STATUS_INIT        = 0;
    const STATUS_START       = 1;
    const STATUS_END_CON     = 2;
    const STATUS_END_NCON    = 3;
    const STATUS_RECORD_FILE = 4;
    static $statusMap = array(
        self::STATUS_INIT         => '初始化',
        self::STATUS_START        => '开始呼叫',
        self::STATUS_END_CON      => '呼叫结束已接通',
        self::STATUS_END_NCON     => '呼叫结束未接通',
        self::STATUS_RECORD_FILE  => '语音文件上传完毕',
    );

    const TYPE_CALL = 0;
    const TYPE_HOMEWORK = 1;
    const TYPE_ATTEND   = 2;
    const TYPE_CONTINUE = 3;
    
    static $typeMap = array(
        self::TYPE_CALL     => '呼叫',
        self::TYPE_HOMEWORK => '作业',
        self::TYPE_ATTEND   => '出勤',
        self::TYPE_CONTINUE => '续报',
    );

    //服务商id
    const SERVER_ID_INT = 1;
    //第三方呼叫id
    const CID_INT = 1;

    private $_objDaoNewCallRecord;

    /**
     * 构造函数
     *
     */
    public function __construct() {
        $this->_objDaoNewCallRecord = new Hkzb_Dao_Fudao_NewCallRecord();
    }

    /**
     * 新增记录
     *
     * @param  array $arrParams 通话属性
     * @return bool true/false
     */
    public function addNewCallRecord($arrParams) {
        if ((trim($arrParams['fromPhone']) == '') || (trim($arrParams['toPhone']) == '')
        ) {
            Bd_Log::warning("Error:[param error], Detail:[param: " . json_encode($arrParams) . "]");

            return false;
        }

        $arrFields = array(
            'fromUid'    => intval($arrParams['fromUid']),
            'toUid'      => intval($arrParams['toUid']),
            'fromPhone'  => strval($arrParams['fromPhone']),
            'toPhone'    => strval($arrParams['toPhone']),
            'courseId'   => isset($arrParams['courseId']) ? intval($arrParams['courseId']) : 0,
            'lessonId'   => isset($arrParams['lessonId']) ? intval($arrParams['lessonId']) : 0,
            'classId'    => isset($arrParams['classId']) ? intval($arrParams['classId']) : 0,
            'serverId'   => isset($arrParams['serverId']) ? intval($arrParams['serverId']) : self::SERVER_ID_INT,
            'cid'        => isset($arrParams['cid']) ? strval($arrParams['cid']) : self::CID_INT,
            'type'       => isset($arrParams['type']) ? intval($arrParams['type']) : self::TYPE_CALL,
            'status'     => isset($arrParams['status']) ? intval($arrParams['status']) : self::STATUS_START,
            'startTime'  => isset($arrParams['startTime']) ? intval($arrParams['startTime']) : time(),
            'endTime'    => isset($arrParams['endTime']) ? intval($arrParams['endTime']) : 0,
            'duration'   => isset($arrParams['duration']) ? intval($arrParams['duration']) : 0,
            'recordFile' => isset($arrParams['recordFile']) ? strval($arrParams['recordFile']) : '',
            'createTime' => time(),
            'updateTime' => time(),
            'extData'    => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '',
        );

        $ret = $this->_objDaoNewCallRecord->insertRecords($arrFields);
        if ($ret === false) {
            return false;
        }

        return $this->_objDaoNewCallRecord->getInsertId();
    }

    /**
     * 更新通话记录
     *
     * @param  array $arrConds  条件
     * @param  array $arrParams 电话属性
     * @return bool true/false
     */
    public function updateNewCallRecord($arrConds, $arrParams) {
        if (empty($arrConds) || empty($arrParams)) {
            Bd_Log::warning("Error:[param error], Detail:[id:$arrConds arrParams:$arrParams]");

            return false;
        }

        do {
            $tmpArrFields = Hkzb_Dao_Fudao_NewCallRecord::$allFields;
            $data         = $this->_objDaoNewCallRecord->getRecordByConds($arrConds, $tmpArrFields);
            if (!$data) {
                Bd_Log::warning("Error:[param error], Detail:[" . json_encode($arrConds) . "]");

                return false;
            }

            //待更新的所有字段
            $arrFields = array();

            //3. 拿回id 和 extdata
            $extData = $data['extData'];
            //extData补全
            if (isset($arrParams['extData']) && is_array($arrParams['extData'])) {
                foreach ($arrParams['extData'] as $key => $value) {
                    $extData[$key] = $value;
                }
                $arrFields['extData'] = json_encode($extData);
                unset($arrParams['extData']);
            }

            //4. 其他字段补全
            $arrAllFields = Hkzb_Dao_Fudao_NewCallRecord::$allFields;
            foreach ($arrParams as $key => $value) {
                //不允许把值置空
                if (!$value && !in_array($key, $arrAllFields)) {
                    continue;
                }
                $arrFields[$key] = $value;
            }

            //5. 更新数据库
            $ret = $this->_objDaoNewCallRecord->updateByConds($arrConds, $arrFields);

            if ($ret === false) {
                //提交失败
                Bd_Log::warning("Error:[update callRecord error], Detail:[" . json_encode($arrConds) . "]");

                return false;
            }

        } while (0);

        return true;
    }

    /**
     * 获取通话详情
     *
     * @param  string $callId call_id
     * @param  array  $arrFields 指定属性
     * @return array
     */
    public function getCallRecordInfo($callId, $arrFields = array()) {
        if (intval($callId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[call_id:$callId]");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = Hkzb_Dao_Fudao_NewCallRecord::$allFields;
        }

        $arrConds = array(
            'callId' => strval($callId),
        );

        $ret = $this->_objDaoNewCallRecord->getRecordByConds($arrConds, $arrFields);

        return $ret;
    }

    /**
     * 列表
     * @param       $arrConds
     * @param array $arrFields
     * @param int   $offset
     * @param int   $limit
     * @return array|false
     */
    public function getNewCallRecordListByConds($arrConds, $arrFields = array(), $offset = 0, $limit = 20) {

        if (empty($arrFields)) {
            $arrFields = Hkzb_Dao_Fudao_NewCallRecord::$allFields;
        }

        $arrAppends = array(
            "order by create_time desc ",
            "limit $offset, $limit",
        );

        $ret = $this->_objDaoNewCallRecord->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }

    /**
     * 获取总记录数
     *
     * @param       $arrConds
     * @return array|false
     */
    public function getNewCallRecordCnt($arrConds) {

        $ret = $this->_objDaoNewCallRecord->getCntByConds($arrConds);

        return $ret;
    }
    
    /**
     * 删除访谈记录
     *
     * @param       $callId
     * @return array|false
     */
    public function delInterviewRecord($callId, $extData = array()) {
        if(intval($callId) < 0){
            Bd_Log::warning("Error:[param error], Detail:[call_id:$callId]");
            return false;
        }
        //若未传入extData
        if(empty($extData)){
            $record     = $this->getCallRecordInfo($callId, array('extData'));
            $extData    = $record['extData'];
        }
        //删除和interview记录的关联
        if(isset($extData['interviewId'])){
            unset($extData['interviewId']);
        } else{
            return true;
        }
        
        //更新DB
        $arrConds   = array('callId' => $callId);
        $arrParams  = array('extData' => json_encode($extData),'updateTime' => time());
        $ret        = $this->_objDaoNewCallRecord->updateByConds($arrConds, $arrParams);

        return $ret;
    }
}
