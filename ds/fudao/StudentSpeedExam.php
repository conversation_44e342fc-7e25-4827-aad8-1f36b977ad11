<?php
/***************************************************************************
 *
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/



/**
 * @file StudentSpeedExam.php
 * <AUTHOR>
 * @date 2015/11/17 14:13:18
 * @brief 学生答题记录
 *
 **/

class Hkzb_Ds_Fudao_StudentSpeedExam {
    //状态
    const ALL_FIELDS = 'id,lessonId,studentUid,examId,rightCnt,allCnt,answerList,createTime,updateTime,extData';


    /**
     * 新增学生老师
     *
     * @param  mix  $arrParams 班级属性
     * @return bool true/false
     */
    public function addStudentStudentSpeedExam($arrParams) {
        if(intval($arrParams['studentUid']) <= 0 || intval($arrParams['lessonId']) <= 0 || intval($arrParams['examId']) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[param: ". json_encode($arrParams). "]");
            return false;
        }

        $arrFields = array(
            'studentUid'   => intval($arrParams['studentUid']),
            'lessonId'     => intval($arrParams['lessonId']),
            'examId'       => intval($arrParams['examId']),
            'rightCnt'     => intval($arrParams['rightCnt']),
            'allCnt'       => intval($arrParams['allCnt']),
            'createTime'   => time(),
            'updateTime'   => time(),
            'answerList'   => isset($arrParams['answerList']) ? json_encode($arrParams['answerList']) : '',
            'extData'      => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '',
        );

        $objDaoStudentSpeedExam = new Hkzb_Dao_Fudao_StudentSpeedExam();
        $ret = $objDaoStudentSpeedExam->insertRecords($arrFields);
        return $ret;
    }
    public function getStudentSpeedExam($studentUid, $lessonId, $examId) {
        if($studentUid <= 0 || $lessonId <= 0 || $examId <= 0){
            Bd_Log::warning("Error:[param error], Detail:[param: ". json_encode(array('studentUid' => $studentUid, 'lessonId' => $lessonId, 'examId' => $examId)). "]");
            return false;
        }
        $arrConds  = array(
            'studentUid' => $studentUid,
            'lessonId'   => $lessonId,
            'examId'     => $examId,
        );
        $arrFields = explode(',', self::ALL_FIELDS);
        $objDaoStudentSpeedExam = new Hkzb_Dao_Fudao_StudentSpeedExam();
        $ret = $objDaoStudentSpeedExam->getRecordByConds($arrConds, $arrFields);
        if(empty($ret) || !$ret){
            return false;//记录为空也反回false
        }
        return $ret;
    }
    //获取排行榜基础数据
    public function getLessonSpeedExamList($lessonId, $examId, $offset = 0, $limit = 100){
        if($lessonId <= 0 || $examId <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[param: ". json_encode(array('lessonId' => $lessonId, 'examId' => $examId)). "]");
            return false;
        }
        $arrConds = array(
            'lessonId'   => $lessonId,
            'examId'     => $examId,
        );

        $arrFields = explode(',', self::ALL_FIELDS);

        $arrAppends = array(
            "order by right_cnt desc",
            "limit $offset, $limit",
        );
        $objDaoStudentSpeedExam = new Hkzb_Dao_Fudao_StudentSpeedExam();
        $ret = $objDaoStudentSpeedExam->getListByConds($arrConds, $arrFields, NULL, $arrAppends, $useIndex);
        return $ret;
    }
}
