<?php
/**
 * 动态分析行为记录表
 * Created by PhpStorm.
 * @author: <PERSON><PERSON><PERSON><PERSON><ji<PERSON><PERSON><PERSON>@zuoyebang.com>
 * @Date: 2018/7/5
 * @Time: 16:08
 */
class Hkzb_Ds_Fudao_TeacherSaysShare
{

    //分享者类型
    const ROLE_STUDENT = 0;
    const ROLE_TEACHER = 1;
    static $ROLE_ARRAY = array(
        self::ROLE_STUDENT => '学生',
        self::ROLE_TEACHER => '老师',
    );
	
    const ALL_FIELDS = 'id,msgId,pubUid,pubType,type,createTime,updateTime';

    private $objDaoTeacherSaysShare;

    /**
     * 构造函数
     *
     */
    public function __construct()
    {
        $this->objDaoTeacherSaysShare = new Hkzb_Dao_Fudao_TeacherSaysShare();
    }

    /**
     * 新增分享
     * @param  mix $arrParams 分享属性
     * @return bool true/false
     */
    public function addTeacherSaysShare($arrParams)
    {
        if (empty($arrParams) || intval($arrParams['msgId']) <= 0 || intval($arrParams['pubUid']) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[param: " . json_encode($arrParams) . "]");
            return false;
        }

        $arrFields = array(
            'msgId'       => isset($arrParams['msgId']) ? intval($arrParams['msgId']) : 0,
            'pubUid'      => isset($arrParams['pubUid']) ? intval($arrParams['pubUid']) : 0,
            'pubType'     => isset($arrParams['pubType']) ? intval($arrParams['pubType']) : 0,
            'type'        => isset($arrParams['type']) ? intval($arrParams['type']) : 0,
            'createTime'  => time(),
            'updateTime'  => time(),
        );

        $ret = $this->objDaoTeacherSaysShare->insertRecords($arrFields);
	    
        if (!$ret) {
            Bd_Log::warning("Error[dbError] Detail[db insert failed]");
            return false;
        }

        return $ret ? $this->objDaoTeacherSaysShare->getInsertId() : 0;
    }


    /**
     * 更新分享信息
     * @param  int $id   分享id
     * @param  array $arrParams 分享属性
     * @return bool true/false
     */
    public function updateTeacherSaysShare($id, $arrParams)
    {
        if (empty($arrParams) || intval($id) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[id:$id arrParams:$arrParams]");
            return false;
        }

        $arrConds = array(
            'id'   => intval($id),
        );

        $arrFields = array();
        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach ($arrParams as $key => $value) {
            if (!in_array($key, $arrAllFields)) {
                continue;
            }

            $arrFields[$key] = $value;
        }
        
        $arrFields['updateTime'] = time();

        $ret = $this->objDaoTeacherSaysShare->updateByConds($arrConds, $arrFields);

        return $ret;
    }


    /**
     * 根据条件更新分享信息
     * @param  array $arrCond   条件
     * @param  array $arrParams 分享属性
     * @return bool true/false
     */
    public function updateTeacherSaysShareByConds($arrCond, $arrParams)
    {
        if (empty($arrParams) || empty($arrCond)) {
            Bd_Log::warning("Error:[param error], Detail:[arrConds:$arrCond arrParams:$arrParams]");
            return false;
        }

        $arrFields = array();
        $arrAllFields = explode(',', self::ALL_FIELDS);

        $arrConds   = array();
        foreach ($arrCond as $key => $value) {
            if (!in_array($key, $arrAllFields)) {
                continue;
            }

            $arrConds[$key] = $value;
        }

        foreach ($arrParams as $key => $value) {
            if (!in_array($key, $arrAllFields)) {
                continue;
            }

            $arrFields[$key] = $value;
        }

        $arrFields['updateTime'] = time();

        $ret = $this->objDaoTeacherSaysShare->updateByConds($arrConds, $arrFields);

        return $ret;
    }


    /**
     * 获取指定分享信息
     * @param  int $id id    分享id
     * @param  array $arrFields 分享属性
     * @return mix
     */
    public function getTeacherSaysShareInfo($id, $arrFields = array())
    {
        if (intval($id) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[id:$id]");
            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'id' => intval($id),
        );

        $ret = $this->objDaoTeacherSaysShare->getRecordByConds($arrConds, $arrFields);

        return $ret;
    }


    /**
     * 获取指定条件的分享信息
     *
     * @param  array $arrConds   筛选条件
     * @param  array $arrFields 分享属性
     * @return mix
     */
    public function getTeacherSaysShareByConds($arrConds, $arrFields = array())
    {

        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $ret = $this->objDaoTeacherSaysShare->getListByConds($arrConds, $arrFields);

        return $ret;
    }


    /**
     * 获取分享数量
     * @param $arrConds
     * @return false|int
     */
    public function getTeacherSaysShareCntByConds($arrConds)
    {
        $ret = $this->objDaoTeacherSaysShare->getCntByConds($arrConds);
        return $ret;
    }


    /**
     * 获取指定条件的分享信息
     *
     * @param array $arrConds     条件参数
     * @param array $arrFields   访问的属性
     * @param int $offset
     * @param int $limit
     * @return array|false
     */
    public function getTeacherSaysShareListByConds($arrConds, $arrFields = array())
    {
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $ret = $this->objDaoTeacherSaysShare->getListByConds($arrConds, $arrFields);

        return $ret;
    }
}