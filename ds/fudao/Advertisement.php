<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file Advertisement.php
 * <AUTHOR>
 * @date 2015/11/17 14:13:18
 * @brief 首页Advertisement
 *  
 **/

class Hkzb_Ds_Fudao_Advertisement {


    const ALL_FIELDS = 'id,title,pid,province,city,url,grade,targetUser,status,createTime,updateTime,operatorUid,operator,extData,urlType';

    const URL_TYPE_WEB = 1;
    const URL_TYPE_NATIVE = 2;
    const URL_TYPE_VIDEO = 3;
    static $URL_TYPE_LIST = array(
        self::URL_TYPE_WEB => 'web',
        self::URL_TYPE_NATIVE => 'native',
        self::URL_TYPE_VIDEO => 'video',
    );

    const ADVERTISEMENT_CACHE_KEY = 'zhiboke_ds_advertisement';

    const TYPE_ALL = 0;
    const TYPE_BAN_NEW  = 1;
    const TYPE_BAN_OLD  = 2;
    const TYPE_ZB_NEW  = 3;
    const TYPE_ZB_OLD  = 4;
    static $TYPE_ARRAY = array(
        self::TYPE_ALL => '所有用户',
        self::TYPE_BAN_NEW => '班课新用户',
        self::TYPE_BAN_OLD => '班课老用户',
        self::TYPE_ZB_NEW => '直播课新用户',
        self::TYPE_ZB_OLD => '直播课老用户',
    );

    const STATUS_TOONLINE = 0;
    const STATUS_ONLINE   = 1;
    const STATUS_DELETED  = 2;
    static $STATUS_ARRAY = array(
        self::STATUS_TOONLINE => '待上线',
        self::STATUS_ONLINE   => '已上线',
        self::STATUS_DELETED  => '已删除',
    );

    /**
     * 新增Advertisement
     *
     * @param  array  $arrParams Advertisement属性
     * @return bool true/false
     */
    public function addAdvertisement($arrParams) {
        if(empty($arrParams['urlType']) || strlen(strval($arrParams['pid'])) <= 0 || strlen(strval($arrParams['title'])) <= 0 || strlen(strval($arrParams['url'])) <= 0 || $arrParams['grade'] < 0 || $arrParams['targetUser'] < 0) {
            Bd_Log::warning("Error:[param error], Detail:[param: ". json_encode($arrParams) . "]");
            return false;
        }

        $arrFields = array(
            'title'             => strval($arrParams['title']),
            'pid'               => strval($arrParams['pid']),
            'url'               => isset($arrParams['url']) ? strval($arrParams['url']) : '',
            'grade'             => strval($arrParams['grade']),
            'province'          => $arrParams['province'],
            'city'              => $arrParams['city'],
            'targetUser'        => intval($arrParams['targetUser']),
            'createTime'        => time(),
            'updateTime'        => time(),
            'operatorUid'       => isset($arrParams['operatorUid']) ? intval($arrParams['operatorUid']) : 0,
            'operator'          => isset($arrParams['operator']) ? strval($arrParams['operator']) : '',
            'extData'           => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '',
            'urlType'           => intval($arrParams['urlType']),
        );
        $objDaoAdvertisement = new Hkzb_Dao_Fudao_Advertisement();
        $ret = $objDaoAdvertisement->insertRecords($arrFields);

        //清理缓存
        $objMemcached = Hk_Service_Memcached::getInstance('zhiboke');
        $cacheKey = self::ADVERTISEMENT_CACHE_KEY;
        $objMemcached->delete($cacheKey);

        return $ret;
    }

    /**
     * 更新Advertisement
     *
     * @param  int  $Advertisement  Advertisementid
     * @param  array  $arrParams Advertisement属性
     * @return bool true/false
     */
    public function updateAdvertisement($AdvertisementId, $arrParams) {
        if(intval($AdvertisementId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[Advertisement:$AdvertisementId]");
            return false;
        }

        $arrConds = array(
            'id' => intval($AdvertisementId),    
        );

        $arrFields = array();
        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach($arrParams as $key => $value) {
            if(!in_array($key, $arrAllFields)) {
                continue;
            }

            $arrFields[$key] = $value;
        }

        //ext由ps补全，json处理在ds
        if(isset($arrFields['extData'])) {
            $arrFields['extData'] = json_encode($arrFields['extData']);
        }

        //补全更新时间
        $arrFields['updateTime'] = time();
        $objDaoAdvertisement = new Hkzb_Dao_Fudao_Advertisement();
        $ret = $objDaoAdvertisement->updateByConds($arrConds, $arrFields);

        //清理缓存
        $objMemcached = Hk_Service_Memcached::getInstance('zhiboke');
        $cacheKey = self::ADVERTISEMENT_CACHE_KEY;
        $objMemcached->delete($cacheKey);

        return $ret;
    }



    /**
     ** 获取Advertisement详情
     **
     ** @param  int  $Advertisement  Advertisementid
     ** @param  mix  $arrFields 指定属性
     ** @return mix
    **/
    public function getAdvertisementInfo($AdvertisementId, $arrFields = array()) {
        if(intval($AdvertisementId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[AdvertisementId:$AdvertisementId]");
            return false;
        }

        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'id' => intval($AdvertisementId),
        );

        $objDaoAdvertisement = new Hkzb_Dao_Fudao_Advertisement();
        $ret = $objDaoAdvertisement->getRecordByConds($arrConds, $arrFields);

        return $ret;

    }

    /**
     * @param $arrConds
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @return array|false
     */
    public function getAdvertisementListByConds($arrConds, $arrFields = array(),$offset = 0, $limit = 20) {
        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrAppends = array(
            "order by create_time desc",
            " limit $offset, $limit",
        );
        $objDaoAdvertisement = new Hkzb_Dao_Fudao_Advertisement();
        $ret = $objDaoAdvertisement->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }


    /**
     * 获取数量
     * @param $arrConds
     * @return false|int
     */
    public function getAdvertisementCntByConds($arrConds) {
        $objDaoAdvertisement = new Hkzb_Dao_Fudao_Advertisement();
        $ret = $objDaoAdvertisement->getCntByConds($arrConds);
        return $ret;
    }

    //首页获取广告
    public function getAdvertisement() {
        //读缓存
        $cacheKey = self::ADVERTISEMENT_CACHE_KEY;
        $objMemcached = Hk_Service_Memcached::getInstance('zhiboke');
        $cacheValue = $objMemcached->get($cacheKey);
        if(!empty($cacheValue)) {
            $adInfoList = json_decode($cacheValue, true);
            return $adInfoList;
        }

        //读数据库
        $arrFields = explode(',', self::ALL_FIELDS);
        $arrConds = array(
            'status' => Hkzb_Ds_Fudao_Advertisement::STATUS_ONLINE,
        );
        $objDaoAdvertisement = new Hkzb_Dao_Fudao_Advertisement();
        $adInfoList = $objDaoAdvertisement->getListByConds($arrConds, $arrFields);
        if(false === $adInfoList) {
            Bd_Log::warning("Error:[getListByConds], Detail:[]");
            return false;
        }

        //写缓存
        $cacheValue = json_encode($adInfoList);
        $objMemcached->set($cacheKey, $cacheValue);

        return $adInfoList;
    }
}
