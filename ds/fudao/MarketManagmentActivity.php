<?php
/**
 * Created by PhpStorm.
 * User: zu<PERSON>eb<PERSON>
 * Date: 2017/11/24
 * Time: 12:18
 *营销管理活动
 */
class Hkzb_Ds_Fudao_MarketManagmentActivity extends Hk_Util_Category
{
    //状态-活动开始的状态
    const STATUS_UNDELETED = 0;   //未开始
    const STATUS_DELETED   = 1;   //结束
    static $STATUS_ARRAY = array(
        self::STATUS_UNDELETED     => '活动未删除',
        self::STATUS_DELETED       => '活动已删除',
    );

    //状态-活动开始的状态
    const RULE_COURSE = 1;   //未开始
    const RULE_RULE = 2;   //未开始
    static $RULE_ARRAY = array(
        self::RULE_COURSE     => '指定课程',
        self::RULE_RULE       => '条件筛选',
    );
    //状态-活动开始的状态
    const FREE_COURSE = 1;   //送课程
    const FREE_COUPON = 2;   //送优惠券
    const FREE_COURSE_COUPON= 3;   //送优惠券+送课程
    static $FREE_ARRAY = array(
        self::FREE_COURSE         => '送课程',
        self::FREE_COUPON         => '送优惠券',
        self::FREE_COURSE_COUPON  => '送优惠券+送课程',
    );

    const ALL_FIELDS = 'activityId,activityName,activityTitle,ruleType,grade,freeType,startTime,endTime,createTime,updateTime,status,operatorUid,operatorName,extData';

    private $_objDaoMarketManagmentActivity;
    public function __construct(){
        $this->_objDaoMarketManagmentActivity = new Hkzb_Dao_Fudao_MarketManagmentActivity();
    }

    /**
     * 创建营销活动
     * @param $arrFields 营销活动属性
     * @return bool
     */
    public function insertMarketActivity($arrFields)
    {
        $arrInsert = array(
            'activityName' => isset($arrFields['activityName']) ? trim($arrFields['activityName']) : '',
            'activityTitle'=> isset($arrFields['activityTitle']) ? trim($arrFields['activityTitle']) : '',
            'ruleType'     => isset($arrFields['ruleType']) ? intval($arrFields['ruleType']) : 0,
            'freeType'     => isset($arrFields['freeType']) ? intval($arrFields['freeType']) : 0,
            'startTime'    => isset($arrFields['startTime']) ? intval($arrFields['startTime']) : 0,
            'endTime'      => isset($arrFields['endTime']) ? intval($arrFields['endTime']) : 0,
            'grade'        => isset($arrFields['grade']) ? intval($arrFields['grade']) : 0,
            'createTime'   => time(),
            'updateTime'   => time(),
            'status'       => isset($arrFields['status']) ? intval($arrFields['status']) : self::STATUS_UNDELETED,
            'operatorUid'  => isset($arrFields['operatorUid']) ? intval($arrFields['operatorUid']) : 0,
            'operatorName' => isset($arrFields['operatorName']) ? trim($arrFields['operatorName']) : '',
            'extData'      => isset($arrFields['extData']) ? json_encode($arrFields['extData']) : '',
        );
        if (empty($arrInsert['activityName']) || $arrInsert['activityTitle'] < 0
            || empty($arrInsert['startTime']) || empty($arrInsert['endTime'])|| empty($arrInsert['operatorUid']))
        {
            Bd_Log::warning("Error:[param error]");
            return false;
        }

        $ret = $this->_objDaoMarketManagmentActivity->insertRecords($arrInsert);
        return $ret;
    }

//    /**
//     * 更新活动
//     * @param $activityId 活动id
//     * @param $arrParams  活动属性
//     * @return bool
//     */
    public function deletedActivity($activityId){
        $activityId = intval($activityId);
        if($activityId <= 0){
            Bd_Log::warning("Error:[param error], Detail:[activityId:$activityId]");
            return false;
        }
        $arrConds = array(
            'activityId' => $activityId,
        );
        $arrFields['status'] = self::STATUS_DELETED;
        //补全更新时间
        $arrFields['updateTime'] = time();

        $ret = $this->_objDaoMarketManagmentActivity->updateByConds($arrConds, $arrFields);
        return $ret;
    }

    /**获取数量
     * @param $arrConds
     * @return false|int
     */
    public function getActivityCntByConds($arrConds){
        $ret = $this->_objDaoMarketManagmentActivity->getCntByConds($arrConds);
        return $ret;
    }

    /**
     * @param $arrConds
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @return array|false
     */
    public function getActivityListByConds($arrConds, $arrFields = array(), $offset = 0, $limit = 20){
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrAppends = array(
            "order by create_time desc",
            "limit $offset, $limit",
        );

        $ret = $this->_objDaoMarketManagmentActivity->getListByConds($arrConds, $arrFields, NULL, $arrAppends);
        return $ret;
    }


    /**
     * 通过活动id获得活动信息
     * @param $activityId
     * @param array $arrFields
     * @return array|bool|false
     */
    public function getActivityById($activityId, $arrFields = array()){
        $activityId = intval($activityId);
        if($activityId <= 0){
            Bd_Log::warning("Error:[param error] Detail:[activityId:$activityId]");
            return false;
        }

        $arrConds = array(
            'activityId' => $activityId,
        );
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $ret = $this->_objDaoMarketManagmentActivity->getRecordByConds($arrConds, $arrFields);
        return $ret;
    }

}