<?php

/**
 * @file    StudentCache.php
 * <AUTHOR>
 * @date    2017-11-23
 * @brief   学生维度的课中缓存
 *          主要包括两部分：1，行为记录 2，状态记录（学分，单个禁言）
 *
 **/
class Hkzb_Ds_Fudao_Inclass_StudentCache {

    private $_objRedis;

    public function __construct() {
        $this->_objRedis = Hk_Service_RedisClient::getInstance("zhibo");
    }

    // 被点名
    const TEACHER_INCLASS_ACTION_CALLNAME = 1;
    // 表扬
    const TEACHER_INCLASS_ACTION_PRAISE = 2;
    // 红包
    const TEACHER_INCLASS_ACTION_REDENVELOPE = 3;
    // 互动题
    const TEACHER_INCLASS_ACTION_EXERCISE = 4;
    // 是否卡
    const TEACHER_INCLASS_ACTION_YESNOCARD = 5;
    // 抢麦
    const TEACHER_INCLASS_ACTION_MIC = 6;
    // 
    

    public static $TEACHER_INCLASS_ACTION_MAP = array(
        self::TEACHER_INCLASS_ACTION_CALLNAME => '点名',
        self::TEACHER_INCLASS_ACTION_PRAISE => '表扬',
        self::TEACHER_INCLASS_ACTION_REDENVELOPE => '红包',
        self::TEACHER_INCLASS_ACTION_EXERCISE => '互动题',
        self::TEACHER_INCLASS_ACTION_YESNOCARD => '是否卡',
        self::TEACHER_INCLASS_ACTION_MIC => '抢麦',
    );

    /**
     * 添加学生课堂行为
     *
     * @param int $intLesson    章节id
     * @param $arrInput  array  信息id
     * 传入值格式：
     * array(
     *    'action' =>  int，标示行为类型
     *    'message' => array
     * )
     *
     * @return  bool 添加成功或者失败
     */
    public function addStudentAction( $intLesson, $arrInput ) {

        $ret = false;
        return $ret;
    }

    


}