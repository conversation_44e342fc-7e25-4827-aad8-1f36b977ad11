<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file: Messager.php
 * @author: huanghe <<EMAIL>>
 * @date: 2017/9/15 上午11:27
 * @brief: 新的课中互动消息发布类
 */
class Hkzb_Ds_Fudao_Messager
{
    //单批发送的最大人数
    const MAX_NUM_PER_SEND = 2000;
    //消息生存周期（单位：秒）
    const MSG_DATA_EXPIRE = 120;
    //异步每批最大人数
    const MAX_NUM_FOR_ASYNC = 10000;
    //超过这个数限制，异步发送
    const MAX_NUM_LIMIT_FOR_ASYNC = 20000;

    //错误码
    const ERRNO_PARAM = -1; // 参数错误
    const ERRNO_RAL = -2; // ral网络交互错误
    const ERRNO_MQS = -3; // mqs处理失败

    /**
     * （必填）信令号，Hkzb_Const_FudaoMsgSignal中的常量
     * @var integer
     */
    protected $signalNo;

    /**
     * （必填）消息来源用户id
     * @var integer
     */
    protected $fromUid;

    /**
     * 消息接收者用户id列表
     * @var array
     */
    protected $toUids;

    /**
     * 消息内容
     * @var string|array
     */
    protected $msgBody;

    /**
     * 调用方明确指定的班级id列表
     * @var array
     */
    protected $classIds;

    /**
     * 章节id
     * @var integer
     */
    protected $lessonId;
    /**
     * 用于班主任后台区分班级用的班级id
     * @var integer
     */
    protected $classIdForAssistant;
    /**
     * (本类内部使用) MQS消息实例
     * @var Hkzb_Service_MQSMessager
     */
    private $mqsMessager;

    /**
     * （本类内部使用）用于日志记录的kv结构 (长连接不需要此结构)
     * @var array
     */
    private $msgLogKv;

    /**
     * (本类内部使用) 辅导消息内容, 提供给长连接
     * @var array
     */
    private $fudaoMsgKv;

    /**
     * 是否需要尝试降级处理，实际降级与否取决于Redis开关
     * @var boolean
     */
    private $isNeedDegrade;

    /**
     * 固定降级比率 （1 - 10）
     * @var integer
     */
    private $fixDegradeRate;

    /**
     * msg_delivery_type 名单列表
     * @var  array
     */
    private $deliveryTypeList;

    /**
     * 消息的msgId
     * @var int
     */
    private $msgId;

    public function __construct()
    {
        $this->fromUid = 0;
        $this->lessonId = 0;
        $this->classIdForAssistant = 0;
        $this->msgBody = array();
        $this->toUids = array();
        $this->classIds = array();
        $this->isNeedDegrade = false;
        $this->fixDegradeRate = -1;
        $this->mqsMessager = new Hkzb_Service_MQSMessager(Hkzb_Service_MQSMessager::MQS_PRODUCT_FUDAO);
        $this->msgId = 0;
        $this->deliveryTypeList = array(Hkzb_Const_FudaoMsgSignal::NEW_MSG, Hkzb_Const_FudaoMsgSignal::STUDENT_LIST_EDIT);
    }

    /**
     * 发送消息
     *
     * @return array err_no：错误码,成功为0; err_msg:错误消息
     */
    public function send()
    {
        //预返回的结果
        $ret = array(
            'err_no' => 0,
            'err_msg' => 'success',
            'msgId' => 0,
        );

        //必备参数校验
        if (empty($this->signalNo) || $this->fromUid <= 0) {
            $ret['err_no'] = self::ERRNO_PARAM;
            $ret['err_msg'] = "param error";
            return $ret;
        }

        //如果没有传章节id，则初始化章节id
        if ($this->lessonId <= 0) {
            $arrRequest = Saf_SmartMain::getCgi();
            $this->lessonId = intval($arrRequest['request_param']['lessonId']);
        }

        if ($this->lessonId <= 0) {
            Bd_Log::warning("double write failed, cann't get to_group_id");
            $ret['err_no'] = self::ERRNO_PARAM;
            $ret['err_msg'] = "need lessonId";
            return $ret;
        }

        //计算出最终的接收人列表
        Hk_Util_Log::start('ds_messager_setuids');
        $this->setFinalUids();
        Hk_Util_Log::stop('ds_messager_setuids');

        if (empty($this->toUids)) {
            $ret['err_no'] = self::ERRNO_PARAM;
            $ret['err_msg'] = "can not find revievers";
            return $ret;
        }

        //设置MQS需要的其它参数
        $this->setMQSOptions();
        //设置消息
        $this->setMessageKv();
        //将用户分组
        $toUidsChunkedList = array_chunk($this->toUids, self::MAX_NUM_PER_SEND);

        //消息id
        $msgId = $this->msgId ? intval($this->msgId) : false;
        //降级比率
        $degradeRate = $this->getDegradedRate();

        //处理降级
        if ($degradeRate) {
            //降级处理
            $msgId = $this->mqsMessager->send();
            $msgModVal = intval($msgId) % 10;

            if ($msgId && $msgModVal < $degradeRate) {
                Bd_Log::addNotice('MsgDegradation', 1);
                Bd_Log::addNotice('MsgLossRate', $degradeRate);
                Bd_Log::addNotice('MsgMsgId', $msgId);
                Bd_Log::addNotice('MsgModVal', $msgModVal);
                //分组用户置空
                $toUidsChunkedList = array();
            }

        }
        //主讲发送给整个lesson的消息
        if (count($this->toUids) > self::MAX_NUM_LIMIT_FOR_ASYNC) {
            // 1. 存消息，得到msgId
            try {
                if (!$msgId) {
                    $msgId = $this->mqsMessager->send();
                }
            } catch (Hk_Util_Exception $ex) {

            }
            // 2. 异步转发
            if ($msgId) {
                //异步参数设置
                $arrCommand = array(
                    'signalNo' => $this->signalNo,
                    'fromUid' => $this->fromUid,
                    'lessonId' => $this->lessonId,
                    'msgBody' => $this->msgBody,
                    'msgId' => $msgId,
                );
                //转发
                $objNmq = new Hk_Service_Nmq();

                $toUidsChunkedList = array_chunk($this->toUids, self::MAX_NUM_FOR_ASYNC);
                foreach ($toUidsChunkedList as $toUids) {
                    $arrCommand['toUids'] = $toUids;
                    $objNmq->talkToQcm(Hk_Const_Command::CMD_FUDAO_FORWARD_TEACHER_MSG, $arrCommand);
                }
            }

            //3. 分组用户置空
            $toUidsChunkedList = array();
        }

        //分批发送，首批为发送，其余批次为转发
        foreach ($toUidsChunkedList as $toUids) {
            try {
                if (!$msgId) {
                    $msgId = $this->mqsMessager->setToUids($toUids)->send();
                } else {
                    $this->mqsMessager->setMsgId($msgId)->setToUids($toUids)->forward();
                }
            } catch (Hk_Util_Exception $ex) {
            }
        }

        if (!$msgId) {
            Bd_Log::warning("get msgId failed");
            $ret['err_no'] = self::ERRNO_RAL;
            $ret['err_msg'] = 'talk with mqs error';
            return $ret;
        }
        //结果中返回msgId
        $ret['msgId'] = $msgId;
        Bd_Log::addNotice('fudao_mqs_msgkv_' . $msgId, json_encode($this->msgLogKv));

        //发送后的一些工作
        if (!$degradeRate) {
            $this->afterSent($msgId);
        }

        //清理参数
        $this->clearOpts();

        return $ret;

    }

    /**
     * 设置信令号，Hkzb_Const_FudaoMsgSignal中的常量
     *
     * @param integer $signalNo 信令号
     * @return Hkzb_Ds_Fudao_Messager
     */
    public function setSignalNo($signalNo)
    {
        $this->signalNo = intval($signalNo);
        return $this;
    }

    /**
     * 设置消息来源uid
     *
     * @param integer $fromUid 消息来源用户id
     * @return Hkzb_Ds_Fudao_Messager
     */
    public function setFromUid($fromUid)
    {
        $this->fromUid = intval($fromUid);
        return $this;
    }

    /**
     * 设置消息接收者用户id列表
     *
     * @param integer|array $toUids 消息接收者用户id列表
     * @return Hkzb_Ds_Fudao_Messager
     */
    public function setToUids($toUids)
    {
        if (!empty($toUids)) {

            if (!is_array($this->toUids)) {
                $this->toUids = array();
            }

            if (is_array($toUids)) {
                $this->toUids = array_merge($this->toUids, $toUids);
            } else if (is_int($toUids)) {
                $this->toUids = array_merge($this->toUids, array(intval($toUids)));
            }

        }
        return $this;
    }

    /**
     * 设置消息内容
     *
     * @param array|string $msgBody 消息内容
     * @return Hkzb_Ds_Fudao_Messager
     */
    public function setMsgBody($msgBody)
    {
        if (empty($msgBody)) {
            $this->msgBody = '';
        } else if (is_array($msgBody)) {
            //$this->msgBody = json_encode($msgBody);
            $this->msgBody = $msgBody;
        } else {
            $this->msgBody = strval($msgBody);
        }
        return $this;
    }

    /**
     * 设置班级id，用于获取班级中学生uid
     * 【注意】指定班级的优先级大于指定章节
     *
     * @param integer $classIds 班级id
     * @return Hkzb_Ds_Fudao_Messager
     */
    public function setClassIds($classIds)
    {
        if (!empty($classIds)) {

            if (!is_array($this->classIds)) {
                $this->classIds = array();
            }

            if (is_array($classIds)) {
                $this->classIds = array_merge($this->classIds, $classIds);
            } else if (is_int($classIds)) {
                $this->classIds = array_merge($this->classIds, array(intval($classIds)));
            }

        }
        return $this;
    }

    /**
     * 设置章节id
     * 【注意】指定班级的优先级大于指定章节
     * @param integer $lessonId
     * @return Hkzb_Ds_Fudao_Messager
     */
    public function setLessonId($lessonId)
    {
        $lessonId = intval($lessonId);
        if ($lessonId > 0) {
            $this->lessonId = intval($lessonId);
        }
        return $this;
    }

    /**
     * 设置班级id，用于辅导端区分班级消息
     * 【注意】辅导端区分班级消息
     * @param integer $classIdForAssistant 用于辅导端区分班级用的班级id
     * @return Hkzb_Ds_Fudao_Messager
     */
    public function setClassIdForAssistant($classIdForAssistant)
    {
        $classIdForAssistant = intval($classIdForAssistant);
        if ($classIdForAssistant > 0) {
            $this->classIdForAssistant = $classIdForAssistant;
        }
        return $this;
    }

    /**
     * 设置是否需要尝试降级处理
     *
     * @param boolean $isNeedDegrade 是否需要开启降级机制
     * @return Hkzb_Ds_Fudao_Messager
     */
    public function setIsNeedDegrade($isNeedDegrade)
    {
        $this->isNeedDegrade = $isNeedDegrade ? true : false;
        return $this;
    }

    /**
     * 设置固定降级比率 （设置此参数将绕过降级工具的设置）
     *
     * @param integer $fixDegradeRate 固定降级比率
     * @return Hkzb_Ds_Fudao_Messager
     */
    public function setFixDegradeRate($fixDegradeRate)
    {
        $this->fixDegradeRate = intval($fixDegradeRate);
        return $this;
    }


    /**
     * 设置消息的msgId
     * @param $msgId
     * @return Hkzb_Ds_Fudao_Messager
     */
    public function setMsgId($msgId)
    {
        $this->msgId = $msgId;
        return $this;
    }

    /**
     * 设置最终的接收人uid列表
     */
    private function setFinalUids()
    {
        //已经明确了uid，则按照指定的uid发送
        if (!empty($this->toUids)) {
            return;
        }

        //已经明确了章节+班级，则按照指定的班级中的学生发送
        if (!empty($this->classIds) && $this->lessonId > 0) {
            $this->initToUidsByClassIds($this->classIds);
            return;
        }

        //按照章节发送
        $this->initToUidsByLessonId();
        
        //加入按章节发送接受者日志便于跟踪
        $uidCnt = count($this->toUids);
        Bd_Log::addNotice('receiver_cnt_'.$this->msgId, $uidCnt);
        if (count($this->toUids) < 300) {
            Bd_Log::addNotice('receiver_list_'.$this->msgId, json_encode($this->toUids));
        } 
    }

    /**
     * 根据班级id列表组装接收人uid列表
     *
     * @param array $classIds 班级id列表
     */
    private function initToUidsByClassIds($classIds)
    {
        $objLesson = new Hkzb_Ds_Fudao_Lesson();

        foreach ($classIds as $classId) {
            $studentUidList = $objLesson->getStudentUidList($this->lessonId, $classId, 1);
            if (empty($studentUidList)) {
                Bd_Log::warning("double write failed, get class uid list failed, lesson_id[" . $this->lessonId . "], class_id[" . $classId . "]");
                continue;
            }
            foreach ($studentUidList as $uid) {
                if ($this->fromUid == $uid) {
                    continue;
                }
                $this->toUids[] = intval($uid);
            }
        }
    }

    /**
     * 根据章节id列表组装接收人uid列表
     */
    private function initToUidsByLessonId()
    {
        if ($this->lessonId <= 0) {
            return;
        }
        $lessonInfo = (new Hkzb_Ds_Fudao_Lesson())->getLessonInfo($this->lessonId, array('courseId'));
        $courseId = intval($lessonInfo['courseId']);
        $subCourseId = $courseId; //子课id 从teacherCourse查询老师列表时需要

        $courseInfo = (new Hkzb_Ds_Fudao_Course())->getCourseInfo($courseId);
        if (empty($courseInfo)) {
            Bd_Log::warning("Error:[get course info error], Detail:[courseId:$courseId]");
            $this->toUids = array();
            return;
        }

        if (intval($courseInfo['pack']) === Hkzb_Ds_Fudao_Course::PACK_YESD) {
            //子课，需要获取打包课id
            $courseId = intval($courseInfo['extData']['packId'][0]);
        }

        if ($courseId <= 0) {
            Bd_Log::warning("Error:[get lesson info error], Detail:[lessonId:$this->lessonId]");
            $this->toUids = array();
            return;
        }

        $objMemcached = Hk_Service_Memcached::getInstance("zhiboke");
        $stuUidListCacheKey = 'zhiboke_ds_messager_getStuUidList_' . $this->lessonId;
        $stuUidListStr = $objMemcached->get($stuUidListCacheKey);

        //获取购课时的版本
        $stuListCourseVer = Hkzb_Util_Fudao_UserListVersion::getVersion($courseId);

        $studentCourseList = false;

        if (!empty($stuUidListStr)) {
            Hk_Util_Log::incrKey("zhiboke_ds_messager_getStuUidList_hit", 1);
            $stuUidListMap = json_decode(utf8_encode($stuUidListStr), true);

            if (is_array($stuUidListMap) && intval($stuUidListMap['v']) === $stuListCourseVer
                && is_array($stuUidListMap['l']) && !empty($stuUidListMap['l'])
            ) {
                $studentCourseList = $stuUidListMap['l'];
            }
        }

        //没有命中缓存，则从db中查询
        if (empty($studentCourseList)) {
            //获取学生信息，并组装
            $objStudentCourse = new Hkzb_Ds_Fudao_StudentCourse();
            $studentCourseList = $objStudentCourse->getStudentList($courseId, 0, array('studentUid'), 0, NULL);

            if (!is_array($studentCourseList) || empty($studentCourseList)) {
                Bd_Log::warning("Error:[student list is empty], Detail:[courseId:$courseId]");
                return;
            }

            foreach ($studentCourseList as $studentCourse) {
                $this->toUids[] = intval($studentCourse['studentUid']);
            }
            $this->toUids = array_merge($this->toUids, $this->getTeacherUidList($subCourseId));

            $objMemcached->set($stuUidListCacheKey, json_encode(array('v' => $stuListCourseVer, 'l' => $this->toUids)), 3600);

        } else {
            $this->toUids = $studentCourseList;
        }

        //高中实验课特殊逻辑处理
        if (Hkzb_Const_FudaoMsgSignal::NEW_MSG == $this->signalNo) {
            $repeatKey = false;
            foreach($this->toUids as $stuUidKey => $stuUid){
                if($stuUid && $stuUid == $this->fromUid){
                    $repeatKey = $stuUidKey;
                    break;
                }
            }
            if($repeatKey){
                unset($this->toUids[$repeatKey]);
            }

        }

    }

    /**
     * 查询指定课程下的老师uid列表
     *
     * @param integer $courseId 指定课程id
     * @return array|mixed
     */
    private function getTeacherUidList($courseId)
    {
        $teacherUidList = array();

        try {
            $objTeacherCourse = new Hkzb_Ds_Fudao_TeacherCourse();
            $teacherCourseList = $objTeacherCourse->getTeacherList($courseId, array('teacherUid', 'classId'), 0, 5000);

            //如果老师没有找到，则不影响学生收到消息
            if (empty($teacherCourseList)) {
                Bd_Log::warning("Error:[getTeacherList error], Detail:[courseId:$courseId]");
                return array();
            }

            foreach ($teacherCourseList as $teacherCourse) {
                if (intval($teacherCourse['classId']) <= 0) {
                    continue;
                }
                if ($this->fromUid == $teacherCourse['teacherUid']) {
                    continue;
                }
                $teacherUidList[] = intval($teacherCourse['teacherUid']);
            }
        } catch (Exception $ex) {
            Bd_Log::warning("Error:[getTeacherList error], Detail:[courseId:$courseId], Msg:[{$ex->getMessage()}]");
        }

        return $teacherUidList;
    }

    /**
     * 设置消息内容
     */
    private function setMessageKv()
    {
        // 辅导专用消息格式，但由于MQS约束，需要将其json_encode后作为mqs的消息内容
        // 【注意】：请不要改动里面的任何key的顺序！！！！顺序！！！！！！！顺序！！！！！！！！！！！
        $this->fudaoMsgKv = array(
            'from_uid' => $this->fromUid,
            'sig_no' => $this->signalNo,
            'data' => $this->msgBody,
            'to_lessonid' => $this->lessonId,
            'to_classid' => $this->classIdForAssistant,
        );

        // 用于日志记录的kv结构 (长连接不需要此结构)
        $this->msgLogKv = array(
            'from_uid' => $this->fromUid,
            'to_uid' => 0,
            'to_group_id' => $this->lessonId,
            'msg_id' => 0,
            'msg_type' => 0,
            'msg_content' => json_encode($this->fudaoMsgKv),
            'msg_time' => intval(microtime(true) * 1000),
            'old_protocol' => 1, //旧版协议
        );

        $this->mqsMessager->setMsgData($this->fudaoMsgKv);

    }


    /**
     * 设置MQS参数选项
     */
    private function setMQSOptions()
    {
        $this->mqsMessager->setMsgExpireTime(self::MSG_DATA_EXPIRE);

        if (in_array($this->signalNo, $this->deliveryTypeList)) {
            $this->mqsMessager->setMsgCleanType(Hkzb_Service_MQSMessager::MSG_CLEAN_TYPE_VOLATILE);
            $this->mqsMessager->setMsgDeliveryType(Hkzb_Service_MQSMessager::MSG_DELIVERY_TYPE_ONLINE);
        } else {
            $this->mqsMessager->setMsgCleanType(Hkzb_Service_MQSMessager::MSG_CLEAN_TYPE_STABLE);
            $this->mqsMessager->setMsgDeliveryType(Hkzb_Service_MQSMessager::MSG_DELIVERY_TYPE_ALL);
        }
    }

    /**
     * 设置 msg_delivery_type 参数
     * @param $intSignalNo
     * @return $this
     */
    public function setDeliveryTypeList($intSignalNo) {
        $this->deliveryTypeList[] = $intSignalNo;
        return $this;
    }


    /**
     * 获取降级比率
     *
     * @return integer
     */
    private function getDegradedRate()
    {

        if (!$this->isNeedDegrade) {
            return 0;
        }

        if (!$this->lessonId || $this->lessonId <= 0) {
            return 0;
        }

        if ($this->fixDegradeRate >= 0) {
            return $this->fixDegradeRate;
        }

        $rate = 0;

        try {
            $degradeSwitchKey = 'FUDAO_LESSON_DEGRADE_' . $this->lessonId;
            $redisConf = Bd_Conf::getConf("/hk/redis/fudao");
            $objCache = new Hk_Service_Redis($redisConf['service']);
            $rate = intval($objCache->get($degradeSwitchKey));
        } catch (Exception $ex) {

        }

        return $rate;

    }


    /**
     * 发送消息成功后的操作
     *
     * 1. H5直播将第一个班级的部分消息写入到redis队列
     *
     * @param integer $msgId 消息id
     */
    private function afterSent($msgId)
    {
        try {
            //非聊天的信令直接存储
            if (Hkzb_Const_FudaoMsgSignal::NEW_MSG != $this->signalNo) {
                Hkzb_Ds_Fudao_ShortMessage::pushShortMessage($this->lessonId, $this->signalNo, $msgId, $this->msgLogKv);
                return;
            }

            if (empty($this->classIds)) {
                return;
            }
            $classId = intval($this->classIds[0]);

            //聊天的信令，将聊天内容存放在有过期时间的redis队列中
            $msgListCacheKey = sprintf(Hkzb_Ds_Fudao_ShortMessage::MSG_LIST_CACHE_KEY, $classId);
            //$objCache = Hk_Service_RedisClient::getInstance("zhibo");
//            $objCacheV2 =  Liveservice_Util_RedisService::getZhiboInstance();
            $objCacheV2 =  Liveservice_Util_StoredService::getZhiboInstance();
            $objCacheV2->lpush($msgListCacheKey, json_encode($this->fudaoMsgKv));
            $objCacheV2->expire($msgListCacheKey, Hkzb_Ds_Fudao_ShortMessage::MSG_LIST_CACHED_EXPIRE);
            $listLength = $objCacheV2->llen($msgListCacheKey);

            if ($listLength > Hkzb_Ds_Fudao_ShortMessage::MSG_LIST_CACHE_MAX_LEN) {
                $objCacheV2->rpop($msgListCacheKey);
            }

        } catch (Exception $ex) {

        }

    }


    /**
     * 发送之后的清理工作，用于重置参数，便于二次发送
     */
    private function clearOpts()
    {
        $this->toUids = array();
        $this->classIdForAssistant = 0;
        $this->classIds = array();
    }

}
