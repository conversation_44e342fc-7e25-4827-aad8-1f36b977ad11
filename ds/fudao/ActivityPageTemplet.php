<?php
/**
 * Created by PhpStorm.
 * User: zuoyebang
 * Date: 2018/5/8
 * Time: 17:40
 */
class Hkzb_Ds_Fudao_ActivityPageTemplet{

    private $_obj;
    const ALL_FIELDS = '';


    public function __construct()
    {
        $this->_obj = new Hkzb_Dao_Fudao_ActivityPageTemplet();
    }

    /**
     * 保存模板信息
     *
     */
    public function saveActivityPageTemplet($arrParams){
        //对数据做初步的验证
        if(empty($arrParams['actId'] )) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR,'',[
                'actId' => $arrParams['actId']
            ]);
        }

        //插入
        $arrInsert = [
            'actId'      => intval($arrParams['actId']),
            'modelInfo'  => strval($arrParams['modelInfo'])
        ];

        $res = $this->_obj->insertRecords($arrInsert);

        return $res;
    }

    //获取模板信息
    public function getActivityPageTemplet($arrInput){

        if(empty($arrInput)){
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR,'',[
               'actId' => $arrInput['actId']
            ]);
        }

        $arrConds = [
            'actId' => intval($arrInput['actId'])
        ];

        $arrAppends = array(
            "order by reorder asc",
        );
        //$arrFields = Hkzb_Dao_Fudao_ActivityPageTemplet::$arrFields;
        $arrFields = ['model_info'];
        $res = $this->_obj->getListByConds($arrConds,$arrFields,null,$arrAppends);
        return $res;
    }

    //判断条数
    public function getActivityPageTempletCnt($arrInput){

        if(empty($arrInput)){
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR,'',[
                'actId' => $arrInput
            ]);
        }

        $arrConds = [
            'actId' => intval($arrInput)
        ];

        $arrFields = Hkzb_Dao_Fudao_ActivityPageTemplet::$arrFields;
        $res = $this->_obj->getCntByConds($arrConds,$arrFields);
        return $res;
    }

    //删除活动模板信息信息
    public function delActivityPageTemplet($catId){
        if(empty($catId)){
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR,'',[
                'actId' => $catId
            ]);
        }

        $arrConds = [
            'actId' => intval($catId)
        ];
        //$arrFields = Hkzb_Dao_Fudao_ActivityPageTemplet::$arrFields;
        $res = $this->_obj->deleteByConds($arrConds);
        return $res;
    }



}