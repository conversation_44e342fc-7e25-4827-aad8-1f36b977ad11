<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file TeacherContract.php
 * <AUTHOR>
 * @date 2016/08/17 13:26:46
 * @brief 老师合同表
 *  
 **/

class Hkzb_Ds_Fudao_TeacherContract {

    const STATUS_ON_DUE   = 0; //合同履行中
    const STATUS_OVER_DUE = 1; //合同已过期
    const STATUS_FORZEN   = 2; //合同已冻结
    const CONTRACT_NO_DUE = 5; //待签约
    static $STATUS_ARRAY = array(
        self::STATUS_ON_DUE   => '合同履行中',
        self::STATUS_OVER_DUE => '合同已过期',
        self::STATUS_FORZEN   => '合同已冻结',
        self::CONTRACT_NO_DUE => '待签约',
    );
    const DELETED_OK      = 0; //正常
    const DELETED_DELETED = 1; //已删除
    static $DELETED_ARRAY = array(
        self::DELETED_OK      => '正常',
        self::DELETED_DELETED => '已删除',
    );

    const ALL_FIELDS = 'id,teacherUid,name,idCard,phone,tel,bankCard,bankAccount,dueTime,rate,status,deleted,operatorUid,operator,startTime,stopTime,createTime,updateTime,extData';

    private $objDaoTeacherContract;

    /**
     * 构造函数
     *
     */
    public function __construct() {
        $this->objDaoTeacherContract = new Hkzb_Dao_Fudao_TeacherContract();
    }

    /**
     * 新增老师合同记录
     *
     * @param  mix  $arrParams 老师合同属性
     * @return bool true/false
     */
    public function addTeacherContract($arrParams) {

        $arrFields = array(
            'teacherUid'    => isset($arrParams['teacherUid']) ? intval($arrParams['teacherUid']) : 0,
            'name'          => isset($arrParams['name']) ? strval($arrParams['name']) : '',
            'idCard'        => isset($arrParams['idCard']) ? strval($arrParams['idCard']) : '',
            'phone'         => isset($arrParams['phone']) ? strval($arrParams['phone']) : '',
            'tel'           => isset($arrParams['tel']) ? strval($arrParams['tel']) : '',
            'bankCard'      => isset($arrParams['bankCard']) ? strval($arrParams['bankCard']) : '',
            'bankAccount'   => isset($arrParams['bankAccount']) ? strval($arrParams['bankAccount']) : '',
            'dueTime'       => isset($arrParams['dueTime']) ? intval($arrParams['dueTime']) : 0,
            'rate'          => isset($arrParams['rate']) ? intval($arrParams['rate']) : 0,
            'status'        => self::CONTRACT_NO_DUE,
            'deleted'       => self::DELETED_OK,
            'operatorUid'   => isset($arrParams['operatorUid']) ? intval($arrParams['operatorUid']) : 0,
            'operator'      => isset($arrParams['operator']) ? strval($arrParams['operator']) : '',
            'startTime'     => 0,
            'stopTime'      => 0,
            'createTime'    => time(),
            'updateTime'    => time(),
            'extData'       => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '',
        );

        $ret = $this->objDaoTeacherContract->insertRecords($arrFields);
        
        return $ret;
    }

    /**
     * 检查老师是否有正在履行的合同
     *
     * @param  int  $uid  uid
     * @param  mix  $arrFields 指定属性
     * @return mix
     */
    public function checkTeacherValid($teacherUid, $contract = self::STATUS_ON_DUE){
        if($teacherUid <= 0){
            Bd_Log::warning("Error:[param error], Detail:[teacherUid:$teacherUid ]");
            return false;
        }
        $arrConds = array(
            'teacherUid' => $teacherUid,
            'deleted'    => self::DELETED_OK,
            'status'     => $contract,
        );

        $ret = $this->objDaoTeacherContract->getRecordByConds($arrConds, array('id'));
        return $ret;
    }

    /**
     * 更新指定老师合同中的信息
     *
     * @param  int  $uid  uid
     * @param  mix  $arrFields 指定属性
     * @return mix
     */
    public function updateTeacherContractById($id, $arrParams){
        if($id <= 0){
            Bd_Log::warning("Error:[param error], Detail:[id:$id ]");
            return false;
        }
        $arrConds = array(
            'id'      => $id,
            'deleted' => self::DELETED_OK,
        );

        $arrFields = array();
        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach($arrParams as $key => $value) {
            if(!in_array($key, $arrAllFields)) {
                continue;
            }
            $arrFields[$key] = $value;
        }

        //ext由ps补全，json处理在ds
        if(isset($arrFields['extData'])) {
            $arrFields['extData'] = json_encode($arrFields['extData']);
        }

        $ret = $this->objDaoTeacherContract->updateByConds($arrConds, $arrFields);
        return $ret;
    }

    /**
     * 获取老师合同信息
     *
     * @param  int  $uid  uid
     * @param  mix  $arrFields 指定属性
     * @return mix
     */
    public function getTeacherContractByUid($teacherUid, $arrFields = array()) {
        if(intval($teacherUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[teacherUid:$teacherUid ]");
            return false;
        }

        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'teacherUid' => $teacherUid,
            'deleted'    => self::DELETED_OK,
        );

        $ret = $this->objDaoTeacherContract->getListByConds($arrConds, $arrFields);

        return $ret;
    }


    /**
     * 获取老师合同信息
     *
     * @param  int  $uid  uid
     * @param  mix  $arrFields 指定属性
     * @return mix
     */
    public function getTeacherContractByUidArr($arrTeacherUid, $arrFields = array()) {
        if(empty($arrTeacherUid)) {
            Bd_Log::warning("Error:[param error], Detail:[arrTeacherUid:json_encode($arrTeacherUid) ]");
            return false;
        }

        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'deleted'    => self::DELETED_OK,
        );
        $arrConds[] = 'teacher_uid in (' . implode(',',$arrTeacherUid) . ')';

        $ret = $this->objDaoTeacherContract->getListByConds($arrConds, $arrFields);

        return $ret;
    }


    /**
     * 获取签约状态的合同列表
     *
     * @param  int  $uid  uid
     * @param  mix  $arrFields 指定属性
     * @return mix
     */
    public function getTeacherContractByUidStatus($teacherUid, $contract, $arrFields = array()) {
        if(intval($teacherUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[teacherUid:$teacherUid ]");
            return false;
        }

        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'teacherUid' => $teacherUid,
            'deleted'    => self::DELETED_OK,
            'status'     => $contract,
        );

        $ret = $this->objDaoTeacherContract->getListByConds($arrConds, $arrFields);

        return $ret;
    }
}
