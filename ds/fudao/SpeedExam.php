<?php
/**
 * Created by PhpStorm.
 * User: zhangxiao
 * Date: 17/4/21
 * Time: 下午8:02
 */
class Hkzb_Ds_Fudao_SpeedExam{
    //知识点属性
    const ALL_FIELDS = 'examId,updateTime,createTime,extData,exerciseList';

    private $objDaoSpeedExam;
    public static $FRE_KEY = 'Fudao_SpeedExam_';
    /**
     * 构造函数
     *
     */
    public function __construct() {
        $this->objDaoSpeedExam = new Hkzb_Dao_Fudao_SpeedExam();
    }

    /**
     * 获取试题信息
     * @param $examId
     * @return array|bool|false|mixed
     * exerciselist 结构 array(
     *          0 => array(
     *              'qid'      => 1,
     *              'content' => '15\div8=\frac{ a }{ b }'
     *              'answer'  => '[1,2]'
     *          ),
     *          ...
     *      )
     */
    public function getExamInfo($examId){
        if($examId <= 0){
            Bd_Log::warning("Error:[param error], Detail:[examId:$examId]");
            return false;
        }
        //缓存设置
        //读缓存
        $objMemcached = Hk_Service_Memcached::getInstance('zhiboke');;
        $cacheKey = self::$FRE_KEY . $examId;
        $cacheValue = $objMemcached->get($cacheKey);
        if (!empty($cacheValue)) {
            $ret = json_decode($cacheValue, true);
            return $ret;
        }

        $arrFields = explode(',', self::ALL_FIELDS);
        $arrConds = array(
            'examId' => $examId,
        );

        $ret = $this->objDaoSpeedExam->getRecordByConds($arrConds, $arrFields);
        if(!empty($ret)){
            $questionList = $ret['exerciseList'];
            $newQuestionList = array();//后端自用，带questionid索引的数据
            foreach($questionList as $questionInfo){
                $newQuestionList[$questionInfo['qid']] = $questionInfo;
            }
            $ret['newExerciseList'] = $newQuestionList;
            //写缓存
            $cacheValue = json_encode($ret);
            $objMemcached->set($cacheKey, $cacheValue, 3600);
        }
        return $ret;
    }

}