<?php
/***************************************************************************
 *
 * Copyright (c) 2018 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file: ExerciseNote.php
 * @author: huanghe <<EMAIL>>
 * @date: 2017/10/9 下午7:53
 * @brief: 习题本DS
 */
class Hkzb_Ds_Fudao_ExerciseNote
{

    const ALL_FIELDS = 'id,studentUid,courseId,courseName,subjectId,courseStartTime,lessonId,lessonName,tid,questionType,noteType,answer,answerShow,comment,subjectPoint,deleted,createTime,updateTime,extData,isOld';


    //笔记类型 -- 主动收藏
    const NOTE_TYPE_FAV = 0;
    //笔记类型 -- 错题自动收藏
    const NOTE_TYPE_WRONG = 1;

    //删除状态 -- 未删除
    const DEL_STATUS_OK = 0;
    //删除状态 -- 已删除
    const DEL_STATUS_DELETED = 1;


    //题类型 -- 旧类型
    const IS_OLD_EXERCISE = 1;
    //题类型 -- 新类型
    const NO_OLD_EXERCISE = 0;

    //已加入错题本tips 文案
    const NOTE_TIPS_NOTICE_MSG = "错题已加入习题本";

    //删除类型描述
    static $DEL_STATUS_DESC = array(
        self::DEL_STATUS_OK => '未删除',
        self::DEL_STATUS_DELETED => '已删除',
    );

    //笔记类型标签描述
    public static $NOTE_TYPE_DESC = array(
        self::NOTE_TYPE_FAV => '收藏',
        self::NOTE_TYPE_WRONG => '错题',
    );

    public function __construct()
    {
        $this->objDsFudaoExamPaper = new Hkzb_Ds_Fudao_Exam_ExamPaper();
        $this->objDsHomework = new Hkzb_Ds_Homework_Homework();
        $this->objDsLesson = new Hkzb_Ds_Fudao_Lesson();
    }

    /**
     * 通过学生科目id获取习题本列表
     *
     * @param integer $studentUid 学生uid
     * @param integer $subjectId 科目id
     * @param integer $expandCourseId 展开的课程id
     * @param array|NULL $fileds 获取的字段列表
     * @param integer $page 当前页数
     * @param integer $pageNum 每页个数
     * @return array
     */
    public function getListByStudentSubjectId($studentUid, $subjectId, $expandCourseId = 0, $offset = 0, $limit = 9000)
    {
        if ($subjectId <= 0 || $studentUid <= 0) {
            return array();
        }
        $fileds = array(
            'id', 'courseId', 'lessonId', 'courseName', 'subjectId', 'tid', 'courseStartTime', 'noteType', 'updateTime', 'questionType', 'isOld'
        );

        //转换后的subjectId, 存储上支持多学科
        $conventSubjectId = 0;
        $conventSubjectId = $subjectId; //如果想要支持多学科，值应该这样：|= 1 << $subjectId - 1;

        $conds = array(
            'studentUid' => $studentUid,
            //查询支持多学科开放时，此处的查询需要改成：array('((subject_id >> ' . $subjectId . ' - 1) & 1)' =>1,)
            'subjectId' => $conventSubjectId,
            'deleted' => self::DEL_STATUS_OK,
//            'question <> ""',
        );

        $arrAppends = array(
            "order by update_time desc",
            "limit $offset, $limit",
        );

        $exerciseNoteDao = new Hkzb_Dao_Fudao_ExerciseNote();
        $noteList = $exerciseNoteDao->getListByConds($studentUid, $conds, $fileds, NULL, $arrAppends);

        if (!is_array($noteList)) {
            return array();
        }
        $courseNoteList = array();
        $courseIdTimeMap = array();

        $newNoteList = array();
        //首次遍历，获取课程排序
        foreach ($noteList as $noteItem) {
            $noteCourseId = intval($noteItem['courseId']);
            $courseStartTime = intval($noteItem['courseStartTime']);
            $courseIdTimeMap[$noteCourseId] = $courseStartTime;
            if (!isset($newNoteList[$noteCourseId])) {
                $newNoteList[$noteCourseId] = array();
            }
            $newNoteList[$noteCourseId][] = $noteItem;
        }
        unset($noteList);

        arsort($courseIdTimeMap, SORT_NUMERIC);
        $allCourseIds = array_keys($courseIdTimeMap);

        //默认展开第一个course的题目列表
        if ($expandCourseId <= 0) {
            $expandCourseId = intval($allCourseIds[0]);
        }

        //避免一次请求所有的题数据
        $arrQuestionList = array();
        if ($expandCourseId > 0) {
            $conds['courseId'] = $expandCourseId;
            $list = $exerciseNoteDao->getListByConds($studentUid, $conds, $fileds, NULL, $arrAppends);
            $arrTids = array();
            if (!empty($list) && is_array($list)) {
                foreach ($list as $note) {
                    if ($note['isOld'] == 0) {
                        $arrTids[] = $note['tid'];
                    }
                }
            }
            if (!empty($arrTids)) {
                $arrQuestionList = Hkzb_Util_Fudao_GetQuestionInfo::getQuestionInfoByTids($arrTids);
            }
        }

        //遍历笔记列表，排序组合数据并展示
        foreach ($allCourseIds as $noteCourseId) {
            $noteItemList = $newNoteList[$noteCourseId];
            foreach ($noteItemList as $noteDetail) {
                $noteId = intval($noteDetail['id']);
                $noteType = intval($noteDetail['noteType']);
                $questionType = intval($noteDetail['questionType']);

                if (!isset($courseNoteList[$noteCourseId])) {
                    $courseNoteList[$noteCourseId] = array(
                        'courseId' => $noteCourseId,
                        'courseName' => $noteDetail['courseName']);
                }
                if (!isset($courseNoteList[$noteCourseId]['noteList'])) {
                    $courseNoteList[$noteCourseId]['noteList'] = array();
                }
                if (!isset($courseNoteList[$noteCourseId]['noteNum'])) {
                    $courseNoteList[$noteCourseId]['noteNum'] = 0;
                }
                //展开的课程和当前课程相同，则累加笔记详情
                if ($expandCourseId === $noteCourseId) {
                    $tid = $noteDetail['tid'];
                    //跟读题不展示
                    if ($questionType == Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_AFTER_LESSON_READ) {
                        continue;
                    }
                    //支持带小题的填空题
                    if ($arrQuestionList[$tid]['category'] == 31 || $arrQuestionList[$tid]['category'] > 32) {
                        continue;
                    }
                    $question = $arrQuestionList[$tid]['question']['title'];
                    $latexQuestion = $arrQuestionList[$tid]['latexQuestion']['title'];
                    if ($arrQuestionList[$tid]['category'] == Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_READING
                        || $arrQuestionList[$tid]['category'] == Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_BLANKS
                        || $arrQuestionList[$tid]['category'] == Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_MULTI_FILL_BLANK) {
                        $question = $arrQuestionList[$tid]['question']['content'];
                        $latexQuestion = $arrQuestionList[$tid]['latexQuestion']['content'];
                    }

                    if ($noteDetail['isOld'] == 1) {
                        //获取选项卡题干和选项数据
                        $objDsExercise = new Hkzb_Ds_Fudao_Exercise();
                        $arrExerciseInfo = $objDsExercise->getExerciseInfo( $tid );
                        if (!empty($arrExerciseInfo['question']['pic']) && substr($arrExerciseInfo['question']['pic'], 0, 5) == 'empty') {
                            continue;
                        }
                        $question = Hkzb_Util_FuDao::getFileUrl($arrExerciseInfo['question']['pic']);
                    }
                    if (empty($question)) {
                        //Bd_Log::warning("Error:[get question info error],Detail:[tid:" . $tid . "]");
                        continue;
                    }

                    $courseNoteList[$noteCourseId]['noteList'][] = array(
                        'noteId' => $noteId,
                        'question' => $question,
                        'latexQuestion' => $latexQuestion,
                        'questionType' => $noteDetail['questionType'],
                        'questionTypeTxt' => isset(Hkzb_Ds_Homework_Homework::$SUBJECT_TYPE_LIST[$noteDetail['questionType']]) ? Hkzb_Ds_Homework_Homework::$SUBJECT_TYPE_LIST[$noteDetail['questionType']] : '互动题',
                        'subQuestionNum' => in_array($questionType, array(Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_BLANKS, Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_READING))
                            ? count($arrQuestionList[$tid]['question']['questionList']) : 0,
                        'noteType' => $noteType,
                        'noteTypeTxt' => self::$NOTE_TYPE_DESC[$noteType],
                        'time' => intval($noteDetail['updateTime'] . '000'),
                        'timeTxt' => $this->getTimeFormat($noteDetail['updateTime']),
                        'lessonId' => $noteDetail['lessonId'],
                        'isOld' => $noteDetail['isOld'],
                    );
                }

                ++$courseNoteList[$noteCourseId]['noteNum'];
            }
        }

        return $courseNoteList;
    }

    /**
     * 通过学生课程id获取习题本列表
     *
     * @param integer $studentUid 学生uid
     * @param array $courseIds 课程id
     * @return array
     */
    public function getListByStudentCourseIds($studentUid, $courseIds)
    {
        if ($studentUid <= 0 || !is_array($courseIds) || empty($courseIds)) {
            return array();
        }

        $fileds = array(
            'id', 'courseId', 'lessonId', 'noteType', 'tid', 'updateTime','questionType','isOld'
        );

        $conds = array(
            'studentUid' => $studentUid,
            'deleted' => self::DEL_STATUS_OK,
//            'question <> ""',
        );

        if (count($courseIds) == 1) {
            $conds['courseId'] = intval($courseIds[0]);
        } else {
            $conds[] = 'course_id in (' . implode(',', $courseIds) . ')';
        }

        $arrAppends = array(
            "order by update_time desc",
        );

        $exerciseNoteDao = new Hkzb_Dao_Fudao_ExerciseNote();
        $noteList = $exerciseNoteDao->getListByConds($studentUid, $conds, $fileds, NULL, $arrAppends);

        if (!is_array($noteList)) {
            return array();
        }

        //add by renkaiyu start 题干和选项的数据从题库获取  20190517
        $arrTids = array();
        foreach ($noteList as $item) {
            if ($item['isOld'] == 0) {
                $arrTids[] = $item['tid'];
            }
        }
        $arrQuestionList = array();
        if (!empty($arrTids)) {
            $arrQuestionList = Hkzb_Util_Fudao_GetQuestionInfo::getQuestionInfoByTids($arrTids);
        }

        $courseNoteList = array();
        //遍历笔记列表，排序并展示
        foreach ($noteList as $noteDetail) {
            $noteId = intval($noteDetail['id']);
            $noteType = intval($noteDetail['noteType']);
            $courseId = intval($noteDetail['courseId']);
            $questionType = intval($noteDetail['questionType']);
            $tid = $noteDetail['tid'];

            if (!isset($courseNoteList[$courseId])) {
                $courseNoteList[$courseId] = array();
            }

            //跟读题不展示
            if ($questionType == Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_AFTER_LESSON_READ) {
                continue;
            }
            //支持带小题的填空题
            if ($arrQuestionList[$tid]['category'] == 31 || $arrQuestionList[$tid]['category'] > 32) {
                continue;
            }
            $question = $arrQuestionList[$tid]['question']['title'];
            $latexQuestion = $arrQuestionList[$tid]['latexQuestion']['title'];
            if ($arrQuestionList[$tid]['category'] == Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_READING
                || $arrQuestionList[$tid]['category'] == Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_BLANKS
                || $arrQuestionList[$tid]['category'] == Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_MULTI_FILL_BLANK) {
                $question = $arrQuestionList[$tid]['question']['content'];
                $latexQuestion = $arrQuestionList[$tid]['latexQuestion']['content'];
            }

            if ($noteDetail['isOld'] == 1) {
                //获取选项卡题干和选项数据
                $objDsExercise = new Hkzb_Ds_Fudao_Exercise();
                $arrExerciseInfo = $objDsExercise->getExerciseInfo( $tid );
                if (!empty($arrExerciseInfo['question']['pic']) && substr($arrExerciseInfo['question']['pic'], 0, 5) == 'empty') {
                    continue;
                }
                $question = Hkzb_Util_FuDao::getFileUrl($arrExerciseInfo['question']['pic']);
            }
            if (empty($question)) {
                Bd_Log::warning("Error:[get question info error],Detail:[tid:" . $tid . "]");
                continue;
            }

            $courseNoteList[$courseId][] = array(
                'noteId' => $noteId,
                'question' => $question,
                'latexQuestion' => $latexQuestion,
                'questionType' => $noteDetail['questionType'],
                'questionTypeTxt' => isset(Hkzb_Ds_Homework_Homework::$SUBJECT_TYPE_LIST[$noteDetail['questionType']]) ? Hkzb_Ds_Homework_Homework::$SUBJECT_TYPE_LIST[$noteDetail['questionType']] : '互动题',
                'subQuestionNum' => in_array($questionType, array(Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_BLANKS, Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_READING))
                    ? count($arrQuestionList[$tid]['question']['questionList']) : 0,
                'noteType' => $noteType,
                'noteTypeTxt' => self::$NOTE_TYPE_DESC[$noteType],
                'time' => intval($noteDetail['updateTime'] . '000'),
                'timeTxt' => $this->getTimeFormat($noteDetail['updateTime']),
                'lessonId' => $noteDetail['lessonId'],
                'isOld' => $noteDetail['isOld'],
            );
        }

        return $courseNoteList;

    }

    /**
     * 时间格式化
     *
     * @param integer $seconds 时间戳
     * @return false|string
     */
    private function getTimeFormat($seconds)
    {
        $seconds = intval($seconds);
        $formatDate = date("Ymd", $seconds);

        if ($formatDate == date("Ymd")) {
            return date("H:i", $seconds);
        } else if ($formatDate == date("Ymd", strtotime("-1 day"))) {
            return "昨天";
        } else {
            return date("Y-m-d", $seconds);
        }
    }

    /**
     * @brief 删除错题
     * @param $arrOrigFields
     * @return bool|int
     */
    public function deleteExerciseNotebyTid($uid, $lessonId, $tids,$courseId=0)
    {
        if ($lessonId > 0 && $courseId <= 0) {
            // //获取课程信息
            $courseInfo = $this->objDsLesson->getPackCourseIdByLesson($lessonId);
            if(empty($courseInfo)) {
                 Bd_Log::warning("Insert ExerciseNote DB error! courseInfo empty detail:json_encode($courseInfo)");
                 return false;
            }
            $courseId = $courseInfo['courseId'];
        }

        $tids = $this->_tidListFormate($tids);
        $conds = array(
            'studentUid' => $uid,
            'courseId' => $courseId,
            "tid in ($tids)",
        );
        $arrFields['deleted'] = self::DEL_STATUS_DELETED;
        $arrFields['updateTime'] = time();
        $exerciseNoteDao = new Hkzb_Dao_Fudao_ExerciseNote();
        $ret = $exerciseNoteDao->updateByConds($uid, $conds, $arrFields);
        if ($ret === false) {
            Bd_Log::warning("update ExerciseNote DB error!");
            return false;
        }

        return true;
    }

    /**
     * @brief 删除错题
     * @param $arrOrigFields
     * @return bool|int
     */
    public function deleteExerciseNotebyId($uid, $ids)
    {

        if ($uid <= 0 or empty($ids)) {
            return false;
        }
        $ids = $this->_tidListFormate($ids);
        $conds = array(
            'studentUid' => $uid,
            "id in ($ids)",
        );
        $arrFields['deleted'] = self::DEL_STATUS_DELETED;
        $arrFields['updateTime'] = time();
        $exerciseNoteDao = new Hkzb_Dao_Fudao_ExerciseNote();
        $ret = $exerciseNoteDao->updateByConds($uid, $conds, $arrFields);
        if ($ret === false) {
            Bd_Log::warning("update ExerciseNote DB error!");
            return false;
        }

        return true;
    }

    /**
     * @brief 收藏错题
     * @param $arrOrigFields
     * @return bool|int
     */
    public function colectExerciseNotebyTid($uid, $courseId, $ids,$type)
    {

        if ($uid <= 0 or empty($ids) or $courseId <= 0 ) {
            return false;
        }
        $ids = $this->_tidListFormate($ids);
        $conds = array(
            'studentUid' => $uid,
            'courseId' => $courseId,
            "tid in ($ids)",
        );
        $arrFields['deleted'] = self::DEL_STATUS_OK;
        $arrFields['updateTime'] = time();
        $arrFields['noteType'] = $type;
        $exerciseNoteDao = new Hkzb_Dao_Fudao_ExerciseNote();
        $ret = $exerciseNoteDao->updateByConds($uid, $conds, $arrFields);
        if ($ret === false) {
            Bd_Log::warning("update ExerciseNote DB error!");
            return false;
        }

        return true;
    }

    /**
     * 格式化tid列表
     * @return [array] [description]
     */
    private function _tidListFormate($tids){
        if(is_array($tids)) {
            foreach ($tids as  &$tid) {
                if(strpos($tid,"_")) {
                    $tmpId = explode("_", $tid);
                    $tid = $tmpId[0];
                    unset($tmpId);
                }
            }
         }else {
           if(strpos($tids,"_")) {
                $tmpId = explode("_", $tids);
                $tids = $tmpId[0];
           }
         }
        return $tids;
    }

    /**
     * @brief 添加错题
     * @param $arrOrigFields
     * @return bool|int
     * 若$isOld=1,$tid 代表exerciseId
     */
    public function addExerciseNote($uid, $lessonId, $tid, $type, $examId,$questionType = 'no',$isOld=0,$courseId=0,$callback = 0)
    {
		if($callback == 0){
             $ExerciseNoteStructure = [
                 'studentUid'   => intval($uid),
                 'lessonId'     => intval($lessonId),
				 'tid'          => intval($tid),
                 'type'         => intval($type),
                 'questionType' => $questionType,
                 'isOld'        => intval($isOld),
				 'courseId'     => intval($courseId),
                 'examId'       => intval($examId),
             ];
			 if($isOld){
				 $ExerciseNoteStructure['exerciseId'] = intval($tid);
			 }
             $sendNoteNmq = Liveservice_Util_Nmq::sendCommandByrmq(Zb_Const_Command::COMMAND_QE_823334, $ExerciseNoteStructure);
			 return true;
		}
        if ($lessonId == 0 && $courseId == 0) {
            Bd_Log::warning("Insert ExerciseNote DB error! param error:courseId[$courseId]lessonId[$lessonId]");
            return false;
        }
        if ($lessonId == 0) {
            $objDscourse = new Hkzb_Ds_Fudao_Course();
            $courseInfo = $objDscourse->getCourseInfo($courseId);
            if(empty($courseInfo)) {
                Bd_Log::warning("courseInfo empty , Detail: lessonId[$lessonId] courseId[$courseId]");
                return false;
            }
            if ($courseInfo['pack'] == Hkzb_Ds_Fudao_Advanced_Course::PACK_YESD) {
                $packCourseIdArr = isset($courseInfo['extData']['packId']) ? $courseInfo['extData']['packId'] : array();
                if (empty($packCourseIdArr)) {
                    return false;
                }

                foreach ($packCourseIdArr as $packCourseId) {
                    $courseId = $packCourseId;
                }
                $courseInfo = $objDscourse->getCourseInfo($courseId);
            }
            $lessonName = '';
        }else{
            // //获取课程信息
            $courseInfo = $this->objDsLesson->getPackCourseIdByLesson($lessonId);
            if(empty($courseInfo)) {
                 Bd_Log::warning("Insert ExerciseNote DB error! courseInfo empty detail:json_encode($courseInfo)");
                 return false;
            }
            $courseId = $courseInfo['courseId'];
            $lessonName = $courseInfo['lessonName'];
        }

        //判断是否已添加
        $arr = $this->getExerciseNoteCheck($uid, $courseId, $tid);
        if (!empty($arr)) {
            $res = $this->colectExerciseNotebyTid($uid, $courseId, $tid,$type);
            if ($res === false) {
                Bd_Log::warning("update ExerciseNote DB error!");
                return false;
            }
            return true;
        }

        //如果是老题
        if ($isOld == 1) {
            $objDsExercise = new Hkzb_Ds_Fudao_Exercise();
            $arrExerciseInfo = $objDsExercise->getExerciseInfo( $tid );

            if ( empty( $arrExerciseInfo ) ) {
                Bd_Log::warning("select exercise is empty  exerciseId[$tid]");
                return false;
            }
            if (empty($arrExerciseInfo['question']['options'])) {
                Bd_Log::warning("exercise pic options empty  exerciseId[$tid]");
                return false;
            }
//            $optionsArr = explode(",",$arrExerciseInfo['question']['options']);
//            $options = array();
//            foreach ($optionsArr as $val) {
//                $options[$val] = '';
//            }
//            $answer = array();
//            if(!empty($arrExerciseInfo['answer'])){
//                $answer = explode(',',$arrExerciseInfo['answer']);
//            }
            $arr = array(
                'studentUid' => $uid,
                'courseId' => $courseId,
                'courseName' => $courseInfo['courseName'],
                'subjectId' => $courseInfo['subject'],
                'courseStartTime' => $courseInfo['startTime'],
                'lessonId' => $lessonId,
                'lessonName' => $lessonName,
                'tid' => $tid,
                'questionType' => $arrExerciseInfo['type'] > 0 ? $arrExerciseInfo['type'] : 0,//习题类型
                'noteType' => $type,//0收藏1错题
//                'question' => '',//习题问题
                'answer' => '',//答案
                'answerShow' => '',//展示用的答案
                'comment' => '',//习题解析
 //               'options' => '',
                'subjectPoint' => '',
                'deleted' => self::DEL_STATUS_OK,
                'createTime' => time(),
                'updateTime' => time(),
                'isOld'      => 1,
            );
            $exerciseNoteDao = new Hkzb_Dao_Fudao_ExerciseNote();
            $ret = $exerciseNoteDao->insertRecords($uid, $arr);
            if ($ret === false) {
                Bd_Log::warning("Insert ExerciseNote DB error!");
                return false;
            }

            //send nmq
            $arrData = array(
                'studentUid' => intval($uid),
                'courseId'   => intval($courseId),
            );
            $bolNmqRet = Liveservice_Util_Nmq::sendCommand(Zb_Const_Command::COMMAND_ACHILLES_889004, $arrData);
            if ($bolNmqRet === false) {
                Bd_Log::warning('add ExerciseNote failed, data='.json_encode($arrData));
            }

            return $exerciseNoteDao->getInsertId();
        }


        //获取题目信息
        if($questionType == 'no'){
            $questionType = $this->getQuestionTypeByExamId($examId, $tid);
        }

//        $questionInfo = $this->objDsHomework->getHomeworkDetailByTid($tid);
//        if (!$questionInfo || !$questionType) {
//            Bd_Log::warning("get questionInfo error! detail tid[$tid] examId[$examId]");
//            return false;
//        }

//        $answerShow = "";
//        if ($questionInfo['question']['questionList']) {
//            //阅读理解或者完形填空
//            $questionList = $questionInfo['question']['questionList'];
//            foreach ($questionList as $question) {
////                $tmpOption['title'] = $question['title'];
////                $tmpOption['options'] = $question['options'];
////                $options[] = $tmpOption;
////                unset($tmpOption);
//                $answer[] = $question['choose'];
//               // $answerShow = isset($question['answer']) ? $question['answer'] : "";
//            }
//            //$questionTitle = $questionInfo['question']['content'];
//
//        } else {
//            //单选多选判断主观题
//            //$questionTitle = $questionInfo['question']['title'];
//            //$options = $questionInfo['question']['options'];
//            if(3!==$questionType){
//                $answer = $this->strtoarray($questionInfo['question']['choose']);
//            }else{
//                $answer = $questionInfo['question']['choose'];
//            }
//
//            $answerShow = isset($questionInfo['answer']['title'])?$questionInfo['answer']['title']:'';
//
//        }

//        $comment = $questionInfo['extContent']['subjectAnalysis']?$questionInfo['extContent']['subjectAnalysis']:"";
//        $subjectPoint = $questionInfo['extContent']['pointsList'];

        // 带小题的填空题 获取答案和答案的展现
//        if(Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_MULTI_FILL_BLANK == $questionType){
//            $answer= array();
//            if(isset($questionInfo['question']['questionList'])) {
//                foreach ($questionInfo['question']['questionList'] as $subQuestion) {
//                    $subAnswer = array();
//                    if(is_array($subQuestion['blanks'])) {
//                        foreach ($subQuestion['blanks'] as $blanks) {
//                            $subAnswer[] = implode(',',$blanks);
//                        }
//                    }
//                    $answer[] = $subAnswer;
//                }
//            }
//            $answerShow = json_encode($answer);
//        }
//
//        // 带小题的填空题 获取答案和答案的展现
//        if(Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_FILL_BLANK == $questionType){
//            $answer= $questionInfo['question']['blanks'];
//
//            $answerShow = json_encode($questionInfo['answer']);
//        }

        $arr = array(
            'studentUid' => $uid,
            'courseId' => $courseId,
            'courseName' => $courseInfo['courseName'],
            'subjectId' => $courseInfo['subject'],
            'courseStartTime' => $courseInfo['startTime'],
            'lessonId' => $lessonId,
            'lessonName' => $lessonName,
            'tid' => $tid,
            'questionType' => $questionType,//习题类型
            'noteType' => $type,//0收藏1错题
//            'question' => '',//习题问题
            'answer' => '',//答案
            'answerShow' => '',//展示用的答案
            'comment' => '',//习题解析
 //           'options' => '',//习题选项
            'subjectPoint' => '',
            'deleted' => self::DEL_STATUS_OK,
            'createTime' => time(),
            'updateTime' => time(),
            'isOld'      => 0,
        );
        $exerciseNoteDao = new Hkzb_Dao_Fudao_ExerciseNote();
        $ret = $exerciseNoteDao->insertRecords($uid, $arr);
        if ($ret === false) {
            Bd_Log::warning("Insert ExerciseNote DB error!");
            return false;
        }

        //send nmq
        $arrData = array(
            'studentUid' => intval($uid),
            'courseId'   => intval($courseId),
        );
        $bolNmqRet = Liveservice_Util_Nmq::sendCommand(Zb_Const_Command::COMMAND_ACHILLES_889004, $arrData);
        if ($bolNmqRet === false) {
            Bd_Log::warning('add ExerciseNote failed, data='.json_encode($arrData));
        }

        return $exerciseNoteDao->getInsertId();
    }


    /**
     * 获取题目的类型
     * @param  int $examId 试卷ID
     * @param  int $intTid 题目TID
     * @return int|booler  题目类型
     */
    public function getQuestionTypeByExamId($examId, $intTid)
    {
        if (!$examId || !$intTid) {
            Bd_Log::warning("getQuestionTypeByExamId  param error!");
            return false;
        }

        $tidListInfo = $this->objDsFudaoExamPaper->getExamInfoById($examId, array('tidList'));
        if (!$tidListInfo) {
            Bd_Log::warning("tidListInfo is empty detail: examId[$examId]");
            return false;
        }
        $tidListInfo = $tidListInfo['tidList'];
        foreach ($tidListInfo as $tid => $tidInfo) {
            if (strpos($tid, '_') && $tidTmp = explode("_", $tid)) {
                $tidListInfo[$tidTmp[0]] = $tidInfo;
            }
        }
        return isset($tidListInfo[$intTid]) ? $tidListInfo[$intTid]['tp'] : 0;
    }

    // 专门用于试卷详情方法使用
    protected function strtoarray($str)
    {
        //用来代替implode('',$str);
        $arr = array();
        for ($i = 0; $i < strlen($str); $i++) {
            $arr[] = $str[$i];
        }

        return $arr;
    }


    /**
     * @brief 判断是否已添加
     * @param $arrOrigFields
     * @return bool|int
     */
    public function getExerciseNoteCheck($uid, $courseId, $tid)
    {
        //获取课程信息
        $ids = $this->_tidListFormate($tid);
        $conds = array(
            'studentUid' => $uid,
            'courseId' => $courseId,
            'tid' => $ids
        );
        $fileds = array(
            'id', 'noteType', 'createTime'
        );
        $exerciseNoteDao = new Hkzb_Dao_Fudao_ExerciseNote();
        $noteList = $exerciseNoteDao->getListByConds($uid, $conds, $fileds);
        return $noteList;
    }
    /**
     * @brief 判断是否已添加且没删除
     * @param $arrOrigFields
     * @return bool|int
     */
    public function getExerciseNoteCheckDelete($uid, $courseId, $tid)
    {
        //获取课程信息
        $ids = $this->_tidListFormate($tid);
        $conds = array(
            'studentUid' => $uid,
            'courseId' => $courseId,
            'deleted' => self::DEL_STATUS_OK,
            'tid' => $ids
        );
        $fileds = array(
            'id', 'noteType', 'createTime'
        );
        $exerciseNoteDao = new Hkzb_Dao_Fudao_ExerciseNote();
        $noteList = $exerciseNoteDao->getListByConds($uid, $conds, $fileds);
        return $noteList;
    }
    /**
     * 根据noteIds获取习题本详情
     * @param  int $studentUid 学生UID
     * @param  array $noteId noteId
     * @param  array $arrFields 查询字段
     * @return array|bool             [description]
     */
    public function getExerciseNoteByNoteId($studentUid, $noteId, $arrFields = array())
    {
        if (!$studentUid || !$noteId) {
            Bd_Log::warning("getExerciseNoteByTids param error");
            return false;
        }

        $arrConds['studentUid'] = $studentUid;
        $arrConds['id'] = $noteId;
        //排序
        $arrFields = empty($arrFields) ? (self::ALL_FIELDS) : $arrFields;
        $exerciseNoteDao = new Hkzb_Dao_Fudao_ExerciseNote();
        $noteList = $exerciseNoteDao->getRecordByConds($studentUid, $arrConds, $arrFields);
        if (false === $noteList) {
            Bd_Log::warning("get ExerciseNote by tid is empty studentUid[$studentUid] noteId[$noteId]");
            return false;
        }
        return $noteList;
    }

    /**
     * 根据NOTEiD获取习题本详情
     * @param  int $studentUid 学生UID
     * @param  array $noteIds noteIds
     * @param  array $arrFields 查询字段
     * @return array|boole             [description]
     */
    public function getExerciseNoteByTids($studentUid, $noteIds, $arrFields = array())
    {
        if (!$studentUid || !$noteIds) {
            Bd_Log::warning("getExerciseNoteByTids param error");
            return false;
        }

        $arrConds['studentUid'] = $studentUid;
        $arrConds[] = "id in (".implode(",", $noteIds).")";
        //排序
        $arrAllFields = explode(',', self::ALL_FIELDS);
        $arrFields = empty($arrFields) ? $arrAllFields : $arrFields;
        $exerciseNoteDao = new Hkzb_Dao_Fudao_ExerciseNote();
        $noteList = $exerciseNoteDao->getListByConds($studentUid, $arrConds, $arrFields);
        if (false === $noteList) {
            Bd_Log::warning("get ExerciseNote by tid is empty studentUid[$studentUid] noteIds[$noteIds]");
            return false;
        }
        return $noteList;
    }

    /**
     * 根据TIDS获取习题本信息
     * @param  [type] $studentUid [description]
     * @param  [type] $tid      [description]
     * @param  array  $arrFields  [description]
     * @return array|bool        [description]
     */
    public function getExerciseNoteInfoByTid($studentUid, $lessonId ,$tid, $arrFields = array() )
    {
        if (!$studentUid || !$tid || !$lessonId) {
            Bd_Log::warning("getExerciseNoteByTids param error");
            return false;
        }


        // //获取课程信息
        $courseInfo = $this->objDsLesson->getPackCourseIdByLesson($lessonId);
        if(empty($courseInfo)) {
             Bd_Log::warning("Insert ExerciseNote DB error! courseInfo empty detail:json_encode($courseInfo)");
            return false;
        }
        $courseId = $courseInfo['courseId'];

        $arrConds['studentUid'] = $studentUid;
        $arrConds['tid'] = $tid;
        $arrConds['courseId'] = $courseId;
        $arrConds['deleted'] = self::DEL_STATUS_OK;
        //排序
        $arrAllFields = explode(',', self::ALL_FIELDS);
        $arrFields = empty($arrFields) ? $arrAllFields : $arrFields;
        $exerciseNoteDao = new Hkzb_Dao_Fudao_ExerciseNote();
        $noteList = $exerciseNoteDao->getRecordByConds($studentUid, $arrConds, $arrFields);
        if (false === $noteList) {
            Bd_Log::warning("get ExerciseNote by tid is empty studentUid[$studentUid] tid[$tid]");
            return false;
        }
        return $noteList;
    }
    /**
     * 根据exerciseId更新数据表
     * @param  [type] $studentUid [description]
     * @param  [type] $tid      [description]
     * @param  array  $arrFields  [description]
     * @return array|bool        [description]
     */
    public function updateNoteByExerciseId ($exerciseId) {

        if ($exerciseId <= 0) {
            Bd_Log::warning("update by exerciseId is empty  exerciseId[$exerciseId]");
            return false;
        }
        $objDsExercise = new Hkzb_Ds_Fudao_Exercise();
        $arrExerciseInfo = $objDsExercise->getExerciseInfo( $exerciseId );

        if ( empty( $arrExerciseInfo ) ) {
            Bd_Log::warning("select exercise is empty  exerciseId[$exerciseId]");
            return false;
        }
        if (empty($arrExerciseInfo['question']['pic'])) {
            Bd_Log::warning("exercise pic is empty  exerciseId[$exerciseId]");
            return false;
        }
        // //获取课程信息
        $lessonId = $arrExerciseInfo['lessonId'];
        $courseInfo = $this->objDsLesson->getPackCourseIdByLesson($lessonId);
        if(empty($courseInfo)) {
             Bd_Log::warning("courseInfo empty detail:json_encode($courseInfo)");
             return false;
        }
        $courseId = $courseInfo['courseId'];
        $question = json_encode($arrExerciseInfo['question']['pic']);

        Bd_Log::warning("此接口已废弃");
        return true;

        $db = Hk_Service_Db::getDb('fudao/zyb_fudao');
        for ($idx = 0;$idx<20;$idx++) {
            $sql = "update `tblExerciseNote".$idx."` set `question`= ".$question;
            $sql .= " where `course_id` = ".$courseId." and `lesson_id` =".$lessonId . " and `tid` = ".$exerciseId;
            $arrStudentReortList = $db->query($sql);
        }
    }
}
