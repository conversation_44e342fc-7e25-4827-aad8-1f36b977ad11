<?php
/***************************************************************************
 *
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/



/**
 * @file StudentCourse.php
 * <AUTHOR>
 * @date 2015/11/17 14:13:18
 * @brief 班级
 *
 **/

class Hkzb_Ds_Fudao_StudentCourse {
    //状态
    const STATUS_OK      = 0;
    const STATUS_DELETED = 1;

    const TIME_BEFORE_LESSON = 1800;

    const ALL_FIELDS = 'id,studentUid,courseId,teacherUid,assistantUid,classId,className,grade,subject,deleted,createTime,updateTime,extData,type,status,lastLessonStopTime,pack';
    //状态
    const STATUS_NOT_FINISHED    = 0; //未结束
    const STATUS_FINISHED        = 1; //已结束
    static $STATUS_ARRAY      = array(
        self::STATUS_NOT_FINISHED => '未结束',
        self::STATUS_FINISHED     => '已结束',
    );

    /**
     * 新增学生课程
     *
     * @param  array  $arrParams 班级属性
     * @return bool true/false
     */
    public function addStudentCourse($arrParams) {
        if(intval($arrParams['studentUid']) <= 0 || intval($arrParams['courseId']) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[param: ". json_encode($arrParams). "]");
            return false;
        }

        $arrFields = array(
            'studentUid'   => intval($arrParams['studentUid']),
            'courseId'     => intval($arrParams['courseId']),
            'teacherUid'   => isset($arrParams['teacherUid']) ? intval($arrParams['teacherUid']) : 0,
            'assistantUid' => isset($arrParams['assistantUid']) ? intval($arrParams['assistantUid']) : 0,
            'classId'      => isset($arrParams['classId']) ? intval($arrParams['classId']) : 0,
            'className'    => isset($arrParams['className']) ? strval($arrParams['className']) : '',
            'deleted'      => self::STATUS_OK,
            'createTime'   => time(),
            'updateTime'   => time(),
            'extData'      => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '',
            'grade'        => isset($arrParams['grade']) ? intval($arrParams['grade']) : 0,
            'subject'      => isset($arrParams['subject']) ? intval($arrParams['subject']) : 0,
            'type'         => isset($arrParams['type']) ? intval($arrParams['type']) : 0,
            'pack'         => isset($arrParams['pack']) ? intval($arrParams['pack']) : 0,
            'lastLessonStopTime' => isset($arrParams['lastLessonStopTime']) ? intval($arrParams['lastLessonStopTime']) : 0,
        );

        $objDaoStudentCourse = new Hkzb_Dao_Fudao_StudentCourse();
        $ret = $objDaoStudentCourse->insertRecords(intval($arrParams['studentUid']), $arrFields);

        return $ret;
    }

    /**
     * 设置扩展字段
     *
     * @param  int $studentUid 学生uid
     * @param  int $courseId   课程id
     * @return mix
     */
    public function setExtData($studentUid, $courseId,$arrExtData)
    {
        if (intval($studentUid) <= 0 || intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid courseId:$courseId");
            return false;
        }
        //ps层补全
        $arrFields = array(
            'extData'    => json_encode($arrExtData),
            'updateTime' => time(),
        );


        $arrConds = array(
            'studentUid' => intval($studentUid),
            'courseId'   => intval($courseId),
        );

        $objDaoStudentCourse = new Hkzb_Dao_Fudao_StudentCourse();
        $ret = $objDaoStudentCourse->updateByConds(intval($studentUid), $arrConds, $arrFields);

        return $ret;
    }

    /**
     * 删除学生课程
     *
     * @param  int  $studentUid 学生uid
     * @param  int  $courseId   课程id
     * @return bool true/false
     */
    public function deleteStudentCourse($studentUid, $courseId) {
        if(intval($studentUid) <= 0 || intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid courseId:$courseId]");
            return false;
        }

        $arrConds = array(
            'studentUid' => intval($studentUid),
            'courseId'   => intval($courseId),
        );

        $arrFields = array(
            'deleted'    => self::STATUS_DELETED,
            'updateTime' => time(),
        );

        $objDaoStudentCourse = new Hkzb_Dao_Fudao_StudentCourse();
        $ret = $objDaoStudentCourse->updateByConds(intval($studentUid), $arrConds, $arrFields);
        
        return $ret;
    }

    /**
     * 课程结束
     *
     * @param  int  $courseId    课程id
     * @return bool true/false
     */
    public function setCourseFinished($courseId) {
        if(intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId]");
            return false;
        }

        $arrConds = array(
            'courseId'   => intval($courseId),
        );

        $arrFields = array(
            'status' => self::STATUS_FINISHED,
        );

        $objDaoStudentCourse = new Hkzb_Dao_Fudao_StudentCourse();
        for($index = 0; $index < 20; $index++) {
            $ret = $objDaoStudentCourse->updateByConds($index, $arrConds, $arrFields);
        }

        return $ret;
    }

    /**
     * 设置刚刚结束的章节信息（用于已上课程列表排序）
     *
     * @param  int  $courseId    课程id
     * @return bool true/false
     */
    public function setLastLessonStopTime($courseId, $lastLessonStopTime) {
        if(intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId]");
            return false;
        }

        $arrConds = array(
            'courseId'   => intval($courseId),
        );

        $arrFields = array(
            'lastLessonStopTime'  => $lastLessonStopTime,
        );

        $objDaoStudentCourse = new Hkzb_Dao_Fudao_StudentCourse();
        for($index = 0; $index < 20; $index++) {
            $ret = $objDaoStudentCourse->updateByConds($index, $arrConds, $arrFields);
        }

        return $ret;
    }

    /**
     * 更新学生信息
     *
     * @param  int  $courseId    课程id
     * @param  array$arrParams   课程属性
     * @return bool true/false
     */
    public function updateStudentCourseByclassId($courseId, $classId = 0, $arrStuList, $arrParams) {
        if(intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId]");
            return false;
        }

        $arrConds = array(
            'classId'        => intval($classId),
            'courseId'   => intval($courseId),
        );
        if(count($arrStuList) > 0){
            $strStudentUid = implode(",",$arrStuList);
            $strStudentUid = 'student_uid in ('.$strStudentUid.')';
            $arrConds[] = $strStudentUid;
        }

        $arrFields = array();
        $arrAllFields = explode(',', self::ALL_FIELDS);
        foreach($arrParams as $key => $value) {
            if(!in_array($key, $arrAllFields)) {
                continue;
            }

            $arrFields[$key] = $value;
        }

        //ext由ps补全，json处理在ds
        if(isset($arrFields['extData'])) {
            $arrFields['extData'] = json_encode($arrFields['extData']);
        }

        //补全更新时间
        $arrFields['updateTime'] = time();

        $objDaoStudentCourse = new Hkzb_Dao_Fudao_StudentCourse();
        for($index = 0; $index < 20; $index++) {
            $ret = $objDaoStudentCourse->updateByConds($index, $arrConds, $arrFields);
        }

        return $ret;
    }

    /**
     * 根据条件获取数据
     * liangshuguang (<EMAIL>)
     * @param $arrConds
     * @param array $arrFields
     */
    public function getStudentListByConds($arrConds, $arrFields = array()){
        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrRet = array();
        $objDaoStudentCourse = new Hkzb_Dao_Fudao_StudentCourse();
        for($index = 0; $index < 20; $index++) {
            $ret = $objDaoStudentCourse->getListByConds($index, $arrConds, $arrFields);
            $arrRet = array_merge($arrRet, $ret);
        }
        return $arrRet;
    }

    /**
     *  根据条件查询数据
     * liangshuguang
     * @param Int $studentUid
     * @param array $arrConds
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @return array|bool
     */
    public function getStudentCourseListByConds($studentUid, $arrConds, $arrFields, $offset = 0, $limit = 0) {
        if (intval($studentUid) == 0){
            return false;
        }
        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        if($limit > 0){
            $arrAppends = array("order by create_time desc limit $offset,$limit");
        }else{
            $arrAppends = array("order by create_time desc");
        }

        $objDaoStudentCourse = new Hkzb_Dao_Fudao_StudentCourse();
        return $objDaoStudentCourse->getListByConds(intval($studentUid),$arrConds, $arrFields, null, $arrAppends);
    }

    /**
     * 获取学生课程详情
     *
     * @param  int  $studentUid 学生uid
     * @param  int  $courseId   课程id
     * @param  mix  $arrFields  指定属性
     * @return mix
     */
    public function getStudentCourseInfo($studentUid, $courseId, $arrFields = array()) {
        if(intval($studentUid) <= 0 || intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid courseId:$courseId]");
            return false;
        }

        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'studentUid' => intval($studentUid),
            'courseId'   => intval($courseId),
        );

        $objDaoStudentCourse = new Hkzb_Dao_Fudao_StudentCourse();
        $ret = $objDaoStudentCourse->getRecordByConds(intval($studentUid), $arrConds, $arrFields);

        return $ret;
    }

    /**
     * 获取学生课程详情
     *
     * @param  int  $studentUid 学生uid
     * @param  int  $courseId   课程id
     * @param  mix  $arrFields  指定属性
     * @return mix
     */
    public function getStudentCourseInfoArr($studentUid, $courseIds, $arrFields = array(),$needDeleted=true) {
        if(intval($studentUid) <= 0 || empty($courseIds)) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid]");
            return false;
        }

        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'studentUid' => intval($studentUid),
        );

        if($needDeleted === false){
            $arrConds['deleted'] = self::STATUS_OK;
        }

        $strCourseIds = implode(',', $courseIds);
        $arrConds[] = "course_id in ($strCourseIds)";

        $objDaoStudentCourse = new Hkzb_Dao_Fudao_StudentCourse();
        $ret = $objDaoStudentCourse->getListByConds(intval($studentUid), $arrConds, $arrFields);

        return $ret;
    }

    /**
     * 获取学生课程数量
     *
     * @param  int  $studentUid 学生uid
     * @return bool true/false
     */
    public function getCourseCnt($studentUid, $type = -1) {
        if(intval($studentUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid");
            return false;
        }

        $arrConds = array(
            'studentUid'  => $studentUid,
            'deleted'     => Hkzb_Ds_Fudao_StudentCourse::STATUS_OK,
            'pack'        => array(Hkzb_Ds_Fudao_Course::PACK_YES, '<>'),
        );

        if($type >= 0) {
            $arrConds['type'] = $type;
        }

        $objDaoStudentCourse = new Hkzb_Dao_Fudao_StudentCourse();
        $ret = $objDaoStudentCourse->getCntByConds($studentUid, $arrConds);

        return $ret;
    }

    /**
     * getStudentCourseCnt
     *
     * 获取学生课程数量
     * @param int $studentUid
     * @param Array $arrConds
     * @access public
     * @return Array
     */
    public function getStudentCourseCnt($studentUid, $arrConds = null)
    {
        if(intval($studentUid) <= 0) {
            Bd_Log::warning("Error:[getStudentCourseCnt error], Detail:[studentUid:$studentUid");
            return false;
        }

        $objDaoStudentCourse = new Hkzb_Dao_Fudao_StudentCourse();
        $ret = $objDaoStudentCourse->getCntByConds($studentUid, $arrConds);

        return $ret;
    }

    /**
     * 获取学生某类型课程列表
     *
     * @param  int  $studentUid 学生uid
     * @return bool true/false
     */
    public function getCourseList($studentUid,$type = -1, $arrFields = array()) {
        if(intval($studentUid) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid");
            return false;
        }

        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'studentUid'  => $studentUid,
            'deleted'     => Hkzb_Ds_Fudao_StudentCourse::STATUS_OK,
            'pack'        => array(Hkzb_Ds_Fudao_Course::PACK_YES, '<>'),
        );

        if($type >= 0) {
            $arrConds['type'] = $type;
        }

        $objDaoStudentCourse = new Hkzb_Dao_Fudao_StudentCourse();
        $ret = $objDaoStudentCourse->getListByConds($studentUid, $arrConds, $arrFields);


        return $ret;
    }

    /**
     * 获取学生列表的某类型课程列表
     *
     * @param  int  $studentUid 学生uid
     * @return bool true/false
     */
    public function getStudentsCourseList($studentArr = array(), $courseArr = array(),$arrFields = array()) {
        if(empty($studentArr) || empty($courseArr)){
            return array();
        }
        if(empty($arrFields)){
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrConds = array(
            'student_uid in ('.implode(',', $studentArr).')',
            'course_id in ('. implode(',', $courseArr).')',
            'deleted' => Hkzb_Ds_Fudao_StudentCourse::STATUS_OK,
        );
        $arrRet = array();
        $objDaoStudentCourse = new Hkzb_Dao_Fudao_StudentCourse();
        for($index = 0; $index < 20; $index++) {
            $ret = $objDaoStudentCourse->getListByConds($index, $arrConds, $arrFields);
            $arrRet = array_merge($arrRet, $ret);
        }
        return $arrRet;
    }

    /**
     * 获取指定课程学生数量
     *
     * @param  int  $courseId      课程id
     * @param  int  $classId       班级id
     * @return int
     */
    public function getStudentCnt($courseId, $classId = 0) {
        if(intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId]");
            return false;
        }

        $arrConds = array(
            'courseId' => intval($courseId),
            'deleted'  => self::STATUS_OK,
        );

        if(intval($classId) > 0) {
            $arrConds['classId'] = intval($classId);
        }

        $totalNum = 0;
        $objDaoStudentCourse = new Hkzb_Dao_Fudao_StudentCourse();
        for($index = 0; $index < 20; $index++) {
            $num = $objDaoStudentCourse->getCntByConds($index, $arrConds);
            $totalNum += intval($num);
        }

        return $totalNum;
    }

    /**
     * 获取指定课程学生列表
     *
     * @param  int  $courseId      课程id
     * @param  int  $classId       班级id
     * @param  mix  $arrFields     指定属性
     * @param  int  $offset    偏移
     * @param  int  $limit     限制
     * @return mix
     */
    public function getStudentList($courseId, $classId = 0, $arrFields = array(), $offset = 0, $limit = 20) {
        if(intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId]");
            return false;
        }

        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'courseId' => intval($courseId),
            'deleted'  => self::STATUS_OK,
        );

        if(intval($classId) > 0) {
            $arrConds['classId'] = intval($classId);
        }

        $arrRet = array();
        $objDaoStudentCourse = new Hkzb_Dao_Fudao_StudentCourse();
        for($index = 0; $index < 20; $index++) {
            $ret = $objDaoStudentCourse->getListByConds($index, $arrConds, $arrFields);
            $arrRet = array_merge($arrRet, $ret);
        }

        $ret = array_slice($arrRet, $offset, $limit);

        return $ret;
    }

    //获取退款的学生列表
    public function getFrozenStudentList($courseId, $lessonId = 0) {
        if(intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId]");
            return false;
        }

        $startTime = 0;
        $stopTime  = 0;
        if(intval($lessonId) > 0) {
            $objLesson = new Hkzb_Ds_Fudao_Lesson();
            $lessonInfo = $objLesson->getLessonInfo($lessonId, array('courseId', 'startTime', 'stopTime', 'status'));
            if(empty($lessonInfo)) {
                Bd_Log::warning("Error:[lessonInfo empty], Detail:[lessonId:$lessonId]");
                return false;
            }

            if(intval($lessonInfo['status']) === Hkzb_Ds_Fudao_Lesson::STATUS_DELETED) {
                Bd_Log::warning("Error:[lesson deleted], Detail:[lessonId:$lessonId]");
                return false;
            }

            if(intval($lessonInfo['courseId']) !== intval($courseId)) {
                Bd_Log::warning("Error:[lesson deleted], Detail:[lessonId:$lessonId]");
                return false;
            }

            $startTime = intval($lessonInfo['startTime']);
            $stopTime  = intval($lessonInfo['stopTime']);
        }

        $arrConds = array(
            'courseId' => intval($courseId),
            'deleted'  => self::STATUS_DELETED,
        );

        $studentIdList = array();
        $objDaoStudentCourse = new Hkzb_Dao_Fudao_StudentCourse();
        for($index = 0; $index < 20; $index++) {
            $studentList = $objDaoStudentCourse->getListByConds($index, $arrConds, array('studentUid', 'extData'));
            foreach($studentList as $student) {
                $deleteTime = intval($student['extData']['deleteTime']);
                if($lessonId > 0 && $startTime - 1800 < $deleteTime) {
                    continue;
                }

                $studentIdList[] = $student['studentUid'];
            }
        }

        return $studentIdList;
    }
    //跟新作业批改时间
    public function updateCorrectTime($studentUid, $courseId, $lessonId){
        if(intval($courseId) <= 0 || $studentUid <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId]");
            return false;
        }

        $arrConds = array(
            'studentUid'        => intval($studentUid),
            'courseId'   => intval($courseId),
        );
        //补全更新时间
        $arrFields['exerciseCorrectTime'] = time();
        //获取extdata
        $studentCourse = $this->getStudentCourseInfo($studentUid, $courseId, array('extData'));
        $extData = $studentCourse['extData'];
        $extData['correctLesson'] = $lessonId;
        $arrFields['extData'] = json_encode($extData);
        $objDaoStudentCourse = new Hkzb_Dao_Fudao_StudentCourse();
        $ret = $objDaoStudentCourse->updateByConds(intval($studentUid), $arrConds, $arrFields);
        return $ret;

    }
    //获取有权限的学生列表
    public function getRealStudentList($courseId, $lessonId = 0, $classId = 0) {
        if(intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId]");
            return false;
        }

        $startTime = 0;
        $stopTime  = 0;
        if(intval($lessonId) > 0) {
            $objLesson = new Hkzb_Ds_Fudao_Lesson();
            $lessonInfo = $objLesson->getLessonInfo($lessonId, array('courseId', 'startTime', 'stopTime', 'status'));
            if(empty($lessonInfo)) {
                Bd_Log::warning("Error:[lessonInfo empty], Detail:[lessonId:$lessonId]");
                return false;
            }

            if(intval($lessonInfo['status']) === Hkzb_Ds_Fudao_Lesson::STATUS_DELETED) {
                Bd_Log::warning("Error:[lesson deleted], Detail:[lessonId:$lessonId]");
                return false;
            }

            if(intval($lessonInfo['courseId']) !== intval($courseId)) {
                Bd_Log::warning("Error:[lesson deleted], Detail:[lessonId:$lessonId]");
                return false;
            }

            $startTime = intval($lessonInfo['startTime']);
            $stopTime  = intval($lessonInfo['stopTime']);
        }

        $arrConds = array(
            'courseId' => intval($courseId),
        );

        if(intval($classId) > 0) {
            $arrConds['classId'] = intval($classId);
        }

        $studentIdList = array();
        $objDaoStudentCourse = new Hkzb_Dao_Fudao_StudentCourse();
        for($index = 0; $index < 20; $index++) {
            $studentList = $objDaoStudentCourse->getListByConds($index, $arrConds, array('studentUid', 'deleted', 'extData'));
            foreach($studentList as $student) {
                $deleted = intval($student['deleted']);
                $deleteTime = intval($student['extData']['deleteTime']);
                if($deleted === self::STATUS_DELETED) {
                    if($lessonId <= 0) {
                        continue;
                    }

                    if($lessonId > 0 && $startTime - 1800 > $deleteTime) {
                        continue;
                    }
                }

                $studentIdList[] = $student['studentUid'];
            }
        }

        return $studentIdList;
    }

    /**
     * @brief 学生有没有报过这个老师的课
     * @param int $studentUid
     * @param int $teacherUid
     * @return array|bool|false
     */
    public function checkStudentTeacher($studentUid, $teacherUid){
        if (intval($studentUid) == 0 || intval($teacherUid) == 0){
            return false;
        }

        $arrConds = array(
            'studentUid' => intval($studentUid),
            'teacherUid' => intval($teacherUid),
            'deleted'    => 0,
        );

        $arrFields = explode(',', self::ALL_FIELDS);

        $objDaoStudentCourse = new Hkzb_Dao_Fudao_StudentCourse();
        $res = $objDaoStudentCourse->getRecordByConds(intval($studentUid),$arrConds, $arrFields, null, null);
        return $res;
    }

    /**
     * @brief 验证学生是否报名过此辅导老师的此门
     * @param int $studentUid
     * @param int $teacherUid
     * @param int $courseId
     * @return array|bool|false
     */
    public function checkStudentAssistant($studentUid = 0, $teacherUid = 0, $courseId = 0){
        if (intval($studentUid) == 0 || intval($teacherUid) == 0 || intval($courseId) == 0){
            return false;
        }

        $arrConds = array(
            'studentUid' => intval($studentUid),
            'assistantUid' => intval($teacherUid),
            'courseId'   => intval($courseId),
            'deleted'    => 0,
        );

        $arrFields = explode(',', self::ALL_FIELDS);

        $objDaoStudentCourse = new Hkzb_Dao_Fudao_StudentCourse();
        $res = $objDaoStudentCourse->getRecordByConds(intval($studentUid),$arrConds, $arrFields, null, null);
        return $res;
    }

    /**
     * @brief 获取学生的待上体验课课程列表
     * @param int $studentUid
     * @return array|bool|false
     */
    public function getStudentUnfinishCourseList($studentUid){
        if (intval($studentUid) == 0){
            return false;
        }

        $arrConds = array(
            'studentUid' => intval($studentUid),
            'deleted'    => 0,
            'status'     => 0,
            'type'       => 4,
        );

        $arrFields = array('courseId');

        $objDaoStudentCourse = new Hkzb_Dao_Fudao_StudentCourse();
        $res = $objDaoStudentCourse->getListByConds(intval($studentUid),$arrConds, $arrFields, null, null);
        return $res;
    }


    /**
     * 获取指定课程学生数量（包括已经删除的学生）
     *
     * @param  int  $courseId      课程id
     * @param  int  $classId       班级id
     * @return int
     */
    public function getAllStudentCnt($courseId, $classId = 0) {
        if(intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId]");
            return false;
        }

        $arrConds = array(
            'courseId' => intval($courseId),
        );

        if(intval($classId) > 0) {
            $arrConds['classId'] = intval($classId);
        }

        $totalNum = 0;
        $objDaoStudentCourse = new Hkzb_Dao_Fudao_StudentCourse();
        for($index = 0; $index < 20; $index++) {
            $num = $objDaoStudentCourse->getCntByConds($index, $arrConds);
            $totalNum += intval($num);
        }

        return $totalNum;
    }

    /**
     * 获取指定课程学生列表（包括已经删除的学生）
     *
     * @param  int  $courseId      课程id
     * @param  int  $classId       班级id
     * @param  mix  $arrFields     指定属性
     * @param  int  $offset    偏移
     * @param  int  $limit     限制
     * @return mix
     */
    public function getAllStudentList($courseId, $classId = 0, $arrFields = array(), $offset = 0, $limit = 20) {
        if(intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[courseId:$courseId]");
            return false;
        }

        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrConds = array(
            'courseId' => intval($courseId),
        );

        if(intval($classId) > 0) {
            $arrConds['classId'] = intval($classId);
        }

        $arrRet = array();
        $objDaoStudentCourse = new Hkzb_Dao_Fudao_StudentCourse();
        for($index = 0; $index < 20; $index++) {
            $ret = $objDaoStudentCourse->getListByConds($index, $arrConds, $arrFields);
            $arrRet = array_merge($arrRet, $ret);
        }

        $ret = array_slice($arrRet, $offset, $limit);

        return $ret;
    }

    // 更新扩展字段
    public function updateStudentCourseExt($intStudentUid, $intCourseId, $arrExt = array()) {
        $intStudentUid = intval($intStudentUid);
        $intCourseId = intval($intCourseId);

        if ($intStudentUid <= 0 || $intCourseId<=0 || empty($arrExt)) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$intStudentUid courseId:$intCourseId ext:" . json_encode($arrExt) . "]");

            return false;
        }

        $arrConds   = array(
            'studentUid' => $intStudentUid,
            'courseId'   => $intCourseId,
        );

        $objDaoStudentCourse = new Hkzb_Dao_Fudao_StudentCourse();

        $arrRecord = $objDaoStudentCourse->getRecordByConds($intStudentUid, $arrConds, array('extData'));
        if (empty($arrRecord)) {
            return false;
        }


        $arrExtxtData   = $arrRecord['extData'];
        foreach ($arrExt as $key => $value) {
            $arrExtxtData[$key] = $value;
        }
        $arrFields = array(
            'extData'=>json_encode($arrExtxtData),
            'updateTime'=>time(),
        );

        $res = $objDaoStudentCourse->updateByConds($intStudentUid, $arrConds, $arrFields);

        return $res;
    }

    /**
     * 获取学生课程详情
     *
     * @param  int  $studentUid 学生uid
     * @param  int  $courseId   课程id
     * @param  array  $arrFields  指定属性
     * @return array|bool
     */
    public function getNormalStudentCourseInfo($studentUid, $courseId, $arrFields = array())
    {
        if (intval($studentUid) <= 0 || intval($courseId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[studentUid:$studentUid courseId:$courseId]");

            return false;
        }
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrConds = array(
            'studentUid' => intval($studentUid),
            'courseId'   => intval($courseId),
            'deleted'    => 0,//是否已删除
        );

        $objDaoStudentCourse = new Hkzb_Dao_Fudao_StudentCourse();
        $ret                 = $objDaoStudentCourse->getRecordByConds(intval($studentUid), $arrConds, $arrFields);

        return $ret;
    }
    
     /**
     * 获取课程学生数量
     *
     * @param  $arrConds    
     * @return int
     */
    public function getAllStudentCondsCnt($arrConds) {
      
        $totalNum = 0;
        $objDaoStudentCourse = new Hkzb_Dao_Fudao_StudentCourse();
        for($index = 0; $index < 20; $index++) {
            $num = $objDaoStudentCourse->getCntByConds($index, $arrConds);
            $totalNum += intval($num);
        }

        return $totalNum;
    }

    /**
     * 获取指定课程学生列表
     *
    
     * @param $arrConds
     * @param  int  $offset    偏移
     * @param  int  $limit     限制
     * @return array
     */
    public function getAllStudentCondsList($arrConds, $arrFields = array(), $offset = 0, $limit = 20)
    {
        if (empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrRet              = array();
        $objDaoStudentCourse = new Hkzb_Dao_Fudao_StudentCourse();
        for ($index = 0; $index < 20; $index++) {
            $ret    = $objDaoStudentCourse->getListByConds($index, $arrConds, $arrFields);
            $arrRet = array_merge($arrRet, $ret);
        }
        $ret = array_slice($arrRet, $offset, $limit);

        return $ret;
    }

    /**
     * 获取用户的任务课程通过科目id
     * @param $studentUid
     * @param array $subjectArr
     * @return array
     */

    public function getStudentTaskCourseIdArrBysubject($studentUid,$subjectArr=array(),$arrFields=array())
    {
        if(empty($studentUid)){
            Bd_Log::warning("studentUid为空");
            return array();
        }

        if(empty($arrFields)){
            $arrFields = $arrFields = explode(',', self::ALL_FIELDS);
        }

        $studentUid = intval($studentUid);

        $arrConds = array(
            'type' => Hkzb_Ds_Fudao_Course::TYPE_PRIVATE_LONG,
            'studentUid' => $studentUid,
            'pack' => Hkzb_Ds_Fudao_Course::PACK_YES,
            'deleted' => Hkzb_Ds_Fudao_StudentCourse::STATUS_OK,
        );

        if(!empty($subjectArr)){
            $subjectStr = join(',',$subjectArr);
            $arrConds[] = "subject in ({$subjectStr})";
        }

        $objDaoStudentCourse = new Hkzb_Dao_Fudao_StudentCourse();
        $res = $objDaoStudentCourse->getListByConds(intval($studentUid),$arrConds, $arrFields, null);

        if($res === false){
            Bd_Log::warning("db error");
            return false;
        }

        return $res;
    }

    /**
     * 获取某个学生某个时间段内的班课
     * @param $studentUid
     * @param int $startTime
     * @param int $stopTime
     * @return array|bool|false
     */

    public function getStudentCourseListByCreateTime($studentUid,$startTime=0,$stopTime=0,$arrFields=array())
    {
        if(empty($studentUid)){
            return array();
        }

        if(empty($arrFields)){
            $arrFields = $arrFields = explode(',', self::ALL_FIELDS);
        }

        $studentUid = intval($studentUid);

        if($startTime <= 0){
            $startTime = time();
        }

        $arrConds = array(
            'type' => Hkzb_Ds_Fudao_Course::TYPE_PRIVATE_LONG,
            'studentUid' => $studentUid,
            'pack' => Hkzb_Ds_Fudao_Course::PACK_YES,
            'deleted' => Hkzb_Ds_Fudao_StudentCourse::STATUS_OK,
        );

        $timeStr = "create_time > {$startTime}";

        if($stopTime > 0){
           $timeStr .= " and create_time < {$stopTime}";
        }

        $arrConds[] = $timeStr;

        $objDaoStudentCourse = new Hkzb_Dao_Fudao_StudentCourse();
        $res = $objDaoStudentCourse->getListByConds($studentUid,$arrConds, $arrFields, null);

        if($res === false){
            Bd_Log::warning("db error");
            return false;
        }

        return $res;
    }

    /**
     * @param $studentUid
     * @param int $startTime
     * @param int $stopTime
     * @param array $learnSeasonArr
     * @return array
     */

    public function getStudentCourseInfoBySeasonArrAndCreateTime($studentUid,$startTime=0,$stopTime=0,$learnSeasonArr=array())
    {
        //获取学生的春季课
        $studentCourseList = $this->getStudentCourseListByCreateTime($studentUid, $startTime, $stopTime, array('courseId'));

        $courseIdArr = array();
        /**
         * @des 统一处理线上报警 start
         * <AUTHOR>
         * @time 2018/04/09
         */
        if(is_array($studentCourseList) && !empty($studentCourseList)){
            foreach ($studentCourseList as $stuCourse) {
                $courseIdArr[] = $stuCourse['courseId'];
            }
        }

        /**
         * end
         */

        //获取某个学季的课程
        $objAdvCourse   = new Hkzb_Ds_Fudao_Advanced_Course();
        $courseInfoList = $objAdvCourse->getCourseInfoArr($courseIdArr, array(), true);

        $courseInfoArr = array();

        foreach ($courseInfoList as $courseInfo) {
            $seasonName = $courseInfo['learnSeason'];
            if (in_array($seasonName, $learnSeasonArr)) {
                $courseInfoArr[] = $courseInfo;
            }
        }

        return $courseInfoArr;
    }
}
