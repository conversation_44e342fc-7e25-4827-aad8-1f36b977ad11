<?php
/***************************************************************************
 *
 * Copyright (c) 2018 zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file  UnUserAlbum.php
 * <AUTHOR>
 * @date  2018/6/1
 * @brief  少年听闻专辑购买
 */
class Hkzb_Ds_Universe_UnUserAlbum
{

    /**
     * 少年听闻购买用户小流量开关，仅从缓存查询, 小流量flag = universeSwitch
     * @param $uid
     * @return bool
     */
    public static function checkSwitchByUid($uid)
    {
        if (empty($uid)) {
            return false;
        }
        $redisConf = Bd_Conf::getConf("/hk/redis/zhiboke");
        $objCache = new Hk_Service_Redis($redisConf['service']);
        $cacheKey = 'mili_universe_had_album_uid_list';
        $cacheValue = $objCache->hget($cacheKey, $uid);
        Bd_Log::addNotice("hkzbUnSw", 'uid:' . $uid . ',ret:' . $cacheValue);
        if (!empty($cacheValue)) {
            return true;
        }
        return false;
    }
}