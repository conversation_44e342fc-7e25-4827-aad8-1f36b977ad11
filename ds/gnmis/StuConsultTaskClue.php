<?php
/**************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @filename:      StuConsultTaskClue
 * @author:        <EMAIL>
 * @desc:
 * @create:        2017-09-07 20:43:20
 * @last modified: 2017-09-20 10:57
 */
class Hkzb_Ds_Gnmis_StuConsultTaskClue
{
    // sc 分配器
    const SCMIS_HASH_SC_ALLOC  = 'SCMIS_HASH_SC_ALLOC';
    // sc 绑定分布状况
    const SCMIS_HASH_SALE_BIND = 'SCMIS_HASH_SALE_BIND';

    const SEX_DEFAULT = 0;
    const SEX_MALE    = 1;
    const SEX_FEMALE  = 2;

    static $SEX_ARRAY = [
        self::SEX_DEFAULT => '无',
        self::SEX_MALE    => '男',
        self::SEX_FEMALE  => '女',
    ];

    const INTENTION_DEFAULT = 5;
    const INTENTION_TOPAY   = 1;
    const INTENTION_HIGH    = 2;
    const INTENTION_GENERAL = 3;
    const INTENTION_LOW     = 4;

    // 意向
    static $INTENTION_ARRAY = [
        self::INTENTION_DEFAULT => '未接通',
        self::INTENTION_TOPAY   => '高意向-待付款',
        self::INTENTION_HIGH    => '高意向',
        self::INTENTION_GENERAL => '一般意向',
        self::INTENTION_LOW     => '无意向',
    ];

    const STATUS_TOASSIGN     = 0;
    const STATUS_BOUND        = 1;
    const STATUS_FINISH       = 2;
    const STATUS_UNBOUND      = 3;

    // 学生状态
    static $STATUS_ARRAY = [
        self::STATUS_TOASSIGN     => '待分配',
        self::STATUS_BOUND        => '待跟进',
        self::STATUS_FINISH       => '已完成',
        self::STATUS_UNBOUND      => '未绑定',
    ];

    const UTYPE_DEFAULT     = 1;
    const UTYPE_PRIVATE     = 2;
    const UTYPE_EXPERIENCE  = 3;
    const UTYPE_PAY_LONG    = 4;
    const UTYPE_REFUND_LONG = 5;

    static $UTYPE_ARRAY = [
        self::UTYPE_DEFAULT     => '默认',
        self::UTYPE_PRIVATE     => '专题课',
        self::UTYPE_EXPERIENCE  => '体验课',
        self::UTYPE_PAY_LONG    => '已购班课',
        self::UTYPE_REFUND_LONG => '已退班课',
    ];

    const UCHANNEL_PRIVATE   = 'private_course';
    const UCHANNEL_LONG      = 'long_course';
    const UCHANNEL_NEW_USER  = 'new_user';
    const UCHANNEL_OTHERS    = 'others';

    static $UCHANNEL_ARRAY = [
        self::UCHANNEL_PRIVATE   => '专题课',
        self::UCHANNEL_LONG      => '班课',
        self::UCHANNEL_NEW_USER  => '新用户',
        self::UCHANNEL_OTHERS    => '其他',
    ];

    static $ALL_FIELDS_ARRAY = [
        'id',
        'stu_phone',
        'stu_touch_phone',
        'stu_uid',
        'stu_utype',
        'stu_channel',
        'course_id',
        'sale_uid',
        'intention',
        'status',
        'trans_time',
        'trans_money',
        'trans_order',
        'trans_status',
        'grade_id',
        'dis_grade_id',
        'sex',
        'province',
        'city',
        'area',
        'school',
        'book_version',
        'stu_name',
        'qq',
        'create_time',
        'bind_time',
        'unbind_time',
        'first_touch_time',
        'next_touch_time',
        'last_touch_time',
        'bit',
        'ext',
    ];

    public function addTaskClue($arrParams)
    {
        $arrFields = [
            'stuPhone'   => $arrParams['stuPhone'],
            'stuUid'     => $arrParams['stuUid'],
            'stuChannel' => $arrParams['stuChannel'],
            'gradeId'    => $arrParams['gradeId'],
            'courseId'   => $arrParams['courseId'],
            'stuUtype'   => $arrParams['stuUtype'] ? $arrParams['stuUtype'] : 0,
            'createTime' => time(),
        ];

        $arrConds = [
            'stuUid' => (int) $arrParams['stuUid'],
        ];
        $objStuConsultTaskClue = new Hkzb_Dao_Gnmis_StuConsultTaskClue();
        $exists = $objStuConsultTaskClue->getRecordByConds($arrConds, ['id']);
        if (!empty($exists)) {
            Bd_Log::warning('Error:[addTaskClue error], Detail:[stu exists]');
            return false;
        }
        $res = $objStuConsultTaskClue->insertRecords($arrFields);
        if (false === $res) {
            Bd_Log::warning('Error:[addTaskClue error], Detail:[role]');
        }
        return $res;
    }

    /**
     * getTaskClueListByConds
     *
     * 获取列表
     * @param mixed $arrConds
     * @param mixed $arrFields
     * @param int $pn
     * @param int $rn
     * @access public
     * @return Array
     */
    public function getTaskClueListByConds($arrConds = null, $arrFields = [], $pn = 0, $rn = 0)
    {
        $arrAppends = null;

        if ($rn > 0) {
            $arrAppends = [
                "limit $pn,$rn",
            ];
        }
        if (empty($arrFields)) {
            $arrFields = self::$ALL_FIELDS_ARRAY;
        }

        $objStuConsultTaskClue = new Hkzb_Dao_Gnmis_StuConsultTaskClue();
        $res = $objStuConsultTaskClue->getListByConds($arrConds,
            $arrFields,
            null,
            $arrAppends
        );
        if (false === $res) {
            Bd_Log::warning('Error:[gettaskclue  error], Detail:[role]');
        }
        return $res;
    }

    /**
     * getSaleandStuRecord
     *
     * 获取销售与学生的关系
     * @param int $saleUid
     * @param int $stuUid
     * @param Array $arrFields
     * @access public
     * @return Array
     */
    public function getSaleandStuRecord($saleUid, $stuUid, $arrFields = [])
    {
        if ($saleUid <= 0 || $stuUid <= 0) {
            Bd_Log::warning('Error:[getSaleandStuRecord error], Detail:[saleUid stuUid]');
            return false;
        }

        $arrConds = [
            'stuUid'  => $stuUid,
            'saleUid' => $saleUid,
        ];

        $res = $this->getTaskClueListByConds($arrConds, $arrFields);
        if (false === $res) {
            Bd_Log::warning('Error:[getSaleandStuRecord  error], Detail:[role]');
        }
        return $res;
    }

    public function getTaskClueById($id, $arrFields = [])
    {
        if ($id <= 0) {
            Bd_Log::warning('Error:[gettaskcluebyid  error], Detail:[id]');
            return false;
        }

        $arrConds = [
            'id' => $id,
        ];
        if (empty($arrFields)) {
            $arrFields = self::$ALL_FIELDS_ARRAY;
        }

        $objStuConsultTaskClue = new Hkzb_Dao_Gnmis_StuConsultTaskClue();
        $res = $objStuConsultTaskClue->getRecordByConds($arrConds, $arrFields);
        if (false === $res) {
            Bd_Log::warning('Error:[gettaskclue  error], Detail:[role]');
        }
        return $res;
    }

    /**
     * getUnBindStuList
     *
     * 获取未绑定学生列表
     * @param Array|Null $arrFields
     * @access public
     * @return Array
     */
    public function getUnBindStuList($arrFields = [])
    {
        $arrConds = [
            'status' => self::STATUS_TOASSIGN,
        ];

        if (empty($arrFields)) {
            $arrFields = self::$ALL_FIELDS_ARRAY;
        }

        $objStuConsultTaskClue = new Hkzb_Dao_Gnmis_StuConsultTaskClue();
        $res = $objStuConsultTaskClue->getlistByConds($arrConds, $arrFields);
        if (false === $res) {
            Bd_Log::warning('Error:[gettaskclue  error], Detail:[role]');
        }
        return $res;
    }

    public function getSalesTaskList($salesList)
    {
        if (empty($salesList)) {
            Bd_Log::warning('Error:[getSalesTaskList  error], Detail:[role]');
            return false;
        }

        $sales = implode(',', $salesList);
        $sales = trim($sales, ',');

        $arrConds = [
            "sale_uid IN($sales)"
        ];

        $arrFields = [
            'sale_uid',
            'max(bind_time) as max_time',
        ];

        $arrAppends = [
            'GROUP BY sale_uid ',
            'ORDER BY max_time ASC ',
        ];

        $objStuConsultTaskClue = new Hkzb_Dao_Gnmis_StuConsultTaskClue();
        $res = $objStuConsultTaskClue->getListByConds($arrConds,
            $arrFields,
            null,
            $arrAppends
        );
        if (false === $res) {
            Bd_Log::warning('Error:[getsaleslist  error], Detail:[role]');
        }
        return $res;
    }

    public function bindSalesById($id, $saleUid)
    {
        if ($id <= 0 || $saleUid <= 0) {
            Bd_Log::warning('Error:[bindsalesbyid  error], Detail:[id or sale_uid]');
            return false;
        }

        $arrConds = [
            'id' => $id
        ];

        $arrFields = [
            'saleUid'  => $saleUid,
            'status'   => self::STATUS_BOUND,
            'bindTime' => time(),
        ];

        $objStuConsultTaskClue = new Hkzb_Dao_Gnmis_StuConsultTaskClue();
        $res = $objStuConsultTaskClue->updateByConds($arrConds,
            $arrFields
        );
        if (false === $res) {
            Bd_Log::warning('Error:[getsaleslist  error], Detail:[role]');
        }
        return $res;
    }

    public function getSexTitle($sexId)
    {
        if ($sexId < 0) {
            Bd_Log::warning('Error:[getSexTitle  error], Detail:[sexId]');
            return false;
        }
        $title = '';
        if (array_key_exists($sexId, self::$SEX_ARRAY)) {
           $title = self::$SEX_ARRAY[$sexId];
        } else {
            $title = false;
        }
        return $title;
    }

    public function getChannelName($channel)
    {
        if (strlen($channel) < 0) {
            Bd_Log::warning('Error:[getSexTitle  error], Detail:[channel]');
            return false;
        }
        $title = '';
        if (array_key_exists($channel, self::$UCHANNEL_ARRAY)) {
           $title = self::$UCHANNEL_ARRAY[$channel];
        } else {
            $title = false;
        }
        return $title;
    }

    /**
     * getTaskClueCntByConds
     *
     * 获取数量
     * @param Array $arrConds
     * @access public
     * @return Array
     */
    public function getTaskClueCntByConds($arrConds)
    {
        $objStuConsultTaskClue = new Hkzb_Dao_Gnmis_StuConsultTaskClue();
        $res = $objStuConsultTaskClue->getCntByConds($arrConds);
        if (false === $res) {
            Bd_Log::warning('Error:[getsalescnt error], Detail:[role]');
        }
        return $res;
    }

    public function getIntentionName($intention)
    {
        if ($intention < 0) {
            Bd_Log::warning('error:[getintentionname error], detail:[intention]');
            return false;
        }

        $title = '';
        if (array_key_exists($intention, self::$INTENTION_ARRAY)) {
           $title = self::$INTENTION_ARRAY[$intention];
        } else {
            $title = '';
        }
        return $title;
    }

    public function getStatusName($status)
    {
        if ($status < 0) {
            Bd_Log::warning('error:[getintentionname error], detail:[status]');
            return false;
        }

        $title = '';
        if (array_key_exists($status, self::$STATUS_ARRAY)) {
           $title = self::$STATUS_ARRAY[$status];
        } else {
            $title = '';
        }
        return $title;
    }

    /**
     * 根据销售学生数据获取信息
     * @param Array $studentUid, $saleUid
     * @access public
     * @return Array |false
     */
    public function getTaskClueRecordByUids($studentUid, $saleUid)
    {
        $arrConds = array(
            'stu_uid'            => $studentUid,
            'sale_uid'           => $saleUid,
        );
        $objStuConsultTaskClue = new Hkzb_Dao_Gnmis_StuConsultTaskClue();
        $res = $objStuConsultTaskClue->getRecordByConds($arrConds,array('*'));
        if (false === $res) {
            Bd_Log::warning('Error:[getsales error], Detail:[role]');
        }
        return $res;
    }

    /**
     * 根据销售学生数据获取信息
     * @param Array $studentUid, $saleUid
     * @access public
     * @return Array |false
     */
    public function getTaskClueRecordByStuid($saleUid)
    {
        $arrConds = array(
            'sale_uid'           => $saleUid,
        );
        $objStuConsultTaskClue = new Hkzb_Dao_Gnmis_StuConsultTaskClue();
        $res = $objStuConsultTaskClue->getRecordByConds($arrConds,array('*'));
        if (false === $res) {
            Bd_Log::warning('Error:[getsales error], Detail:[role]');
        }
        return $res;
    }

    /**
     * getStuInfoByUid
     *
     * 获取学生信息(已绑定状态)
     * @param mixed $uid
     * @param mixed $arrFields
     * @access public
     * @return Array
     */
    public function getStuInfoByUid($uid, $arrFields = [])
    {
        if ($uid <= 0) {
            Bd_Log::warning('Error:[getStuInfoByUid error], Detail:[uid]');
            return false;
        }
        $arrConds = [
            'stuUid' => $uid,
            'status' => self::STATUS_BOUND,
        ];

        if (empty($arrFields)) {
            $arrFields = self::$ALL_FIELDS_ARRAY;
        }

        $objStuConsultTaskClue = new Hkzb_Dao_Gnmis_StuConsultTaskClue();
        $res = $objStuConsultTaskClue->getRecordByConds($arrConds, $arrFields);
        if (false === $res) {
            Bd_Log::warning('Error:[gettaskclue  error], Detail:[role]');
        }
        return $res;
    }

    /**
     * 更新学生意向
     * @param Array $studentUid, $saleUid
     * @access public
     * @return Array |false
     */
    public function updateStudentIntentionByConds($arrConds)
    {
        if (empty($arrConds['sale_uid']) || empty($arrConds['student_uid'])){
            Bd_Log::warning("Error:[param error], Detail:[param: " . json_encode($arrConds) . "]");
            return false;
        }
        $Conds = array(
            'stu_uid'            => $arrConds['stu_uid'],
            'sale_uid'           => $arrConds['sale_uid'],
        );
        $arrFields = array(
            'intention'         => $arrConds['intention'],
        );
        $objStuConsultTaskClue = new Hkzb_Dao_Gnmis_StuConsultTaskClue();
        $res = $objStuConsultTaskClue->updateByConds($Conds,$arrFields);
        if (false === $res) {
            Bd_Log::warning('Error:[update db error], Detail:[role]');
        }

        return $res;
    }

    public function getUTypeName($uType)
    {
        if ($uType < 0) {
            Bd_Log::warning('error:[getutypename error], detail:[uType]');
            return false;
        }

        $title = '';
        if (array_key_exists($uType, self::$UTYPE_ARRAY)) {
           $title = self::$UTYPE_ARRAY[$uType];
        } else {
            return $title;
        }
        return $title;
    }

    public function editTaskClueById($id, $arrFields)
    {
        if ($id <= 0) {
            Bd_Log::warning('error:[getintentionname error], detail:[status]');
            return false;
        }

        $arrConds = [
            'id' => $id,
        ];

        $objStuConsultTaskClue = new Hkzb_Dao_Gnmis_StuConsultTaskClue();
        $res = $objStuConsultTaskClue->updateByConds($arrConds, $arrFields);
        if (false === $res) {
            Bd_Log::warning('Error:[edit taskclue byid error], Detail:[id]');
        }
        return $res;
    }

    /**
     * 批量获取销售指定时间分配用户数量
     * @param Array $saleUidArr,$Conds
     * @access public
     * @return Array |false
     */
    public function getSaleCntBySaleUidArr($saleUidArr,$Conds,$arrFields)
    {
        if (empty($saleUidArr) || empty($Conds)){
            Bd_Log::warning("Error:[param error], Detail:[param: " . json_encode($saleUidArr) . json_encode($Conds)."]");
            return false;
        }

        $saleUids = implode(",", $saleUidArr);
        $arrConds[] = "sale_uid in ({$saleUids})";
        $arrConds['bind_time'] = array($Conds['startTime'],'>',$Conds['endTime'],'<');
        $arrConds['stu_utype'] = $Conds['stuType'];

        if (empty($arrFields)){
            $Fields = array('count(1) as count,sale_uid');
        }else{
            $Fields = $arrFields;
        }

        $arrAppends = "group by sale_uid";
        $objStuConsultTaskClue = new Hkzb_Dao_Gnmis_StuConsultTaskClue();
        $res = $objStuConsultTaskClue->getListByConds($arrConds,$Fields,null,$arrAppends);
        if (false === $res) {
            Bd_Log::warning('Error:[get list error], Detail:[param: '. json_encode($saleUidArr) . json_encode($Conds).']');
        }

        return $res;
    }

    /**
     * 批量获取销售指定时间分配用户意向
     * @param Array $saleUidArr,$Conds
     * @access public
     * @return Array |false
     */
    public function getStuIntentionBySaleUidArr($saleUidArr,$Conds,$arrFields)
    {
        if (empty($saleUidArr) || empty($Conds)){
            Bd_Log::warning("Error:[param error], Detail:[param: " . json_encode($saleUidArr) . json_encode($Conds)."]");
            return false;
        }

        $saleUids = implode(",", $saleUidArr);
        $arrConds[] = "sale_uid in ({$saleUids})";
        $arrConds['bind_time'] = array($Conds['startTime'],'>',$Conds['endTime'],'<');

        if (empty($arrFields)){
            $Fields = array('*');
        }

        $objStuConsultTaskClue = new Hkzb_Dao_Gnmis_StuConsultTaskClue();
        $res = $objStuConsultTaskClue->getListByConds($arrConds,$Fields);
        if (false === $res) {
            Bd_Log::warning('Error:[get list error], Detail:[param: '. json_encode($saleUidArr) . json_encode($Conds).']');
        }

        return $res;
    }

    /**
     * 获取销售的所有分配用户数量
     * @param Array $saleUidArr
     * @access public
     * @return Array |false
     */
    public function getSaleAllCntBySaleUidArr($saleUidArr)
    {
        if (empty($saleUidArr)){
            Bd_Log::warning("Error:[param error], Detail:[param: " . json_encode($saleUidArr) ."]");
            return false;
        }

        $saleUids = implode(",", $saleUidArr);
        $arrConds[] = "sale_uid in ({$saleUids})";
        $arrConds[] = "bind_time>0";

        $Fields = array(
            'count(1) as allBindUser', // 历史绑定所有用户
            'sale_uid',
        );

        $arrAppends = "group by sale_uid";
        $objStuConsultTaskClue = new Hkzb_Dao_Gnmis_StuConsultTaskClue();
        $res = $objStuConsultTaskClue->getListByConds($arrConds,$Fields,null,$arrAppends);
        if (false === $res) {
            Bd_Log::warning('Error:[get list error], Detail:[param: '. json_encode($saleUidArr) . ']');
        }

        return $res;
    }

    /**
     * 获取销售时间段的数据信息
     * @param Array $saleUidArr
     * @access public
     * @return Array |false
     */
    public function getSaleBySaleUid($saleUid,$Conds)
    {
        if (empty($saleUid)){
            Bd_Log::warning("Error:[param error], Detail:[param: " . $saleUid ."]");
            return false;
        }

        $arrConds['sale_uid'] = $saleUid;
        $arrConds['bind_time'] = array($Conds['startTime'],'>',$Conds['endTime'],'<');
        $arrConds[] = "bind_time>0";

        $Fields = array('*');

        $objStuConsultTaskClue = new Hkzb_Dao_Gnmis_StuConsultTaskClue();
        $res = $objStuConsultTaskClue->getListByConds($arrConds,$Fields);
        if (false === $res) {
            Bd_Log::warning('Error:[get list error], Detail:[param: '. json_encode($saleUid) . ']');
        }

        return $res;
    }

    /**
     * 获取销售历史数据信息
     * @param Array $saleUidArr
     * @access public
     * @return Array |false
     */
    public function getSaleAllDataBySaleUid($saleUid)
    {
        if (empty($saleUid)){
            Bd_Log::warning("Error:[param error], Detail:[param: " . $saleUid ."]");
            return false;
        }

        $arrConds['sale_uid'] = $saleUid;
        $arrConds[] = "bind_time>0";

        $Fields = array('*');

        $objStuConsultTaskClue = new Hkzb_Dao_Gnmis_StuConsultTaskClue();
        $res = $objStuConsultTaskClue->getListByConds($arrConds,$Fields);
        if (false === $res) {
            Bd_Log::warning('Error:[get list error], Detail:[param: '. json_encode($saleUid) . ']');
        }

        return $res;
    }


    /**
     * 为到课短信获取课程对应的sc(已绑定/预绑定/已完成状态)
     * 绑定: status=1，预绑定: status=1&sale_uid>0
     * @param int $courseId
     * @return false|Array
     */
    public function getPrebindSaleInfoForSmsByCourseId($startTime,$courseId,$arrStudentId) {
        if (0 >= $courseId || empty($startTime) || empty($arrStudentId)) {
            return false;
        }
        Bd_Log::notice("laxinbf_class_send_sms_{$courseId}_{$startTime}" . json_encode($arrStudentId));
        $workStatus = [
            self::STATUS_BOUND,
            self::STATUS_TOASSIGN,
            self::STATUS_FINISH,
        ];
        $now = time();
        $status = implode(',', $workStatus);
        $strUids = implode(',', $arrStudentId);
        $arrConds = [
            "status in ({$status})",
            "{$now} < expire_time",
            "stu_uid in ($strUids)",
            'type' => 1,  //过滤公海例子
            'sale_uid > 0',
        ];
        $arrFields = Hkzb_Ds_Gnmis_StuConsultClue::$ALL_FIELDS_ARRAY;
        $objStuConsultClue = new Hkzb_Dao_Gnmis_StuConsultClue();
        $res = $objStuConsultClue->getListByConds($arrConds, $arrFields);
        if (false === $res) {
            Bd_Log::warning('Error:[saleSendSms getNewClue error], Detail:[role]');
            return false;
        }
        if ($res) {
            $objSale = new Hkzb_Ds_Gnmis_StuConsultSales();
            $sendLog = [];
            foreach ($res as $key => $val) {
                $saleInfo = $objSale->getSaleByUid($val['sale_uid']);
                if ($saleInfo) {
                    $sendMessage = [];
                    $sendMessage[] = $startTime;
                    $sendMessage[] = $saleInfo['petName'] . '老师';
                    $sendMessage[] = $saleInfo['phone'];
                    $ret = Hk_Service_SmsCommon::sendSmsByTemplateId($val['stu_phone'], $sendMessage, 810);
                    $sendMessage[] = $val['stu_phone'];
                    $sendLog[] = $sendMessage;
                    if (false == $ret) {
                        Bd_Log::warning("send_bf_class_30_error,detail:{" . json_encode($sendMessage) . $val['stu_phone'] . "}");
                    }
                }
            }
            Bd_Log::notice("bf_class_send_sms_{$courseId}" . json_encode($sendLog));
        }
    }

    /**
     * getSaleByStuCourseId
     *
     * 获取人课对应的销售
     * @param int $stuUid
     * @param int $courseId
     * @param Array $arrFields
     * @access public
     * @return Array
     */
    public function getSaleByStuCourseId($stuUid, $courseId, $arrFields = [])
    {
        $stuUid   = (int) $stuUid;
        $courseId = (int) $courseId;
        if ($stuUid <= 0 || $courseId <= 0) {
            Bd_Log::warning("Error:[getsalebystucourseid_error], Detail:[stuUid:{$stuUid},courseId:{$courseId}]");
            return false;
        }

        $arrConds = [
            'stuUid'   => $stuUid,
            'courseId' => $courseId,
            'status IN('.self::STATUS_TOASSIGN.','.self::STATUS_BOUND.','.self::STATUS_FINISH.')',
        ];

        if (empty($arrFields)) {
            $arrFields = self::$ALL_FIELDS_ARRAY;
        }

        $objStuConsultTaskClue = new Hkzb_Dao_Gnmis_StuConsultTaskClue();
        $res = $objStuConsultTaskClue->getRecordByConds($arrConds, $arrFields);
        if (false === $res) {
            Bd_Log::warning("Error:[getsalebystucourseid_error], Detail:[stuUid:{$stuUid},courseId:{$courseId}]");
        }
        return $res;
    }

    // sc分配引擎
    public function getScUidByStuCourseId($stuUid, $courseId)
    {
        $stuUid   = (int) $stuUid;
        $courseId = (int) $courseId;
        if ($stuUid <= 0 || $courseId <= 0) {
            Bd_Log::warning("Error:[getScUidByStuCourseId, Detail:[stu:{$stuUid},cid:{$courseId}]]");
            return false;
        }

        // 是否已经分配(面向端微信)
        $taskInfo = $this->getSaleByStuCourseId(
            $stuUid,
            $courseId, [
                'saleUid'
            ]
        );

        $scUid = 0;
        // 已存在
        if ($taskInfo) {
           $scUid = (int) $taskInfo['saleUid'];
           return $scUid;
        }

        // 销售配课信息
        $objSchedules = new Hkzb_Ds_Gnmis_StuCourseSchedules();
        $range = $objSchedules->getSalesListByCourseId($courseId);
        if (!$range) {
            // 当前课程未配课
            return 0;
        }

        // redis-gnmis
        $redisConf = Bd_Conf::getConf("/hk/redis/gnmis");
        if (!$redisConf) {
            Bd_Log::warning("Error[getReids] Detail[no redis conf]");
            return false;
        }

        $bindKey = $courseId . '_' . $stuUid;
        $objRedis = new Hk_Service_Redis($redisConf['service']);
        $scUid = (int) $objRedis->hget(
            self::SCMIS_HASH_SC_ALLOC,
            $bindKey
        );

        if ($scUid && in_array($scUid, $range)) {
            // 缓存 && 未离职
            return $scUid;
        }

        // 销售绑定分布情况
        $saleDis = $objRedis->hget(
            self::SCMIS_HASH_SALE_BIND,
            $courseId
        );

        // 分布情况初始化
        if (!$saleDis) {
            $key = array_rand($range, 1);
            $scUid = $range[$key];

            $saleDis = [
                $scUid => time(),
            ];
        } else {
            // 历史分布情况
            $saleDis = json_decode($saleDis, true);
            if (empty($saleDis)) {
                Bd_Log::warning('choseScuidErr:' . $saleDis);
                return 0;
            }
            // 剔除已被删除的
            $tmpSaleDis = [];
            foreach ($saleDis as $scUid => $scDispTime) {
                if (in_array($scUid, $range)) {
                    $tmpSaleDis[$scUid] = $scDispTime;;
                }
            }
            $saleDis = $tmpSaleDis;
            // 历史已分配
            $sales = array_keys($saleDis);

            // 新销售入职加入分配组
            $newSales = array_diff($range, $sales);
            if (!empty($newSales)) {
                // 追加分配(新销售分配时间为0)
                $saleDis += array_fill_keys($newSales, 0);
            }

            // 时间升序
            asort($saleDis);
            // 获取第一个销售
            $scUid = key($saleDis);
            // 更新时间权重
            $saleDis[$scUid] = time();
        }

        $saleDis = json_encode($saleDis);
        // 上次分配时间(目的:平均, 伪随机)
        $objRedis->hset(
            self::SCMIS_HASH_SALE_BIND,
            $courseId,
            $saleDis
        );

        // 绑定情况
        $objRedis->hset(
            self::SCMIS_HASH_SC_ALLOC,
            $bindKey,
            $scUid
        );
        return $scUid;
    }

    /**
     * @根据流转粒子id来批量线索
     * @param array $flowId 粒子流转id
     * @param unknown $arrFields
     * @return boolean| array
     */
    public function getClueByFlowIds($flowIds, $arrFields = [])
    {
        $flowIds = implode(',', $flowIds);
        $arrConds = [
            "flow_id in ({$flowIds})",
        ];
        if (empty($arrFields)) {
            $arrFields = self::$ALL_FIELDS_ARRAY;
        }
        $objClue =  new Hkzb_Dao_Gnmis_StuConsultTaskClue();
        $res = $objClue->getListByConds($arrConds, $arrFields);
        if (false === $res) {
            Bd_Log::warning('Error[getClueByFlowIds] Abstract[dbError] Detail[db error]');
            return false;
        }
        return $res;
    }
}
