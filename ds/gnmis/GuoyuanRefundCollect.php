<?php

/**
 * @file: GuoyuanRefundCollect.php
 * @desc: 直播退款汇总
 * @date: 2018年5月3日 下午5:39:20
 * @author: l<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
 */

class Hkzb_Ds_Gnmis_GuoyuanRefundCollect
{
    const STATUS_WAITREADY   = 1; //等待退款
    const STATUS_HASREADY    = 2; //退款准备完毕
    const STAUT_REFUNDDOING  = 3; //退款中
    const STAUT_REFUNDFINISH = 4; //退款成功
    
    private $_objGuoyuanRefundCollect;
    
    public function __construct(){
        $this->_objGuoyuanRefundCollect = new Hkzb_Dao_Gnmis_GuoyuanRefundCollect();
    }
    
    /**
     * 获取退款请求id
     * @return int
     */
    public static function getRefundRequestNo() {
        $objIdAlloc = new Hkzb_Service_IdAlloc(Hkzb_Service_IdAlloc::NAME_PAY_REFUND_ORDER_ID);
        $requestNo = $objIdAlloc->getIdAlloc();
        if(false === $requestNo) {
            Bd_Log::warning("Error:[idalloc error], Detail:[name:pay_refund_order_id]");
            return false;
        }
        return $requestNo;
    }
    
    /**
    * @desc: 新增汇总退款数据
    * @date: 2018年5月4日 上午10:53:14
    * @author: liangjian <<EMAIL>>
    * @param: $arrParams array
    * @return:    id |false
    */
    public function addNewRefundCollectData($arrParams)
    {
        if (empty($arrParams) || !is_array($arrParams)){
            Bd_Log::warning("[error] [param error] [detail:the arrParams is empty]");
            return false;
        }

        //插入数据
        $arrFields = array(
            'refundRecordId' => $arrParams['refundRecordId'],
            'orderUid'       => isset($arrParams['orderUid']) ? $arrParams['orderUid'] : 0,
            'source'         => isset($arrParams['source']) ? $arrParams['source'] : '',
            'packOrderIds'   => isset($arrParams['packOrderIds']) ? json_encode($arrParams['packOrderIds']) : '',
            'packChargeIds'  => isset($arrParams['packChargeIds']) ? json_encode($arrParams['packChargeIds']) : '',
            'orderDetail'    => isset($arrParams['orderDetail']) ? json_encode($arrParams['orderDetail']) : '',
            'updateTime'     => time(),
            'createTime'     => time(),
            'refundFee'      => isset($arrParams['refundFee']) ? $arrParams['refundFee'] : 0,
            'status'         => isset($arrParams['status']) ? $arrParams['status'] : 0,
            'deleted'        => isset($arrParams['deleted']) ? $arrParams['deleted'] : 0,
            'extBit'         => isset($arrParams['extBit']) ? $arrParams['extBit'] : 0,
            'extData'        => isset($arrParams['extData']) ? json_encode($arrParams['extData']) : '[]',
        );
        $ret       = $this->_objGuoyuanRefundCollect->insertRecords($arrFields);
        if ($ret === false) {
            return false;
        }
        
        return $arrParams['refundRecordId'];
    }
    
    /**
     * @desc: 更新汇总退款数据
     * @date: 2018年5月4日 上午10:53:14
     * @author: liangjian <<EMAIL>>
     * @param: $id,$arrFields array
     * @return:    id |false
     */
    public function updateRefundCollectByConds($id,$arrParam)
    {
        if (empty($id) || empty($arrParam)){
            Bd_Log::warning("[error] [param error] [detail:the arrParams is empty]");
            return false;
        }
        
        $arrConds     = array('refundRecordId' => intval($id));
        $arrAllFields = Hkzb_Dao_Gnmis_GuoyuanRefundCollect::$allFields;
        foreach ($arrParam as $key => $value) {
            if (!in_array($key, $arrAllFields)) {
                continue;
            }
        
            $arrFields[$key] = $value;
        }
        
        //ext由ps补全，json处理在ds
        if (isset($arrFields['packOrderIds'])) {
            $arrFields['packOrderIds'] = json_encode($arrFields['packOrderIds']);
        }
        if (isset($arrFields['packChargeIds'])) {
            $arrFields['packChargeIds'] = json_encode($arrFields['packChargeIds']);
        }
        if (isset($arrFields['orderDetail'])) {
            $arrFields['orderDetail'] = json_encode($arrFields['orderDetail']);
        }
        if (isset($arrFields['extData'])) {
            $arrFields['extData'] = json_encode($arrFields['extData']);
        }
        
        $ret = $this->_objGuoyuanRefundCollect->updateByConds($arrConds, $arrFields);
        
        return $ret;
    }
    
    /**
    * @desc: 获取汇总退款数据
    * @date: 2018年5月4日 下午5:14:23
    * @author: liangjian <<EMAIL>>
    * @param: $id
    * @return:   array |false
    */
    public function getRefundRecordByRefundId($refundId){
        if (empty($refundId)){
            Bd_Log::warning("[error] [param error] [get refundId fail,param is empty]");
            return false;
        }
        
        $arrConds = array(
            'refundRecordId'          => $refundId,
        );
        $arrFields = Hkzb_Dao_Gnmis_GuoyuanRefundCollect::$allFields;
        
        $ret = $this->_objGuoyuanRefundCollect->getRecordByConds($arrConds, $arrFields);
        
        return $ret;
    }
    
    /**
     * @desc: 获取汇总退款数据
     * @date: 2018年5月4日 下午5:14:23
     * @author: liangjian <<EMAIL>>
     * @param: $arrConds ,$arrFields
     * @return:   array |false
     */
    public function getRefundRecordByConds($arrConds,$arrFields){
        if (empty($arrConds)){
            Bd_Log::warning("[error] [param error] [get arrConds fail,param is empty]");
            return false;
        }
    
        if (empty($arrFields)){
            $arrFields = Hkzb_Dao_Gnmis_GuoyuanRefundCollect::$allFields;
        }
    
        $ret = $this->_objGuoyuanRefundCollect->getListByConds($arrConds, $arrFields);
    
        return $ret;
    }
}
