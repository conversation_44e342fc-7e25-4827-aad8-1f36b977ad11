<?php
/**
 * 帮帮英语版本控制
 * User: wang<PERSON><PERSON>@zuoyebang.com
 * Date: 2019/5/17
 * Time: 17:55
 */
class Hkzb_Ds_Xeng_VersionControl {

    const APP_TYPE_IOS          = 'ios';
    const APP_TYPE_ANDROID      = 'android';
    const APP_ID_HOMEWORK       = 'homework'; // 作业帮APP
    const APP_ID_AIR_CLASS      = 'airclass'; // 一课APP

    /*
     * @desc 作业帮语音SDK与先声SDK切换
     * @param $appId
     * @param $appType
     * @param $zbkvc  作业帮版本号
     * @param $ykvc   一课版本号
     * @return true/false
     */
    public static function getAudioSdkSelectorOpenFlag($appId='', $os='', $zbkvc = 0, $ykvc = 0) {
        // 版本控制由前端控制，后端放开
        return true;

        if (empty($appId) || empty($os)) {
            Bd_Log::warning("invalid params,appId[$appId],os[$os]");
            return false;
        }

        // android
        if ($appId == self::APP_ID_HOMEWORK && $os == self::APP_TYPE_ANDROID && $zbkvc >= 85) {
            return true;
        }

        // ios
        if ($appId == self::APP_ID_HOMEWORK && $os == self::APP_TYPE_IOS && $zbkvc >= 85) {
            return true;
        }

        return false;
    }

}