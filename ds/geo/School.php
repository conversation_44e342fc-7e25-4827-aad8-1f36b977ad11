<?php

/**
 * Class Lxjxlib_Ds_Geo_School
 * 学校地理位置
 * <AUTHOR>
 * @property Redis $objRedis
 */
class Lxjxlib_Ds_Geo_School extends Lxjxlib_Ds_BaseData
{
    // 搜索的字段
    const SEARCH_FIELDS = [
        "id",
        "schoolId",
        "schoolName",
        "namePya",
        "provinceId",
        "provinceName",
        "cityId",
        "cityName",
        "countyId",
        "countyName",
        "longitude",            //经度
        "latitude"              //纬度
    ];

    // 学校经纬度坐标缓存
    const CACHE_LXJXSCHOOL_SCHOOL_LIST_GEOHASH = 'lxjx_school_school_list_geohash';

    //状态
    const NORMAL_STATUS = 1;

    //学校表
    protected $daoClassName = 'Lxjxlib_Dao_Crm_SchoolCustomer';
    private   $objRedis;

    public function __construct()
    {
        parent::__construct();
        $this->objRedis     = Hk_Service_RedisClient::getInstance(Lxjxlib_Const_Cache::REDIS_NAME_LXJX);
    }

    /**
     * 批量获取学校经纬度信息
     * @param $schoolIds
     * @param bool $isCheckConnStatus
     * @return array
     */
    public function multiGetSchoolGeo($schoolIds, $isCheckConnStatus = false)
    {
        $r = [];
        if(empty($schoolIds)){
            return $r;
        }

        //断开重连
        if($isCheckConnStatus){
            $this->checkConnStatus();
        }

        array_unshift($schoolIds, self::CACHE_LXJXSCHOOL_SCHOOL_LIST_GEOHASH);
        $list = call_user_func_array([$this->objRedis, 'geopos'], $schoolIds);
        if(empty($list)){
            return $r;
        }
        array_shift($schoolIds);
        foreach ($schoolIds as $key => $schoolId){
            if(!empty($list[$key])){
                $r[$schoolId] = [
                    'longitude' => $list[$key][0],
                    'latitude'  => $list[$key][1],
                ];
            }
        }

        return $r;
    }

    /**
     * 批量添加数据
     * @param $data
     * @return int|mixed
     */
    public function multiAddSchoolGeo($data, $isCheckConnStatus = false)
    {
        $count = 0;
        if(empty($data)){
            return $count;
        }

        $params[] = self::CACHE_LXJXSCHOOL_SCHOOL_LIST_GEOHASH;

        //拼接参数
        foreach ($data as $item){
            if(isset($item['longitude']) && isset($item['latitude'])){
                $params[] = $item['longitude'];
                $params[] = $item['latitude'];
                $params[] = $item['schoolId'];
            }
        }

        if($isCheckConnStatus){
            $this->checkConnStatus();
        }

        $count = call_user_func_array([$this->objRedis, "geoadd"], $params);
        return $count;
    }

    /**
     * 获取附近的学校列表
     * @param $longitude  经度
     * @param $latitude   纬度
     * @param $distince int  距离
     * @param string $company 单位 [km,m]
     * @param int $count 查询条数
     * @return array
     */
    public function getNearbySchoolList($longitude, $latitude, $distince, $company = 'km', $count = 100)
    {
        $options = [
            'count' => $count,
            'WITHDIST',         //返回距离
            'ASC'               //距离由近到远排序
        ];

        $r = $this->objRedis->georadius(
            self::CACHE_LXJXSCHOOL_SCHOOL_LIST_GEOHASH,
            $longitude,
            $latitude,
            $distince,
            $company,
            $options
        );
        $r = (is_array($r) && !empty($r)) ? $r : [];

        //返回值处理
        return array_reduce($r, function($schools, $item){
            $schools[$item[0]] = $item[1];
            return $schools;
        }, []);
    }

    /**
     * 检测KEY是否存在
     * @return bool
     */
    public function existsGeo()
    {
        return $this->objRedis->exists(self::CACHE_LXJXSCHOOL_SCHOOL_LIST_GEOHASH);
    }

    //断开重连
    private function checkConnStatus(){
        $status = $this->objRedis->ping();
        if($status != "+PONG" || empty($status)){
            $this->objRedis = Hk_Service_RedisClient::getInstance(Lxjxlib_Const_Cache::REDIS_NAME_LXJX);
        }
    }
}