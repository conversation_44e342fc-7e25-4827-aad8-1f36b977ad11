<?php
/**
 * @file FillBlank.php.
 * <AUTHOR>
 * @date: 2018/11/1
 */
//出参：
//'tid1' =>array(
//    'correct' => 1,//0错误，1正确，2半对半错
//    'detail'  => array(
//        array('correctRet'=>'1小题批改结果','stuAnswer'=> '1学生答案', 'rightAnswer'=> '1正确答案'),
//        array('correctRet'=>'2小题批改结果','stuAnswer'=> '2学生答案', 'rightAnswer'=> '2正确答案'),
//    ),
//);

class Hkzb_Ds_ZbtikuApi_Judge_Category_FillBlank implements Hkzb_Ds_ZbtikuApi_Judge_Category_Base_CategoryBase
{
    public  $_dbQuestionInfo;
    public  $_arrStuAnswerInfo;
    private $_arrOriStuAnswer;
    private $_dbArrBlanksInfo;
    private $_dbFormatAnswer;
    private $_arrOutput;
    private $_arrDetail;
    private $_objFormat;

    public function __construct()
    {
        $this->_objFormat = new Hkzb_Ds_ZbtikuApi_Judge_Formatter_PreviewFormat();
    }

    public function Judge()
    {
        $this->_getDbAnswerInfo('latex');
        $this->_formatDbAnswer();
        $this->_formatStuAnswer();

        $logInfo  = array();
        $rightCnt = 0;
        foreach ($this->_arrStuAnswerInfo as $key=>$val){
            $littleCorrect = Hkzb_Ds_ZbtikuApi_Const_Judge::SUBJECT_RESULT_WRONG;
            if(isset($val) && in_array($val , $this->_dbFormatAnswer[$key]) ) {
                $rightCnt++;
                $littleCorrect = Hkzb_Ds_ZbtikuApi_Const_Judge::SUBJECT_RESULT_RIGHT;
            }
            $this->_arrDetail[] = array(
                'stuAnswer'    => $this->_arrOriStuAnswer[$key], //学生答题数据
                'rightAnswer'  => $this->_dbArrBlanksInfo[$key],//正确答案
                'correctRet'   => $littleCorrect, //批改结果
            );

            $logInfo['detail'][] = array(
                'littleCorrect'  => $littleCorrect,
//                'oriStuAnswer'   => $this->_arrOriStuAnswer[$key],
                'stuFormatAfter' => $val,
                'dbFormatAfter'  => $this->_dbFormatAnswer[$key],
//                'dbAnswer'       => $this->_dbArrBlanksInfo[$key],
            );
        }

        $corRet = ($rightCnt == count($this->_dbArrBlanksInfo))
            ? Hkzb_Ds_ZbtikuApi_Const_Judge::SUBJECT_RESULT_RIGHT
            : Hkzb_Ds_ZbtikuApi_Const_Judge::SUBJECT_RESULT_WRONG;

        $logInfo['correctRet'] = $corRet;
        Bd_Log::addNotice($this->_dbQuestionInfo['tid'].'_fillBlank',json_encode($logInfo,JSON_UNESCAPED_UNICODE));

        $this->_arrOutput = array($corRet);

        return $this->_arrOutput;
    }

    public function getDetailInfo() {
        return $this->_arrDetail;
    }

    /**
     * @param string $formatter
     * @return $this
     * @throws Zbtikuapi_Exception
     */
    private function _getDbAnswerInfo($formatter = 'latex')
    {
        if (empty($this->_dbQuestionInfo['question']['blanks'])
            && empty($this->_dbQuestionInfo['latexQuestion']['blanks'])) {
            Bd_Log::warning('fillBlank param error db_questionInfo_'.json_encode($this->_dbQuestionInfo));
            throw new Zbtikuapi_Exception(Zbtikuapi_ExceptionCodes::PARAM_ERROR);
        }

        switch ($formatter){
            case 'svg':
                $this->_dbArrBlanksInfo = $this->_dbQuestionInfo['question']['blanks'];
                break;
            case 'latex':
                if (empty($this->_dbQuestionInfo['latexQuestion']['blanks'])){
                    Bd_Log::Warning('fillBlank dbLatexInfo error');
                    $this->_dbArrBlanksInfo = $this->_dbQuestionInfo['question']['blanks'];
                }else{
                    $this->_dbArrBlanksInfo = $this->_dbQuestionInfo['latexQuestion']['blanks'];
                }
                break;
            default:
                $this->_dbArrBlanksInfo = $this->_dbQuestionInfo['question']['blanks'];
                break;
        }

        if (!is_array($this->_dbArrBlanksInfo)){
            Bd_Log::warning('fillBlank param error dbLatexInfo_error_'.json_encode($this->_dbQuestionInfo));
            throw new Zbtikuapi_Exception(Zbtikuapi_ExceptionCodes::PARAM_ERROR);
        }

        return $this;
    }

    private function _formatDbAnswer()
    {
        foreach ($this->_dbArrBlanksInfo as $idx=>$blankInfo){
            foreach ($blankInfo as $val){
                $val = $this->_objFormat->formatter($val, array('cnToEn', 'latex', 'clearBoth', 'similarFormula'));
                $this->_dbFormatAnswer[$idx][] = $val;
            }
        }
    }

    private function _formatStuAnswer()
    {
        foreach ($this->_dbArrBlanksInfo as $idx=>$blankInfo){
            $this->_arrStuAnswerInfo[] = isset($this->_arrOriStuAnswer[$idx]) ?
                $this->_objFormat->formatter($this->_arrOriStuAnswer[$idx], array('cnToEn', 'latex', 'clearBoth', 'similarFormula'))
                : '';
        }
    }

    /**
     * @param $arrStuAnswer
     * @return $this
     */
    public function setStudentAnswer($arrStuAnswer)
    {
        $this->_arrOriStuAnswer = $arrStuAnswer;
        return $this;
    }

    /**
     * @param $tidList
     * @return $this
     */
    public function setQuestionInfo($questionInfo)
    {
        $this->_dbQuestionInfo = $questionInfo;
        return $this;
    }
}