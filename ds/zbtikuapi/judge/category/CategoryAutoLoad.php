<?php

/**
 * 方法生成器
 */
class Hkzb_Ds_ZbtikuApi_Judge_Category_CategoryAutoLoad  {

    private $category; //题型标识
    public static $arrClassNameMap = array(
        Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_MULTI_FILL_BLANK => 'Hkzb_Ds_ZbtikuApi_Judge_Category_MultiFillBlank',
        Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_SINGLE           => 'Hkzb_Ds_ZbtikuApi_Judge_Category_SingleChoose',
        Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_MULTI_TYPE_DRAG  => 'Hkzb_Ds_ZbtikuApi_Judge_Category_MultiTypeDrag',
        Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_FILL_BLANK       => 'Hkzb_Ds_ZbtikuApi_Judge_Category_FillBlank',
        Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_MULTIPLE         => 'Hkzb_Ds_ZbtikuApi_Judge_Category_MultiChoose',
        Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_JUDGMENT         => 'Hkzb_Ds_ZbtikuApi_Judge_Category_Judgement',
        Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_BLANKS           => 'Hkzb_Ds_ZbtikuApi_Judge_Category_Blanks',
        Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_READING          => 'Hkzb_Ds_ZbtikuApi_Judge_Category_Reading',
        Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_NEW_READING      => 'Hkzb_Ds_ZbtikuApi_Judge_Category_NewReading',
        Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_NEW_BLANKS       => 'Hkzb_Ds_ZbtikuApi_Judge_Category_NewBlanks',
//        Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_READING_JUDGMENT => 'Hkzb_Ds_ZbtikuApi_Judge_Category_ReadingJudgment',
        Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_MAKE_SENTENCE    => 'Hkzb_Ds_ZbtikuApi_Judge_Category_MakeSentence',
        Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_MAKE_WORD        => 'Hkzb_Ds_ZbtikuApi_Judge_Category_MakeWord',
        Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_LISTEN_CHOOSE    => 'Hkzb_Ds_ZbtikuApi_Judge_Category_Reading',
        Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_BANKED_CLOZE     => 'Hkzb_Ds_ZbtikuApi_Judge_Category_BankedCloze',
        Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_READING_REDUCT   => 'Hkzb_Ds_ZbtikuApi_Judge_Category_ReadingReduct',
        Hkzb_Ds_Homework_Homework::SUBJECT_TYPE_SYNTHESIS        => 'Hkzb_Ds_ZbtikuApi_Judge_Category_Synthesis',//综合题

        //阿拉丁批改
        Hkzb_Ds_Homework_Homework::SUBJECT_ALADDIN_SINGLE_CHOOSE      => 'Hkzb_Ds_ZbtikuApi_Judge_Category_SingleChoose',
        Hkzb_Ds_Homework_Homework::SUBJECT_ALADDIN_COMPOSE_WOAD       => 'Hkzb_Ds_ZbtikuApi_Judge_Category_FillBlank',
        Hkzb_Ds_Homework_Homework::SUBJECT_ALADDIN_COMPOSE_STATEMENT  => 'Hkzb_Ds_ZbtikuApi_Judge_Category_FillBlank',
        Hkzb_Ds_Homework_Homework::SUBJECT_ALADDIN_LISTEN_AND_CHOICE  => 'Hkzb_Ds_ZbtikuApi_Judge_Category_Reading',
        Hkzb_Ds_Homework_Homework::SUBJECT_ALADDIN_READING            => 'Hkzb_Ds_ZbtikuApi_Judge_Category_Reading',
        Hkzb_Ds_Homework_Homework::SUBJECT_ALADDIN_JUDGE              => 'Hkzb_Ds_ZbtikuApi_Judge_Category_Reading',
        Hkzb_Ds_Homework_Homework::SUBJECT_ALADDIN_COMPOSE_WORD_GROUP => 'Hkzb_Ds_ZbtikuApi_Judge_Category_FillBlank',
        Hkzb_Ds_Homework_Homework::SUBJECT_ALADDIN_CLOZE_TEST         => 'Hkzb_Ds_ZbtikuApi_Judge_Category_Reading',
        Hkzb_Ds_Homework_Homework::SUBJECT_ALADDIN_LISTEN_CHOOSE      => 'Hkzb_Ds_ZbtikuApi_Judge_Category_Reading',

        //帮帮英语低幼批改
        Hkzb_Ds_Homework_Homework::SUBJECT_ALADDIN_KIDS_LISTEN_AND_CHOICE => 'Hkzb_Ds_ZbtikuApi_Judge_Category_SingleChoose',

        //鸭鸭语文批改
        Hkzb_Ds_Homework_Homework::SUBJECT_TODDLING_SINGLE                  => 'Service_Data_Category_SingleChoose',
        Hkzb_Ds_Homework_Homework::SUBJECT_TODDLING_MULTIPLE                => 'Service_Data_Category_MultiChoose',
    );

    public function setCategory($category) {
        $this->category = $category;
        return $this;
    }

    public function makeCategoryObj() {
        if(!$this->category) {
            return false;
        }
        if(!isset(self::$arrClassNameMap[$this->category]))
        {
            return false;
        }
        return new self::$arrClassNameMap[$this->category]();
    }
}