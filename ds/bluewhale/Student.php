<?php

/**
 * @file Month.php
 * <AUTHOR>
 * @date 2017-11-4
 * @brief 获取章节数据
 *
 * */
class Hkzb_Ds_Bluewhale_Student {

    const API_TOKEN = 'F94D9B1F7EFF0051442C6FD1B3A34175';

    public function __construct() {
        $arrConf = Bd_Conf::getConf('hk/bluewhale/bos');
        $this->_bluewhale = $arrConf;
    }

    /**
     * 获取学生章节作业情况
     * @param $studentUids
     * @param array $lessonIds
     */
    public static function getStudentHomework($studentUids, $lessonIds = array()) {

        $arrHeader = array(
            'cookie' => $_COOKIE,
            'pathinfo' => '/bluewhale/api/gethomework',
            'referer' => '/assistantdesk/'
        );

        if (!$studentUids || !is_array($studentUids) || ($lessonIds && !is_array($lessonIds))) {
            return false;
        }

        $arrParams['uid'] = json_encode(array_values($studentUids));
        if ($lessonIds) {
            $arrParams['lessonId'] = json_encode((array_values($lessonIds)));
        }
        $arrParams['token'] = self::API_TOKEN;

        $ret = ral('bluewhale', 'POST', $arrParams, 123, $arrHeader);
        Bd_Log::debug("URI:" . $arrHeader['pathinfo'] . ";Param:" . json_encode($arrParams) . ";response:" . $ret);

        if (false === $ret) {
            $errNo = ral_get_errno();
            $errMsg = ral_get_error();
            $protocolStatus = ral_get_protocol_code();
            Bd_Log::warning("Error[getStudentHomework ral connect error] Detail[errno:$errNo errmsg:$errMsg protocol_status:$protocolStatus params:" . json_encode($arrParams) . "]");
            return false;
        }

        $ret = json_decode($ret, true);
        if (!isset($ret['errNo']) || (isset($ret['errNo']) && $ret['errNo'] != 0 )) {
            Bd_Log::warning("Error[getStudentHomework ] Detail[ errNo:{$ret['errNo']} params:" . json_encode($arrParams) . "]");
            return false;
        }

        return $ret['data'];
    }

}
