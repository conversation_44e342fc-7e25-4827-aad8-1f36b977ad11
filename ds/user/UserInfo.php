<?php


/**
 * @befie   获取用户信息基础方法
 * @file    ds/user/UserInfo.php
 * <AUTHOR>
 * @version 1.0
 * @since
 * @date    2019/10/19 16:28
 */
class Lxjxlib_Ds_User_UserInfo
{
    private $redis;
    private $userInfoDao;
    private static $userData = [];
    private $userRegisterDao;

    public function __construct()
    {
        $this->redis = Hk_Service_RedisClient::getInstance(Lxjxlib_Const_Cache::REDIS_NAME_LXJX_USER);
        $this->userInfoDao = new Lxjxlib_Dao_User_UserInfo();
        $this->userRegisterDao = new Lxjxlib_Dao_User_UserRegister();
    }

    private function cacheKey($uid)
    {
        $key = sprintf(Lxjxlib_Const_Cache::RK_USER_INFO, $uid);
        return $key;
    }

    /**
     * $uid 可以是整型，可以是数组。返回值根据参数不同，可能是一维数组，可能是二维数组。
     * 获取用户基本数据，尽量用这个。逐步解除ext依赖.
     * @param $uid
     * @param bool $useCache
     * @return array|mixed
     */
    public function getUserInfo($uid, $useCache = true)
    {
        $single = false;
        if (is_scalar($uid)) {
            $single = true;
            $uid = [$uid];
        }
        $data = $this->getUserInfoByArray($uid, $useCache);
        // 如果是取单个用户信息，返回一维数组
        if ($single) {
            return array_pop($data);
        }
        return $data;
    }

    /**
     * 用uid数组获取用户基本数据
     * @param $arrUids
     * @param bool $useCache
     * @return array
     */
    public function getUserInfoByArray($arrUids, $useCache = true)
    {
        $arrOutput = [];
        if (empty($arrUids)) {
            return $arrOutput;
        }

        // 用cache一定要注意返回值不能超过65536字节，估的20，需要验证
        if (count($arrUids) <= 20 && $useCache) {
            $cache = $this->getUserCacheByArray($arrUids);
            $leftUids = [];
            foreach ($cache as $uid => $one) {
                if (!empty($one)) {
                    // 设置static缓存，避免重复取
                    if (count(self::$userData) < 1000) {
                        self::$userData[$uid] = $one;
                    }
                    $arrOutput[$uid] = $one;
                } else {
                    $leftUids[] = $uid;
                }
            }
        } else {
            $leftUids = $arrUids;
        }

        // 在cache里全部取到数据，就不用往下走了
        if (empty($leftUids)) {
            return $arrOutput;
        }

        $more = $this->getDaoUserInfoByArray($leftUids);
        if (empty($more)) {
            return $arrOutput;
        }

        foreach ($more as $one) {
            $this->setUserCache($one['uid'], $one);
            $arrOutput[$one['uid']] = $one;
        }
        return $arrOutput;
    }


    /**
     * 批量获取cache数据
     *
     * @param array $uids
     * @return array
     */
    private function getUserCacheByArray($uids)
    {
        $data = array();
        foreach ($uids as $uid) {
            if (isset(self::$userData[$uid])) {
                $data[$uid] = self::$userData[$uid];
            } else {
                $leftUids[] = $uid;
            }
        }
        if (empty($leftUids)) {
            return $data;
        }

        $keys = [];
        foreach ($leftUids as $leftUid) {
            $keys[] = $this->cacheKey($leftUid);
        }
        $ret = $this->redis->mGet($keys);
        if (false === $ret || empty($ret) || !is_array($ret)) {
            Bd_Log::warning('userinfo cache mget failed, uids: ' . json_encode($uids));
            return $data;
        }

        foreach ($ret as $seq => $dt) {
            $uid = $leftUids[$seq];
            if (false !== $dt) {
                $data[$uid] = Lxjxlib_Util_Json::decode($dt);
            } else {
                $data[$uid] = [];
            }
        }
        return $data;
    }

    /**
     * 设置用户cache
     * @param $uid
     * @param $user
     * @return bool
     */
    private function setUserCache($uid, $user)
    {
        if (empty($user)) {
            return false;
        }

        $user['exist'] = 1;
        if (count(self::$userData) < 1000) {
            self::$userData[$uid] = $user;
        }

        $ret = $this->redis->setEx($this->cacheKey($uid), Lxjxlib_Const_Cache::RK_EXPIRE_USERINFO, Lxjxlib_Util_Json::encode($user));
        if (false === $ret) {
            Hk_Util_Log::setLog('userinfo_redis_set_fail', $uid);
            return false;
        }
        Hk_Util_Log::setLog('userinfo_redis_set_ok', $uid);
        return $ret;
    }

    /**
     * 从数据库获取用户数据
     * @param array $uids
     * @return array
     */
    private function getDaoUserInfoByArray($uids)
    {
        $arrOutput = [];
        foreach ($uids as $uid) {
            $arrConds = [
                'uid' => $uid,
            ];
            $arrUserInfo = $this->userInfoDao->getRecordByConds($uid, $arrConds, $this->userInfoDao->getAllFields());
            if (!empty($arrUserInfo)) {
                $arrUserInfo['phone'] = Lxjxlib_Util_Tools::decodePhone($arrUserInfo['phoneCrypt']);

                $arrOutput[] = $arrUserInfo;
            }
        }
        return $arrOutput;
    }

    public function getAllUserInfoByTime($startTime, $endTime)
    {
        $ret = $this->userInfoDao->getAllUserInfoByTime($startTime, $endTime);
        return $ret;
    }

    /**
     * 获取用户手机号对应的uid
     * @param $phone
     * @param bool $force
     * @return array|false
     */
    public function getUidByPhone($phone, $force = false)
    {
        if ($force) {
            $this->userRegisterDao->startTransaction();
            $uid = $this->userRegisterDao->getUidByPhone($phone);
            $this->userRegisterDao->commit();
        } else {
            $uid = $this->userRegisterDao->getUidByPhone($phone);
        }
        return $uid;
    }
}
