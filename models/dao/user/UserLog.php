<?php

/**
 * @befie   钉钉用户表
 * @file    models/dao/user/DdUser.php
 * <AUTHOR>
 * @version 1.0
 * @date    2020-03-09
 */
class Dao_User_UserLog extends Lxjxlib_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName      = Lxjxlib_Const_Db::DB_LXJX_USERLOG;
        $this->_table       = 'tblUserLog';
        $this->_logFile     = Lxjxlib_Const_Db::DBLOG_LXJX;
        $this->arrFieldsMap = [
            'id'                => 'id',
            'timeConsuming'     => 'time_consuming',
            'urlPath'           => 'url_path',
            'apiVersion'        => 'api_version',
            'apiModule'         => 'api_module',
            'apiAction'         => 'api_action',
            'uid'               => 'uid',
            'userName'          => 'user_name',
            'role'              => 'role',
            'logId'             => 'log_id',
            'parameters'        => 'parameters',
            'httpAccept'        => 'http_accept',
            'httpHost'          => 'http_host',
            'httpUserAgent'     => 'http_user_agent',
            'remoteAddr'        => 'remote_addr',
            'httpXForwardedFor' => 'http_x_forwarded_for',
            'createTime'        => 'create_time',
        ];
        $this->arrTypesMap  = [
            'id'                 => Hk_Service_Db::TYPE_INT,
            'timeConsuming'      => Hk_Service_Db::TYPE_INT,
            'urlPath'            => Hk_Service_Db::TYPE_STR,
            'apiVersion'         => Hk_Service_Db::TYPE_STR,
            'apiModule'          => Hk_Service_Db::TYPE_STR,
            'apiAction'          => Hk_Service_Db::TYPE_STR,
            'uid'                => Hk_Service_Db::TYPE_INT,
            'userName'           => Hk_Service_Db::TYPE_STR,
            'role'               => Hk_Service_Db::TYPE_STR,
            'logId'              => Hk_Service_Db::TYPE_STR,
            'parameters'         => Hk_Service_Db::TYPE_STR,
            'httpAccept'         => Hk_Service_Db::TYPE_STR,
            'httpHost'           => Hk_Service_Db::TYPE_STR,
            'httpUserAgent'      => Hk_Service_Db::TYPE_STR,
            'remoteAddr'         => Hk_Service_Db::TYPE_STR,
            'httpXForwardedFor'  => Hk_Service_Db::TYPE_STR,
            'createTime'       => Hk_Service_Db::TYPE_INT,
        ];

        $this->_fields = $this->getQueryFields();
    }

}