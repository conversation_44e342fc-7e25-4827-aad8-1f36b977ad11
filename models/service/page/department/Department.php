<?php
/**
 * @befie   用户相关
 * @file    models/service/department/Department.php
 * <AUTHOR>
 * @version 1.0
 * @date    2020-03-05
 */

class Service_Page_Department_Department
{
    private $_serviceLxjxauth = null;

    public function __construct()
    {
        $this->_serviceLxjxauth = new Lxjxlib_Service_Lxjxauth_Client();
    }

    public function execute($arrInput)
    {
        $ret = [];
        $act = $arrInput['act'];
        unset($arrInput['act']);

        switch ($act) {
            case 'add': // 新增
                $ret = $this->addDepartment($arrInput);
                break;
            case 'list': // 列表
                $ret = $this->getDepartmentList($arrInput);
                break;
            case 'del': // 删除子链接
                $ret = $this->delDepartment($arrInput);
                break;
            default:
                break;
        }
        return $ret;
    }

    public function delDepartment($arrInput)
    {
        $subDepartmentList = $this->_serviceLxjxauth->getDepartSub($arrInput['departId'], true);
        if (!empty($subDepartmentList)) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::DEPARTMENT_HAS_SUB_DEPARTMENT);
        }

        $where  = [
            'departId' => $arrInput['departId'],
        ];
        $fields = ['uid', 'name'];

        # 无下属部门，展示本部门BD数据（按BD分组）
        $userList = $this->_serviceLxjxauth->getUserListNoPage($where, $fields);
        if (!empty($userList)) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::DEPARTMENT_HAS_USER);
        }

        return $this->_serviceLxjxauth->deleteDepart($arrInput['departId']);
    }

    public function addDepartment($arrInput)
    {
        //获取父部门
        $parentDepartment = $this->_serviceLxjxauth->getDepartBasic($arrInput['pId']);

        if (empty($parentDepartment)) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::REQUEST_PARAM_ERROR);
        }
        $pStr = $parentDepartment['pStr'];

        if (empty($arrInput['departId'])) {
            $data = [
                'departName' => $arrInput['departName'],
                'pId'        => $arrInput['pId'],
                'pStr'       => $pStr . $arrInput['pId'] . '_',
                'opId'       => $arrInput['opId'],
            ];
            return $this->_serviceLxjxauth->addDepart($data);
        } else {
            return $this->_serviceLxjxauth->updateDepart($arrInput['departId'], $arrInput['departName'], $arrInput['opId'], $arrInput['pId']);
        }
    }

    public function getDepartmentList($input)
    {
        $uid     = $input['opId'];
        $tree[0] = $this->_serviceLxjxauth->getUserDepartTree($uid, 'son');
        return $tree;
    }

    protected function _findDepartCascade($departList, $departId)
    {
        if (empty($departList)) {
            return [];
        }

        foreach ($departList as $departItem) {
            if ($departItem['departId'] == $departId) {
                return $departItem;
            } else {
                $departInfo = $this->_findDepartCascade($departItem['subList'], $departId);
                if (!empty($departInfo)) {
                    return $departInfo;
                }
            }
        }

        return [];
    }

    public function getUserDepartmentList($userInfo)
    {
        return $this->_serviceLxjxauth->getUserDepartTree($userInfo['uid']);
    }

    public function getDepartmentInfoById($departId)
    {
        return $this->_serviceLxjxauth->getDepartBasic($departId);
    }


    /**
     * 获取用户授权的部门Ids
     *
     * @param $uid
     * @return array
     * @throws Hk_Util_Exception
     */
    public function getAuthDepartList($uid)
    {
        $serviceLxjxauth  = new Lxjxlib_Service_Lxjxauth_Client();
        $crmUser          = $serviceLxjxauth->getUserDetail($uid);
        $authDepartList   = $serviceLxjxauth->getSubDepartIdList($crmUser['departId'], true);

        return $authDepartList;
    }
}