<?php

/**
 * @befie   钉钉授权认证
 * @file    models/service/auth/Oauth.php
 * <AUTHOR>
 * @version 1.0
 * @date    2020-03-05
 */
class Service_Page_Auth_Oauth
{
    private $userData = null;
    private $_serviceLxjxauth = null;
    private $dDclient = null;

    public function __construct()
    {
        $this->userData       = new Service_Data_User_CrmUser();
        $this->_serviceLxjxauth = new Lxjxlib_Service_Lxjxauth_Client();
        $this->dDclient       = new Lxjxcrm_Dingtalk_DingTalkService();
    }

    public function execute($input)
    {
        $code   = $input['code'];
        $source = $input['source'];

        $accessToken = $this->dDclient->getToken();
        if (!$accessToken) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::DING_TALK_GET_ACCESS_TOKEN_FAILED);
        }

        $dingTalkName = $this->dDclient->getName($code, $accessToken);
        if (!$dingTalkName) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::DING_TALK_GET_UID_FAILED);
        }
        $userInfo = $this->_serviceLxjxauth->getBdUserInfoByBDName($dingTalkName);

        if (empty($userInfo)) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::CRM_USER_NOT_OPENED_YET, '', [], Lxjxlib_Const_Exception::NOTICE);
        }

        if ($userInfo['enable']) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::CRM_USER_NOT_ENABLE_YET, '', [], Lxjxlib_Const_Exception::NOTICE);
        }

        // 必须要有指定的字段，否则视为系统异常
        $arrNecessaryKey = ['uid', 'email', 'emailPrefix', 'name'];
        foreach ($arrNecessaryKey as $kk) {
            if (!$userInfo[$kk]) {
                throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::CRM_USER_LOGIN_LOST, '钉钉登录异常', [], Lxjxlib_Const_Exception::WARNING);
            }
        }

        $lxjxcrmuss = $this->userData->createSession($userInfo);

        if (empty($lxjxcrmuss)) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::SESSION_CREATE_FAILED);
        }
        if ($source == Lxjxcrm_Const_Const::LOGIN_TYPE_PC) {
            Lxjxcrm_Session_Session::setReqDomainCookie($lxjxcrmuss);
            return ["LXJXCRMUSS" => $lxjxcrmuss];
        } else {
            return ["LXJXCRMUSS" => $lxjxcrmuss];
        }

    }
}

