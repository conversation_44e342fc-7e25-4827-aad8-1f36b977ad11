<?php
class Service_Page_School_FileUpload
{
    protected $_cosServ;

    public function __construct()
    {
        $this->_cosServ = new Lxjxlib_Service_Tcos(Lxjxlib_Const_Common::COS_CONFIG_NAME_LXJX);
    }

    public function execute()
    {
        $output = $this->_cosServ->uploadObjectByFile('image', 'upload');
        if (false === $output) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::ERROR_UPLOAD_PIC_FAILED);
        }

        return $output;
    }

}