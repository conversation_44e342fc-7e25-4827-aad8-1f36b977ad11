<?php

/**************************************************************************
 * Copyright (c) 2019 Zuoyebang.com, Inc. All Rights Reserved
 * @file    Customer.php
 * @date    2020-03-09
 **************************************************************************/
class Service_Page_School_Customer extends Service_Page_School_Base
{
    protected $_schoolService = null;
    protected $_dsLibSchool = null;
    protected $_dsLibGeoDistrict = null;
    protected $_dsCrmSchoolArea = null;
    protected $_dataTraces = null;
    protected $_dataVisit = null;
    protected $_dataContact = null;
    protected $_authCityList = null;
    protected $_querySchool = null;
    protected $_serviceLxjxauth = null;
    protected $_clientLxjxdata = null;
    protected $_clientFuxi = null;

    protected $_redis = null;
    protected $_spamService = [];
    protected $denyFields = [];
    protected $ctlFields = [];

    const OPT_ADD    = 'add';
    const OPT_MODIFY = 'modify';

    /**
     * Service_Page_School_Customer constructor.
     */
    public function __construct()
    {
        parent::__construct();

        $this->_schoolService    = new Lxjxlib_Service_Crm_School();
        $this->_dsLibSchool      = new Lxjxlib_Ds_Crm_SchoolCustomer();
        $this->_dsLibGeoDistrict = new Lxjxlib_Ds_Geo_District();
        $this->_dsCrmSchoolArea  = new Service_Data_School_Area();
        $this->_dataTraces       = new Service_Data_School_Trace();
        $this->_dataVisit        = new Service_Data_School_Visit();
        $this->_dataContact      = new Service_Data_School_Contact();
        $this->_spamService      = new Lxjxlib_Service_Spam_Client();
        $this->_serviceLxjxauth  = new Lxjxlib_Service_Lxjxauth_Client();
        $this->_clientLxjxdata   = new Lxjxcrm_Lxjxdata_Client();
        $this->_clientFuxi       = new Lxjxcrm_Fuxi_Client();

        $this->_redis = Hk_Service_RedisClient::getInstance(Lxjxlib_Const_Cache::REDIS_NAME_LXJX);

        $this->_authCityList = null;
        $this->_querySchool  = null;
        // 不可修改的字段
        $this->denyFields = ['schoolName', 'simpleName'];
        // 需要控制修改的字段
        $this->ctlFields = [
            'schoolName', 'provinceId', 'provinceName', 'cityId', 'cityName', 'countyId', 'countyName',
            'address', 'eduPhase', 'eduStage', 'longitude', 'latitude', 'schoolNature',
        ];
    }

    protected function _pickSchoolSummaryInfo($schoolInfo)
    {
        $examDate = [];
        if (!empty($schoolInfo['examDates']) && is_string($schoolInfo['examDates'])) {
            $examDate = json_decode($schoolInfo['examDates'], true);
        }

        $ret = [
            'schoolId'          => $schoolInfo['schoolId'],
            'schoolName'        => $schoolInfo['schoolName'],
            'simpleName'        => $schoolInfo['simpleName'],
            'nature'            => $schoolInfo['schoolNature'],
            'natureStr'         => Lxjxlib_Const_Crm_School::getNatureName($schoolInfo['schoolNature']),
            'level'             => $schoolInfo['schoolLevel'],
            'levelStr'          => Lxjxlib_Const_Crm_School::getLevelName($schoolInfo['schoolLevel']),
            'eduPhase'          => $schoolInfo['eduPhase'],
            'eduPhaseStr'       => Lxjxlib_Const_Crm_School::getEduStageName($schoolInfo['eduPhase']),
            'courseStatus'      => $schoolInfo['courseStatus'],
            'courseStatusStr'   => Lxjxlib_Const_Crm_School::getCourseStatusName($schoolInfo['courseStatus']),
            // 'courseStandard'    => $schoolInfo['courseStandard'],
            // 'courseStandardStr' => Lxjxlib_Const_Crm_School::getCourseStandardName($schoolInfo['courseStandard']),
            'provinceId'        => $schoolInfo['provinceId'],
            'provinceName'      => $schoolInfo['provinceName'],
            'cityId'            => $schoolInfo['cityId'],
            'cityName'          => $schoolInfo['cityName'],
            'countyId'          => $schoolInfo['countyId'],
            'countyName'        => $schoolInfo['countyName'],
            'address'           => $schoolInfo['address'],
            'longitude'         => $schoolInfo['longitude'],
            'latitude'          => $schoolInfo['latitude'],
            'scale'             => $schoolInfo['schoolScale'],
            'claimStatus'       => $schoolInfo['claimStatus'],
            'claimantUid'       => $schoolInfo['claimantUid'],
            'isIntroduce'       => $schoolInfo['isIntroduce'],
            'claimantName'      => $schoolInfo['claimantName'],
            'dormitory'         => $schoolInfo['dormitoryRatio'] . '%',
            'examDates'         => is_array($examDate) ? $examDate : [],
        ];

        return $ret;
    }

    protected function _processAreaCondition()
    {
        if (!empty($this->_querySchool['provinceId'])) {
            $cityList = !empty($this->_querySchool['cityId']) ? $this->_querySchool['cityId'] : [];

            // 查询省份下的城市
            foreach ($this->_querySchool['provinceId'] as $provId) {
                $provCityList = $this->_dsLibGeoDistrict->getCityList($provId);
                $cityIdList   = array_column($provCityList, 'cityId');
                $cityList     = array_merge($cityList, $cityIdList);
            }

            $this->_querySchool['cityId']     = array_unique($cityList);
            $this->_querySchool['provinceId'] = [];
        }
    }

    /**
     * @desc 生成要查询的条件。要查询的字段，才传入。
     * @param array $queryFields
     * @param array $curUser
     * @param int $schoolScope
     * @return void
     * @throws Hk_Util_Exception
     */
    protected function _initQuerySchoolConditions($queryFields, $curUser, $schoolScope)
    {
        if (!is_null($this->_querySchool)) {
            return;
        }

        $this->_querySchool = [];
        foreach ($queryFields as $field => $value) {
            // 过滤掉空的筛选条件
            // if (empty($value)) {
            //     continue;
            // }
            $this->_querySchool[$field] = $value;
        }

        // 处理省市多选条件
        $this->_processAreaCondition();

        if (Lxjxlib_Const_Crm_School::SCHOOL_SCOPE_PRIVATE == $schoolScope) {
            /** 查询私海学校 **/
            $this->_querySchool['claimantUid'] = $curUser['uid'];
            $this->_querySchool['claimStatus'] = Lxjxlib_Const_Crm_School::CLAIM_STATUS_YES;
        } else {
            /** 查询公海学校 **/
            switch ($curUser['auth_scope']) {
                case Lxjxlib_Service_PrivilegeScopeNew::AUTH_SCOPE_ALL:
                    break;
                case Lxjxlib_Service_PrivilegeScopeNew::AUTH_SCOPE_PARTIAL:
                    $crmUser      = $this->_serviceLxjxauth->getUserDetail($curUser['uid']);
                    $departList   = $this->_serviceLxjxauth->getSubDepartIdList($crmUser['departId']);
                    $departList[] = $crmUser['departId'];

                    $this->_querySchool['claimantDepartId'] = array_unique($departList);
                    break;
                case Lxjxlib_Service_PrivilegeScopeNew::AUTH_SCOPE_SELF:
                    if (!isset($this->_querySchool['claimantUid'])) {
                        $this->_querySchool['claimantUid'] = $curUser['uid'];
                    } else if ($this->_querySchool['claimantUid'] != $curUser['uid']) {
                        $this->_querySchool = '';
                    }
                    break;
                default:
                    $this->_querySchool = '';
                    break;
            }
        }
    }

    public function getSchoolAmount($queryFields, $curUser, $schoolScope)
    {
        $this->_initQuerySchoolConditions($queryFields, $curUser, $schoolScope);
        if (!is_array($this->_querySchool)) {
            return 0;
        }

        return $this->_schoolService->getSchoolsAmount($this->_querySchool);
    }

    /**
     * @function getSchoolSuggestionList
     * @desc     获取学校名称的搜索建议
     * @param    array $curUser 用户信息
     * @param    array $arrInput 要查询的字段，若key存在，则说明需要对该字段进行条件查询
     * @param    int $pn
     * @param    int $rn
     * @return   array
     */
    public function getSchoolSuggestionList($curUser, $arrInput, $pn, $rn)
    {
        $queryParams = [];
        $arrKeyMap = [
            'searchName' => 'schoolName',
        ];
        foreach ($arrKeyMap as $keyFrom => $keyTo) {
            // key存在，就表示需要对该字段进行条件查询
            if (!isset($arrInput[$keyFrom])) {
                continue;
            }
            // 不过滤空值
            $queryParams[$keyTo] = $arrInput[$keyFrom];
        }
        $schoolList = $this->_schoolService->getSchoolsList($queryParams, $pn, $rn);

        $ret = [];
        foreach ($schoolList['list'] as $schoolInfo) {
            $ret[] = $this->_pickSchoolSummaryInfo($schoolInfo);
        }

        return $ret;
    }


    /**
     * @function        getSchoolsList
     * @access          public
     * @date            2020-08-31
     * @desc            查询学校
     * @param           $queryFields
     * @param           $curUser
     * @param           $schoolScope
     * @param           $pn
     * @param           $rn
     * @return          array
     */
    public function getSchoolsList($queryFields, $curUser, $schoolScope, $pn, $rn)
    {
        $this->_initQuerySchoolConditions($queryFields, $curUser, $schoolScope);
        if (!is_array($this->_querySchool)) {
            return [];
        }

        $schoolList = $this->_schoolService->getSchoolsList($this->_querySchool, $pn, $rn);

        $ret = [];
        foreach ($schoolList['list'] as $schoolInfo) {
            $ret[] = $this->_pickSchoolSummaryInfo($schoolInfo);
        }

        return $ret;
    }

    /**
     * @function        exportSchoolList
     * @access          public
     * @date            2020-08-31
     * @desc            导出学校
     * @param           $queryFields
     * @param           $curUser
     * @param           $schoolScope
     * @return          void
     */
    public function exportSchoolList($queryFields, $curUser, $schoolScope)
    {
        $exportList = [];

        // 文件名称
        $fileName = '学校公海.csv';

        // 表头行
        $title = [
            '序号','学校ID',
            '学校名称', '学校性质', '学校等级', '学校规模',
            '省', '市', '区县', '详细地址',
            '住宿情况', '教育阶段', '分配状态', '客户归属人', '经度', '纬度',
        ];

        // 单元格字段
        $cellKey = [
            'id','schoolId',
            'schoolName', 'natureStr', 'levelStr', 'scale',
            'provinceName', 'cityName', 'countyName', 'address',
            'dormitory', 'eduPhaseStr', 'claimStatus', 'claimantName', 'longitude', 'latitude',
        ];

        $this->_initQuerySchoolConditions($queryFields, $curUser, $schoolScope);

        if (is_array($this->_querySchool)) {
            // 获取学校数量
            $amount = $this->getSchoolAmount($queryFields, $curUser, $schoolScope);

            $rowNum = 1;
            $pn     = 0;
            $rn     = 1000;

            while (true) {
                if ($amount <= $pn * $rn) {
                    break;
                }

                // 分多次请求学校数据
                $schoolList = $this->_schoolService->getSchoolsList($this->_querySchool, $pn, $rn);

                foreach ($schoolList['list'] as $schoolInfo) {
                    $exportItem                = $this->_pickSchoolSummaryInfo($schoolInfo);
                    $exportItem['id']          = $rowNum++;
                    $exportItem['claimStatus'] = Lxjxlib_Const_Crm_School::getClaimStatusName($exportItem['claimStatus']);
                    $exportList[]              = $exportItem;
                }

                $schoolList = null;
                $pn++;
            }
        }

        Lxjxlib_Util_Tools::export($fileName, $title, $exportList, $cellKey);
    }

    /**
     * @deprecated
     * @function        createSchool
     * @access          public
     * @date            2020-08-31
     * @desc            创建学校
     * @param           $schoolData
     * @param           $userInfo
     * @param           $schoolScope
     * @return          array
     */
    /*
    public function createSchool($schoolData, $userInfo, $schoolScope)
    {
        $ret = [
            'res'      => Lxjxcrm_ExceptionCodes::SUCCESS,
            'schoolId' => 0,
        ];

        // 私海和公海的默认认领逻辑
        $schoolData['claimantUid']      = 0;
        $schoolData['claimantName']     = '';
        $schoolData['claimStatus']      = Lxjxlib_Const_Crm_School::CLAIM_STATUS_NO;
        $schoolData['claimantDepartId'] = 0;
        $schoolData['creatorUid']       = $userInfo['uid'];
        $schoolData['creatorName']      = $userInfo['name'];
        $schoolData['status']           = Lxjxlib_Const_Common::DB_STATUS_OK;

        $crmUser = $this->_serviceLxjxauth->getUserDetail($userInfo['uid']);
        if (Lxjxlib_Const_Crm_School::SCHOOL_SCOPE_PRIVATE == $schoolScope) {
            $schoolData['claimantUid']      = $userInfo['uid'];
            $schoolData['claimantName']     = $userInfo['name'];
            $schoolData['claimStatus']      = Lxjxlib_Const_Crm_School::CLAIM_STATUS_YES;
            $schoolData['claimantDepartId'] = $crmUser['departId'];
        }

        // 向伏羲学校库申请schoolID
        $schoolId = 0;
        // 通知伏羲学校库:新增学校
        Hk_Util_Log::start("ps_school_fuxi_add");
        $resFuxi = $this->_clientFuxi->addSchool($schoolData);
        Bd_Log::addNotice("fuxiSchAdd", 'unknown');
        if ($resFuxi && is_array($resFuxi) && 0 < $resFuxi['schoolId']) {
            Bd_Log::addNotice("fuxiSchAdd", 'done');
            $schoolId = $resFuxi['schoolId'];
        } else {
            Bd_Log::addNotice("fuxiSchAdd", 'undone');
            Bd_Log::warning("fuxi school add failed, resMsg:{$resFuxi['resMsg']}");
        }
        Hk_Util_Log::stop("ps_school_fuxi_add");
        if (0 >= $schoolId) {
            $ret['errno'] = Lxjxcrm_ExceptionCodes::SCHOOL_FUXI_ADD_SCHOOL_FAILED;
            return $ret;
        }
        $schoolData['schoolId'] = $schoolId;

        $resDb = $this->_schoolService->createSchool($schoolData);
        if (false === $resDb) {
            $ret['res'] = Lxjxcrm_ExceptionCodes::DB_UPDATE_ERR;
            return $ret;
        }
        if (1 == $resDb) {
            $ret['schoolId'] = $schoolData['schoolId'];

            // 添加追踪记录
            $this->_dataTraces->createTrace($schoolData['schoolId'], Lxjxlib_Const_Crm_School::TRACE_TYPE_CREATE_SCHOOL, $crmUser);

            // 如果修改成功，且修改了省、市、区、学校名称，需要删除给魔方提供的省市区学校的缓存
            if ($this->_dsCrmSchoolArea->chkHasAreaChanged($schoolData)){
                Hk_Util_Log::start("ps_schoolarea_cache_refresh");
                $this->_dsCrmSchoolArea->RefreshAreaListCache([$schoolData]);
                Hk_Util_Log::stop("ps_schoolarea_cache_refresh");
            }
        }

        return $ret;
    }
    */

    /**
     * 创建学校-PC端
     * @param $schoolData
     * @param $correctFields
     * @param $userInfo
     * @param $schoolScope
     * @return array
     */
    public function pcCreateSchool($schoolData, $correctFields, $userInfo, $schoolScope)
    {
        $arrOutput = [
            'errno'    => Lxjxcrm_ExceptionCodes::SUCCESS,
            'schoolId' => 0,
        ];

        // 学校标准名称不能为空
        if (0 >= strlen($schoolData['schoolName'])) {
            $arrOutput['errno'] = Lxjxcrm_ExceptionCodes::SCHOOL_NAME_INVALID;
            return $arrOutput;
        }

        // 检验省市区是否级联
        $checkRet = $this->_checkAreaCodeName($schoolData['provinceId'], $schoolData['provinceName'], $schoolData['cityId'], $schoolData['cityName'], $schoolData['countyId'], $schoolData['countyName']);
        if (!Lxjxcrm_ExceptionCodes::isSuccess($checkRet['errno'])) {
            $arrOutput['errno'] = $checkRet['errno'];
            return $arrOutput;
        }

        // spam检查。未对接，先下掉，线上调用的参数也不对。
        /*
        $spamData = [
            'qid'     => Lxjxlib_Service_IdAlloc::createRecordId(),
            'uid'     => intval($userInfo['uid']),
            'content' => $schoolData['schoolName'],
            'uip'     => Bd_Ip::getClientIp(),
        ];
        Hk_Util_Log::start("ps_school_spam");
        $this->_spamService->sendToSpam($spamData);
        Hk_Util_Log::stop("ps_school_spam");
        */

        // 检验重复
        $checkRet = $this->_chkSchoolRepeat($schoolData['schoolName'], $schoolData['countyId'], self::OPT_ADD);
        if (!Lxjxcrm_ExceptionCodes::isSuccess($checkRet['errno'])) {
            $arrOutput['errno'] = $checkRet['errno'];
            return $arrOutput;
        }

        // 私海和公海的默认认领逻辑
        $schoolData['claimantUid']      = 0;
        $schoolData['claimantName']     = '';
        $schoolData['claimStatus']      = Lxjxlib_Const_Crm_School::CLAIM_STATUS_NO;
        $schoolData['claimantDepartId'] = 0;
        $schoolData['creatorUid']       = $userInfo['uid'];
        $schoolData['creatorName']      = $userInfo['name'];
        $schoolData['status']           = Lxjxlib_Const_Common::DB_STATUS_OK;

        $crmUser = $this->_serviceLxjxauth->getUserDetail($userInfo['uid']);
        if (Lxjxlib_Const_Crm_School::SCHOOL_SCOPE_PRIVATE == $schoolScope) {
            $schoolData['claimantUid']      = $userInfo['uid'];
            $schoolData['claimantName']     = $userInfo['name'];
            $schoolData['claimStatus']      = Lxjxlib_Const_Crm_School::CLAIM_STATUS_YES;
            $schoolData['claimantDepartId'] = $crmUser['departId'];
        }

        // 获取字段校准控制
        $schoolData['correct'] = $this->_genCorrectCtl($correctFields);

        // 向伏羲学校库申请schoolID
        $schoolId = 0;
        // 通知伏羲学校库:新增学校
        Hk_Util_Log::start("ps_school_fuxi_add");
        $arrFuxiSchData = [
            'provinceId'   => $schoolData['provinceId'],
            'cityId'       => $schoolData['cityId'],
            'countyId'     => $schoolData['countyId'],
            'schoolName'   => $schoolData['schoolName'],
            'operatorName' => $userInfo['email_prefix'],
        ];
        $resFuxi = $this->_clientFuxi->addSchool($arrFuxiSchData);
        Bd_Log::addNotice("fuxiSchAdd", 'unknown');
        if ($resFuxi && is_array($resFuxi) && 0 < $resFuxi['schoolId']) {
            Bd_Log::addNotice("fuxiSchAdd", 'done');
            $schoolId = $resFuxi['schoolId'];
        } else {
            Bd_Log::addNotice("fuxiSchAdd", 'undone');
            Bd_Log::warning("fuxi school add failed, resMsg:{$resFuxi['resMsg']}");
        }
        Hk_Util_Log::stop("ps_school_fuxi_add");
        if (0 >= $schoolId) {
            $arrOutput['errno'] = Lxjxcrm_ExceptionCodes::SCHOOL_FUXI_ADD_SCHOOL_FAILED;
            return $arrOutput;
        }
        $schoolData['schoolId'] = $schoolId;

        $res = $this->_schoolService->createSchool($schoolData);
        if (false === $res) {
            $arrOutput['errno'] = Lxjxcrm_ExceptionCodes::DB_INSERT_ERR;
            return $arrOutput;
        }
        if (1 == $res) {
            $arrOutput['schoolId'] = $schoolData['schoolId'];

            // 添加追踪记录
            $this->_dataTraces->createTrace($schoolData['schoolId'], Lxjxlib_Const_Crm_School::TRACE_TYPE_CREATE_SCHOOL, $crmUser);

            // 如果修改成功，且修改了省、市、区、学校名称，需要删除给魔方提供的省市区学校的缓存
            if ($this->_dsCrmSchoolArea->chkHasAreaChanged($schoolData)) {
                Hk_Util_Log::start("ps_schoolarea_cache_refresh");
                $this->_dsCrmSchoolArea->RefreshAreaListCache([$schoolData]);
                Hk_Util_Log::stop("ps_schoolarea_cache_refresh");
            }
        }

        return $arrOutput;
    }

    /**
     * 创建学校-小程序
     * @param $schoolData
     * @param $userInfo
     * @param $schoolScope
     * @return array|int
     */
    public function mpCreateSchool($schoolData, $userInfo, $schoolScope)
    {
        $arrOutput = [
            'errno'    => Lxjxcrm_ExceptionCodes::SUCCESS,
            'schoolId' => 0,
        ];

        // 学校标准名称不能为空
        if (0 >= strlen($schoolData['schoolName'])) {
            $arrOutput['errno'] = Lxjxcrm_ExceptionCodes::SCHOOL_NAME_INVALID;
            return $arrOutput;
        }

        // 检验省市区是否级联
        $checkRet = $this->_checkAreaCodeName($schoolData['provinceId'], $schoolData['provinceName'], $schoolData['cityId'], $schoolData['cityName'], $schoolData['countyId'], $schoolData['countyName']);
        if (!Lxjxcrm_ExceptionCodes::isSuccess($checkRet['errno'])) {
            $arrOutput['errno'] = $checkRet['errno'];
            return $arrOutput;
        }

        // spam检查。未对接，先下掉，线上调用的参数也不对。
        /*
        $spamData = [
            'qid'     => Lxjxlib_Service_IdAlloc::createRecordId(),
            'uid'     => intval($userInfo['uid']),
            'content' => $schoolData['schoolName'],
            'uip'     => Bd_Ip::getClientIp(),
        ];
        Hk_Util_Log::start("ps_school_spam");
        $this->_spamService->sendToSpam($spamData);
        Hk_Util_Log::stop("ps_school_spam");
        */

        // 检验重复
        $checkRet = $this->_chkSchoolRepeat($schoolData['schoolName'], $schoolData['countyId'], self::OPT_ADD);
        if (!Lxjxcrm_ExceptionCodes::isSuccess($checkRet['errno'])) {
            $arrOutput['errno'] = $checkRet['errno'];
            return $arrOutput;
        }

        // 私海和公海的默认认领逻辑
        $schoolData['claimantUid']      = 0;
        $schoolData['claimantName']     = '';
        $schoolData['claimStatus']      = Lxjxlib_Const_Crm_School::CLAIM_STATUS_NO;
        $schoolData['claimantDepartId'] = 0;
        $schoolData['creatorUid']       = $userInfo['uid'];
        $schoolData['creatorName']      = $userInfo['name'];
        $schoolData['status']           = Lxjxlib_Const_Common::DB_STATUS_OK;

        $crmUser = $this->_serviceLxjxauth->getUserDetail($userInfo['uid']);
        if (Lxjxlib_Const_Crm_School::SCHOOL_SCOPE_PRIVATE == $schoolScope) {
            $schoolData['claimantUid']      = $userInfo['uid'];
            $schoolData['claimantName']     = $userInfo['name'];
            $schoolData['claimStatus']      = Lxjxlib_Const_Crm_School::CLAIM_STATUS_YES;
            $schoolData['claimantDepartId'] = $crmUser['departId'];
        }


        // 向伏羲学校库申请schoolID
        $schoolId = 0;
        // 通知伏羲学校库:新增学校
        Hk_Util_Log::start("ps_school_fuxi_add");
        $arrFuxiSchData = [
            'provinceId'   => $schoolData['provinceId'],
            'cityId'       => $schoolData['cityId'],
            'countyId'     => $schoolData['countyId'],
            'schoolName'   => $schoolData['schoolName'],
            'operatorName' => $userInfo['email_prefix'],
        ];
        $resFuxi = $this->_clientFuxi->addSchool($arrFuxiSchData);
        Bd_Log::addNotice("fuxiSchAdd", 'unknown');
        if ($resFuxi && is_array($resFuxi) && 0 < $resFuxi['schoolId']) {
            Bd_Log::addNotice("fuxiSchAdd", 'done');
            $schoolId = $resFuxi['schoolId'];
        } else {
            Bd_Log::addNotice("fuxiSchAdd", 'undone');
            Bd_Log::warning("fuxi school add failed, resMsg:{$resFuxi['resMsg']}");
        }
        Hk_Util_Log::stop("ps_school_fuxi_add");
        if (0 >= $schoolId) {
            $arrOutput['errno'] = Lxjxcrm_ExceptionCodes::SCHOOL_FUXI_ADD_SCHOOL_FAILED;
            return $arrOutput;
        }
        $schoolData['schoolId'] = $schoolId;

        $res = $this->_schoolService->createSchool($schoolData);
        if (false === $res) {
            $arrOutput['errno'] = Lxjxcrm_ExceptionCodes::DB_INSERT_ERR;
            return $arrOutput;
        }
        if (1 == $res) {
            $arrOutput['schoolId'] = $schoolData['schoolId'];

            // 添加追踪记录
            $this->_dataTraces->createTrace($schoolData['schoolId'], Lxjxlib_Const_Crm_School::TRACE_TYPE_CREATE_SCHOOL, $crmUser);

            // 如果修改成功，且修改了省、市、区、学校名称，需要删除给魔方提供的省市区学校的缓存
            if ($this->_dsCrmSchoolArea->chkHasAreaChanged($schoolData)) {
                Hk_Util_Log::start("ps_schoolarea_cache_refresh");
                $this->_dsCrmSchoolArea->RefreshAreaListCache([$schoolData]);
                Hk_Util_Log::stop("ps_schoolarea_cache_refresh");
            }
        }

        return $arrOutput;
    }

    /**
     * @deprecated
     * @function        claimSchool
     * @access          public
     * @date            2020-08-31
     * @desc            认领学校
     * @param           $schoolId
     * @param           $userInfo
     * @return          int
     */
    public function claimSchool($schoolId, $userInfo)
    {
        // 获取学校信息
        $schoolInfo = $this->_schoolService->getSchoolInfoBySchoolId($schoolId);
        if (empty($schoolInfo)) {
            return Lxjxcrm_ExceptionCodes::SCHOOL_NOT_EXIST;
        }

        // 已认领学校不可重复认领
        if ($schoolInfo['claimStatus'] == Lxjxlib_Const_Crm_School::CLAIM_STATUS_YES ||
            0 != $schoolInfo['claimantUid']) {
            return Lxjxcrm_ExceptionCodes::SCHOOL_CLAIMED_ALREADY;
        }

        // 检查是否有权限认领
        if (Lxjxlib_Service_PrivilegeScopeNew::AUTH_SCOPE_ALL != $userInfo['auth_scope'] &&
            !$this->checkClaimPrivilege($schoolInfo, $userInfo)) {
            return Lxjxcrm_ExceptionCodes::CRM_USER_AUTH_LIMIT;
        }

        $crmUser = $this->_serviceLxjxauth->getUserDetail($userInfo['uid']);

        // 修改状态为已认领
        $modifySchool = [
            'claimStatus'      => Lxjxlib_Const_Crm_School::CLAIM_STATUS_YES,
            'claimantUid'      => $userInfo['uid'],
            'claimantName'     => $userInfo['name'],
            'claimantDepartId' => $crmUser['departId'],
        ];

        $res = $this->_schoolService->modifySchool($modifySchool, $schoolId);
        if (false === $res) {
            return Lxjxcrm_ExceptionCodes::DB_UPDATE_ERR;
        }
        if (1 <= $res) {
            // 添加跟进记录
            $this->_dataTraces->createTrace($schoolId, Lxjxlib_Const_Crm_School::TRACE_TYPE_CLAIM_SCHOOL, $crmUser);

            // 修改联系人的学校所属部门
            $modifyContact = [
                'claimantDepartId' => $crmUser['departId'],
            ];
            $this->_dataContact->modifyContactBySchool($modifyContact, $schoolId);
        }
        return Lxjxcrm_ExceptionCodes::SUCCESS;
    }

    /**
     * @function        releaseSchool
     * @access          public
     * @date            2020-08-31
     * @desc            释放学校
     * @param           $schoolId
     * @param           $userInfo
     * @return          int
     */
    public function releaseSchool($schoolId, $userInfo)
    {
        // 获取学校信息
        $schoolInfo = $this->_schoolService->getSchoolInfoBySchoolId($schoolId);
        if (empty($schoolInfo)) {
            return Lxjxlib_Const_Errors::ERROR_SCHOOL_NOT_EXIST;
        }

        // 未认领学校不可释放
        if ($schoolInfo['claimStatus'] == Lxjxlib_Const_Crm_School::CLAIM_STATUS_NO ||
            0 == $schoolInfo['claimantUid']) {
            return Lxjxlib_Const_Errors::ERROR_SCHOOL_UN_CLAIMED;
        }

        // 检查是否有释放权限
        if (Lxjxlib_Service_PrivilegeScopeNew::AUTH_SCOPE_ALL != $userInfo['auth_scope'] &&
            !$this->checkReleasePrivilege($schoolInfo, $userInfo)) {
            return Lxjxlib_Const_Errors::ERROR_SCHOOL_RELEASE_AUTH;
        }

        $modifySchool = [
            'claimStatus'      => Lxjxlib_Const_Crm_School::CLAIM_STATUS_NO,
            'claimantUid'      => 0,
            'claimantName'     => '',
            'claimantDepartId' => 0,
        ];

        $res = $this->_schoolService->modifySchool($modifySchool, $schoolId);
        if (Lxjxlib_Const_Errors::ERROR_SUCCESS == $res) {
            $crmUser = $this->_serviceLxjxauth->getUserDetail($userInfo['uid']);
            // 添加追踪记录
            $this->_dataTraces->createTrace($schoolId, Lxjxlib_Const_Crm_School::TRACE_TYPE_RELEASE_SCHOOL, $crmUser, '', strval($schoolInfo['claimantUid']));

            // 修改联系人的学校所属部门
            $modifyContact = [
                'claimantDepartId' => 0,
            ];
            $this->_dataContact->modifyContactBySchool($modifyContact, $schoolId);
        }

        return $res;
    }

    /**
     * @function        releaseSchoolBatch
     * @access          public
     * @date            2020-09-01
     * @desc            批量释放学校
     * @param           $schoolIds
     * @param           $userInfo
     * @return          int
     * @throws          Lxjxlib_Const_Exception
     */
    public function releaseSchoolBatch($schoolIds, $userInfo)
    {
        // 获取学校信息
        $schoolList = $this->_schoolService->getSchoolCustomerBySchoolIds($schoolIds);
        if (empty($schoolList)) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::REQUEST_PARAM_VALUE);
        }
        $schoolList = array_column($schoolList, null, 'schoolId');

        // 有权限的下属用户
        $authUids = $this->getAuthUidList($userInfo['uid'], $userInfo['auth_scope']);

        // 可释放的学校
        $releaseIds = [];

        if (Lxjxlib_Service_PrivilegeScopeNew::DATA_AUTH_ADMINISTRATOR == $authUids) {
            $releaseIds = array_column($schoolList, 'schoolId');
        } else {
            foreach ($schoolList as $schoolInfo) {
                // 已认领且对认领人有权限
                if (Lxjxlib_Const_Crm_School::CLAIM_STATUS_YES == $schoolInfo['claimStatus'] &&
                    in_array($schoolInfo['claimantUid'], $authUids)
                ) {
                    $releaseIds[] = $schoolInfo['schoolId'];
                }
            }
        }

        $ret = 0;
        if (!empty($releaseIds)) {
            $modifyFields = [
                'claimStatus'      => Lxjxlib_Const_Crm_School::CLAIM_STATUS_NO,
                'claimantUid'      => 0,
                'claimantName'     => '',
                'claimantDepartId' => 0,
            ];

            $ret = $this->_schoolService->modifySchoolBatch($modifyFields, $schoolIds);
            if ($ret > 0) {
                $crmUser = $this->_serviceLxjxauth->getUserDetail($userInfo['uid']);

                // 添加追踪记录
                foreach ($releaseIds as $schoolId) {
                    $this->_dataTraces->createTrace($schoolId, Lxjxlib_Const_Crm_School::TRACE_TYPE_RELEASE_SCHOOL, $crmUser, '', strval($schoolInfo['claimantUid']));
                }

                // 修改联系人的学校所属部门
                $modifyContact = [
                    'claimantDepartId' => 0,
                ];
                $this->_dataContact->modifyContactBySchool($modifyContact, $releaseIds);
            }
        }

        return $ret;
    }

    /**
     * @function        allocateSchoolBatch
     * @access          public
     * @date            2020-08-31
     * @desc            批量分配学校
     * @param           $schoolIds
     * @param           $toUid
     * @param           $curUser
     * @return          int
     * @throws          Lxjxlib_Const_Exception
     */
    public function allocateSchoolBatch($schoolIds, $toUid, $curUser)
    {
        // 检查用户
        $toCrmUser = $this->_serviceLxjxauth->getUserDetail($toUid);
        if (empty($toCrmUser)) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::CRM_USER_NOT_EXIST);
        }

        // 当前登录用户
        $curCrmUser = $this->_serviceLxjxauth->getUserDetail($curUser['uid']);
        if (empty($curCrmUser)) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::CRM_USER_LOGIN_LOST);
        }

        $ret = 0;

        // 获取学校信息
        $schoolList = $this->_schoolService->getSchoolCustomerBySchoolIds($schoolIds);
        if (empty($schoolList)) {
            return $ret;
        }

        // 可以分配的学校ID
        $allocateIds = [];
        foreach ($schoolList as $school) {
            // 学校未认领的才能分配
            if (Lxjxlib_Const_Crm_School::CLAIM_STATUS_NO == $school['claimStatus']) {
                $allocateIds[] = $school['schoolId'];
            }
        }

        if (empty($allocateIds)) {
            return $ret;
        }

        // 修改状态为已认领
        $modifyFields = [
            'claimStatus'      => Lxjxlib_Const_Crm_School::CLAIM_STATUS_YES,
            'claimantUid'      => $toCrmUser['uid'],
            'claimantName'     => $toCrmUser['name'],
            'claimantDepartId' => $toCrmUser['departId'],
        ];

        $ret = $this->_schoolService->modifySchoolBatch($modifyFields, $allocateIds);

        if ($ret > 0) {
            foreach ($allocateIds as $schoolId) {
                // 添加跟进记录
                $this->_dataTraces->createTrace($schoolId, Lxjxlib_Const_Crm_School::TRACE_TYPE_DISPATCH_SCHOOL, $curCrmUser, '', strval($toUid));
            }

            // 修改联系人的学校所属部门
            $modifyContact = [
                'claimantDepartId' => $toCrmUser['departId'],
            ];
            $this->_dataContact->modifyContactBySchool($modifyContact, $allocateIds);
        }

        return $ret;
    }

    /**
     * @deprecated
     * @function        modifySchool
     * @access          public
     * @date            2020-08-31
     * @desc            修改学校信息
     * @param           $modifyFields
     * @param           $schoolId
     * @param           $userInfo
     * @return          int
     */
    /*
    public function modifySchool($modifyFields, $schoolId, $userInfo)
    {
        $schoolInfo = $this->_schoolService->getSchoolInfoBySchoolId($schoolId);
        if (empty($schoolInfo)) {
            return Lxjxcrm_ExceptionCodes::SCHOOL_NOT_EXIST;
        }

        // 暂先保留，后续万一用到呢？
        // 已认领学校需检查用户权限
        // if (Lxjxlib_Const_Crm_School::CLAIM_STATUS_YES == $schoolInfo['claimStatus'] &&
        //     Lxjxlib_Service_PrivilegeScopeNew::AUTH_SCOPE_ALL != $userInfo['auth_scope'] &&
        //     $schoolInfo['claimantUid'] != $userInfo['uid'] &&
        //     !$this->isSuperiorLeader($userInfo['uid'], $schoolInfo['claimantUid'], $userInfo['auth_scope'])) {
        //     return Lxjxlib_Const_Errors::ERROR_UN_AUTH;
        // }

        $changedFields = [];
        // 去掉没有变化的字段
        foreach ($modifyFields as $fieldName => $fieldValue) {
            // 去掉不存在的属性字段
            if (!isset($schoolInfo[$fieldName])) {
                continue;
            }
            if (strval($fieldValue) != strval($schoolInfo[$fieldName])) {
                $changedFields[$fieldName] = $fieldValue;
            }
        }

        if (empty($changedFields)) {
            return Lxjxcrm_ExceptionCodes::SUCCESS;
        }

        $ret = $this->_schoolService->modifySchool($modifyFields, $schoolId);
        if (false === $ret) {
            return Lxjxcrm_ExceptionCodes::DB_UPDATE_ERR;
        }

        if ($ret) {
            // 如果修改成功，且修改了省、市、区、学校名称，需要删除给魔方提供的省市区学校的缓存
            if ($this->_dsCrmSchoolArea->chkHasAreaChanged($changedFields)) {
                Hk_Util_Log::start("ps_schoolarea_cache_refresh");
                $this->_dsCrmSchoolArea->RefreshAreaListCache([$schoolInfo]);
                Hk_Util_Log::stop("ps_schoolarea_cache_refresh");
            }
        }

        return Lxjxcrm_ExceptionCodes::SUCCESS;
    }
    */

    /**
     * 修改学校-PC端
     * @param $schoolId
     * @param $modifyFields
     * @param $correctFields
     * @param $userInfo
     * @return mixed|bool|int
     */
    public function pcModifySchool($schoolId, $modifyFields, $correctFields, $userInfo)
    {
        $arrOutput = [
            'errno' => Lxjxcrm_ExceptionCodes::SUCCESS,
        ];

        // 学校标准名称不能为空
        if (isset($modifyFields['schoolName']) && 0 >= strlen($modifyFields['schoolName'])) {
            $arrOutput['errno'] = Lxjxcrm_ExceptionCodes::SCHOOL_NAME_INVALID;
            return $arrOutput;
        }

        // 检验省市区是否级联
        $bHasAreaKey = false;
        $arrKeysArea = ['provinceId', 'provinceName', 'cityId', 'cityName', 'countyId', 'countyName'];
        foreach ($arrKeysArea as $keyChk) {
            if (isset($modifyFields[$keyChk])) {
                $bHasAreaKey = true;
                break;
            }
        }
        if ($bHasAreaKey) {
            $checkRet = $this->_checkAreaCodeName($modifyFields['provinceId'], $modifyFields['provinceName'], $modifyFields['cityId'], $modifyFields['cityName'], $modifyFields['countyId'], $modifyFields['countyName']);
            if (!Lxjxcrm_ExceptionCodes::isSuccess($checkRet['errno'])) {
                $arrOutput['errno'] = $checkRet['errno'];
                return $arrOutput;
            }
        }

        $schoolInfo = $this->_dsLibSchool->getRecordByConds(['schoolId' => $schoolId]);
        if (empty($schoolInfo)) {
            $arrOutput['errno'] = Lxjxcrm_ExceptionCodes::SCHOOL_NOT_EXIST;
            return $arrOutput;
        }

        // 暂先保留，后续万一用到呢？
        // 已认领学校需检查用户权限
        /*
        if (Lxjxlib_Const_Crm_School::CLAIM_STATUS_YES == $schoolInfo['claimStatus'] &&
            Lxjxlib_Service_PrivilegeScopeNew::AUTH_SCOPE_ALL != $userInfo['auth_scope'] &&
            $schoolInfo['claimantUid'] != $userInfo['uid'] &&
            !$this->isSuperiorLeader($userInfo['uid'], $schoolInfo['claimantUid'], $userInfo['auth_scope'])) {
            return Lxjxlib_Const_Errors::ERROR_UN_AUTH;
        }
        */

        // 将字段校准加入到要修改的字段中
        $modifyFields['correct'] = $this->_genCorrectCtl($correctFields);

        $changedFields = [];
        // 去掉没有变化的字段
        foreach ($modifyFields as $fieldName => $fieldValue) {
            // 去掉不存在的属性字段
            if (!isset($schoolInfo[$fieldName])) {
                continue;
            }
            if (strval($fieldValue) != strval($schoolInfo[$fieldName])) {
                $changedFields[$fieldName] = $fieldValue;
            }
        }
        if (empty($changedFields)) {
            return $arrOutput;
        }

        // spam检查。未对接，先下掉，线上调用的参数也不对。
        /*
        if ($changedFields['schoolName']) {
            $spamData = [
                'qid'     => intval($schoolId),
                'uid'     => intval($userInfo['uid']),
                'content' => $changedFields['schoolName'],
                'uip'     => Bd_Ip::getClientIp(),
            ];
            $this->_spamService->sendToSpam($spamData);
        }
        */

        // 编辑学校时，编辑学校名称、省市区任意一项才需要验重
        if ($changedFields['schoolName'] || $changedFields['provinceId'] || $changedFields['cityId'] || $changedFields['countyId']) {
            $chkSchName    = ($changedFields['schoolName'])?? $schoolInfo['schoolName'];
            $chkSchCountyId = ($changedFields['countyId'])?? $schoolInfo['countyId'];
            $checkRet = $this->_chkSchoolRepeat($chkSchName, $chkSchCountyId, self::OPT_MODIFY, $schoolId);
            if (!Lxjxcrm_ExceptionCodes::isSuccess($checkRet['errno'])) {
                $arrOutput['errno'] = $checkRet['errno'];
                return $arrOutput;
            }
        }

        $ret = $this->_schoolService->modifySchool($changedFields, $schoolId);
        if (false === $ret) {
            $arrOutput['errno'] = Lxjxcrm_ExceptionCodes::DB_ERROR;
            return $arrOutput;
        }

        if ($ret) {
            // 如果修改成功，且修改了省、市、区、学校名称，需要删除给魔方提供的省市区学校的缓存
            if ($this->_dsCrmSchoolArea->chkHasAreaChanged($changedFields)) {
                Hk_Util_Log::start("ps_schoolarea_cache_refresh");
                $this->_dsCrmSchoolArea->RefreshAreaListCache([$schoolInfo]);
                Hk_Util_Log::stop("ps_schoolarea_cache_refresh");
            }

            // 如果修改成功，且修改了省、市、区、学校名称，需要通知伏羲学校库修改学校信息
            $arrFuxiSchData = []; // 有变化的字段才通知伏羲
            $arrKeyForFuxiChanged = ['provinceId', 'cityId', 'countyId', 'schoolName'];
            foreach ($arrKeyForFuxiChanged as $keyChg) {
                if (isset($changedFields[$keyChg])) {
                    $arrFuxiSchData[$keyChg] = $changedFields[$keyChg];
                }
            }
            if ($arrFuxiSchData) {
                $arrFuxiSchData['operatorName'] = $userInfo['email_prefix'];

                Hk_Util_Log::start("ps_school_fuxi_modify");
                $resFuxi = $this->_clientFuxi->modifySchool($schoolId, $arrFuxiSchData);
                Bd_Log::addNotice("fuxiSchUpd", 'unknown');
                if ($resFuxi && is_array($resFuxi) && 0 < $resFuxi['schoolId']) {
                    Bd_Log::addNotice("fuxiSchUpd", 'done');
                } else {
                    Bd_Log::addNotice("fuxiSchUpd", 'undone');
                    Bd_Log::warning("fuxi school add failed, resMsg:{$resFuxi['resMsg']}");
                }
                Hk_Util_Log::stop("ps_school_fuxi_modify");
            }
        }

        return $arrOutput;;
    }

    /**
     * 小程序修改学校信息
     * @param $schoolId
     * @param $modifyFields
     * @param $userInfo
     * @return mixed|bool|int
     */
    public function mpModifySchool($schoolId, $modifyFields, $userInfo)
    {
        $arrOutput = [
            'errno' => Lxjxcrm_ExceptionCodes::SUCCESS,
        ];

        // 学校标准名称不能为空
        if (isset($modifyFields['schoolName']) && 0 >= strlen($modifyFields['schoolName'])) {
            $arrOutput['errno'] = Lxjxcrm_ExceptionCodes::SCHOOL_NAME_INVALID;
            return $arrOutput;
        }

        // 检验省市区是否级联
        $bHasAreaKey = false;
        $arrKeysArea = ['provinceId', 'provinceName', 'cityId', 'cityName', 'countyId', 'countyName'];
        foreach ($arrKeysArea as $keyChk) {
            if (isset($modifyFields[$keyChk])) {
                $bHasAreaKey = true;
                break;
            }
        }
        if ($bHasAreaKey) {
            $checkRet = $this->_checkAreaCodeName($modifyFields['provinceId'], $modifyFields['provinceName'], $modifyFields['cityId'], $modifyFields['cityName'], $modifyFields['countyId'], $modifyFields['countyName']);
            if (!Lxjxcrm_ExceptionCodes::isSuccess($checkRet['errno'])) {
                $arrOutput['errno'] = $checkRet['errno'];
                return $arrOutput;
            }
        }

        $schoolInfo = $this->_dsLibSchool->getRecordByConds(['schoolId' => $schoolId]);
        if (empty($schoolInfo)) {
            $arrOutput['errno'] = Lxjxcrm_ExceptionCodes::SCHOOL_NOT_EXIST;
            return $arrOutput;
        }

        // 暂先保留，后续万一用到呢？
        // 已认领学校需检查用户权限
        /*
        if (Lxjxlib_Const_Crm_School::CLAIM_STATUS_YES == $schoolInfo['claimStatus'] &&
            Lxjxlib_Service_PrivilegeScopeNew::AUTH_SCOPE_ALL != $userInfo['auth_scope'] &&
            $schoolInfo['claimantUid'] != $userInfo['uid'] &&
            !$this->isSuperiorLeader($userInfo['uid'], $schoolInfo['claimantUid'], $userInfo['auth_scope'])) {
            $arrOutput['errno'] = Lxjxcrm_ExceptionCodes::CRM_USER_AUTH_LIMIT;
            return $arrOutput;
        }
        */

        $changedFields = [];
        // 去掉没有变化的字段
        foreach ($modifyFields as $fieldName => $fieldValue) {
            // 去掉不存在的属性字段
            if (!isset($schoolInfo[$fieldName])) {
                continue;
            }
            if (strval($fieldValue) != strval($schoolInfo[$fieldName])) {
                $changedFields[$fieldName] = $fieldValue;
            }
        }
        if (empty($changedFields)) {
            return $arrOutput;
        }

        // 只能修改可以修改的字段
        $resChk = $this->_checkIsModifiable($changedFields, $schoolInfo);
        if (!Lxjxcrm_ExceptionCodes::isSuccess($resChk)) {
            $arrOutput['errno'] = $resChk;
            return $arrOutput;
        }

        // spam检查。未对接，先下掉，线上调用的参数也不对。
        /*
        if ($changedFields['schoolName']) {
            $spamData = [
                'qid'     => intval($schoolId),
                'uid'     => intval($userInfo['uid']),
                'content' => $changedFields['schoolName'],
                'uip'     => Bd_Ip::getClientIp(),
            ];
            $this->_spamService->sendToSpam($spamData);
        }
        */

        // 编辑学校时，编辑学校名称、省市区任意一项才需要验重
        if ($changedFields['schoolName'] || $changedFields['provinceId'] || $changedFields['cityId'] || $changedFields['countyId']) {
            $chkSchName    = ($changedFields['schoolName'])?? $schoolInfo['schoolName'];
            $chkSchCountyId = ($changedFields['countyId'])?? $schoolInfo['countyId'];
            $checkRet = $this->_chkSchoolRepeat($chkSchName, $chkSchCountyId, self::OPT_MODIFY, $schoolId);
            if (!Lxjxcrm_ExceptionCodes::isSuccess($checkRet['errno'])) {
                $arrOutput['errno'] = $checkRet['errno'];
                return $arrOutput;
            }
        }

        $ret = $this->_schoolService->modifySchool($changedFields, $schoolId);
        if (false === $ret) {
            $arrOutput['errno'] = Lxjxcrm_ExceptionCodes::DB_UPDATE_ERR;
            return $arrOutput;
        }

        if ($ret) {
            // 如果修改成功，且修改了省、市、区、学校名称，需要删除给魔方提供的省市区学校的缓存
            if ($this->_dsCrmSchoolArea->chkHasAreaChanged($changedFields)) {
                Hk_Util_Log::start("ps_schoolarea_cache_refresh");
                $this->_dsCrmSchoolArea->RefreshAreaListCache([$schoolInfo]);
                Hk_Util_Log::stop("ps_schoolarea_cache_refresh");
            }

            // 如果修改成功，且修改了省、市、区、学校名称，需要通知伏羲学校库修改学校信息
            $arrFuxiSchData = []; // 有变化的字段才通知伏羲
            $arrKeyForFuxiChanged = ['provinceId', 'cityId', 'countyId', 'schoolName'];
            foreach ($arrKeyForFuxiChanged as $keyChg) {
                if (isset($changedFields[$keyChg])) {
                    $arrFuxiSchData[$keyChg] = $changedFields[$keyChg];
                }
            }
            if ($arrFuxiSchData) {
                $arrFuxiSchData['operatorName'] = $userInfo['email_prefix'];

                Hk_Util_Log::start("ps_school_fuxi_modify");
                $resFuxi = $this->_clientFuxi->modifySchool($schoolId, $arrFuxiSchData);
                Bd_Log::addNotice("fuxiSchUpd", 'unknown');
                if ($resFuxi && is_array($resFuxi) && 0 < $resFuxi['schoolId']) {
                    Bd_Log::addNotice("fuxiSchUpd", 'done');
                } else {
                    Bd_Log::addNotice("fuxiSchUpd", 'undone');
                    Bd_Log::warning("fuxi school add failed, resMsg:{$resFuxi['resMsg']}");
                }
                Hk_Util_Log::stop("ps_school_fuxi_modify");
            }
        }

        return $arrOutput;
    }


    /**
     * 修改学校-伏羲学校库将学校信息同步到进校
     * @param $schoolId
     * @param $modifyFields
     * @return mixed|bool|int
     */
    public function fuxiModifySchool($schoolId, $modifyFields)
    {
        $arrOutput = [
            'errno' => Lxjxcrm_ExceptionCodes::SUCCESS,
        ];

        // 严格控制伏羲学校库的可修改的字段
        $modifyFieldsTmp = [];
        $arrKeysFuxi = ['schoolName', 'provinceId', 'provinceName', 'cityId', 'cityName', 'countyId', 'countyName'];
        foreach ($arrKeysFuxi as $fieldName) {
            if (isset($modifyFields[$fieldName])) {
                $modifyFieldsTmp[$fieldName] = $modifyFields[$fieldName];
            }
        }
        if (!$modifyFieldsTmp) {
            $arrOutput['errno'] = Lxjxcrm_ExceptionCodes::REQUEST_PARAM_ERROR;
            return $arrOutput;
        }
        $modifyFields = $modifyFieldsTmp;

        // 学校标准名称不能为空
        if (isset($modifyFields['schoolName']) && 0 >= strlen($modifyFields['schoolName'])) {
            $arrOutput['errno'] = Lxjxcrm_ExceptionCodes::SCHOOL_NAME_INVALID;
            return $arrOutput;
        }

        // 获取学校原数据
        $schoolInfo = $this->_dsLibSchool->getRecordByConds(['schoolId' => $schoolId]);
        if (!$schoolInfo) {
            $arrOutput['errno'] = Lxjxcrm_ExceptionCodes::SCHOOL_NOT_EXIST;
            return $arrOutput;
        }

        $changedFields = [];
        // 去掉没有变化的字段
        foreach ($modifyFields as $fieldName => $fieldValue) {
            // 去掉不存在的属性字段
            if (!isset($schoolInfo[$fieldName])) {
                continue;
            }
            if (strval($fieldValue) != strval($schoolInfo[$fieldName])) {
                $changedFields[$fieldName] = $fieldValue;
            }
        }
        if (!$changedFields) {
            return $arrOutput;
        }

        // 检验省市区是否级联
        $bHasAreaKey = false;
        $arrKeysArea = ['provinceId', 'provinceName', 'cityId', 'cityName', 'countyId', 'countyName'];
        foreach ($arrKeysArea as $keyChk) {
            if (isset($changedFields[$keyChk])) {
                $bHasAreaKey = true;
                break;
            }
        }
        if ($bHasAreaKey) {
            $modProvinceId   = ($changedFields['provinceId']) ?? $schoolInfo['provinceId'];
            $modProvinceName = ($changedFields['provinceName']) ?? $schoolInfo['provinceName'];
            $modCityId       = ($changedFields['cityId']) ?? $schoolInfo['cityId'];
            $modCityName     = ($changedFields['cityName']) ?? $schoolInfo['cityName'];
            $modCountyId     = ($changedFields['countyId']) ?? $schoolInfo['countyId'];
            $modCountyName   = ($changedFields['countyName'])?? $schoolInfo['countyName'];
            $checkRet = $this->_checkAreaCodeName($modProvinceId, $modProvinceName, $modCityId, $modCityName, $modCountyId, $modCountyName);
            if (!Lxjxcrm_ExceptionCodes::isSuccess($checkRet['errno'])) {
                $arrOutput['errno'] = $checkRet['errno'];
                return $arrOutput;
            }
        }

        // 编辑学校时，编辑学校名称、省市区任意一项才需要验重
        if ($changedFields['schoolName'] || $changedFields['provinceId'] || $changedFields['cityId'] || $changedFields['countyId']) {
            $chkSchName    = ($changedFields['schoolName'])?? $schoolInfo['schoolName'];
            $chkSchCountyId = ($changedFields['countyId'])?? $schoolInfo['countyId'];
            $checkRet = $this->_chkSchoolRepeat($chkSchName, $chkSchCountyId, self::OPT_MODIFY, $schoolId);
            if (!Lxjxcrm_ExceptionCodes::isSuccess($checkRet['errno'])) {
                $arrOutput['errno'] = $checkRet['errno'];
                return $arrOutput;
            }
        }

        $ret = $this->_schoolService->modifySchool($changedFields, $schoolId);
        if (false === $ret) {
            $arrOutput['errno'] = Lxjxcrm_ExceptionCodes::DB_ERROR;
            return $arrOutput;
        }

        // 如果修改成功，且修改了省、市、区、学校名称，需要删除给魔方提供的省市区学校的缓存
        if ($ret && $this->_dsCrmSchoolArea->chkHasAreaChanged($changedFields)) {
            Hk_Util_Log::start("ps_schoolarea_cache_refresh");
            $this->_dsCrmSchoolArea->RefreshAreaListCache([$schoolInfo]);
            Hk_Util_Log::stop("ps_schoolarea_cache_refresh");
        }

        return $arrOutput;
    }

    /**
     * 检验字段是否可以修改
     * @param $changedFields
     * @param $schoolInfo
     * @return int
     */
    private function _checkIsModifiable($changedFields, $schoolInfo)
    {
        // 不可修改的字段
        $denyFields = $this->denyFields;
        // 需要控制修改的字段
        $ctlFields = $this->ctlFields;

        foreach ($changedFields as $field => $val) {
            if (in_array($field, $denyFields)) {
                return Lxjxcrm_ExceptionCodes::SCHOOL_FIELD_CANNOT_MODIFY;
            }
        }
        if (empty($schoolInfo['correct'])) {
            return Lxjxcrm_ExceptionCodes::SUCCESS;
        }
        $correct = json_decode($schoolInfo['correct'], true);
        // 未校准任何字段
        if (empty($correct)) {
            return Lxjxcrm_ExceptionCodes::SUCCESS;
        }
        // 需要控制的字段，未校准可以修改，已校准不能修改
        foreach ($changedFields as $field => $val) {
            // 需要控制修改的字段
            if (!in_array($field, $ctlFields)) {
                continue;
            }
            // 已校准字段不允许修改
            if (isset($correct[$field]) && $correct[$field] == Lxjxcrm_Const_School::ADJUST_YES) {
                return Lxjxcrm_ExceptionCodes::SCHOOL_CORRECT_FIELD_CANNOT_MODIFY;
            }
        }
        return Lxjxcrm_ExceptionCodes::SUCCESS;
    }


    /**
     * 检测学校是否重复:进校一期需求的校验逻辑
     * @param string $schoolName
     * @param int $countyId
     * @param string $optType :操作类型
     * @param int $schoolId
     * @return array
     */
    private function _chkSchoolRepeat($schoolName, $countyId, $optType = self::OPT_ADD, $schoolId = 0)
    {
        // 展示的最大重复数量
        $maxRepeatNum = 10;

        $ret = [
            'errno' => Lxjxcrm_ExceptionCodes::SUCCESS,
        ];

        $county = $this->_dsLibGeoDistrict->getCounty($countyId);
        if (empty($county)) {
            $ret['errno'] = Lxjxlib_Const_Errors::ERROR_COUNTY_NOT_EXIST;
            return $ret;
        }
        $condArr = [
            'schoolName' => $schoolName,
            'provinceId' => $county['provinceId'],
            'cityId'     => $county['cityId'],
            'countyId'   => $countyId,
            'status'     => Lxjxlib_Const_Common::DB_STATUS_OK,
        ];

        if (self::OPT_MODIFY == $optType) {
            $condArr['schoolId'] = [$schoolId ?? 0, '<>'];
        }
        $schoolList     = $this->_dsLibSchool->getListByConds($condArr, $this->_dsLibSchool->getQueryFields());
        // 不能有学校名称相同的学校，若有重复学校，返回学校id、名称
        $repeatSchoolList = [];
        if ($schoolList && is_array($schoolList)) {
            foreach ($schoolList as $schoolInfo) {
                if ($maxRepeatNum) {
                    $repeatSchoolList[] = $this->_pickSchoolSummaryInfo($schoolInfo);
                    $maxRepeatNum--;
                    if ($maxRepeatNum < 1) {
                        break;
                    }
                }
            }
            if ($repeatSchoolList) {
                if ($optType == self::OPT_ADD) {
                    $ret['errno'] = Lxjxcrm_ExceptionCodes::SCHOOL_NAME_DUPLICATE;
                } else {
                    $ret['errno'] = Lxjxcrm_ExceptionCodes::SCHOOL_NAME_DUPLICATE;
                }
                $ret['schoolList'] = $repeatSchoolList;
                return $ret;
            }
        }
        return $ret;
    }

    /**
     * 检测学校是否重复:老的校验逻辑
     * @param $countyId
     * @param $phase
     * @param $schoolName
     * @param $optType :操作类型
     * @param $schoolId
     * @return array
     */
    private function _isSchoolRepeat($countyId, $phase, $schoolName, $optType = self::OPT_ADD, $schoolId = null)
    {
        // 展示的最大重复数量
        $maxRepeatNum = 10;
        $ret          = [
            'errno' => Lxjxlib_Const_Errors::ERROR_SUCCESS,
        ];

        $county = $this->_dsLibGeoDistrict->getCounty($countyId);
        if (empty($county)) {
            $ret['errno'] = Lxjxlib_Const_Errors::ERROR_COUNTY_NOT_EXIST;
            return $ret;
        }
        $condArr = [
            'provinceId' => $county['provinceId'],
            'cityId'     => $county['cityId'],
            'countyId'   => $countyId,
            'eduPhase'   => $phase,
            'status'     => Lxjxlib_Const_Common::DB_STATUS_OK,
        ];
        if ($optType == self::OPT_MODIFY) {
            $condArr['schoolId'] = [$schoolId ?? 0, '<>'];
        }
        // 名字去掉行政区划字号
        $pureSchoolName = $this->_schoolService->removeRegion($county['provinceName'], $county['cityName'], $county['countyName'], $schoolName);
        $schoolList     = $this->_dsLibSchool->getListByConds($condArr, $this->_dsLibSchool->getQueryFields());
        // 同省市区下，同学部，去掉行政字号名称相同，视为同一所学校
        // 有重复学校，返回学校id、名称
        $repeatSchoolList = null;
        if ($schoolList && is_array($schoolList)) {
            foreach ($schoolList as $k => $schoolInfo) {
                $tmpPureSchoolName = $this->_schoolService->removeRegion($schoolInfo['provinceName'], $schoolInfo['cityName'], $schoolInfo['countyName'], $schoolInfo['schoolName']);
                // 去省市区后名称一致
                if ($tmpPureSchoolName == $pureSchoolName && $maxRepeatNum) {
                    $repeatSchoolList[] = $this->_pickSchoolSummaryInfo($schoolInfo);
                    $maxRepeatNum--;
                    if ($maxRepeatNum < 1) {
                        break;
                    }
                }
            }
            if ($repeatSchoolList) {
                if ($optType == self::OPT_ADD) {
                    $ret['errno'] = Lxjxlib_Const_Errors::ERROR_ADD_SCOPE_SCHOOL_REPEAT;
                } else {
                    $ret['errno'] = Lxjxlib_Const_Errors::ERROR_MODIFY_SCOPE_SCHOOL_REPEAT;
                }
                $ret['schoolList'] = $repeatSchoolList;
                return $ret;
            }
        }
        return $ret;
    }

    /**
     * 获取不可修改的字段
     * @param $schoolId
     * @return array|string[]
     */
    public function getCantModifyFields($schoolId)
    {
        // 不可修改的字段
        $cantModifyArr = $this->denyFields;
        $schoolInfo    = $this->_dsLibSchool->getRecordByConds(['schoolId' => $schoolId]);
        // 已校准的控制字段，不可修改
        if (empty($schoolInfo['correct'])) {
            return $cantModifyArr;
        }
        $correct = json_decode($schoolInfo['correct'], true);
        if (empty($correct) || !is_array($correct)) {
            return $cantModifyArr;
        }
        foreach ($this->ctlFields as $field) {
            if (isset($correct[$field]) && $correct[$field] == Lxjxcrm_Const_School::ADJUST_YES) {
                $cantModifyArr[] = $field;
            }
        }
        return $cantModifyArr;
    }

    /**
     * @param $schoolId
     * @param $curUser
     * @return array
     */
    public function getSchoolInfoWithAuth($schoolId, $curUser)
    {
        // 查询学校信息
        $schoolInfo = $this->_schoolService->getSchoolInfoBySchoolId($schoolId);
        if (!is_array($schoolInfo) || empty($schoolInfo)) {
            return [];
        }

        $bHasAuth = false;
        switch ($curUser['auth_scope']) {
            case Lxjxlib_Service_PrivilegeScopeNew::AUTH_SCOPE_ALL:
                $bHasAuth = true;
                break;
            case Lxjxlib_Service_PrivilegeScopeNew::AUTH_SCOPE_PARTIAL:
                // 只能看到自己和下属跟进的学校客户
                $arrUidList = $this->_serviceLxjxauth->getUserSubUid($curUser['uid'], 1, 1);
                if (Lxjxlib_Const_Crm_School::CLAIM_STATUS_YES == $schoolInfo['claimStatus'] && 0 < $schoolInfo['claimantUid']
                    && $arrUidList && is_array($arrUidList['subUids']) && in_array($schoolInfo['claimantUid'], $arrUidList['subUids'])) {
                    $bHasAuth = true;
                }
                break;
            case Lxjxlib_Service_PrivilegeScopeNew::AUTH_SCOPE_SELF:
                // 只能看到自己跟进的学校客户
                if (Lxjxlib_Const_Crm_School::CLAIM_STATUS_YES == $schoolInfo['claimStatus']
                    && 0 < $schoolInfo['claimantUid'] && $curUser['uid'] == $schoolInfo['claimantUid']) {
                    $bHasAuth = true;
                }
                break;
        }

        if (!$bHasAuth) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::CRM_USER_AUTH_LIMIT, '请确认账号角色', ['roleName' => $curUser['role']], Lxjxlib_Const_Exception::WARNING);
        }

        $fieldIsCorrect    = $this->_genFieldIsCorrect($schoolInfo); // 学校字段的校准状态
        $ret               = $this->_pickSchoolSummaryInfo($schoolInfo);
        $ret['isClaimant'] = $ret['claimantUid'] == $curUser['uid'] ? 1 : 0;
        return array_merge($ret, $fieldIsCorrect);
    }

    public function getSchoolInfoBySchoolId($schoolId, $curUser)
    {
        // 查询学校信息
        $schoolInfo = $this->_schoolService->getSchoolInfoBySchoolId($schoolId);
        if (!is_array($schoolInfo) || empty($schoolInfo)) {
            return [];
        }

        // 学校字段的校准状态
        $fieldIsCorrect    = $this->_genFieldIsCorrect($schoolInfo);
        $ret               = $this->_pickSchoolSummaryInfo($schoolInfo);
        $ret['isClaimant'] = $ret['claimantUid'] == $curUser['uid'] ? 1 : 0;
        $ret               = array_merge($ret, $fieldIsCorrect);

        return $ret;
    }

    /**
     * 生成字段是否校准
     * @param $schoolInfo
     * @return mixed
     */
    private function _genFieldIsCorrect($schoolInfo)
    {
        if ($schoolInfo && $schoolInfo['correct']) {
            if (is_string($schoolInfo['correct'])) {
                $correct = json_decode($schoolInfo['correct'], true);
            } else if (is_array($schoolInfo['correct'])) {
                $correct = $schoolInfo['correct'];
            }
        }
        // 需要控制的字段的校准状态
        $fieldCorrect['isNameCorrect']       = $correct['schoolName'] ?? 0;
        $fieldCorrect['isNatureCorrect']     = $correct['schoolNature'] ?? 0;
        $fieldCorrect['isPhaseCorrect']      = $correct['eduPhase'] ?? 0;
        $fieldCorrect['isProvinceIdCorrect'] = $correct['provinceId'] ?? 0;
        $fieldCorrect['isCityIdCorrect']     = $correct['cityId'] ?? 0;
        $fieldCorrect['isCountyIdCorrect']   = $correct['countyId'] ?? 0;
        $fieldCorrect['isAddressCorrect']    = $correct['address'] ?? 0;
        $fieldCorrect['isLongitudeCorrect']  = $correct['longitude'] ?? 0;
        $fieldCorrect['isLatitudeCorrect']   = $correct['latitude'] ?? 0;
        return $fieldCorrect;
    }

    public function getSchoolAuthInfo($userInfo)
    {
        $bHasAllocateAuth = Lxjxlib_Const_Crm_User::chkHasAuthSchoolCustomerManagement($userInfo['role']);
        // 不再能认领
        $bHasClaimAuth = false;

        return [
            'sign'     => Lxjxlib_Const_Crm_School::AUTH_SIGN_YES,
            'allocate' => !$bHasAllocateAuth ? 0 : 1,
            'claim'    => !$bHasClaimAuth ? 0 : 1,
        ];
    }

    public function checkSchool($params)
    {
        $ret        = [];
        $schoolList = $this->_schoolService->getSchoolListByCityOrName($params);
        if (empty($schoolList)) {
            return $ret;
        }

        $checkLocation = isset($params['longitude']) || isset($params['latitude']);

        $checkPoint = [
            'longitude' => floatval($params['longitude']),
            'latitude'  => floatval($params['latitude']),
        ];

        foreach ($schoolList as $schoolInfo) {
            $matchType = '';
            if (!empty($params['schoolName']) && $schoolInfo['schoolName'] == $params['schoolName']) {
                // 名字匹配
                $matchType = 'name';
            } else if ($checkLocation) {
                // 计算坐标偏移
                $startPoint = [
                    'longitude' => floatval($schoolInfo['longitude']),
                    'latitude'  => floatval($schoolInfo['latitude']),
                ];
                $distance   = $this->getDistanceInPoints($startPoint, $checkPoint);

                // 位置匹配
                $distance <= Lxjxlib_Const_Crm_School::SCHOOL_LOCATION_OFFSET && $matchType = 'location';
            }

            if (!empty($matchType)) {
                $schoolInfo              = $this->_pickSchoolSummaryInfo($schoolInfo);
                $schoolInfo['matchType'] = $matchType;
                $ret[]                   = $schoolInfo;
            }
        }

        return $ret;
    }

    /**
     * 小程序检验学校重复
     * @param $inputs
     * @param $userInfo
     * @return array
     */
    public function mpCheckRepeat($inputs, $userInfo)
    {
        $schoolId = Lxjxlib_Service_IdAlloc::createRecordId();
        $spamData = [
            'qid'     => intval($schoolId),
            'uid'     => intval($userInfo['uid']),
            'content' => $inputs['schoolName'],
            'uip'     => Bd_Ip::getClientIp(),
        ];

        // spam检查。未对接，先下掉，线上调用的参数也不对。
        // $this->_spamService->sendToSpam($spamData);
        return $this->_chkSchoolRepeat($inputs['schoolName'], $inputs['countyId']);
    }

    /**
     * @param $countyId
     * @param $schoolName
     * @param $phase
     * @return false|string
     */
    private function _genSimpleName($countyId, $schoolName, $phase, $optType = self::OPT_ADD, $schoolId = null)
    {
        $county = $this->_dsLibGeoDistrict->getCounty($countyId);
        if (empty($county)) {
            Bd_Log::warning('countyId not exist :' . $countyId);
            return $schoolName;
        }
        // 名称后去掉末级行政区
        $schoolName = $this->_removeLastOrigin($county, $schoolName);
        // 本次处理的学校为新的学部，则在本次处理的学校别名后加'|学部'
        $condArr = [
            'provinceId' => $county['provinceId'],
            'cityId'     => $county['cityId'],
            'countyId'   => $countyId,
            'eduPhase'   => [$phase, '<>'],
            'status'     => Lxjxlib_Const_Common::DB_STATUS_OK,
        ];
        if ($optType == self::OPT_MODIFY) {
            $condArr['schoolId'] = [$schoolId ?? 0, '<>'];
        }
        // 名字去掉行政区划字号
        $pureSchoolName = $this->_schoolService->removeRegion($county['provinceName'], $county['cityName'], $county['countyName'], $schoolName);
        $condArr[]      = 'school_name like "%' . $pureSchoolName . '%"';
        $schoolExist    = $this->_dsLibSchool->getCntByConds($condArr);
        if ($schoolExist) {
            // 存在其他学部学校；在本次处理的学校别名后加学部
            $schoolName .= '|' . Lxjxcrm_Const_School::getEduStageName($phase) . '部';
        }
        return $schoolName;
    }

    /**
     * 去掉末级行政区
     * @param $county
     * @param $schoolName
     * @return false|string
     */
    private function _removeLastOrigin($county, $schoolName)
    {
        // 去掉末级行政区
        $pos = strpos($schoolName, $county['countyName']);
        if ($pos !== false) {
            return substr($schoolName, $pos);
        }
        $pos = strpos($schoolName, $county['cityName']);
        if ($pos !== false) {
            return substr($schoolName, $pos);
        }
        $pos = strpos($schoolName, $county['provinceName']);
        if ($pos !== false) {
            return substr($schoolName, $pos);
        }
        return $schoolName;
    }

    /**
     * 获取字段校准
     * @param $correctFields
     * @return false|string
     */
    private function _genCorrectCtl($correctFields)
    {
        // 记录需要控制的字段是否校准
        $correct = [
            'schoolName'   => $correctFields['isNameCorrect'] ?? 1,
            'schoolNature' => $correctFields['isNatureCorrect'] ?? 1,
            'eduPhase'     => $correctFields['isPhaseCorrect'] ?? 1,
            'provinceId'   => $correctFields['isProvinceIdCorrect'] ?? 1,
            'cityId'       => $correctFields['isCityIdCorrect'] ?? 1,
            'countyId'     => $correctFields['isCountyIdCorrect'] ?? 1,
            'address'      => $correctFields['isAddressCorrect'] ?? 1,
            'longitude'    => $correctFields['isLongitudeCorrect'] ?? 1,
            'latitude'     => $correctFields['isLatitudeCorrect'] ?? 1,
        ];
        return json_encode($correct);
    }

    public function getSchoolTraceAmount($schoolId)
    {
        return $this->_dataTraces->getSchoolTraceAmount($schoolId);
    }

    public function getSchoolTraceList($schoolId, $pn, $rn)
    {
        return $this->_dataTraces->getSchoolTraceList($schoolId, $pn, $rn);
    }

    public function deleteSchool($schoolId, $userInfo)
    {
        // 检查学校是否可删除
        $schoolInfo = $this->_schoolService->getSchoolInfoBySchoolId($schoolId);
        if (empty($schoolInfo) || Lxjxlib_Const_Common::DB_STATUS_OK != $schoolInfo['status']) {
            return Lxjxcrm_ExceptionCodes::SCHOOL_NOT_EXIST;
        }

        // 需要学校无拜访计划
        $queryScheme = ['schoolId' => $schoolId];
        $cntScheme = $this->_dataVisit->getVisitSchemeAmountV1($queryScheme);
        if (0 < $cntScheme) {
            return Lxjxcrm_ExceptionCodes::SCHOOL_DELETE_4_HAS_VISIT_SCHEME;
        }
        // 需要学校无领课数据
        $retCR = $this->_clientLxjxdata->getCourseRecordAmountBySchoolId($schoolId);
        if (!$retCR) {
            return Lxjxcrm_ExceptionCodes::RAL_ERR;
        }
        if (0 < $retCR['total']) {
            return Lxjxcrm_ExceptionCodes::SCHOOL_DELETE_4_HAS_ZEROACT_COURSE_RECORD;
        }

        // 删除学校
        $res = $this->_schoolService->deleteSchoolById($schoolId);
        if (false === $res) {
            return Lxjxcrm_ExceptionCodes::DB_DELETE_ERR;
        }
        if (0 < $res) {
            // 删除学校联系人
            $this->_dataContact->deleteContactBySchool($schoolId);

            // 如果修改成功，且修改了省、市、区、学校名称，需要删除给魔方提供的省市区学校的缓存
            Hk_Util_Log::start("ps_schoolarea_cache_refresh");
            $this->_dsCrmSchoolArea->RefreshAreaListCache([$schoolInfo]);
            Hk_Util_Log::stop("ps_schoolarea_cache_refresh");

            // 通知伏羲学校库:删除学校
            Hk_Util_Log::start("ps_school_fuxi_del");
            $arrFuxiSchData = ['operatorName' => $userInfo['email_prefix']];
            $resFuxi = $this->_clientFuxi->deleteSchool($schoolId, $arrFuxiSchData);
            Bd_Log::addNotice("fuxiSchDel", 'unknown');
            if ($resFuxi && is_array($resFuxi)) {
                Bd_Log::addNotice("fuxiSchDel", 'done');
                if (0 != $resFuxi['resCode']) {
                    Bd_Log::addNotice("fuxiSchDel", 'undone');
                    Bd_Log::warning("fuxi school del failed, resCode:{$resFuxi['resCode']}, resMsg:{$resFuxi['resMsg']}");
                }
            }
            Hk_Util_Log::stop("ps_school_fuxi_del");
        }
        return Lxjxcrm_ExceptionCodes::SUCCESS;
    }

    /**
     * @function transferSchool
     * @access   public
     * @date     2020-08-31
     * @desc     转移学校
     * @param    $schoolIds
     * @param    $toUid
     * @return   int
     * @throws   Lxjxlib_Const_Exception
     */
    public function transferSchool($schoolIds, $toUid)
    {
        $ret = 0;

        // 用户有效性验证
        $toCrmUser = $this->_serviceLxjxauth->getUserDetail($toUid);
        if (empty($toCrmUser)) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::CRM_USER_NOT_EXIST);
        }

        // 获取学校信息
        $schoolList = $this->_schoolService->getSchoolCustomerBySchoolIds($schoolIds);
        if (!is_array($schoolList) || empty($schoolList)) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::REQUEST_PARAM_VALUE);
        }

        $transferIds = [];
        foreach ($schoolList as $school) {
            // 未认领或者认领人不需变更
            if (Lxjxlib_Const_Crm_School::CLAIM_STATUS_NO == $school['claimStatus'] || $toUid == $school['claimantUid']) {
                continue;
            }

            $transferIds[] = $school['schoolId'];
        }

        if (!empty($transferIds)) {
            // 批量更新学校认领人
            $upFields = [
                'claimantUid'      => $toCrmUser['uid'],
                'claimantName'     => $toCrmUser['name'],
                'claimantDepartId' => $toCrmUser['departId'],
            ];

            $ret = $this->_schoolService->modifySchoolBatch($upFields, $transferIds);
        }

        return $ret;
    }

    /**
     * @function transferSchoolByCondition
     * @access   public
     * @date     2024-06-24
     * @desc     根据条件转移全部学校
     * @param    $queryFields 查询条件
     * @param    $toUid 目标用户ID
     * @param    $curUser 当前用户信息
     * @param    $schoolScope 查询范围
     * @return   int 转移的学校数量
     * @throws   Lxjxlib_Const_Exception
     */
    public function transferSchoolByCondition($queryFields, $toUid, $curUser, $schoolScope)
    {
        $ret = 0;

        // 用户有效性验证
        $toCrmUser = $this->_serviceLxjxauth->getUserDetail($toUid);
        if (empty($toCrmUser)) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::CRM_USER_NOT_EXIST);
        }

        // 构建查询条件
        $this->_initQuerySchoolConditions($queryFields, $curUser, $schoolScope);
        if (!is_array($this->_querySchool)) {
            return 0;
        }

        // 获取符合条件的所有学校ID（分批获取，避免内存溢出）
        $allSchoolIds = [];
        $pn = 0;
        $rn = 1000; // 每次获取1000条

        while (true) {
            $schoolList = $this->_schoolService->getSchoolsList($this->_querySchool, $pn, $rn);

            if (empty($schoolList['list'])) {
                break;
            }

            foreach ($schoolList['list'] as $school) {
                $allSchoolIds[] = $school['schoolId'];
            }

            // 如果返回的数据少于rn，说明已经是最后一页
            if (count($schoolList['list']) < $rn) {
                break;
            }

            $pn++;
        }

        if (empty($allSchoolIds)) {
            return 0;
        }

        // 调用原有的转移逻辑
        $ret = $this->transferSchool($allSchoolIds, $toUid);

        return $ret;
    }

    /**
     * 获取可见的学部列表
     * @return array|null
     */
    public function getSchoolPhaseList()
    {
        return [
            Lxjxlib_Const_Crm_School::EDU_STAGE_PRIMARY => Lxjxlib_Const_Crm_School::getEduStageName(Lxjxlib_Const_Crm_School::EDU_STAGE_PRIMARY),
            Lxjxlib_Const_Crm_School::EDU_STAGE_JUNIOR  => Lxjxlib_Const_Crm_School::getEduStageName(Lxjxlib_Const_Crm_School::EDU_STAGE_JUNIOR),
            Lxjxlib_Const_Crm_School::EDU_STAGE_SENIOR  => Lxjxlib_Const_Crm_School::getEduStageName(Lxjxlib_Const_Crm_School::EDU_STAGE_SENIOR),
        ];
    }

    /**
     * 检验省市区是否级联
     * @param $provinceId
     * @param $provinceName
     * @param $cityId
     * @param $cityName
     * @param $countyId
     * @param $countyName
     * @return array
     */
    private function _checkAreaCodeName($provinceId, $provinceName, $cityId, $cityName, $countyId, $countyName)
    {
        $ret = [
            'errno' => Lxjxcrm_ExceptionCodes::SUCCESS,
        ];

        $dbCounty = $this->_dsLibGeoDistrict->getCounty($countyId);
        if (empty($dbCounty)) {
            $ret['errno'] = Lxjxcrm_ExceptionCodes::AREA_COUNTY_NOT_EXIST;
            return $ret;
        }
        if ($dbCounty['provinceId'] != $provinceId || $dbCounty['provinceName'] !== $provinceName) {
            $ret['errno'] = Lxjxcrm_ExceptionCodes::AREA_PROVINCE_NOT_EXIST;
            return $ret;
        }
        if ($dbCounty['cityId'] != $cityId || $dbCounty['cityName'] !== $cityName) {
            $ret['errno'] = Lxjxcrm_ExceptionCodes::AREA_CITY_NOT_EXIST;
            return $ret;
        }
        if ($dbCounty['countyId'] != $countyId || $dbCounty['countyName'] !== $countyName) {
            $ret['errno'] = Lxjxcrm_ExceptionCodes::AREA_COUNTY_NOT_EXIST;
            return $ret;
        }
        return $ret;
    }

    /**
     * 用于CRM后台：获取指定区域下的BD负责的学校(支持搜索，不带BD信息缓存)
     * @param array $arrInput
     * @param array $curUser
     * @return array
     **/
    public function getBdSchoolList($arrInput, $curUser)
    {
        $ret = [
            'total' => 0,
            'list'  => [],
        ];

        if (0 >= $curUser['uid']) {
            return $ret;
        }
        if (!$curUser['role']) {
            return $ret;
        }

        $subDepartIdList = [];
        if (Lxjxlib_Const_Crm_User::ROLE_NAME_CITY_MANAGER === $curUser['role'] || Lxjxlib_Const_Crm_User::ROLE_NAME_REGIONAL_DIRECTOR === $curUser['role']) {
            $crmUser = $this->_serviceLxjxauth->getUserDetail($curUser['uid']);
            if (0 < $crmUser['departId']) {
                $resSubDepartIdList = $this->_serviceLxjxauth->getSubDepartIdList($crmUser['departId']);
                $subDepartIdList = array_merge($resSubDepartIdList, [$crmUser['departId']]);
            }
        }

        return $this->_getSchoolListByCountyIdWithAuth($arrInput, $curUser, $subDepartIdList, 0);
    }

    /**
     * 用于MIS后台：获取指定区域下的学校(支持搜索，不带BD信息缓存)
     * @param array $arrInput
     * @param int $withCached 是否启用缓存。非0为启用
     * @return array
     **/
    public function getSchoolListByCounty($arrInput, $withCached = 1)
    {
        $curUser = [];
        $subDepartIdList = [];
        return $this->_getSchoolListByCountyIdWithAuth($arrInput, $curUser, $subDepartIdList, $withCached);
    }

    /**
     * 用于用户端活动页：获取指定区域下的BD负责的学校(支持搜索，带BD信息缓存)
     * @param array $arrInput
     * @param array $curUser
     * @param int $withCached 是否启用缓存。非0为启用
     * @return array
     **/
    public function getBdSchoolListForMofang($arrInput, $curUser, $withCached = 1)
    {
        $ret = [
            'total' => 0,
            'list'  => [],
        ];

        if (0 >= $curUser['uid']) {
            return $ret;
        }

        do {
            if ($withCached) {
                // 做短时间缓存，应对可能的高qps
                $cacheKey = sprintf('lxjxcrm_bdschool_departIdList_for_bd_%d', $curUser['uid']);
                $cacheData = $this->_redis->get($cacheKey);
                if ($cacheData) {
                    $bdAttrs = json_decode($cacheData, true);
                    Bd_Log::AddNotice('BdRoleSchCache', 'get');
                    break;
                }
            }

            Bd_Log::AddNotice('BdRoleSchCache', 'new');

            $bdAttrs = [
                'role'            => '',
                'subDepartIdList' => [],
            ];

            // 获取用户信息
            $crmUser = $this->_serviceLxjxauth->getUserDetail($curUser['uid']);
            if ($crmUser['role']) {
                $bdAttrs['role'] = $crmUser['role'];
                if (Lxjxlib_Const_Crm_User::ROLE_NAME_CITY_MANAGER === $crmUser['role'] || Lxjxlib_Const_Crm_User::ROLE_NAME_REGIONAL_DIRECTOR === $crmUser['role']) {
                    // 获取下属部门ID
                    if (0 < $crmUser['departId']) {
                        $resSubDepartIdList = $this->_serviceLxjxauth->getSubDepartIdList($crmUser['departId']);
                        $bdAttrs['subDepartIdList'] = array_merge($resSubDepartIdList, [$crmUser['departId']]);
                    }
                }
            }

            if ($withCached) {
                // 设置缓存，拿不到也缓存，避免被前端流量击穿
                $resCached = $this->_redis->set($cacheKey, json_encode($bdAttrs), Lxjxlib_Const_Common::EXPIRE_ONEMINUTE * 5);
                Bd_Log::AddNotice('BdRoleSchCache', sprintf('set_%d', intval($resCached)));
            }
        } while(0);

        Bd_Log::AddNotice('BdRoleSchRole', strval($bdAttrs['role']));

        $curUser['role'] = $bdAttrs['role'];
        if (!$curUser['role']) {
            return $ret;
        }

        return $this->_getSchoolListByCountyIdWithAuth($arrInput, $curUser, $bdAttrs['subDepartIdList'], $withCached);
    }

    /**
     * 获取指定区域内的学校(支持BD权限范围、支持学校名搜索)
     * @params $arrInput array
     * @return array
     **/
    private function _getSchoolListByCountyIdWithAuth($arrInput, $curUser, $subDepartIdList, $withCached = 1)
    {
        $ret = [
            'total' => 0,
            'list'  => [],
        ];

        if (0 >= $arrInput['countyId']) {
            return $ret;
        }

        $subDepartIdList = is_array($subDepartIdList)? $subDepartIdList : [];

        $isSupported = false; // 暂只支持uid和role同时有效和同时无效。
        $funcAuthCheck = function ($tmpSchInfo) use ($curUser, $subDepartIdList) {
            return false;
        };

        // 若未传入uid和role值，则表明不需要控制权限范围
        if (0 >= $curUser['uid'] && !$curUser['role']) {
            $isSupported = true;

            $funcAuthCheck = function ($tmpSchInfo) use ($curUser, $subDepartIdList) {
                return true;
            };
        } else if (0 < $curUser['uid'] && $curUser['role']) { // 若传入了role值，则表明需要控制权限
            $isSupported = true;

            // 进校BD本人：取分配给自己的学校客户
            // 进校BD城市经理：取本部门以及下级部门的学校客户
            // 进校BD大区总监：取本部门以及下级部门的学校客户
            // 进校全国总监、进校管理员、进校产研、进校中台运营： 取学校客户管理的全部学校
            switch ($curUser['role']) {
                case Lxjxlib_Const_Crm_User::ROLE_NAME_BD:
                    if (0 >= $curUser['uid']) {
                        return $ret;
                    }
                    $funcAuthCheck = function ($tmpSchInfo) use ($curUser, $subDepartIdList) {
                        if (!$tmpSchInfo || !is_array($tmpSchInfo) || 0 >= $curUser['uid']) {
                            return false;
                        }
                        if (Lxjxlib_Const_Crm_School::CLAIM_STATUS_YES == $tmpSchInfo['claimStatus'] && 0 < $tmpSchInfo['claimantUid'] && $curUser['uid'] == $tmpSchInfo['claimantUid']) {
                            return true;
                        }
                        return false;
                    };
                    break;
                case Lxjxlib_Const_Crm_User::ROLE_NAME_CITY_MANAGER:
                case Lxjxlib_Const_Crm_User::ROLE_NAME_REGIONAL_DIRECTOR:
                    $funcAuthCheck = function ($tmpSchInfo) use ($curUser, $subDepartIdList) {
                        if (!$tmpSchInfo || !is_array($tmpSchInfo) || !$subDepartIdList) {
                            return false;
                        }
                        if (0 < $tmpSchInfo['claimantDepartId'] && in_array($tmpSchInfo['claimantDepartId'], $subDepartIdList, true)) {
                            return true;
                        }
                        return false;
                    };
                    break;
                case Lxjxlib_Const_Crm_User::ROLE_NAME_ADMIN:
                case Lxjxlib_Const_Crm_User::ROLE_NAME_RD:
                case Lxjxlib_Const_Crm_User::ROLE_NAME_OPERATOR:
                case Lxjxlib_Const_Crm_User::ROLE_NAME_GENERAL_DIRECTOR:
                    $funcAuthCheck = function ($tmpSchInfo) use ($curUser, $subDepartIdList) {
                        return true;
                    };
                    break;
            }
        }

        if (!$isSupported) {
            return $ret;
        }

        $pn = $arrInput['pn'];
        $rn = $arrInput['rn'];

        //获取区县下所有有效学校
        $resSchList = $this->_dsCrmSchoolArea->getSchoolListForMoFang($arrInput['countyId'], $withCached);
        if (!$resSchList) {
            return $ret;
        }

        $arrSurplus = [];
        foreach ($resSchList as $itemSch) {
            if (is_callable($funcAuthCheck) && !$funcAuthCheck($itemSch)) {
                continue;
            }
            if (0 < $arrInput['schoolId'] && $itemSch['schoolId'] != $arrInput['schoolId']) {
                continue;
            }
            if ($arrInput['searchName'] && false === mb_strpos($itemSch['schoolName'], $arrInput['searchName'])) {
                continue;
            }

            $arrSurplus[] = [
                "schoolId"   => $itemSch["schoolId"],
                "schoolName" => $itemSch["schoolName"],
            ];
        }

        if (!$arrSurplus) {
            return $ret;
        }

        $ret['total'] = count($arrSurplus);
        // 排序
        usort($arrSurplus, function($a, $b) {
            return ($a['schoolId'] < $b['schoolId'])? -1 : 1;
        });
        $chunks = array_chunk($arrSurplus, $rn);
        if ($pn < count($chunks)) {
            $ret["list"] = $chunks[$pn];
        }

        return $ret;
    }

    public function getSchoolListBySchoolIds($arrInput){
        $schoolIds = explode(",",$arrInput['schoolIds']);
        // 获取学校信息
        $schoolList = $this->_schoolService->getSchoolCustomerBySchoolIds($schoolIds,['schoolId', 'schoolName']);
        if (empty($schoolList)) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::REQUEST_PARAM_VALUE);
        }
        return $schoolList;
    }

    public function getCountySchoolList($countyId){
        $countyIds = explode(",",$countyId);
        $ret = [];
        foreach ($countyIds as $id) {
            $resSchList = $this->_dsCrmSchoolArea->getSchoolListForMoFang($id, 1);
            if (!$resSchList) {
                continue;
            }
            $ret = array_merge($ret,$resSchList);
        }
        return $ret;
    }
}
