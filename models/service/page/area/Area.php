<?php

/**
 * @befie   获取省份城市相关
 * @file    models/service/area/Area.php
 * <AUTHOR>
 * @version 1.0
 * @date    2020-03-05
 */
class Service_Page_Area_Area
{
    protected $_dataDistrict = null;
    protected $_lxjxauthService = null;
    protected $_privilegeSer = null;

    public function __construct()
    {
        $this->_dataDistrict  = new Lxjxlib_Ds_Geo_District();
        $this->_lxjxauthService = new Lxjxlib_Service_Lxjxauth_Client();
        $this->_privilegeSer  = new Lxjxlib_Service_PrivilegeScopeNew();

    }

    public function execute()
    {
        $provinceList = $this->_dataDistrict->getAllProvince();

        foreach ($provinceList as &$value) {
            $cityList          = $this->_dataDistrict->getCityList($value['provinceId']);
            $value['cityList'] = $cityList;
        }
        return $provinceList;
    }

    public function getAreaAll()
    {
        return $this->_getAllProvinceCityCountyBatch();
    }

    /**
     * @function        _getAllProvinceCityCounty
     * @access          protected
     * @date            2020-04-09
     * @desc            获取全部的省市县数据
     * @return          array
     */
    protected function _getAllProvinceCityCounty()
    {
        $ret = [];

        // 全部省份
        $provinceList = $this->_dataDistrict->getAllProvince();

        foreach ($provinceList as $provinceItem) {
            $provinceData = [
                'provinceId'   => $provinceItem['provinceId'],
                'provinceName' => $provinceItem['provinceName'],
                'cityList'     => [],
            ];

            // 省份包含的城市
            $cityList = $this->_dataDistrict->getCityList($provinceItem['provinceId']);
            foreach ($cityList as $cityItem) {
                // 城市包含的区县
                $cityItem['countyList']     = $this->_dataDistrict->getCountyList($cityItem['cityId']);
                $provinceData['cityList'][] = $cityItem;
            }

            $ret[] = $provinceData;
        }

        return $ret;
    }


    /**
     * @function        _getAllProvinceCityCountyBatch
     * @access          protected
     * @date            2020-04-09
     * @desc            获取全部的省市县数据
     * @return          array
     */
    protected function _getAllProvinceCityCountyBatch()
    {
        $ret = [];

        // 全部省份
        $provinceList = $this->_dataDistrict->getAllProvince();

        $keyProvIdList = [];
        foreach ($provinceList as $provinceItem) {
            $provinceData = [
                'provinceId'   => $provinceItem['provinceId'],
                'provinceName' => $provinceItem['provinceName'],
                'cityList'     => [],
            ];

            $keyProvIdList[] = $provinceItem['provinceId'];
            $ret[$provinceItem['provinceId']] = $provinceData;
        }

        // 全部城市
        $mapCityReferenced = []; // provId => cityId => index. means: cityId's index in $ret[$provId]['cityList']
        $resProv2City = $this->_dataDistrict->getCityListBatch($keyProvIdList);
        foreach ($resProv2City as $provId => $valCityList) {
            if (!isset($ret[$provId])) {
                continue;
            }
            $newCityList = [];
            foreach ($valCityList as $idx => $itemCity) {
                $mapCityReferenced[$itemCity['provinceId']][$itemCity['cityId']] = $idx;
                $newCityList[$idx] = [
                    'cityId'     => $itemCity['cityId'],
                    'cityName'   => $itemCity['cityName'],
                    'countyList' => [],
                ];
            }
            $ret[$provId]['cityList'] = $newCityList;
        }

        // 全部区域
        $resProv2County = $this->_dataDistrict->getCountyListByProvinceIds($keyProvIdList);
        foreach ($resProv2County as $provId => $valCountyList) {
            foreach ($valCountyList as $valCounty) {
                if (0 >= $provId || 0 >= $valCounty['cityId'] || !isset($mapCityReferenced[$provId])
                    || !isset($mapCityReferenced[$provId][$valCounty['cityId']])) {
                    continue;
                }

                $idx = $mapCityReferenced[$provId][$valCounty['cityId']];
                if (!isset($ret[$provId]) || !isset($ret[$provId]['cityList'][$idx])) {
                    continue;
                }

                $ret[$provId]['cityList'][$idx]['countyList'][] = [
                    'countyId'   => $valCounty['countyId'],
                    'countyName' => $valCounty['countyName'],
                ];
            }
            $ret[$provId]['cityList'] = array_values($ret[$provId]['cityList']);
        }

        return array_values($ret);
    }

    /**
     * @desc 获取指定用户的下属用户的负责省市区聚合树
     * @param int $uid
     * @return array
     */
    public function getUserAreaAll($uid)
    {
        return $this->_getAllProvinceCityCountyBatch();

        /* 以下为原逻辑，暂时不需要
        // 获取该uid的下属BD
        $userInfo = $this->_lxjxauthService->getUserDetail($uid);
        if (Lxjxlib_Const_Crm_User::ROLE_NAME_CITY_MANAGER != $userInfo['role'] && Lxjxlib_Const_Crm_User::ROLE_NAME_REGIONAL_DIRECTOR != $userInfo['role']) {
            return [];
        }
        $uids = $this->_privilegeSer->getPrivilege($uid, 2);
        //把自己的uid去掉
        if (($key = array_search($uid, $uids['userList']))) {
            unset($uids['userList'][$key]);
        }
        if (!$uids['userList']) {
            return [];
        }

        // 获取下属BD的负责省市区
        $userCityList = array_unique($this->_lxjxauthService->getCityIdList($uids['userList']));
        if (!$userCityList) {
            return [];
        }

        // 去重，组织成树状结构
        $provinceList = $this->_dataDistrict->getAllProvince();
        $provinceList = array_column($provinceList, 'provinceName', 'provinceId');
        $ret = [];
        foreach ($userCityList as $cityId) {
            // 获取城市信息
            // TODO: LAXIN: 考虑批量
            $cityInfo = $this->_dataDistrict->getCity($cityId);
            if (empty($cityInfo)) {
                continue;
            }

            $provinceId = $cityInfo['provinceId'];
            if (!isset($provinceList[$provinceId])) {
                continue;
            }

            if (!isset($ret[$provinceId])) {
                $ret[$provinceId] = [
                    'provinceId'   => $provinceId,
                    'provinceName' => $provinceList[$provinceId],
                    'cityList'     => [],
                ];
            }

            $ret[$provinceId]['cityList'][] = [
                'cityId'     => $cityId,
                'cityName'   => $cityInfo['cityName'],
                'countyList' => $this->_dataDistrict->getCountyList($cityId),
            ];
        }
        return array_values($ret);
        */
    }

    /**
     * @desc 获取该用户的负责省市区树。该接口在钉钉和WEB的一些筛选框用，因为PM设计的BD负责省市区属性，不包含这些使用地方，故这个接口只能先返回全部省市区。
     * @param array $arrUser
     * @return array
     */
    public function getUserArea($arrUser)
    {
        return $this->_getAllProvinceCityCountyBatch();

        /* 以下为原逻辑，暂时不需要
        // 判断该用户的数据权限
        // 若是全部权限，则直接返回全部省市区
        if (Lxjxlib_Service_PrivilegeScope::AUTH_SCOPE_ALL == $arrUser['auth_scope']) {
            return $this->_getAllProvinceCityCounty();
        }

        // 获取用户配置的城市信息
        $userCityList = array_unique($this->_lxjxauthService->getCityIdList([$arrUser['uid']]));

        // 获取全部省份
        $provinceList = $this->_dataDistrict->getAllProvince();
        $provinceList = array_column($provinceList, 'provinceName', 'provinceId');

        $ret = [];
        foreach ($userCityList as $cityId) {
            // 获取城市信息
            // TODO: LAXIN: 考虑批量
            $cityInfo = $this->_dataDistrict->getCity($cityId);
            if (empty($cityInfo)) {
                continue;
            }

            $provinceId = $cityInfo['provinceId'];
            if (!isset($provinceList[$provinceId])) {
                continue;
            }

            if (!isset($ret[$provinceId])) {
                $ret[$provinceId] = [
                    'provinceId'   => $provinceId,
                    'provinceName' => $provinceList[$provinceId],
                    'cityList'     => [],
                ];
            }

            $ret[$provinceId]['cityList'][] = [
                'cityId'     => $cityId,
                'cityName'   => $cityInfo['cityName'],
                'countyList' => $this->_dataDistrict->getCountyList($cityId),
            ];
        }
        return array_values($ret);
        */
    }

    /**
     * @desc 获取该用户负责的省市区树状列表。目前该函数只用于BD绑定0元课活动时展示可选的省市区，理论上应该统一用 $this->getUserArea()。
     * @param array $arrUser
     * @return array
     */
    public function getUserResponsibleArea($arrUser)
    {
        // 获取用户配置的城市信息
        $userAreaList = array_unique($this->_lxjxauthService->getResponsibleAreaIdList([$arrUser['uid']]));

        if (!$userAreaList) {
            return [];
        }

        // 组织成用户的树状映射 map[$prov][$city][$county] = 1;
        // []: 对所有省都无权限
        // {"$prov":[]}: 代表只选中了省，对该省下的全部市区都有权限，对其他省无权限
        // {"$prov":{"$city":[]}: 代表选中了市，对该市下的全部区都有权限，对本省其他市无权限
        // {"$prov":{"$city":{"$county":1}}: 代表选中了区，对该区有权限，对本市其他区无权限
        $mapUserArea = [];
        foreach ($userAreaList as $valId) {
            $arrGeo = Lxjxlib_Ds_Geo_District::genGeoPathByCode($valId);
            if (!$arrGeo['isOk']) {
                continue;
            }
            if (0 < $arrGeo['provinceId']) {
                if (!isset($mapUserArea[$arrGeo['provinceId']])) {
                    $mapUserArea[$arrGeo['provinceId']] = [];
                }
                if (0 < $arrGeo['cityId']) {
                    if (!isset($mapUserArea[$arrGeo['provinceId']][$arrGeo['cityId']])) {
                        $mapUserArea[$arrGeo['provinceId']][$arrGeo['cityId']] = [];
                    }
                    if (0 < $arrGeo['countyId']) {
                        $mapUserArea[$arrGeo['provinceId']][$arrGeo['cityId']][$arrGeo['countyId']] = 1;
                    }
                }
            }
        }

        if (!$mapUserArea) {
            return [];
        }

        $arrTree = [];

        // 与全部省市区合并，组织成省市区树状结构
        $arrAreaAll = $this->_getAllProvinceCityCountyBatch();
        foreach ($arrAreaAll as $itemProvince) {
            // 未选择该省
            if (!isset($mapUserArea[$itemProvince['provinceId']])) {
                continue;
            }
            // 若未指定市，则代表全选了省
            if (0 >= count($mapUserArea[$itemProvince['provinceId']])) {
                $arrTree[] = $itemProvince;
                continue;
            }

            $newNodeProv = [
                'provinceId'   => $itemProvince['provinceId'],
                'provinceName' => $itemProvince['provinceName'],
                'cityList'     => [],
            ];
            // 若指定了市
            foreach ($itemProvince['cityList'] as $itemCity) {
                // 未选择该市
                if (!isset($mapUserArea[$itemProvince['provinceId']][$itemCity['cityId']])) {
                    continue;
                }
                // 未指定区，则代表全选了市
                if (0 >= count($mapUserArea[$itemProvince['provinceId']][$itemCity['cityId']])) {
                    $newNodeProv['cityList'][] = $itemCity;
                    continue;
                }

                $newNodeCity = [
                    'cityId'     => $itemCity['cityId'],
                    'cityName'   => $itemCity['cityName'],
                    'countyList' => [],
                ];
                // 若指定了区
                foreach ($itemCity['countyList'] as $itemCounty) {
                    // 未选择该区
                    if (!isset($mapUserArea[$itemProvince['provinceId']][$itemCity['cityId']][$itemCounty['countyId']])) {
                        continue;
                    }
                    $newNodeCity['countyList'][] = [
                        'countyId'   => $itemCounty['countyId'],
                        'countyName' => $itemCounty['countyName'],
                    ];
                }

                $newNodeProv['cityList'][] = $newNodeCity;
            }

            $arrTree[] = $newNodeProv;
        }

        return $arrTree;

        /*
        $provinceList = $this->_dataDistrict->getAllProvince();
        foreach ($provinceList as $provinceItem) {
            if (!isset($mapUserArea[$provinceItem['provinceId']])) {
                continue;
            }

            $provinceData = [
                'provinceId'   => $provinceItem['provinceId'],
                'provinceName' => $provinceItem['provinceName'],
                'cityList'     => [],
            ];

            // 是否限制了指定的市
            $bIsLimitedCity = 0 < count($mapUserArea[$provinceItem['provinceId']]);

            // 省份包含的城市
            $cityList = $this->_dataDistrict->getCityList($provinceItem['provinceId']);
            foreach ($cityList as $cityItem) {
                if ($bIsLimitedCity && !isset($mapUserArea[$provinceItem['provinceId']][$cityItem['cityId']])) {
                    continue;
                }

                $cityData = [
                    'cityId'     => $cityItem['cityId'],
                    'cityName'   => $cityItem['cityName'],
                    'countyList' => [],
                ];

                // 是否限制了指定的市
                $bIsLimitedCounty = ($bIsLimitedCity && 0 < count($mapUserArea[$provinceItem['provinceId']][$cityItem['cityId']]));

                // 城市包含的区县
                $countyList = $this->_dataDistrict->getCountyList($cityItem['cityId']);
                foreach ($countyList as $countyItem) {
                    if ($bIsLimitedCounty && !isset($mapUserArea[$provinceItem['provinceId']][$cityItem['cityId']][$countyItem['countyId']])) {
                        continue;
                    }

                    $cityData['countyList'][] = [
                        'countyId'   => $countyItem['countyId'],
                        'countyName' => $countyItem['countyName'],
                    ];
                };

                $provinceData['cityList'][] = $cityData;
            }

            $arrTree[] = $provinceData;
        }

        return array_values($arrTree);
        */
    }

    /**
     * 获取省市区县列表树
     * @return array
    */
    public function getProviceSubList()
    {
        $tree = $this->_getAllProvinceCityCountyBatch();
        return $tree;
    }

    /**
     * @deprecated 使用 _getAllProvinceCityCountyBatch 来替代，这个函数没有使用mget，请求次数太多
     * @desc 获取全量省市区县列表树
     * @return array
     */
    protected function _getAllProvinceCityCountyTree()
    {
        $ret = [];
        // 全部省份
        $provinceList = $this->_dataDistrict->getAllProvince();
        foreach ($provinceList as $provinceItem) {
            $provinceData = [
                'provinceId'   => $provinceItem['provinceId'],
                'provinceName' => $provinceItem['provinceName'],
                'cityList'     => [],
            ];
            // 省份包含的城市
            $cityListArr = $this->_dataDistrict->getCityList($provinceItem['provinceId']);
            $cityList = [];
            foreach ($cityListArr as $k => $cityItem) {
                $cityList[] = [
                    "cityId"     => $cityItem['cityId'],
                    "cityName"   => $cityItem['cityName'],
                ];
            }
            foreach ($cityList as $cityItem) {
                // 城市包含的区县
                $tmpCountyData = $this->_dataDistrict->getCountyList($cityItem['cityId']);
                $countyList = [];
                foreach ($tmpCountyData as $countyData) {
                    $countyList[] = [
                        "countyId"     => $countyData['countyId'],
                        "countyName"   => $countyData['countyName'],
                    ];
                }
                $cityItem['countyList']     = $countyList;
                $provinceData['cityList'][] = $cityItem;
            }
            $ret[] = $provinceData;
        }

        return $ret;
    }
    public function getAreaList($arrInput)
    {
        $ids = explode(",",$arrInput['areaIds']);
        //根据父级ID获取省市区,省的父级ID为0
        $list = $this->_dataDistrict->getDistrictList($arrInput['pId'],$arrInput['dataType']);
        $data = array_column($list,null,"id");
        $res = [];
        foreach ($ids as $item) {
            $res[] = $data[$item];
        }
        return $res;
    }

    /**
     * 通过省Ids集合获取省市区信息
     * @param array $provinceIds
     * @return array
     */
    public function getAreaInfoByProvinceId($provinceIds) {
        $resProv2County = $this->_dataDistrict->getCountyListByProvinceIds($provinceIds);
        return $resProv2County;
    }
}

