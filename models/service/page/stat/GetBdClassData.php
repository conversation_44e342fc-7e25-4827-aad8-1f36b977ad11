<?php

/**
 * 班级看板数据
 */
class Service_Page_Stat_GetBdClassData
{
    private $_serviceLxjxData;
    private $_serviceSchool;
    private $_serviceLxjxauth;
    private $_dataDistrict;

    public function __construct()
    {
        $this->_serviceLxjxData = new Lxjxcrm_LxjxData_Client();
        $this->_serviceSchool = new Lxjxlib_Service_Crm_School();
        $this->_serviceLxjxauth   = new Lxjxlib_Service_Lxjxauth_Client();
        $this->_dataDistrict = new Lxjxlib_Ds_Geo_District();
    }

    /**
     * @desc 获取0元课活动的已有领课记录的期次列表
     * @param array $arrInput
     * @return array
     */
    public function execute($arrInput)
    {
        $arrOutput = [
            'errNo' => Lxjxlib_Const_ExceptionCodes::SUCCESS,
            'total' => 0,
            'list'  => [],
        ];
        //判断权限，如果authScope=3,只能查看自己
        if($arrInput["authScope"] == 3 && $arrInput["uid"] != $arrInput["bdUid"]) {
            return $arrOutput;
        }
        $list = $this->_serviceLxjxData->getBdClassData($arrInput);
        if(!empty($list)){
            $schoolIds = [];
            $provinceIds = [];
            $cityIds = [];
            $countyIds = [];
            $bdUids = [];
            $provinceMap = [];
            $cityMap = [];
            $countyMap = [];
            foreach ($list as $item){
                $schoolIds[] = $item['schoolId'];
                $provinceIds[] = $item['provinceId'];
                $cityIds[] = $item['cityId'];
                $countyIds[] = $item['countyId'];
                $bdUids[] = $item['bdUid'];
            }
            $schoolIds = array_unique($schoolIds);
            $provinceIds = array_unique($provinceIds);
            $cityIds = array_unique($cityIds);
            $countyIds = array_unique($countyIds);
            $bdUids = array_unique($bdUids);
            $areaInfo = $this->_getAreaInfo($provinceIds);
            foreach ($areaInfo as $items) {
                foreach ($items as $item) {
                    $provinceMap[$item['provinceId']] = $item['provinceName'];
                    if (in_array($item['cityId'], $cityIds)) {
                        $cityMap[$item['cityId']] = $item['cityName'];
                    }
                    if (in_array($item['countyId'], $countyIds)) {
                        $countyMap[$item['countyId']] = $item['countyName'];
                    }
                }
            }
            $schoolMap = $this->_getSchoolInfo($schoolIds);
            $userMap = $this->_getUserInfo($bdUids);
            $listArr = [];
            foreach ($list as $val){
                $listArr[]=[
                    "bdUid" => $val['bdUid'],
                    'schoolName' => !empty($schoolMap[$val['schoolId']]) ?$schoolMap[$val['schoolId']]["schoolName"]: "",
                    "bdName" => !empty($userMap[$val['bdUid']]) ?$userMap[$val['bdUid']]['name']: "",
                    "provinceName" => $provinceMap[$val['provinceId']] ?? "",
                    "cityName" => $cityMap[$val['cityId']] ?? "",
                    "countyName" => $countyMap[$val['countyId']] ?? "",
                    "receiveNum" => $val['receiveCnt'] ?? 0,
                    "transNum" => $val['transCnt'] ?? 0,
                    "attendOneNum" => $val['attendOneCnt'] ?? 0,
                    "attendTwoNum" => $val['attendTwoCnt'] ?? 0,
                    "attendThreeNum" => $val['attendThreeCnt'] ?? 0,
                    "attendFourNum" => $val['attendFourCnt'] ?? 0,
                    "attendFiveNum" => $val['attendFiveCnt'] ?? 0,
                    "gradeName" => $val['gradeName'] ? $val['gradeName']: "",
                    "className" => $val['classId'] ? $val['classId']."班": "",
                ];
            }
        }
        //lxjxdata根据receiveData 降序排序
        //$uids = array_column($listArr, 'bdUid');
        //array_multisort($uids, SORT_ASC, $listArr);
        if($arrInput["export"] == 1) {
            $arrInput["pn"] = 0;
            $arrInput["rn"] = 20000;
        }
        $pgData = array_chunk($listArr,$arrInput["rn"]);
        $currData = [];
        if($arrInput["pn"] <= count($pgData) - 1){
            $currData = $pgData[$arrInput["pn"]];
        }
        $arrOutput['total'] = count($list);
        $arrOutput['list'] = $currData;
        return $arrOutput;
    }

    private function _getUserInfo($uids)
    {
        $data = [];
        $userData = $this->_serviceLxjxauth->mgetUserDetail($uids);
        if (!empty($userData)) {
            $data = array_column($userData, null,'uid');
        }
        return $data;
    }
    private function _getAreaInfo($provinceIds)
    {
        $mapArea = [];
        $resProv2County = $this->_dataDistrict->getCountyListByProvinceIds($provinceIds);
        if (!empty($resProv2County)) {
            $mapArea = $resProv2County;
        }
        return $mapArea;
    }

    private function _getSchoolInfo($schoolIds)
    {
        $mapSchool = [];
        $resSchoolList = $this->_serviceSchool->getSchoolByIds($schoolIds, 'schoolName');
        if (is_array($resSchoolList) && !empty($resSchoolList)) {
            $mapSchool = array_column($resSchoolList, null, 'schoolId');
        }
        return $mapSchool;
    }
}
