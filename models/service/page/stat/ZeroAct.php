<?php

/**
 * @befie   统计：0元课活动相关
 */
class Service_Page_Stat_ZeroAct
{

    private $_serviceLxjxdata;
    private $_serviceLxjxauth;
    private $_serviceUcloud;
    private $_serviceZeroAct;
    private $_serviceSchool;

    public function __construct()
    {
        $this->_serviceLxjxdata = new Lxjxcrm_Lxjxdata_Client();
        $this->_serviceLxjxauth = new Lxjxlib_Service_Lxjxauth_Client();
        $this->_serviceUcloud   = new Hk_Service_Ucloud();
        $this->_serviceZeroAct = new Lxjxcrm_ZeroAct_Client();
        $this->_serviceSchool = new Lxjxlib_Service_Crm_School();
    }

    /**
     * @desc 用领课记录中的班级ID转换班级名称
     * @param int $ActClassId 领课记录中的班级ID
     * @return strng
     */
    public function _geActClassNameByActClassID(int $ActClassId): string
    {
        return "班级{$ActClassId}";
    }

    /**
     * @desc 获取0元课活动的已有领课记录的期次列表
     * @param array $arrInput 入参:actId,searchName
     * @return array
     * @throws Hk_Util_Exception
     */
    public function getEffectivePeriodList($arrInput)
    {
        $arrOutput = [
            'list' => [],
        ];
        $ret = $this->_serviceLxjxdata->getPeriodListWithGenFromCourseRecord($arrInput['actId']);
        // 本地分页
        if ($ret && is_array($ret['list'])) {
            foreach ($ret['list'] as $val) {
                if ($arrInput['searchName']) {
                    if (false === strpos($val['period'], $arrInput['searchName'])) {
                        continue;
                    }
                }
                $arrOutput['list'][] = [
                    'actId'  => $val['zeroActId'],
                    'period' => $val['period'],
                ];
            }
        }
        return $arrOutput;
    }

    /**
     * @desc 获取0元课领课记录的明细列表-为action层的list列表拼装数据
     * @param array $arrUser 用户信息
     * @param array $arrInput 查询入参
     * @param int $pn 页码
     * @param int $rn 每页条数
     * @return array
     * @throws Hk_Util_Exception|Lxjxlib_Const_Exception
     */
    public function getCourseRecordListForActionList($arrUser, $arrInput, $pn, $rn): array
    {
        $arrOutput = [
            'errNo' => Lxjxlib_Const_ExceptionCodes::SUCCESS,
            'total' => 0,
            'list'  => [],
        ];

        // 用户phone换成用户uid
        if ($arrInput['customerPhone']) {
            // yapi: https://yapi.zuoyebang.cc/project/1661/interface/api/821336
            $retUcUid = $this->_serviceUcloud->getUidByScopeAndPhone('zyb', $arrInput['customerPhone']);
            if (false === $retUcUid) {
                throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::RAL_ERR, '无法获取用户uid');
            }
            if (0 < $retUcUid) {
                $arrInput['customerUid'] = $retUcUid;
            }
        }

        $arrQuery = $this->_transToQueryFieldsForClassRecord($arrInput, $arrUser);
        // 查询条件为空也去请求数据
        //if (!$arrQuery) {
        //    return $arrOutput;
        //}

        $ret = $this->_getCourseRecordList($arrQuery, $pn, $rn);
        if (!Lxjxlib_Const_ExceptionCodes::isSuccess($ret['errNo'])) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::RAL_ERR, '无法获取领课记录');
        }
        $arrOutput['total'] = $ret['total'];
        $arrOutput['list']  = $ret['list'];

        return $arrOutput;
    }


    /**
     * @desc 导出0元课领课记录的明细列表
     * @param array $arrUser 用户信息
     * @param array $arrInput 查询入参
     * @return array
     * @throws Lxjxlib_Const_Exception|Hk_Util_Exception
     */
    public function getCourseRecordListForActionExport($arrUser, $arrInput): array
    {
        $arrOutput = [
            // 表头行
            'title'      => [
                'UID', '姓名', '活动名称', '活动期次',
                '省份', '城市', '区域', '学校', '年级', '班级',
                '归属BD', '领课时间',
            ],
            // 每行的逐个单元格keymap
            'dataKeyMap' => [
                'customerUid', 'customerName', 'actName', 'period',
                'provinceName', 'cityName', 'countyName', 'schoolName', 'gradeName', 'className',
                'bdName', 'receivedTimeStr',
            ],
            // 每行的数据，至少包含count(dataKeyMap)个单元格
            'dataList' => [],
        ];

        // 用户phone换成用户uid
        if ($arrInput['customerPhone']) {
            // yapi: https://yapi.zuoyebang.cc/project/1661/interface/api/821336
            $retUcUid = $this->_serviceUcloud->getUidByScopeAndPhone('zyb', $arrInput['customerPhone']);
            if (false === $retUcUid) {
                throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::RAL_ERR, '无法获取用户uid');
            }
            if (0 < $retUcUid) {
                $arrInput['customerUid'] = $retUcUid;
            }
        }

        $arrQuery = $this->_transToQueryFieldsForClassRecord($arrInput, $arrUser);
        // 查询条件为空也去请求数据
        //if (!$arrQuery) {
        //    return $arrOutput;
        //}

        $exportList = [];

        $pnCur   = 0;
        $rnCur   = 2000;
        $pnLimit = 10;
        for (; $pnCur < $pnLimit; $pnCur+=1) {
            $resList = $this->_getCourseRecordList($arrQuery, $pnCur, $rnCur);
            if (!Lxjxlib_Const_ExceptionCodes::isSuccess($resList['errNo'])) {
                throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::RAL_ERR, '无法获取领课记录');
            }
            $cntTotal = intval($resList['total']);
            if (0 >= $cntTotal) {
                return $arrOutput;
            }
            if (20000 <= $cntTotal) {
                throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::ERROR_SELF_DEFINE, '已超出单次2W导出限制，请重新筛选或分批次导出');
            }

            $exportList = array_merge($exportList, $resList['list']);
            if ($cntTotal- ($pnCur+1)*$rnCur <= 0){
                break;
            }
        }
        $arrOutput['dataList'] = $exportList;

        return $arrOutput;
    }

    /**
     * @desc 生成查询结构
     * @param array $arrInput 要查询的字段，若key存在，则说明需要对该字段进行条件查询
     * @param array $arrUser 用户信息:uid,auth_scope
     * @return array
     * @throws Hk_Util_Exception
     */
    private function _transToQueryFieldsForClassRecord($arrInput, $arrUser): array
    {
        $queryFields = [];
        $arrKeyMap = [
            'customerName' => 'customerName',
            'customerUid'  => 'customerUid',
            'provinceId'   => 'provinceId',
            'cityId'       => 'cityId',
            'countyId'     => 'countyId',
            'schoolId'     => 'schoolId',
            'actId'        => 'zeroActId',
            'bdUid'        => 'bdUid',
            'period'       => 'period',
            'startTime'    => 'startTime',
            'endTime'      => 'endTime',
            'courseId'     => 'courseId',
        ];

        foreach ($arrKeyMap as $keyFrom => $keyTo) {
            // key存在，就表示需要对该字段进行条件查询
            if (!isset($arrInput[$keyFrom])) {
                continue;
            }
            // 不过滤空值
            // if (empty($arrInput[$keyFrom])) {
            //     continue;
            // }
            $queryFields[$keyTo] = $arrInput[$keyFrom];
        }

        // 处理数据权限
        switch ($arrUser['auth_scope']) {
            case Lxjxlib_Service_PrivilegeScopeNew::AUTH_SCOPE_ALL:
                break;
            case Lxjxlib_Service_PrivilegeScopeNew::AUTH_SCOPE_PARTIAL:
                $crmUser      = $this->_serviceLxjxauth->getUserDetail($arrUser['uid']);
                if ($crmUser['departId']) {
                    $departList   = $this->_serviceLxjxauth->getSubDepartIdList($crmUser['departId']);
                    $departList[] = $crmUser['departId'];
                    $queryFields['departmentIds'] = array_unique($departList);
                }
                break;
            case Lxjxlib_Service_PrivilegeScopeNew::AUTH_SCOPE_SELF:
                $queryFields['bdUid'] = $arrUser['uid'];
                break;
        }

        return $queryFields;
    }

    /**
     * @desc 获取0元课领课记录的明细列表
     * @param array $arrQuery 查询条件的字典
     * @param int $pn 页码
     * @param int $rn 每页条数
     * @return array
     * @throws Hk_Util_Exception
     */
    private function _getCourseRecordList($arrQuery, $pn, $rn): array
    {
        $arrOutput = [
            'errNo' => Lxjxlib_Const_ExceptionCodes::SUCCESS,
            'total' => 0,
            'list'  => [],
        ];

        $ret = $this->_serviceLxjxdata->getCourseRecordList($arrQuery, $pn, $rn);
        if (!$ret) {
            $arrOutput['errNo'] = Lxjxcrm_ExceptionCodes::RAL_ERR;
            return $arrOutput;
        }

        $arrOutput['total'] = intval($ret['total']);

        $arrRecordList = [];
        // 存储ID，获取名称
        $arrTmpActIds = [];
        $arrTmpSchoolIds = [];
        $arrTmpBdIds = [];
        // $arrTmpDepartIds = [];
        foreach ($ret['list'] as $val) {
            if (!isset($arrTmpActIds[$val['zeroActId']])) {
                $arrTmpActIds[$val['zeroActId']] = 1;
            }
            if (!isset($arrTmpSchoolIds[$val['schoolId']])) {
                $arrTmpSchoolIds[$val['schoolId']] = 1;
            }
            if (!isset($arrTmpBdIds[$val['bdUid']])) {
                $arrTmpBdIds[$val['bdUid']] = 1;
            }
            // if (!isset($arrTmpDepartIds[$val['departmentId']])) {
            //     $arrTmpDepartIds[$val['departmentId']] = 1;
            // }

            $arrRecordList[] = [
                'customerUid'     => $val['customerUid'],
                'customerName'    => $val['customerName'],
                'provinceId'      => $val['provinceId'],
                'provinceName'    => $val['provinceName'],
                'cityId'          => $val['cityId'],
                'cityName'        => $val['cityName'],
                'countyId'        => $val['countyId'],
                'countyName'      => $val['countyName'],
                'schoolId'        => $val['schoolId'],
                'schoolName'      => '',
                'gradeId'         => $val['gradeId'],
                'gradeName'       => Lxjxlib_Util_Category::getGradeName($val['gradeId']),
                'classId'         => $val['classId'],
                'className'       => $this->_geActClassNameByActClassID($val['classId']),
                'bdUid'           => $val['bdUid'],
                'bdName'          => '',
                'actId'           => $val['zeroActId'],
                'actName'         => '',
                'period'          => $val['period'],
                'receivedTime'    => $val['receiveTime'],
                'receivedTimeStr' => (0 < $val['receiveTime'])? date('Y-m-d H:i:s', $val['receiveTime']) : '',
                'courseName'      => $val['courseName'] ? $val['courseName'] : '-',
                'giftCourseNameList' => $val['giftCourseNameList'] ? $val['giftCourseNameList'] : [],
                // 'departmentId'    => $val['departmentId'],
                // 'departmentName'  => '',
            ];
        }

        // 转换活动名称
        $mapAct = [];
        if ($arrTmpActIds) {
            $resActList = $this->_serviceZeroAct->getListByActIds(array_keys($arrTmpActIds), true);
            if ($resActList && is_array($resActList['list'])) {
                $mapAct = array_column($resActList['list'], null, 'actId');
            }
        }
        // 转换学校名称
        $mapSchool = [];
        if ($arrTmpSchoolIds) {
            $resSchoolList = $this->_serviceSchool->getSchoolByIds(array_keys($arrTmpSchoolIds), 'schoolName');
            if (is_array($resSchoolList)) {
                $mapSchool = array_column($resSchoolList, null, 'schoolId');
            }
        }
        // 转换BD名称
        $mapBd = [];
        if ($arrTmpBdIds) {
            $resBdList = $this->_serviceLxjxauth->mgetUserDetail(array_keys($arrTmpBdIds));
            if (is_array($resBdList)) {
                $mapBd = array_column($resBdList, null, 'uid');
            }
        }
        // 转换部门名称
        // $mapDepartment = [];
        // if ($arrTmpDepartIds) {
        //     $resDepartList = $this->_serviceLxjxauth->getDepartByIds($arrTmpDepartIds);
        //     if (is_array($resDepartList)) {
        //         $mapDepartment = array_column($resDepartList, null, 'departId');
        //     }
        // }

        // 填充名称
        foreach ($arrRecordList as &$modifyValRec) {
            if ($mapAct[$modifyValRec['actId']]) {
                $modifyValRec['actName'] = $mapAct[$modifyValRec['actId']]['actName'];
            }
            if ($mapSchool[$modifyValRec['schoolId']]) {
                $modifyValRec['schoolName'] = $mapSchool[$modifyValRec['schoolId']]['schoolName'];
            }
            if ($mapBd[$modifyValRec['bdUid']]) {
                $modifyValRec['bdName'] = $mapBd[$modifyValRec['bdUid']]['name'];
            }
            // if ($mapDepartment[$modifyValRec['departmentId']]) {
            //     $modifyValRec['departmentName'] = $mapDepartment[$modifyValRec['departmentId']];
            // }
        }

        $arrOutput['list'] = $arrRecordList;

        return $arrOutput;
    }

    public function getCourseList($arrInput)
    {
        $arrOutput = [
            'list' => [],
        ];
        $ret = $this->_serviceLxjxdata->getCourseList($arrInput);
        if ($ret && is_array($ret)) {
            foreach ($ret as $val) {
                $arrOutput['list'][] = [
                    'courseId'   => $val['courseId'],
                    'courseName' => $val['courseName'] ."-". $val['courseId'],
                ];
            }
        }
        return $arrOutput;
    }
}

