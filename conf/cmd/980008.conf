# 课件zip资源生成通知自动化巡检
[lessonId]
type:int
must:1
remark:章节id
[teacherUid]
type:int
must:1
remark:老师uid
[liveStage]
type:int
must:0
remark:出镜类型 1课中 2课前 3课后 4辅导课中出境
[coursewareId]
type:int
must:1
remark:课件id
[resourceName]
type:string
must:1
remark:zip包资源
[resourceMd5]
type:string
must:1
remark:zip包资源md5
[hdktZipName]
type:string
must:1
remark:半身zip包资源
[hdktZipMd5]
type:string
must:1
remark:半身zip包资源md5
[nobookZipName]
type:string
must:1
remark:虚拟实验zip包资源
[nobookZipMd5]
type:string
must:1
remark:虚拟实验zip包资源md5
[bosBucket]
type:int
must:1
remark:zipBos地址类型 0--zyb-charge, 1--zyb-kejian