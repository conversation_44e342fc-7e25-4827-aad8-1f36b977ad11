# 学生章节维度数据汇聚上报
[groupKey]
type:int
must:0
remark:时序保证
[studentUid]
type:int
must:1
remark:学生uid
[lessonId]
type:int
must:1
remark:章节id
[attendedDuration]
type:int
must:0
remark:到课时长
[isAttended]
type:int
must:0
remark:是否到课
[isAttendedLong]
type:int
must:0
remark:是否到课(30分钟)
[isFinished]
type:int
must:0
remark:是否完课
[isPlayback]
type:int
must:0
remark:是否看回放
[playbackDuration]
type:int
must:0
remark:回放时长
[micNum]
type:int
must:0
remark:抢麦次数
[chatNum]
type:int
must:0
remark:聊天次数
[praiseNum]
type:int
must:0
remark:表扬次数
[reviewStar]
type:int
must:0
remark:课堂星级
[interactRightNum]
type:int
must:0
remark:互动题答对数
[interactAnswerNum]
type:int
must:0
remark:互动题回答数
[interactTotalNum]
type:int
must:0
remark:互动题应答总数
[examRightNum]
type:int
must:0
remark:课中测试答对数
[examAnswerNum]
type:int
must:0
remark:课中测试回答数
[examTotalNum]
type:int
must:0
remark:课中测试总数
[homeworkStatus]
type:int
must:0
remark:课后作业状态
[homeworkSubmit]
type:int
must:0
remark:课后作业
[homeworkLevel]
type:int
must:0
remark:作业等级ABCD
[lessonScore]
type:int
must:0
remark:章节学分


