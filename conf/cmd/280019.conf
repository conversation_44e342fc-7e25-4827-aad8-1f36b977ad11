#排班小班变更
[courseId]
type : int
must : 1
remark : 课程Id

[assistantUid]
type : int
must : 1
remark : assistantUid

[classId]
type : int
must : 1
remark : 小班id

[studentCnt]
type : int
must : 0
remark : 报名人数

[studentMaxCnt]
type : int
must : 0
remark : 该班级报名的最大人数

[rank]
type : int
must : 0
remark : 排序

[status]
type : int
must : 1
remark : 状态

[deleted]
type : int
must : 1
remark : 有效性字段：1删除

[createTime]
type : int
must : 0
remark : 创建时间

[updateTime]
type : int
must : 0
remark : 更新时间

[operatorUid]
type : int
must : 0
remark : 操作人Uid

[operator]
type : string
must : 0
remark : 操作人

[extData]
type : string
must : 0
remark : 扩展属性

[code]
type : int
must : 0
remark : 小班编号

[groupKey]
type:int
must:1
remark:时序保证

[isAdd]
type : int
must : 0
remark : 0更新，1添加