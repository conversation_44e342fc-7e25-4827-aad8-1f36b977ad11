#预热数据pre_live
[strategyId]
type : int
must : 1
remark : 预热生成组织树策略id(courseId,policyId)
[strategyType]
type : int
must : 1
remark : 策略类型（1. 克隆3方业务组织生成组织，听众静态挂载；2. 根据定义的组织树配置生成组织，听众动态挂载）
[leafCn]
type : int
must : 1
remark : 容量(SKU中确定的听众的数目)
[lecturerUid]
type : int
must : 1
remark : 主讲id
[lessonId]
type : int
must : 1
remark : 课程章节id
[sendTime]
type : int
must : 1
remark : 发送时间
[tryCn]
type : int
must : 0
remark : 尝试次数
[isClosedCheck]
type : int
must : 0
remark : 是否关闭检查
[extData]
type : string
must : 0
remark : json字符串扩展
