#用户访问轨迹通知
[configId]
type : int
must : 1
remark : 分销活动ID

[api]
type : string
must : 1
remark : 接口路径

[uid]
type : int
must : 1
remark : 用户uid

[uidType]
type : int
must : 1
remark : 用户uid类型(1:招募员 2:分销员 3:新用户)

[accessTime]
type : int
must : 1
remark : 访问时间(时间戳单位ms)

[data]
  type : map
  must : 1
  remark : 数据
  [.map]
     [..uid2]
     type : int
     must : 0
     remark : 用户uid直接关联的uid
     [..uid3]
     type : int
     must : 0
     remark : 用户uid间接关联的uid
     [..recruitCode]
     type : string
     must : 0
     remark : 招募码
     [..shareCode]
     type : string
     must : 0
     remark : 分销码
     [..apiInput]
     type : string
     must : 0
     remark : 请求的api接口需要的格式化后的输入参数(json串)
     [..apiOutput]
     type : string
     must : 0
     remark : 请求的api接口输出结果(json串)
     [..logInfo]
     type : string
     must : 0
     remark : logInfo信息(json串)

[commitTime]
type:string
must:1
remark:提交时间(格式：2021-09-15 15:00:00)