<?php

/**
 * @befie 领课趋势
 */
class Action_PcReceiveTrend extends Lxjxcrm_Common_BaseAction
{
    // 时间日志key
    const LOG_KEY_TIME = 'lxjxcrm_stat_stat_pcreceivetrend';

    // 返回结构体
    protected $_retModel = array(
        'list' => array(),
    );

    protected $_isCheckLogin = true;
    protected $_checkDataAuth = true;
    protected $_checkApiAuth = false;

    public function invoke()
    {
        $userInfo     = $this->_userInfo;
        $validator    = new Lxjxlib_Util_Validator($this->_requestParam);
        $actId        = intval($validator->integer()->validate('actId', 0));
        $period       = strval($validator->validate('period', ""));
        $provinceId   = intval($validator->integer()->validate('provinceId', 0));
        $cityId       = intval($validator->integer()->validate('cityId', 0));
        $countyId     = intval($validator->integer()->validate('countyId', 0));
        $schoolId     = intval($validator->integer()->validate('schoolId', 0));
        $departmentId = intval($validator->integer()->validate('departmentId', 0));
        $dateStart    = intval($validator->integer()->validate('dateStart', 0));
        $dateEnd      = intval($validator->integer()->validate('dateEnd', 0));
        $authScope    = $userInfo["auth_scope"];
        $bdUid        = $userInfo["uid"];

        if ($validator->hasErrors()) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::PARAM_ERROR, $validator->getAllErrors(), [], Lxjxlib_Const_Exception::NOTICE);
        }
        if ($dateStart > $dateEnd) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::PARAM_ERROR, "开始时间必须大于结束时间", [], Lxjxlib_Const_Exception::NOTICE);
        }

        if (!Lxjxlib_Util_Time::chkDateValid($dateStart, 'YMD')) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::PARAM_ERROR, '截止时间格式错误', [], Lxjxlib_Const_Exception::DEBUG);
        }

        if (!Lxjxlib_Util_Time::chkDateValid($dateEnd, 'YMD')) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::PARAM_ERROR, '截止时间格式错误', [], Lxjxlib_Const_Exception::DEBUG);
        }

        $departList = [];
        if ($authScope == Lxjxlib_Service_PrivilegeScopeNew::AUTH_SCOPE_ALL) {
            if (!empty($departmentId)) {
                $serviceLxjxauth = new Lxjxlib_Service_Lxjxauth_Client();
                $departList = $serviceLxjxauth->getSubDepartIdList($departmentId, true);
                Bd_Log::addNotice("selectDep", json_encode([$departmentId, $departList]));
            }
        } else {
            $authDepartList = (new Service_Page_Department_Department)->getAuthDepartList($userInfo["uid"]);
            Bd_Log::addNotice("authDep", json_encode([$departmentId, $authDepartList]));

            if (empty($authDepartList)) {
                throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::CRM_DATA_USER_NOT_DEPARTMENT_PERMISSION);
            }
            if (!empty($departmentId)) {
                $serviceLxjxauth = new Lxjxlib_Service_Lxjxauth_Client();
                $departList      = $serviceLxjxauth->getSubDepartIdList($departmentId, true);
                Bd_Log::addNotice("selectDep", json_encode([$departmentId, $departList]));
                $departList = array_intersect($authDepartList, $departList);
            } else {
                $departList = $authDepartList;
            }

            if (empty($departList)) {
                throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::CRM_DATA_USER_NOT_DEPARTMENT_PERMISSION);
            }
            $departList = array_unique($departList);
        }

        $params = [
            "actId"          => $actId,
            "period"         => $period,
            "provinceId"     => $provinceId,
            "cityId"         => $cityId,
            "countyId"       => $countyId,
            "schoolId"       => $schoolId,
            "departmentId" => $departList,
            "startTime"      => strtotime($dateStart),
            "endTime"        => strtotime("+1 day -1 second", strtotime($dateEnd)),
            "startDay"       => $dateStart,
            "endDay"         => $dateEnd,
            "authScope"      => $authScope,
            "bdUid"          => $bdUid,
        ];

        Hk_Util_Log::start(self::LOG_KEY_TIME);
        $service = new Service_Page_Stat_PcReceiveTrend();
        $result  = $service->execute($params);
        Hk_Util_Log::stop(self::LOG_KEY_TIME);

        return $result;
    }

}
