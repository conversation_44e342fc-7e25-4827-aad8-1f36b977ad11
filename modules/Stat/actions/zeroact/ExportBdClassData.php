<?php

/**班级数据看板
 * @befie
 */
class Action_ExportBdClassData extends Lxjxcrm_Common_BaseAction
{
    // 时间日志key
    const LOG_KEY_TIME = 'lxjxcrm_stat_stat_exportbdclassdata';

    // 返回结构体
    protected $_retModel = array(
        'list' => array(),
    );

    protected $_isCheckLogin = true;
    protected $_checkDataAuth = true;
    protected $_checkApiAuth = false;

    public function invoke()
    {
        $userInfo     = $this->_userInfo;
        $validator    = new Lxjxlib_Util_Validator($this->_requestParam);
        $period       = strval($validator->validate('period', ""));
        $provinceId   = intval($validator->integer()->validate('provinceId', 0));
        $cityId       = intval($validator->integer()->validate('cityId', 0));
        $countyId     = intval($validator->integer()->validate('countyId', 0));
        $schoolId     = intval($validator->integer()->validate('schoolId', 0));
        $departmentId = intval($validator->integer()->validate('departmentId', 0));
        $receiveDateStart    = intval($validator->integer()->validate('receiveDateStart', 0));
        $receiveDateEnd      = intval($validator->integer()->validate('receiveDateEnd', 0));
        $transDateStart    = intval($validator->integer()->validate('transDateStart', 0));
        $transDateEnd      = intval($validator->integer()->validate('transDateEnd', 0));
        $authScope    = $userInfo["auth_scope"];
        $uid          = $userInfo["uid"];
        $bdUid        = intval($validator->integer()->validate('bdUid', 0));

        if ($validator->hasErrors()) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::PARAM_ERROR, $validator->getAllErrors(), [], Lxjxlib_Const_Exception::NOTICE);
        }
        if ($receiveDateStart > $receiveDateEnd || $transDateStart > $transDateEnd) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::PARAM_ERROR, "结束时间必须大于开始时间", [], Lxjxlib_Const_Exception::NOTICE);
        }

        // 翻页
        $pn = intval($validator->integer()->validate('pn', 0));
        $rn = intval($validator->integer()->validate('rn', 20));

        $departList = [];
        if ($authScope == Lxjxlib_Service_PrivilegeScopeNew::AUTH_SCOPE_ALL) {
            if (!empty($departmentId)) {
                $serviceLxjxauth = new Lxjxlib_Service_Lxjxauth_Client();
                $departList = $serviceLxjxauth->getSubDepartIdList($departmentId, true);
                Bd_Log::addNotice("[exportBdClassData]selectDep", json_encode([$departmentId, $departList]));
            }
        } else {
            $authDepartList = (new Service_Page_Department_Department)->getAuthDepartList($userInfo["uid"]);
            Bd_Log::addNotice("[exportBdClassData]authDep", json_encode([$departmentId, $authDepartList]));

            if (empty($authDepartList)) {
                throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::CRM_DATA_USER_NOT_DEPARTMENT_PERMISSION);
            }
            if (!empty($departmentId)) {
                $serviceLxjxauth = new Lxjxlib_Service_Lxjxauth_Client();
                $departList      = $serviceLxjxauth->getSubDepartIdList($departmentId, true);
                Bd_Log::addNotice("[exportBdClassData]selectDep", json_encode([$departmentId, $departList]));
                $departList = array_intersect($authDepartList, $departList);
            } else {
                $departList = $authDepartList;
            }

            if (empty($departList)) {
                throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::CRM_DATA_USER_NOT_DEPARTMENT_PERMISSION);
            }
            $departList = array_unique($departList);
        }

        $params = [
            "period"             => $period,
            "provinceId"         => $provinceId,
            "cityId"             => $cityId,
            "countyId"           => $countyId,
            "schoolId"           => $schoolId,
            "departmentId"       => $departList,
            "receiveStartTime"   => $receiveDateStart ? strtotime($receiveDateStart) :0,
            "receiveEndTime"     => $receiveDateEnd?strtotime("+1 day -1 second", strtotime($receiveDateEnd)) : 0,
            "transStartTime"     => $transDateStart ? strtotime($transDateStart) :0,
            "transEndTime"       => $transDateEnd ? strtotime("+1 day -1 second", strtotime($transDateEnd)) : 0,
            "authScope"          => $authScope,
            "bdUid"              => $authScope == Lxjxlib_Service_PrivilegeScopeNew::AUTH_SCOPE_SELF ? $uid : $bdUid,
            "uid"                => $uid,
            "pn"                 => $pn,
            "rn"                 => $rn,
            "export"             => 1,
        ];

        Hk_Util_Log::start(self::LOG_KEY_TIME);
        $service = new Service_Page_Stat_GetBdClassData();
        $result  = $service->execute($params);
        Hk_Util_Log::stop(self::LOG_KEY_TIME);
        // 响应文件下载
        $map = [
            // 表头行
            'title'      => [
                '姓名', '省份', '城市', '地区', '学校', '年级', '班级',
                '领课人数', '1课到课人数','2课到课人数','3课到课人数','4课到课人数','5课到课人数','转化人数'
            ],
            // 每行的逐个单元格keymap
            'dataKeyMap' => [
                'bdName', 'provinceName', 'cityName', 'countyName', 'schoolName', 'gradeName', 'className',
                'receiveNum', 'attendOneNum','attendTwoNum','attendThreeNum','attendFourNum','attendFiveNum','transNum'
            ],
            // 每行的数据，至少包含count(dataKeyMap)个单元格
        ];
        Lxjxlib_Util_Tools::export('班级数据看板.csv', $map['title'], $result['list'], $map['dataKeyMap']);

        return $result;
    }
}
