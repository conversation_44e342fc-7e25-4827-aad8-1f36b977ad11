<?php
/**
 * 看起来是拜访记录的统计逻辑
 * @desc 一期关闭了此功能，暂时保留代码
 **/
class Action_ExportEmail extends Lxjxcrm_Common_BaseAction
{
    /**
     * 时间日志key
     */
    const LOG_KEY_TIME = 'lxjxcrm_stat_visit_exportemail';

    /**
     * @var     $_retModel
     * @desc    接口模型
     */
    protected $_retModel = array(
        'res' => Lxjxcrm_Const_Errors::ERROR_SUCCESS,
        'msg' => 'success',
        'list' => array(),
    );

    /**
     * @var     $_isCheckLogin
     * @desc    验证登录
     */
    protected $_isCheckLogin = true;

    /**
     * @var     $_checkDataAuth
     * @desc    数据权限
     */
    protected $_checkDataAuth = true;

    protected function invoke()
    {
        ini_set('memory_limit', '512M');
        $validator = new Lxjxlib_Util_Validator($this->_requestParam);

        // 部门ID
        $departId = intval($validator->integer()->validate('departId', 1));
        // 日期
        $startDate = intval($validator->integer()->validate('startDate', date('Ymd')));
        $endDate = intval($validator->integer()->validate('endDate', date('Ymd')));

        if ($validator->hasErrors()) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::PARAM_ERROR, $validator->getAllErrors(), [], Lxjxlib_Const_Exception::NOTICE);
        }

        Hk_Util_Log::start(self::LOG_KEY_TIME);

        $statService = new Service_Page_Stat_Visit();

        $dataList = $statService->getVisitRecordEmail($departId, $startDate, $endDate, $this->_userInfo);

        Hk_Util_Log::stop(self::LOG_KEY_TIME);

        return $dataList;
    }

}
