<?php

/**
 * 数据报表相关
 **/
class Controller_Zeroact extends Ap_Controller_Abstract
{
    public $actions = [
        // B端
//        'searchactlist'       => 'actions/stat/SearchActList.php', // 活动列表
//        'searchactperiodlist' => 'actions/stat/SearchActPeriodList.php', // 活动期次

        'pcsummarydata'  => 'modules/Stat/actions/zeroact/PcSummaryData.php', // 汇总数据，领课，自然日，同源
        'pcreceivetrend' => 'modules/Stat/actions/zeroact/PcReceiveTrend.php', // 领课趋势
        'pcreceiverank'  => 'modules/Stat/actions/zeroact/PcReceiveRank.php', // 领课排行榜单

        'mpusersummarydata'      => 'modules/Stat/actions/zeroact/MpUserSummaryData.php', // 小程序汇总数据，领课，自然日
        'mpuserreceivetrend'     => 'modules/Stat/actions/zeroact/MpUserReceiveTrend.php', // 小程序领课趋势
        'mpareareceiverank'      => 'modules/Stat/actions/zeroact/MpAreaReceiveRank.php', // 区域领课排行榜单
        'mpschooldatarank'       => 'modules/Stat/actions/zeroact/MpSchoolDataRank.php', // 学校数据排行榜

        // 后台
        'getcourserecordlist'    => 'modules/Stat/actions/zeroact/GetCourseRecordList.php',    // 0元课领课记录列表
        'exportcourserecordlist' => 'modules/Stat/actions/zeroact/ExportCourseRecordList.php', // 0元课领课记录列表-导出excel
        'geteffectiveperiodlist' => 'modules/Stat/actions/zeroact/GetEffectivePeriodList.php', // 获取指定0元课活动的有领课记录的期次列表
        'searchcourseinfo'       => 'modules/Stat/actions/zeroact/SearchCourseInfo.php', //通过课程名称或者课程ID检索课信息
        'getbdclassdata'         => 'modules/Stat/actions/zeroact/GetBdClassData.php', //获取BD班级看板数据
        'exportbdclassdata'      => 'modules/Stat/actions/zeroact/ExportBdClassData.php', //下载BD班级看板数据
    ];
}
