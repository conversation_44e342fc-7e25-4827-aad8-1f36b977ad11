<?php

/**
 * @befie   (内部)批量获取学校信息
 * @file    modules/inner/actions/schoolcustomer/SchoolInfoByIds.php
 */
class Action_SchoolInfoByIds extends Lxjxcrm_Common_BaseAction
{
    // 内部的接口
    public function invoke()
    {
        $validator = new Lxjxlib_Util_Validator($this->_requestParam);
        $arrInput['schoolIds'] = $validator->required()->validate('schoolIds');
        if ($validator->hasErrors()) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::PARAM_ERROR, $validator->getAllErrors(), [], Lxjxlib_Const_Exception::NOTICE);
        }
        Hk_Util_Log::start('lxjxcrm_inner_school_schoolinfobyids');
        $objPs = new Service_Page_School_Customer();
        $arrOutPut["list"] = $objPs->getSchoolListBySchoolIds($arrInput);
        Hk_Util_Log::stop('lxjxcrm_inner_school_schoolinfobyids');

        return $arrOutPut;
    }
}
