<?php

/**
 * @befie 获取指定区域下的BD负责的学校(支持搜索)。 yapi: https://yapi.zuoyebang.cc/project/11141/interface/api/870558 。
 */
class Action_BdSchoolList extends Lxjxcrm_Common_BaseAction
{
    // 内部的接口


    public function invoke()
    {
        $validator = new Lxjxlib_Util_Validator($this->_requestParam);

        $bdUid      = intval($validator->required()->integer()->min(1)->validate('bdUid'));
        $countyId   = intval($validator->required()->integer()->min(1)->validate('countyId'));
        $searchName = strval($validator->validate('searchName', ''));
        $schoolId   = intval($validator->validate('schoolId', 0));
        $withCached = intval($validator->validate('withCached', 1));
        $pn = intval($validator->validate('pn', 0));
        $rn = intval($validator->validate('rn', 20));
        if ($validator->hasErrors()) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::PARAM_ERROR, $validator->getAllErrors(), [], Lxjxlib_Const_Exception::DEBUG);
        }

        $pn = (0 < $pn)? $pn : 0;
        $rn = (0 >= $rn || 100 < $rn)? $rn : 20;

        $arrInput = [
            'countyId'   => $countyId,
            'searchName' => $searchName,
            'schoolId'   => $schoolId,
            'pn'         => $pn,
            'rn'         => $rn,
        ];
        $arrUser = [
            'uid'  => $bdUid,
        ];

        Hk_Util_Log::start('lxjxcrm_inner_school_bdschoollist');
        $objPs = new Service_Page_School_Customer();
        $dataPs = $objPs->getBdSchoolListForMofang($arrInput, $arrUser, $withCached);

        Hk_Util_Log::stop('lxjxcrm_inner_school_bdschoollist');

        $arrOutput = [
            "total" => 0,
            "list"  => [],
        ];
        if($dataPs) {
            $arrOutput['total'] = $dataPs['total'];
            $arrOutput['list']  = $dataPs['list'];
        }

        return $arrOutput;
    }
}
