<?php

/**
 * @befie   通过name模糊查询用户数据
 * @file    modules/inner/actions/user/SearchBdInfoByName.php
 * <AUTHOR>
 * @date    2024-09-03 17:12:21
 */
class Action_SearchBdInfoByName extends Lxjxcrm_Common_BaseAction
{
    public function invoke()
    {
        $validator  = new Lxjxlib_Util_Validator($this->_requestParam);
        $arrInput['name']    = $validator->validate('name', '');
        $arrInput['pn']      = $validator->validate('pn', 0);
        $arrInput['rn']      = $validator->validate('rn', 20);
        if ($validator->hasErrors() || $arrInput['name'] == "") {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::PARAM_ERROR, $validator->getAllErrors(), [], Lxjxlib_Const_Exception::NOTICE);
        }
        Hk_Util_Log::start('lxjxcrm_inner_user_getuserinfobyname');
        $serviceLxjxAuth = new Lxjxlib_Service_Lxjxauth_Client();
        $arrOutPut = $serviceLxjxAuth->searchBdInfoByBDName($arrInput['name'],$arrInput['pn'],$arrInput['rn']);
        Hk_Util_Log::stop('lxjxcrm_inner_user_getuserinfobyname');
        return $arrOutPut;
    }
}
