<?php

/**
 * @befie   (内部)通过name查询用户数据
 * @file    modules/inner/actions/user/GetUserInfoByQUdaoId.php
 * <AUTHOR>
 * @date    2024-08-06 17:12:21
 */
class Action_GetUserInfoByName extends Lxjxcrm_Common_BaseAction
{
    public function invoke()
    {
        $validator  = new Lxjxlib_Util_Validator($this->_requestParam);
        $name    = $validator->validate('name', '');
        if ($validator->hasErrors() || $name == "") {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::PARAM_ERROR, $validator->getAllErrors(), [], Lxjxlib_Const_Exception::NOTICE);
        }

        Hk_Util_Log::start('lxjxcrm_inner_user_getuserinfobyname');
        $serviceLxjxAuth = new Lxjxlib_Service_Lxjxauth_Client();
        //1:name查询，2:qudaoId查询
        $data = $serviceLxjxAuth->getBdUserInfoByBDName($name);
        $arrOutput = [
            "info" => (object)[],
        ];
        if(!empty($data)) {
            $arrOutput['info'] = $data;
        }
        Hk_Util_Log::stop('lxjxcrm_inner_user_getuserinfobyname');
        return $arrOutput;
    }
}
