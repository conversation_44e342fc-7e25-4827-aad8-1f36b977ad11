<?php

/**
 * @befie   (内部)通过邮箱或邮箱前缀查询用户数据。 yapi: https://yapi.zuoyebang.cc/project/11141/interface/api/870569 。
 */
class Action_GetUserInfoByEmail extends Lxjxcrm_Common_BaseAction
{
    public function invoke()
    {
        $validator  = new Lxjxlib_Util_Validator($this->_requestParam);
        $emailPrefix = strval($validator->required()->validate('emailPrefix', ''));
        if ($validator->hasErrors() || "" == $emailPrefix) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::PARAM_ERROR, $validator->getAllErrors(), [], Lxjxlib_Const_Exception::NOTICE);
        }

        Hk_Util_Log::start('lxjxcrm_inner_user_getuserinfobyemail');
        $serviceLxjxAuth = new Lxjxlib_Service_Lxjxauth_Client();
        $data = $serviceLxjxAuth->getBdUserInfoByEmailPrefix($emailPrefix);
        $arrOutput = [
            "info" => (object)[],
        ];
        if(!empty($data)) {
            $arrOutput['info'] = $data;
        }
        Hk_Util_Log::stop('lxjxcrm_inner_user_getuserinfobyemail');
        return $arrOutput;
    }
}
