<?php

/**
 * @befie   (内部)获取省市区县数据
 * @file    modules/inner/actions/area/AreainfoByType.php
 * <AUTHOR>
 * @date    2024-08-06 20:32:12
 */
class Action_AreainfoByType extends Lxjxcrm_Common_BaseAction
{
    public function invoke()
    {
        $validator = new Lxjxlib_Util_Validator($this->_requestParam);
        $arrInput['areaIds'] = $validator->required()->validate('areaIds');
        $arrInput['dataType'] = intval($validator->required()->validate('dataType'));
        $arrInput['pId'] = intval($validator->required()->validate('pId'));
        if ($validator->hasErrors()) {
            throw new Lxjxlib_Const_Exception(Lxjxcrm_ExceptionCodes::PARAM_ERROR, $validator->getAllErrors(), [], Lxjxlib_Const_Exception::NOTICE);
        }
        Hk_Util_Log::start('lxjxcrm_inner_area_areainfobytype');
        $objPs = new Service_Page_Area_Area();
        $arrOutPut["list"] = $objPs->getAreaList($arrInput);
        Hk_Util_Log::stop('lxjxcrm_inner_area_areainfobytype');
        return $arrOutPut;
    }
}
