<?php
/**
 * 内部接口:人员相关
 * @file    modules/Inner/controllers/User.php
 **/
class Controller_User extends Ap_Controller_Abstract
{
    public $actions = [
        'getuserinfobyqudaoid' => 'modules/Inner/actions/user/GetUserInfoByQudaoId.php', // (内部)通过渠道获取BD信息
        'getuserinfobyname'    => 'modules/Inner/actions/user/GetUserInfoByName.php',    // (内部)通过name获取BD信息
        'getuserinfobybduid'   => 'modules/Inner/actions/user/GetUserInfoByBdUid.php',   // (内部)通过Uid获取用户信息
        'getuserinfobyemail'   => 'modules/Inner/actions/user/GetUserInfoByEmail.php',   // (内部)通过邮箱或邮箱前缀查询用户数据
        'searchbdinfobyname'   => 'modules/Inner/actions/user/SearchBdInfoByName.php',   // (内部)通过name模糊检索用户信息
    ];
}
