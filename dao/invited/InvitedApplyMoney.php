<?php
/**
 * @file    ApplyMoney.php
 * <AUTHOR>
 * @date    2018-03-13
 * @brief   三条-财务转账审核
 */
class Hkzb_Dao_Invited_InvitedApplyMoney extends Hk_Common_BaseDao{
    public function __construct(){
        $this->_dbName = 'fudao/zyb_fudao';
        $this->_db     = Hk_Service_Db::getDB($this->_dbName);
        $this->_table  = "tblInvitedApplyMoney";
        $this->arrFieldsMap = array(
            'id'            => 'id',
            'uid'           => 'uid',
            'uname'         => 'uname',
            'money'         => 'money',
            'account'       => 'account',
            'identityNum'   => 'identity_num',
            'tradeNo'       => 'trade_no',
            'thirdTradeNo'  => 'third_trade_no',
            'status'        => 'status',
            'deleted'       => 'deleted',
            'type'           => 'type',
            'activityId'    => 'activity_id',
            'createTime'    => 'create_time',
            'updateTime'    => 'update_time',
            'examineUid'    => 'examine_uid',
            'examineName'   => 'examine_name',
            'examineStatus' => 'examine_status',
            'examineTime'   => 'examine_time',
            'refuseReason' => 'refuse_reason',
            'payTime'       => 'pay_time',
        );

        $this->arrTypesMap = array(
            'id'           => Hk_Service_Db::TYPE_INT,
            'uid'          => Hk_Service_Db::TYPE_INT,
            'uname'        => Hk_Service_Db::TYPE_STR,
            'money'        => Hk_Service_Db::TYPE_STR,
            'account'      => Hk_Service_Db::TYPE_STR,
            'identityNum'   => Hk_Service_Db::TYPE_STR,
            'tradeNo'       => Hk_Service_Db::TYPE_INT,
            'thirdTradeNo' => Hk_Service_Db::TYPE_STR,
            'status'       => Hk_Service_Db::TYPE_INT,
            'deleted'      => Hk_Service_Db::TYPE_INT,
            'type'         => Hk_Service_Db::TYPE_INT,
            'activityId'   => Hk_Service_Db::TYPE_INT,
            'createTime'   => Hk_Service_Db::TYPE_INT,
            'updateTime'   => Hk_Service_Db::TYPE_INT,
            'examineUid'   => Hk_Service_Db::TYPE_INT,
            'examineName'  => Hk_Service_Db::TYPE_STR,
            'examineStatus'=> Hk_Service_Db::TYPE_INT,
            'examineTime'  => Hk_Service_Db::TYPE_INT,
            'refuseReason' => Hk_Service_Db::TYPE_STR,
            'payTime'      => Hk_Service_Db::TYPE_INT,
        );
    }
}