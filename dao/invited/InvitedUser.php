<?php

/**
 *
 * <AUTHOR>
 * @date   2018-03-13
 * @brief
 **/
class Hkzb_Dao_Invited_InvitedUser extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName      = 'fudao/zyb_fudao';
        $this->_db          = Hk_Service_Db::getDB($this->_dbName);
        $this->_table       = "tblInvitedUser";
        $this->_tableName   = "tblInvitedUser";

        $this->arrFieldsMap = array(
            'uid'         => 'uid',
            'award'       => 'award',
            'status'      => 'status',
            'inviteCode'  => 'invite_code',
            'inviteId'    => 'invite_id',
            'activityId' => 'activity_id',
            'courseId'    => 'course_id',
            'courseName'  => 'course_name',
            'createTime'  => 'create_time',
            'updateTime'  => 'update_time',
            'extData'     => 'ext_data'
        );

        $this->arrTypesMap = array(
            'uid'        => Hk_Service_Db::TYPE_INT,
            'award'      => Hk_Service_Db::TYPE_INT,
            'status'     => Hk_Service_Db::TYPE_INT,
            'inviteCode' => Hk_Service_Db::TYPE_INT,
            'activityId' =>  Hk_Service_Db::TYPE_INT,
            'inviteId'   => Hk_Service_Db::TYPE_INT,
            'courseId'   => Hk_Service_Db::TYPE_INT,
            'courseName' => Hk_Service_Db::TYPE_STR,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'extData'    => Hk_Service_Db::TYPE_JSON,

        );
    }
}