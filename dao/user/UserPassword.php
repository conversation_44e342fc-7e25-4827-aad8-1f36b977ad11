<?php

/**
 * @befie   用户密码和session表
 * @file    dao/UserPassword.php
 * <AUTHOR>
 * @version 1.0
 * @since
 * @date    2019/10/19 16:28
 */
class Lxjxlib_Dao_User_UserPassword extends Lxjxlib_Common_BaseMultiDao
{
    public function __construct()
    {
        $this->_dbName = Lxjxlib_Const_Db::DB_LXJX_USER;
        $this->_logFile = Lxjxlib_Const_Db::DBLOG_LXJX;
        $this->_db = null;
        $this->_table = 'tblUserPassword';
        $this->_tableName = 'tblUserPassword';
        $this->_partionKey = 'uid';
        $this->_partionType = self::TYPE_TABLE_PARTION_MOD;
        $this->_partionNum = 20;

        $this->arrFieldsMap = [
            'id' => 'id',
            'uid' => 'uid',
            'password' => 'password',
            'session' => 'session',
            'forbidden' => 'forbidden',
            'lastLogin' => 'last_login',
            'ext' => 'ext',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
        ];

        $this->arrTypesMap = [
            'id' => Hk_Service_Db::TYPE_INT,
            'uid' => Hk_Service_Db::TYPE_INT,
            'password' => Hk_Service_Db::TYPE_STR,
            'session' => Hk_Service_Db::TYPE_JSON,
            'forbidden' => Hk_Service_Db::TYPE_INT,
            'lastLogin' => Hk_Service_Db::TYPE_INT,
            'ext' => Hk_Service_Db::TYPE_JSON,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
        ];
    }

    /**
     * 判断用户是否拥有登录信息
     * @param int $uid
     * @return boolean
     */
    public function passInfoCount($uid)
    {
        $where = [
            'uid' => $uid,
        ];
        $count = $this->getCntByConds($uid, $where);
        return $count;
    }

    /**
     * 获取用户登录信息
     * @param int $uid
     * @return array
     */
    public function getPassInfo($uid)
    {
        $where = [
            'uid' => $uid,
        ];
        $ret = $this->getRecordByConds($uid, $where, $this->getAllFields());

        return $ret;
    }

    /**
     * 增加用户密码信息
     * @param int $uid
     * @param string $password
     * @param array $session
     * @param int $forbidden
     * @param array $ext
     * @param int $createTime 默认为系统自带
     * @return boolean
     */
    public function addPassInfo($uid, $password, $session, $forbidden = 0, $ext = [], $createTime = 0)
    {
        $nowTime = 0 === $createTime ? time() : $createTime;
        $data = [
            'uid' => $uid,
            'password' => $password,
            'forbidden' => $forbidden,
            'session' => Lxjxlib_Util_Json::encode($session),
            'lastLogin' => time(),
            'ext' => Lxjxlib_Util_Json::encode($ext),
            'createTime' => $nowTime,
            'updateTime' => $nowTime,
        ];

        $ret = $this->insertRecords($uid, $data);
        return $ret;
    }

    /**
     * 更新用户密码信息
     * @param int $uid
     * @param array $data
     * @return boolean
     */
    public function updatePassInfo($uid, array $data)
    {
        $where = [
            'uid' => $uid,
        ];

        if (isset($data['session'])) {
            $data['session'] = Lxjxlib_Util_Json::encode($data['session']);
        }
        if (isset($data['ext'])) {
            $data['ext'] = Lxjxlib_Util_Json::encode($data['ext']);
        }
        $data['updateTime'] = time();

        $ret = $this->updateByConds($uid, $where, $data);
        return $ret;
    }
}
