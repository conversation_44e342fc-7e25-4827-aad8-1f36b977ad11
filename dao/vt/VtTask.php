<?php

/**
 * @file: vtTask.php
 * <AUTHOR>
 * @Datetime: 2018/5/15 14:29
 * @brief : description
 */
class Hkzb_Dao_Vt_VtTask extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = 'fudao/zyb_fudao';
        $this->_db = Hk_Service_Db::getDB($this->_dbName);
        $this->_table = "tblVtTask";
        $this->arrFieldsMap = array(
            'taskId' => 'task_id',
            'videoId' => 'video_id',
            'source' => 'source',
            'opUid' => 'op_uid',
            'originVideoUrl' => 'origin_video_url',
            'needRestore' => 'need_restore',
            'needEncode' => 'need_encode',
            'retryTimes' => 'retry_times',
            'status' => 'status',
            'queueId' => 'queue_id',
            'taskFailReason' => 'task_fail_reason',
            'startTime' => 'start_time',
            'retryStartTime' => 'retry_start_time',
            'endTime' => 'end_time',
            'callbackUrl' => 'callback_url',
            'callbackErrno' => 'callback_errno',
            'callbackErrmsg' => 'callback_errmsg',
            'transData' => 'trans_data',
            'videoData' => 'video_data',
            'createTime' => 'create_time',
            'extData' => 'ext_data',
            'videoHash' => 'video_hash',
        );

        $this->arrTypesMap = array(
            'taskId' => Hk_Service_Db::TYPE_INT,
            'videoId' => Hk_Service_Db::TYPE_STR,
            'source' => Hk_Service_Db::TYPE_STR,
            'opUid' => Hk_Service_Db::TYPE_INT,
            'originVideoUrl' => Hk_Service_Db::TYPE_STR,
            'needRestore' => Hk_Service_Db::TYPE_INT,
            'needEncode' => Hk_Service_Db::TYPE_INT,
            'retryTimes' => Hk_Service_Db::TYPE_INT,
            'status' => Hk_Service_Db::TYPE_INT,
            'queueId' => Hk_Service_Db::TYPE_INT,
            'taskFailReason' => Hk_Service_Db::TYPE_STR,
            'startTime' => Hk_Service_Db::TYPE_INT,
            'retryStartTime' => Hk_Service_Db::TYPE_INT,
            'endTime' => Hk_Service_Db::TYPE_INT,
            'callbackUrl' => Hk_Service_Db::TYPE_STR,
            'callbackErrno' => Hk_Service_Db::TYPE_INT,
            'callbackErrmsg' => Hk_Service_Db::TYPE_STR,
            'transData' => Hk_Service_Db::TYPE_STR,
            'videoData' => Hk_Service_Db::TYPE_STR,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'extData' => Hk_Service_Db::TYPE_STR,
            'videoHash' => Hk_Service_Db::TYPE_STR,
        );
    }
}