<?php
/**
 * Created by PhpStorm.
 * User: <EMAIL>
 * Date: 2020/10/16
 * Time: 2:48 下午
 */
class Lxjxlib_Dao_School_TestConvergeUrl extends Lxjxlib_Dao_School_BaseDao{

    public function __construct()
    {

        parent::__construct();

        $this->_table       = 'tblTestConvergeUrl';
        $this->arrFieldsMap = [
            'id'                        => 'id',
            'testId'                    => 'test_id',
            'cosUrl'                    => 'cos_url',
            'createTime'                => 'create_time',
            'updateTime'                => 'update_time',
        ];

        $this->arrTypesMap = [
            'id'                        => Hk_Service_Db::TYPE_INT,
            'testId'                    => Hk_Service_Db::TYPE_INT,
            'cosUrl'                    => Hk_Service_Db::TYPE_STR,
            'createTime'                => Hk_Service_Db::TYPE_INT,
            'updateTime'                => Hk_Service_Db::TYPE_INT,
        ];
    }
}