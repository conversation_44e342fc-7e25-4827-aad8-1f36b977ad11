<?php
/**
 * Created by PhpStorm.
 * User: zhang<PERSON><EMAIL>
 * Date: 2021/3/17
 * Time: 14:06
 * 口算活动表
 */
class Lxjxlib_Dao_School_ActCalcExam extends Lxjxlib_Dao_School_BaseDao
{

    public function __construct()
    {
        parent::__construct();

        $this->_table       = 'tblActCalcExam';
        $this->arrFieldsMap = [
            'id'          => 'id',
            'turnId'      => 'turn_id',
            'actId'       => 'act_id',
            'gradeId'     => 'grade_id',
            'tids'        => 'tids',
            'examSort'    => 'exam_sort',
            'tCount'      => 't_count',
            'repeatCount' => 'repeat_count',
            'isDel'       => 'is_del',
            'createTime'  => 'create_time',
            'updateTime'  => 'update_time',
        ];

        $this->arrTypesMap = [
            'id'          => Hk_Service_Db::TYPE_INT,
            'turnId'      => Hk_Service_Db::TYPE_INT,
            'actId'       => Hk_Service_Db::TYPE_INT,
            'gradeId'     => Hk_Service_Db::TYPE_INT,
            'tids'        => Hk_Service_Db::TYPE_STR,
            'examSort'    => Hk_Service_Db::TYPE_INT,
            'tCount'      => Hk_Service_Db::TYPE_INT,
            'repeatCount' => Hk_Service_Db::TYPE_INT,
            'isDel'       => Hk_Service_Db::TYPE_INT,
            'createTime'  => Hk_Service_Db::TYPE_INT,
            'updateTime'  => Hk_Service_Db::TYPE_INT,

        ];
    }

  
}
