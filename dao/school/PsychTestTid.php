<?php
/**
 * Created by PhpStorm.
 * User: pangjun<PERSON><PERSON>@zuoyebang.com
 * Date: 2021/8/6
 * Time: 11:05 上午
 */
class Lxjxlib_Dao_School_PsychTestTid extends Lxjxlib_Dao_School_BaseDao{

    public function __construct(){

        parent::__construct();

        $this->_table       = 'tblPsychTestTid';
        $this->arrFieldsMap = [
            'id'        => 'id',
            'examId'    => 'exam_id',
            'title'     => 'title',
            'score'     => 'score',
            'options'   => 'options',
            'sort'      => 'sort',
            'type'      => 'type',
            'deleted'       => 'deleted',
            'createTime'    => 'create_time',
            'updateTime'    => 'update_time',
        ];

        $this->arrTypesMap = [
            'id'        => Hk_Service_Db::TYPE_INT,
            'examId'    => Hk_Service_Db::TYPE_INT,
            'title'     => Hk_Service_Db::TYPE_STR,
            'score'     => Hk_Service_Db::TYPE_JSON,
            'options'   => Hk_Service_Db::TYPE_JSON,
            'sort'      => Hk_Service_Db::TYPE_INT,
            'type'      => Hk_Service_Db::TYPE_INT,
            'deleted'       => Hk_Service_Db::TYPE_INT,
            'createTime'    => Hk_Service_Db::TYPE_INT,
            'updateTime'    => Hk_Service_Db::TYPE_INT,
        ];
    }
}