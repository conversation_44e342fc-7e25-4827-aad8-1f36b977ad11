<?php
/**
 * Created by PhpStorm.
 * User: pangjun<PERSON><PERSON>@zuoyebang.com
 * Date: 2021/1/4
 * Time: 11:12 上午
 * 学生信息表
 */
class Lxjxlib_Dao_School_User extends Lxjxlib_Dao_School_BaseDao{

    public function __construct()
    {
        parent::__construct();

        $this->_table       = 'tblUser';
        $this->arrFieldsMap = [
            'id'         => 'id',
            'uid'        => 'uid',
            'phone'      => 'phone',
            'name'       => 'name',
            'origin'     => 'origin',
            'deleted'    => 'deleted',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
        ];

        $this->arrTypesMap = [
            'id'         => Hk_Service_Db::TYPE_INT,
            'uid'        => Hk_Service_Db::TYPE_INT,
            'phone'      => Hk_Service_Db::TYPE_INT,
            'name'       => Hk_Service_Db::TYPE_STR,
            'origin'     => Hk_Service_Db::TYPE_INT,
            'deleted'    => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
        ];
    }
}