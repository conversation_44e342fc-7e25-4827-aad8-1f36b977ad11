<?php
/**************************************************************************
 *
 * Copyright (c) 2019 Zuoyebang.com, Inc. All Rights Reserved
 *
 * @file    UserExams.php
 * @date    2020-09-08
 *
 **************************************************************************/

/**
 * Class        Lxjxlib_Dao_School_UserExams
 * @date        2020-09-08
 * @desc        用户组建表
 */
class Lxjxlib_Dao_School_UserExams extends Lxjxlib_Common_BaseDao
{
    /**
     * Lxjxlib_Dao_School_UserExams constructor.
     */
    public function __construct()
    {
        parent::__construct();

        $this->_dbName      = Lxjxlib_Const_Db::DB_LXJX_SCHOOL;
        $this->_logFile     = Lxjxlib_Const_Db::DBLOG_LXJX;
        $this->_table       = 'tblUserExams';

        $this->arrFieldsMap = array(
            'id'            => 'id',
            'uid'           => 'uid',
            'subject'       => 'subject',
            'subjectName'   => 'subject_name',
            'examId'        => 'exam_id',
            'examName'      => 'exam_name',
            'grade'         => 'grade',
            'tidAmount'     => 'tid_amount',
            'isDeleted'     => 'is_deleted',
            'createTime'    => 'create_time',
            'updateTime'    => 'update_time',
            'filePath'      => 'file_path',
            'status'        => 'status',
        );

        $this->arrTypesMap = array(
            'id'            => Hk_Service_Db::TYPE_INT,
            'uid'           => Hk_Service_Db::TYPE_INT,
            'subject'       => Hk_Service_Db::TYPE_INT,
            'subjectName'   => Hk_Service_Db::TYPE_STR,
            'examId'        => Hk_Service_Db::TYPE_INT,
            'examName'      => Hk_Service_Db::TYPE_STR,
            'grade'         => Hk_Service_Db::TYPE_INT,
            'tidAmount'     => Hk_Service_Db::TYPE_INT,
            'isDeleted'     => Hk_Service_Db::TYPE_INT,
            'createTime'    => Hk_Service_Db::TYPE_INT,
            'updateTime'    => Hk_Service_Db::TYPE_INT,
            'file_path'     => Hk_Service_Db::TYPE_STR,
            'status'        => Hk_Service_Db::TYPE_INT,
        );

        $this->_fields = array_keys($this->arrFieldsMap);
    }

    /**
     * @function        getUserExamAmount
     *
     * @access          public
     * @date            2020-09-10
     * @desc            组卷数量
     *
     * @param           $uid
     *
     * @return          int
     */
    public function getUserExamAmount($uid)
    {
        $where = array(
            'uid'   => $uid,
        );

        return (int)$this->getCntByConds($where);
    }

    /**
     * @function        getUserExamsList
     *
     * @access          public
     * @date            2020-09-08
     * @desc            查询用户组卷
     *
     * @param           $uid
     * @param           $pn
     * @param           $rn
     *
     * @return          array
     */
    public function getUserExamsList($uid, $pn, $rn)
    {
        $ret = array();

        $offset = Lxjxlib_Util_Tools::getOffsetStart($pn, $rn);

        $appends = array(
            'order by id desc',
            'limit ' . $offset . ',' . $rn,
        );

        $where = array(
            'uid'   => $uid,
        );

        $examList = $this->getListByConds($where, $this->_fields, null, $appends);
        if (false !== $examList && !empty($examList)) {
            $ret = $examList;
        }

        return $ret;
    }

    public function getUserExamById($examId)
    {
        $where = array(
            'examId'    => $examId,
            'isDeleted' => Lxjxlib_Const_Common::DB_STATUS_OK,
        );

        $ret = $this->getRecordByConds($where, $this->_fields);
        return $ret;
    }
}
