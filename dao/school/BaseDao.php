<?php
/**
 * Created by PhpStorm.
 * User: pangjuncha<PERSON>@zuoyebang.com
 * Date: 2020/9/1
 * Time: 1:46 PM
 * 通用Dao层--按照DB拆分
 */
class Lxjxlib_Dao_School_BaseDao extends Hk_Common_BaseDao
{

    protected $_dbName  = 'zyblaxinjinxiao/zyblxjxschool';
    protected $_logFile = APP_NAME.'/'.APP_NAME.'_sql.log'; // 'lxjx-sql/lxjx_sql.log';
    protected $_pk      = 'id';

    /**
     * 获取默认查询字段
     * @return array
     */
    public function getFields()
    {
        return array_keys($this->arrFieldsMap);
    }

    /**
     * 获取数据表名称
     * @return string
     */
    public function tableName()
    {
        return $this->_table;
    }

    /**
     * 获取主键
     * @return string
     */
    public function getPk()
    {
        return $this->_pk;
    }

    /**
     * 将驼峰命名转换为数据库表字段命名格式
     * @param array $arrFields
     * @param array $arrFieldsMap
     * @return array $ret
     */
    private function mapFields($arrFields, $arrFieldsMap)
    {
        //参数的校验
        if (empty($arrFields) || !is_array($arrFields) || empty($arrFieldsMap)) {
            return $arrFields;
        }
        //将$arrFields中的名称转换成映射表中对应的名称
        $ret = [];
        foreach ($arrFields as $field) {
            if (isset($arrFieldsMap[$field]) && $field != $arrFieldsMap[$field]) {
                $ret[] = $arrFieldsMap[$field];
            } else {
                $ret[] = $field;
            }
        }
        return $ret;
    }

    /**
     * Insert插入，用于一次插入多行数据
     *
     * @param  array $arrFields
     * <code>
     * ['columnName1','columnName2']
     * </code>
     * @param  array $arrValues
     * <code>
     * [['test1',1],['test2',2]]
     * </code>
     * @param  array $options INSERT插入选项，支持"LOW_PRIORITY","DELAYED", "HIGH_PRIORITY", "IGNORE"
     * @param  array $onDup 主键冲突时更新的键值列表，格式参见BaseDao getListByConds()的conds参数
     * @return bool|integer 插入成功返回插入成功行数，插入失败返回false
     */
    public function multiInsert($arrFields, $arrValues, $options = null, $onDup = null)
    {
        if (empty($this->_db)) {
            $this->_db = Hk_Service_Db::getDB($this->_dbName, $this->_new, $this->_logFile, $this->_autoRotate);
        }

        $arrFields = $this->mapFields($arrFields, $this->arrFieldsMap);
        $onDup     = Hk_Service_Db::mapRow($onDup, $this->arrFieldsMap);
        return $this->_db->multiInsert($this->_table, $arrFields, $arrValues, $options, $onDup);
    }

    /**
     *
     * 此方法替代db基类批量插入时字段数据是逗号分隔时会插入失败的bug
     * 谨慎使用
     *
     * @param array $data
     * @return bool
     */
    public function insertBatch($data)
    {
        if (!is_array($data) || !isset($data[0])) {
            return false;
        }
        $f = [];
        foreach (array_keys($data[0]) as $v) {
            isset($this->arrFieldsMap[$v]) ? $f[] = $this->arrFieldsMap[$v] : $f[] = $v;
        }

        $fStr   = implode(',', $f);
        $values = [];
        foreach ($data as $v) {
            if (count($f) != count($v)) {
                return false;
            }
            $tmp = [];
            foreach ($v as $vv) {
                $tmp[] = sprintf('"%s"', $vv);
            }
            $values[] = sprintf('( %s )', implode(',', $tmp));
        }
        $sql = sprintf('INSERT INTO %s ( %s ) VALUES %s', $this->_table, $fStr, implode(',', $values));
        return $this->query($sql);
    }
}
