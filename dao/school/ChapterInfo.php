<?php

/**
 * 课件的章信息
 * Created by Phpstorm
 * User: ha<PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * Date: 2021/5/12
 * Time: 5:54 下午
 */
class Lxjxlib_Dao_School_ChapterInfo extends Lxjxlib_Dao_School_BaseDao
{
    public function __construct()
    {
        $this->_dbName = Lxjxlib_Const_Db::DB_LXJX_SCHOOL;
        $this->_table = 'tblChapterInfo';
        $this->_logFile = Lxjxlib_Const_Db::DBLOG_LXJX;
        $this->arrFieldsMap = [
            'id' => 'id',
            'materialId' => 'material_id',
            'materialName' => 'material_name',
            'subjectId' => 'subject_id',
            'subjectName' => 'subject_name',
            'gradeId' => 'grade_id',
            'gradeName' => 'grade_name',
            'termId' => 'term_id',
            'termName' => 'term_name',
            'chapterName' => 'chapter_name',
            'status' => 'status',
            'sortVal' => 'sort_val',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
        ];
        $this->arrTypesMap = [
            'id' => Hk_Service_Db::TYPE_INT,
            'materialId' => Hk_Service_Db::TYPE_INT,
            'materialName' => Hk_Service_Db::TYPE_STR,
            'subjectId' => Hk_Service_Db::TYPE_INT,
            'subjectName' => Hk_Service_Db::TYPE_STR,
            'gradeId' => Hk_Service_Db::TYPE_INT,
            'gradeName' => Hk_Service_Db::TYPE_STR,
            'termId' => Hk_Service_Db::TYPE_INT,
            'termName' => Hk_Service_Db::TYPE_STR,
            'chapterName' => Hk_Service_Db::TYPE_STR,
            'status' => Hk_Service_Db::TYPE_INT,
            'sortVal' => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
        ];

        $this->_fields = array_keys($this->arrFieldsMap);
    }
}
