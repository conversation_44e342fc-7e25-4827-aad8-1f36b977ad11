<?php

/**
 * Created by PhpStorm.
 * User: yanlimin
 * Date: 2020/10/16
 * Time: 2:22 PM
 */
class Lxjxlib_Dao_School_TestExamAnswerSheet extends Lxjxlib_Dao_School_BaseDao {

    public function __construct()
    {

        parent::__construct();

        $this->_table       = 'tblTestExamAnswerSheet';
        $this->arrFieldsMap = [
            'id'         => 'id',
            'uid'        => 'uid',
            'testId'     => 'test_id',
            'examId'     => 'exam_id',
            'status'     => 'status',
            'subjectId'  => 'subject_id',
            'submitTime' => 'submit_time',
            'score'      => 'score',
            'isGetScore' => 'is_get_score',
            'ext'        => 'ext',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
        ];

        $this->arrTypesMap = [
            'id'         => Hk_Service_Db::TYPE_INT,
            'uid'        => Hk_Service_Db::TYPE_INT,
            'testId'     => Hk_Service_Db::TYPE_INT,
            'examId'     => Hk_Service_Db::TYPE_INT,
            'status'     => Hk_Service_Db::TYPE_INT,
            'score'      => Hk_Service_Db::TYPE_INT,
            'isGetScore' => Hk_Service_Db::TYPE_INT,
            'subjectId'  => Hk_Service_Db::TYPE_INT,
            'submitTime' => Hk_Service_Db::TYPE_INT,
            'ext'        => Hk_Service_Db::TYPE_STR,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
        ];
    }
}