<?php
/**
 * Created by PhpStorm.
 * User: zhang<PERSON><EMAIL>
 * Date: 2021/3/17
 * Time: 14:21
 * 口算参加记录表
 */
class Lxjxlib_Dao_School_ActCalcStuQuickCalRecord extends Lxjxlib_Dao_School_BaseDao{

    public function __construct(){
        parent::__construct();

        $this->_table       = 'tblActCalcStuQuickCalRecord';
        $this->arrFieldsMap = [
            'id'          => 'id',
            'uid'         => 'uid',
            'actId'       => 'act_id',
            'turnId'      => 'turn_id',
            'classId'     => 'class_id',
            'gradeId'     => 'grade_id',
            'examId'      => 'exam_id',
            'examSort'    => 'exam_sort',
            'questionNum' => 'question_num',
            'consumeTime' => 'consume_time',
            'accuracy'    => 'accuracy',
            'answerSpeed' => 'answer_speed',
            'beginTime'   => 'begin_time',
            'status'      => 'status',
            'createTime'  => 'create_time',
            'updateTime'  => 'update_time',
        ];

        $this->arrTypesMap = [
            'id'          => Hk_Service_Db::TYPE_INT,
            'uid'         => Hk_Service_Db::TYPE_INT,
            'actId'       => Hk_Service_Db::TYPE_INT,
            'turnId'      => Hk_Service_Db::TYPE_INT,
            'classId'     => Hk_Service_Db::TYPE_INT,
            'gradeId'     => Hk_Service_Db::TYPE_INT,
            'examId'      => Hk_Service_Db::TYPE_INT,
            'examSort'    => Hk_Service_Db::TYPE_INT,
            'questionNum' => Hk_Service_Db::TYPE_INT,
            'consumeTime' => Hk_Service_Db::TYPE_INT,
            'accuracy'    => Hk_Service_Db::TYPE_STR,
            'answerSpeed' => Hk_Service_Db::TYPE_INT,
            'beginTime'   => Hk_Service_Db::TYPE_INT,
            'status'      => Hk_Service_Db::TYPE_INT,
            'createTime'  => Hk_Service_Db::TYPE_INT,
            'updateTime'  => Hk_Service_Db::TYPE_INT,
        ];
    }
}