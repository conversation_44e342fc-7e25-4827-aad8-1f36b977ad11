<?php
/**
 * Created by PhpStorm.
 * User: zhang<PERSON><EMAIL>
 * Date: 2021/1/4
 * Time: 14:53
 * 阅读：活动-老师报名记录
 */
class Lxjxlib_Dao_School_ActTchApply extends Lxjxlib_Dao_School_BaseDao{

    public function __construct()
    {
        parent::__construct();

        $this->_table       = 'tblActTchApply';
        $this->arrFieldsMap = [
            'id'         => 'id',
            'actId'      => 'act_id',
            'uid'     => 'uid',
            'bdUid'      => 'bd_uid',
            'ext'        => 'ext',
            'status'     => 'status',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'actType' => 'act_type',
        ];

        $this->arrTypesMap = [
            'id'         => Hk_Service_Db::TYPE_INT,
            'actId'      => Hk_Service_Db::TYPE_INT,
            'uid'        => Hk_Service_Db::TYPE_INT,
            'bdUid'      => Hk_Service_Db::TYPE_INT,
            'ext'        => Hk_Service_Db::TYPE_STR,
            'status'     => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'actType' => Hk_Service_Db::TYPE_INT,
        ];
    }
}