<?php
/**
 * Created by PhpStorm.
 * User: zhang<PERSON><EMAIL>
 * Date: 2021/4/20
 * Time: 14:21
 * 速算-排名提醒表
 */
class Lxjxlib_Dao_School_ActCalcStuNotifyRank extends Lxjxlib_Dao_School_BaseDao{

    public function __construct(){
        parent::__construct();

        $this->_table       = 'tblActCalcStuNotifyRank';
        $this->arrFieldsMap = [
            'id'          => 'id',
            'uid'         => 'uid',
            'actId'       => 'act_id',
            'turnId'      => 'turn_id',
            'classId'     => 'class_id',
            'gradeId'     => 'grade_id',
            'createTime'  => 'create_time',
            'updateTime'  => 'update_time',
        ];

        $this->arrTypesMap = [
            'id'          => Hk_Service_Db::TYPE_INT,
            'uid'         => Hk_Service_Db::TYPE_INT,
            'actId'       => Hk_Service_Db::TYPE_INT,
            'turnId'      => Hk_Service_Db::TYPE_INT,
            'classId'     => Hk_Service_Db::TYPE_INT,
            'gradeId'     => Hk_Service_Db::TYPE_INT,
            'createTime'  => Hk_Service_Db::TYPE_INT,
            'updateTime'  => Hk_Service_Db::TYPE_INT,
        ];
    }
}