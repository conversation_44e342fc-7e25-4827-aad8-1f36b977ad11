<?php
/**
 * 用户积分变动记录表
 * @file UserPointRecord.php
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/22
 */

class Lxjxlib_Dao_School_UserPointRecord extends Lxjxlib_Common_BaseMultiDao
{
    /**
     * Dao_Point_UserPointRecord constructor.
     */
    public function __construct()
    {
        parent::__construct();

        $this->_partionType = Hk_Common_BaseMultiDao::TYPE_TABLE_PARTION_MOD;

        $this->_dbName     = Lxjxlib_Const_Db::DB_LXJX_SCHOOL;
        $this->_tableName  = 'tblUserPointRecord';
        $this->_table      = 'tblUserPointRecord';
        $this->_logFile    = Lxjxlib_Const_Db::DBLOG_LXJX;
        $this->_partionNum = $this->getMaxTableNum(true);
        $this->_partionKey = 'uid';

        // 字段别名映射
        $this->arrFieldsMap = array(
            'id'          => 'id',
            'uid'         => 'uid',
            'recordId'    => 'record_id',
            'changeType'  => 'change_type',
            'changePoint' => 'change_point',
            'changeMode'  => 'change_mode',
            'changeDesc'  => 'change_desc',
            'remainPoint' => 'remain_point',
            'createTime'  => 'create_time',
            'updateTime'  => 'update_time',
        );

        // 字段类型映射
        $this->arrTypesMap = array(
            'id'          => Hk_Service_Db::TYPE_INT,
            'uid'         => Hk_Service_Db::TYPE_INT,
            'recordId'    => Hk_Service_Db::TYPE_INT,
            'changeType'  => Hk_Service_Db::TYPE_INT,
            'changePoint' => Hk_Service_Db::TYPE_INT,
            'changeMode'  => Hk_Service_Db::TYPE_INT,
            'changeDesc'  => Hk_Service_Db::TYPE_STR,
            'remainPoint' => Hk_Service_Db::TYPE_INT,
            'createTime'  => Hk_Service_Db::TYPE_INT,
            'updateTime'  => Hk_Service_Db::TYPE_INT,
        );

        $this->_fields = array_keys($this->arrFieldsMap);
    }
}