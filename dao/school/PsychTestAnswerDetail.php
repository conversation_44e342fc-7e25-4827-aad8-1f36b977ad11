<?php
/**
 * Created by PhpStorm.
 * User: pangjuncha<PERSON>@zuoyebang.com
 * Date: 2021/8/6
 * Time: 11:05 上午
 */
class Lxjxlib_Dao_School_PsychTestAnswerDetail extends Lxjxlib_Dao_School_BaseDao{

    public function __construct(){

        parent::__construct();

        $this->_table       = 'tblPsychTestAnswerDetail';
        $this->arrFieldsMap = [
            'id'      => 'id',
            'uid'     => 'uid',
            'examId'  => 'exam_id',
            'testId'  => 'test_id',
            'tid'        => 'tid',
            'answer'     => 'answer',
            'score'      => 'score',
            'duration'   => 'duration',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
        ];

        $this->arrTypesMap = [
            'id'      => Hk_Service_Db::TYPE_INT,
            'uid'     => Hk_Service_Db::TYPE_INT,
            'examId'  => Hk_Service_Db::TYPE_INT,
            'testId'  => Hk_Service_Db::TYPE_INT,
            'tid'     => Hk_Service_Db::TYPE_INT,
            'answer'  => Hk_Service_Db::TYPE_STR,
            'score'      => Hk_Service_Db::TYPE_INT,
            'duration'   => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
        ];
    }
}