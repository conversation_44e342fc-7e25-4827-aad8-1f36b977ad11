<?php
/**
 * Created by PhpStorm.
 * User: <EMAIL>
 * Date: 2021/2/7
 * Time: 11:30 上午
 */
class Lxjxlib_Dao_School_Question extends Lxjxlib_Dao_School_BaseDao{

    public function __construct()
    {

        parent::__construct();

        $this->_table       = 'tblQuestion';
        $this->arrFieldsMap = [
            'id'          => 'id',
            'tid'         => 'tid',
            'gradeId'     => 'grade_id',
            'subjectId'   => 'subject_id',
            'category'    => 'category',
            'sourceYear'  => 'source_year',
            'sourceType'  => 'source_type',
            'difficult'   => 'difficult',
            'provinceId'  => 'province_id',
            'pointId'     => 'point_id',
            'bookId'      => 'book_id',
            'realTime'    => 'real_time',
            'questionFormat'  => 'question_format',
            'answerFormat'    => 'answer_format',
            'analysisFormat'  => 'analysis_format',
            'business'        => 'business',
            'tags'            => 'tags',
            'downloadUrl'     => 'download_url',
            'status'          => 'status',
            'deleted'         => 'deleted',
            'createTime'      => 'create_time',
            'updateTime'      => 'update_time',
        ];

        $this->arrTypesMap = [
            'id'           => Hk_Service_Db::TYPE_INT,
            'tid'          => Hk_Service_Db::TYPE_INT,
            'gradeId'      => Hk_Service_Db::TYPE_INT,
            'subjectId'    => Hk_Service_Db::TYPE_INT,
            'category'     => Hk_Service_Db::TYPE_INT,
            'sourceYear'   => Hk_Service_Db::TYPE_JSON,
            'sourceType'   => Hk_Service_Db::TYPE_JSON,
            'difficult'    => Hk_Service_Db::TYPE_INT,
            'provinceId'   => Hk_Service_Db::TYPE_JSON,
            'pointId'      => Hk_Service_Db::TYPE_JSON,
            'bookId'       => Hk_Service_Db::TYPE_JSON,
            'realTime'     => Hk_Service_Db::TYPE_INT,
            'questionFormat'  => Hk_Service_Db::TYPE_STR,
            'answerFormat'    => Hk_Service_Db::TYPE_STR,
            'analysisFormat'  => Hk_Service_Db::TYPE_STR,
            'business'        => Hk_Service_Db::TYPE_INT,
            'tags'            => Hk_Service_Db::TYPE_JSON,
            'downloadUrl'     => Hk_Service_Db::TYPE_JSON,
            'status'          => Hk_Service_Db::TYPE_INT,
            'deleted'         => Hk_Service_Db::TYPE_INT,
            'createTime'      => Hk_Service_Db::TYPE_INT,
            'updateTime'      => Hk_Service_Db::TYPE_INT,
        ];
    }
}
