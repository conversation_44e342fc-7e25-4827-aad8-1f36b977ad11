<?php

/**
 * 解压记录表
 * User: zhangxiaoxu01
 */
class Lxjxlib_Dao_School_CoursewareUnpacking extends Lxjxlib_Dao_School_BaseDao
{

    public function __construct()
    {
        $this->_dbName = Lxjxlib_Const_Db::DB_LXJX_SCHOOL;
        $this->_logFile = Lxjxlib_Const_Db::DBLOG_LXJX;
        $this->_db = null;
        $this->_table = 'tblCoursewareUnpacking';
        $this->_tableName = 'tblCoursewareUnpacking';

        $this->arrFieldsMap = [
            'id'         => 'id',
            'pid'        => 'pid',
            'result'     => 'result',
            'status'     => 'status',
            'createtime' => 'createtime',
            'updatetime' => 'updatetime',
        ];

        $this->arrTypesMap = [
            'id'         => Hk_Service_Db::TYPE_INT,
            'pid'        => Hk_Service_Db::TYPE_STR,
            'result'     => Hk_Service_Db::TYPE_STR,
            'status'     => Hk_Service_Db::TYPE_INT,
            'createtime' => Hk_Service_Db::TYPE_INT,
            'updatetime' => Hk_Service_Db::TYPE_INT,
        ];

    }
}