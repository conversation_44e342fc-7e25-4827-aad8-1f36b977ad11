<?php

/**
 * Created by PhpStorm.
 * User: yanlimin
 * Date: 2021/2/19
 * Time: 2:53 PM
 */
class Lxjxlib_Dao_School_RequestLimitLog extends Lxjxlib_Dao_School_BaseDao {

    public function __construct()
    {
        parent::__construct();
        $this->_table       = 'tblRequestLimitLog';
        $this->arrFieldsMap = [
            'id'         => 'id',
            'uid'        => 'uid',
            'phone'      => 'phone',
            'ip'         => 'ip',
            'type'       => 'type',
            'sourceType' => 'source_type',
            'business'   => 'business',
            'ext'        => 'ext',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
        ];

        $this->arrTypesMap = [
            'id'         => Hk_Service_Db::TYPE_INT,
            'uid'        => Hk_Service_Db::TYPE_INT,
            'phone'      => Hk_Service_Db::TYPE_STR,
            'ip'         => Hk_Service_Db::TYPE_STR,
            'type'       => Hk_Service_Db::TYPE_INT,
            'sourceType' => Hk_Service_Db::TYPE_INT,
            'business'   => Hk_Service_Db::TYPE_INT,
            'ext'        => Hk_Service_Db::TYPE_STR,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
        ];
    }
}