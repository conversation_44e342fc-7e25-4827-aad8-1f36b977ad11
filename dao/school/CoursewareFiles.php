<?php
/**
 * @brief
 * <AUTHOR>
 * @version 1.0
 * @date 2020/1/8
 */

class Lxjxlib_Dao_School_CoursewareFiles extends Lxjxlib_Dao_School_BaseDao
{
    public function __construct()
    {
        parent::__construct();

        $this->_table = 'tblCoursewareFiles';

        $this->arrFieldsMap = [
            'id'          => 'id',
            'cwId'        => 'cw_id',
            'fileType'    => 'file_type',
            'filePath'    => 'file_path',
            'fileName'    => 'file_name',
            'fileSize'    => 'file_size',
            'ext'         => 'ext',
            'fileNameExt' => 'file_name_ext',
            'createTime'  => 'create_time',
            'updateTime'  => 'update_time',
            'deleted'     => 'deleted',
            'convertPath' => 'convert_path',
        ];

        $this->arrTypesMap = [
            'id'          => Hk_Service_Db::TYPE_INT,
            'cwId'        => Hk_Service_Db::TYPE_INT,
            'fileType'    => Hk_Service_Db::TYPE_INT,
            'filePath'    => Hk_Service_Db::TYPE_STR,
            'fileName'    => Hk_Service_Db::TYPE_STR,
            'fileSize'    => Hk_Service_Db::TYPE_INT,
            'ext'         => Hk_Service_Db::TYPE_STR,
            'fileNameExt' => Hk_Service_Db::TYPE_STR,
            'createTime'  => Hk_Service_Db::TYPE_INT,
            'updateTime'  => Hk_Service_Db::TYPE_INT,
            'deleted'     => Hk_Service_Db::TYPE_INT,
            'convertPath' => Hk_Service_Db::TYPE_STR,
        ];
        $this->_fields = array_keys($this->arrFieldsMap);

    }

    /**
     * 新增课件
     * @param $arrFields
     * @return bool
     */
    public function addCwFiles($arrFields)
    {
        $nowTime = time();
        if (!isset($arrFields['createTime'])) {
            $arrFields['createTime'] = $nowTime;
        }

        if (!isset($arrFields['updateTime'])) {
            $arrFields['updateTime'] = $nowTime;
        }
        
        $ret = $this->insertRecords($arrFields);
        return $ret;
    }

    public function editCwFiles($where, $arrFields, $time = 0)
    {
        if (empty($time)) {
            $time = time();
        }
        $arrFields['updateTime'] = $time;
        $ret = $this->updateByConds($where, $arrFields);
        return $ret;
    }

    public function getCwFiles($where)
    {
        $ret = $this->getListByConds($where, $this->_fields);
        return $ret;
        
    }

    /**
     * 批量插入
     *
     * @param $data array
     * @return bool
     */
    public function batchInsert($fields, $data)
    {
        return $this->multiInsert($fields, $data);
    }

    /**
     * 获取课件包详情
     * @param $where
     * @param $fields
     * @return array
     */
    public function getCoursewarePackageInfo($where, $fields = null)
    {
        if (empty($where)) {
            return [];
        }

        $fields = $fields ?? $this->_fields;
        return $this->getListByConds($where, $fields);
    }
}