<?php
/**
 * Created by PhpStorm.
 * User: zhang<PERSON><EMAIL>
 * Date: 2021/3/17
 * Time: 14:13
 * 速算轮次表
 */
class Lxjxlib_Dao_School_ActCalcQuickCalTurn extends Lxjxlib_Dao_School_BaseDao
{

    public function __construct()
    {
        parent::__construct();

        $this->_table       = 'tblActCalcQuickCalTurn';
        $this->arrFieldsMap = [
            'id'              => 'id',
            'actId'           => 'act_id',
            'turnId'          => 'turn_id',
            'turnName'        => 'turn_name',
            'startTime'       => 'start_time',
            'endTime'         => 'end_time',
            'gradeId'         => 'grade_id',
            'answerTimeLimit' => 'answer_time_limit',
            'everydayLimit'   => 'everyday_limit',
            'sort'            => 'sort',
            'questionNum'     => 'question_num',
            'kIds'            => 'k_ids',
            'isImport'        => 'is_import',
            'status'          => 'status',
            'createTime'      => 'create_time',
            'updateTime'      => 'update_time',
        ];

        $this->arrTypesMap = [
            'id'              => Hk_Service_Db::TYPE_INT,
            'actId'           => Hk_Service_Db::TYPE_INT,
            'turnId'          => Hk_Service_Db::TYPE_INT,
            'turnName'        => Hk_Service_Db::TYPE_STR,
            'startTime'       => Hk_Service_Db::TYPE_INT,
            'endTime'         => Hk_Service_Db::TYPE_INT,
            'gradeId'         => Hk_Service_Db::TYPE_INT,
            'answerTimeLimit' => Hk_Service_Db::TYPE_INT,
            'everydayLimit'   => Hk_Service_Db::TYPE_INT,
            'sort'            => Hk_Service_Db::TYPE_INT,
            'questionNum'     => Hk_Service_Db::TYPE_INT,
            'kIds'            => Hk_Service_Db::TYPE_STR,
            'isImport'        => Hk_Service_Db::TYPE_INT,
            'status'          => Hk_Service_Db::TYPE_INT,
            'createTime'      => Hk_Service_Db::TYPE_INT,
            'updateTime'      => Hk_Service_Db::TYPE_INT,
        ];
    }
}
