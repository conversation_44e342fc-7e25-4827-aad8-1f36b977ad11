<?php

/**
 * @befie   用户微信信息表
 * @file    dao/user/UserWechatInfo.php
 * <AUTHOR>
 * @version 1.0
 * @since
 * @date    2019/10/19 16:28
 */
class Lxjxlib_Dao_School_UserWechatInfo extends Lxjxlib_Dao_School_BaseDao
{
    public function __construct()
    {
        parent::__construct();

        $this->_dbName = Lxjxlib_Const_Db::DB_LXJX_SCHOOL;
        $this->_logFile = Lxjxlib_Const_Db::DBLOG_LXJX;
        $this->_db = null;
        $this->_table = 'tblUserWechatInfo';

        $this->arrFieldsMap = [
            'id' => 'id',
            'openid' => 'openid',
            'uid' => 'uid',
            'unionid' => 'unionid',
            'appId' => 'app_id',
            'nickname' => 'nickname',
            'sex' => 'sex',
            'province' => 'province',
            'city' => 'city',
            'country' => 'country',
            'headImgUrl' => 'head_img_url',
            'isSubscribe' => 'is_subscribe',
            'firstSubscribeTime' => 'first_subscribe_time',
            'subscribeTime' => 'subscribe_time',
            'subscribeScene' => 'subscribe_scene',
            'lastTime' => 'last_time',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
        ];

        $this->arrTypesMap = [
            'id' => Hk_Service_Db::TYPE_INT,
            'openid' => Hk_Service_Db::TYPE_STR,
            'uid' => Hk_Service_Db::TYPE_INT,
            'unionid' => Hk_Service_Db::TYPE_STR,
            'appId' => Hk_Service_Db::TYPE_STR,
            'nickname' => Hk_Service_Db::TYPE_STR,
            'sex' => Hk_Service_Db::TYPE_INT,
            'province' => Hk_Service_Db::TYPE_STR,
            'city' => Hk_Service_Db::TYPE_STR,
            'country' => Hk_Service_Db::TYPE_STR,
            'headImgUrl' => Hk_Service_Db::TYPE_STR,
            'isSubscribe' => Hk_Service_Db::TYPE_INT,
            'firstSubscribeTime' => Hk_Service_Db::TYPE_INT,
            'subscribeTime' => Hk_Service_Db::TYPE_INT,
            'subscribeScene' => Hk_Service_Db::TYPE_STR,
            'lastTime' => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
        ];
    }
}
