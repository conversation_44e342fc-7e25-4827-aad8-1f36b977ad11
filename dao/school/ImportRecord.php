<?php
/**
 * Created by PhpStorm.
 * User: pangjun<PERSON><PERSON>@zuoyebang.com
 * Date: 2021/5/19
 * Time: 11:21 上午
 * 导入记录表
 */
class Lxjxlib_Dao_School_ImportRecord extends Lxjxlib_Dao_School_BaseDao{

    public function __construct(){
        parent::__construct();

        $this->_table       = 'tblImportRecord';
        $this->arrFieldsMap = [
            'id'         => 'id',
            'type'       => 'type',
            'title'      => 'title',
            'status'     => 'status',
            'operator'   => 'operator',
            'deleted'    => 'deleted',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
        ];

        $this->arrTypesMap = [
            'id'          => Hk_Service_Db::TYPE_INT,
            'type'        => Hk_Service_Db::TYPE_INT,
            'title'       => Hk_Service_Db::TYPE_STR,
            'status'      => Hk_Service_Db::TYPE_INT,
            'operator'    => Hk_Service_Db::TYPE_STR,
            'deleted'     => Hk_Service_Db::TYPE_INT,
            'createTime'  => Hk_Service_Db::TYPE_INT,
            'updateTime'  => Hk_Service_Db::TYPE_INT,
        ];
    }
}
