<?php

/**
 * 测评web用户
 * Class Lxjxlib_Dao_School_TestPcUsers
 */
class Lxjxlib_Dao_School_TestPcUsers extends Lxjxlib_Dao_School_BaseDao{
    public function __construct()
    {
        parent::__construct();

        $this->_table       = 'tblTestPcUsers';
        $this->arrFieldsMap = [
            'uid'           => 'uid',
            'bindUid'       => 'bind_uid',
            'pwd'           => 'pwd',
            'phone'         => 'phone',
            'batch'         => 'batch',
            'deleted'       => 'deleted',
            'createTime'    => 'create_time',
            'updateTime'    => 'update_time',
        ];

        $this->arrTypesMap = [
            'uid'           => Hk_Service_Db::TYPE_INT,
            'bindUid'       => Hk_Service_Db::TYPE_INT,
            'pwd'           => Hk_Service_Db::TYPE_STR,
            'phone'         => Hk_Service_Db::TYPE_STR,
            'batch'         => Hk_Service_Db::TYPE_INT,
            'deleted'       => Hk_Service_Db::TYPE_INT,
            'createTime'    => Hk_Service_Db::TYPE_INT,
            'updateTime'    => Hk_Service_Db::TYPE_INT,
        ];
    }
}