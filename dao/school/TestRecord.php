<?php
/**
 * Created by PhpStorm.
 * User: <EMAIL>
 * Date: 2020/10/15
 * Time: 10:34 上午
 * 测评记录表
 */
class Lxjxlib_Dao_School_TestRecord extends Lxjxlib_Dao_School_BaseDao{

    public function __construct()
    {

        parent::__construct();

        $this->_table       = 'tblTestRecord';
        $this->arrFieldsMap = [
            'id'                => 'id',
            'testId'            => 'test_id',
            'name'              => 'name',
            'content'           => 'content',
            'classNum'          => 'class_num',
            'answerShow'        => 'answer_show',
            'reportScoreConf'   => 'report_score_conf',
            'isInner'           => 'is_inner',
            'testType'          => 'test_type',
            'examList'          => 'exam_list',
            'posterPid'         => 'poster_pid',
            'status'            => 'status',
            'versionId'         => 'version_id',
            'deleted'           => 'deleted',
            'ext'               => 'ext',
            'createTime'        => 'create_time',
            'updateTime'        => 'update_time',
        ];

        $this->arrTypesMap = [
            'id'                => Hk_Service_Db::TYPE_INT,
            'testId'            => Hk_Service_Db::TYPE_INT,
            'name'              => Hk_Service_Db::TYPE_STR,
            'content'           => Hk_Service_Db::TYPE_STR,
            'classNum'          => Hk_Service_Db::TYPE_INT,
            'answerShow'        => Hk_Service_Db::TYPE_INT,
            'reportScoreConf'   => Hk_Service_Db::TYPE_JSON,
            'isInner'           => Hk_Service_Db::TYPE_INT,
            'testType'          => Hk_Service_Db::TYPE_INT,
            'examList'          => Hk_Service_Db::TYPE_JSON,
            'posterPid'         => Hk_Service_Db::TYPE_STR,
            'status'            => Hk_Service_Db::TYPE_INT,
            'versionId'         => Hk_Service_Db::TYPE_INT,
            'deleted'           => Hk_Service_Db::TYPE_INT,
            'ext'               => Hk_Service_Db::TYPE_JSON,
            'createTime'        => Hk_Service_Db::TYPE_INT,
            'updateTime'        => Hk_Service_Db::TYPE_INT,
        ];
    }

}