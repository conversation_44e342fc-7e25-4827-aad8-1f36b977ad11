<?php
/**
 * Created by PhpStorm.
 * User: zhang<PERSON><EMAIL>
 * Date: 2021/1/4
 * Time: 19:23
 * 阅读：阅读：活动-学生任务记录表
 */
class Lxjxlib_Dao_School_ActTaskStuRecord extends Lxjxlib_Dao_School_BaseDao{

    public function __construct()
    {
        parent::__construct();

        $this->_table       = 'tblActTaskStuRecord';
        $this->arrFieldsMap = [
            'id'          => 'id',
            'actId'       => 'act_id',
            'taskId'      => 'task_id',
            'subTaskId'   => 'sub_task_id',
            'classId'     => 'class_id',
            'stuUid'      => 'stu_uid',
            'status'      => 'status',
            'isDropOut'   => 'is_drop_out',
            'createTime'  => 'create_time',
            'updateTime'  => 'update_time',
            'ext'         => 'ext',
            //诗词学习需求ext数据存储结构注释(2021-05-10 15:04)：
            // {
            //     "setting":{
            //         "1":{   //诗词学习
            //             "status":4 //1未完成 4已完成
            //         },
            //         "2":{ //朗读
            //             "status":4, 
            //             "newestId":51, //最新的提交记录id （tblActSubTaskSubmitHistory.id）
            //             "score":75,  //目前最高得分
            //             "lengthMs":6420, //最高分数的那条记录的音频时长（单位：毫秒）
            //             "hisId":51，//最高分数的那条记录的id(tblActSubTaskSubmitHistory.id)
            //         },
            //         "3":{ //背诵
            //             "status":4,
            //             "newestId":52,
            //             "score":63,
            //             "lengthMs":7770,
            //             "hisId":52
            //         },
            //         "4":{ //巩固练习
            //             "status":2
            //         }
            //     },
            //     "correctNum":5
            // }

        ];

        $this->arrTypesMap = [
            'id'          => Hk_Service_Db::TYPE_INT,
            'actId'       => Hk_Service_Db::TYPE_INT,
            'taskId'      => Hk_Service_Db::TYPE_INT,
            'subTaskId'   => Hk_Service_Db::TYPE_INT,
            'classId'     => Hk_Service_Db::TYPE_INT,
            'stuUid'      => Hk_Service_Db::TYPE_INT,
            'status'      => Hk_Service_Db::TYPE_INT,
            'isDropOut'   => Hk_Service_Db::TYPE_INT,
            'createTime'  => Hk_Service_Db::TYPE_INT,
            'updateTime'  => Hk_Service_Db::TYPE_INT,
            'ext'         => Hk_Service_Db::TYPE_STR,
        ];
    }
}