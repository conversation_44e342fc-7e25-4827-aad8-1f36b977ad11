<?php
/**
 * @file: SaleCallRecord.php
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @date: 2017-09-04
 * @desc: 销售通话记录表
 */
class Hkzb_Dao_Gnmis_SaleCallRecord extends Hk_Common_BaseDao {
    public function __construct() {
        $this->_dbName      = 'gnmis/gnmis';
        $this->_db          = null;
        $this->_table       = "tblSaleCallRecord";
        $this->_logFile     = "gnmis-sql/gnmis_sql.log";
        
        $this->arrFieldsMap = array(
            'id'                    => 'id',
            'saleUid'               => 'sale_uid',
            'saleName'              => 'sale_name',
            'callCenterId'          => 'call_center_id',
            'phone'                 => 'phone',
            'studentUid'            => 'student_uid',
            'createTime'            => 'create_time',
            'updateTime'            => 'update_time',
            'callTime'              => 'call_time',
            'endTime'               => 'end_time',
            'nextConnectTime'       => 'next_connect_time',
            'duration'              => 'duration',
            'connectType'           => 'connect_type',
            'connectDetail'         => 'connect_detail',
            'status'                => 'status',
            'satisty'               => 'satisty',
            'recordFile'            => 'record_file',
            'classTraceId'          => 'class_trace_id',
            'extData'               => 'ext_data',
            'serveType'             => 'serve_type',
            'flowId'                => 'flow_id',
            'bit'                   => 'bit',
            'userEditTime'          => 'user_edit_time',
        );
        
        $this->arrTypesMap = array(
            'id'                    => Hk_Service_Db::TYPE_INT,
            'saleUid'               => Hk_Service_Db::TYPE_INT,
            'saleName'              => Hk_Service_Db::TYPE_STR,
            'callCenterId'          => Hk_Service_Db::TYPE_STR,
            'phone'                 => Hk_Service_Db::TYPE_STR,
            'studentUid'            => Hk_Service_Db::TYPE_INT,
            'createTime'            => Hk_Service_Db::TYPE_INT,
            'updateTime'            => Hk_Service_Db::TYPE_INT,
            'callTime'              => Hk_Service_Db::TYPE_INT,
            'endTime'               => Hk_Service_Db::TYPE_INT,
            'nextConnectTime'       => Hk_Service_Db::TYPE_INT,
            'duration'              => Hk_Service_Db::TYPE_INT,
            'connectType'           => Hk_Service_Db::TYPE_INT,
            'connectDetail'         => Hk_Service_Db::TYPE_STR,
            'status'                => Hk_Service_Db::TYPE_INT,
            'satisty'               => Hk_Service_Db::TYPE_INT,
            'recordFile'            => Hk_Service_Db::TYPE_STR,
            'classTraceId'          => Hk_Service_Db::TYPE_INT,
            'extData'               => Hk_Service_Db::TYPE_JSON,
            'flowId'                => Hk_Service_Db::TYPE_INT,
            'bit'                   => Hk_Service_Db::TYPE_INT,
            'userEditTime'          => Hk_Service_Db::TYPE_INT,
        );
    }
    
    public function getAllFields() {
        return array_keys($this->arrFieldsMap);
    }
} 