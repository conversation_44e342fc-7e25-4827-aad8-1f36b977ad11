<?php
/**************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @filename:      StuConsultClue
 * @author:        <EMAIL>
 * @desc:          人维度的例子
 * @create:        2018-3-19 14:22:22
 */

class Hkzb_Dao_Gnmis_StuConsultClue extends Hk_Common_BaseDao {

    public static $bitPackMap = array(
        'isConfirmToTmk'        => 1,   // 向tmk确认拿到微信
        'isTmkFlowToSc'         => 2,   // tmk流转到sc
        'isFlowToScAfterClass'  => 4,   // 课后流转到sc
        'isShow'                => 8,   // 例子可见标记
        'isInvalid'             => 16,  // 无效例子
        'isExpire'              => 32,  // 过期失效例子
    );

    public function __construct($db = null) {
        $this->_dbName      = 'gnmis/gnmis';
        $this->_db          = $db;
        $this->_table       = 'tblStuConsultClue';
        $this->_logFile     = 'gnmis-sql/gnmis_sql.log';
        $this->arrFieldsMap = [
            'id'              => 'id',
            'stuPhone'        => 'stu_phone',
            'stuTouchPhone'   => 'stu_touch_phone',
            'stuUid'          => 'stu_uid',
            'type'            => 'type',
            'courseId'        => 'course_id',
            'courseStime'     => 'course_stime',
            'courseEtime'     => 'course_etime',
            'saleUid'         => 'sale_uid',
            'intention'       => 'intention',
            'status'          => 'status',
            'transMoney'      => 'trans_money',
            'transOrder'      => 'trans_order',
            'transStatus'     => 'trans_status',
            'transLevel'      => 'trans_level',
            'gradeId'         => 'grade_id',
            'createTime'      => 'create_time',
            'bindTime'        => 'bind_time',
            'unbindTime'      => 'unbind_time',
            'expireTime'      => 'expire_time',
            'confirmTime'     => 'confirm_time',
            'firstTouchTime'  => 'first_touch_time',
            'nextTouchTime'   => 'next_touch_time',
            'lastTouchTime'   => 'last_touch_time',
            'bit'             => 'bit',
            'ext'             => 'ext',
            'flowId'          => 'flow_id',
            'cvtValue'        => 'cvt_value',
            'cvtTime'         => 'cvt_time',
            'classAttend'     => 'class_attend',
            'cvtQuota'        => 'cvt_quota',
        ];

        $this->arrTypesMap = [
            'id'             => Hk_Service_Db::TYPE_INT,
            'stuPhone'       => Hk_Service_Db::TYPE_STR,
            'stuTouchPhone'  => Hk_Service_Db::TYPE_STR,
            'stuUid'         => Hk_Service_Db::TYPE_INT,
            'type'           => Hk_Service_Db::TYPE_INT,
            'stuChannel'     => Hk_Service_Db::TYPE_STR,
            'courseId'       => Hk_Service_Db::TYPE_INT,
            'courseStime'    => Hk_Service_Db::TYPE_INT,
            'courseEtime'    => Hk_Service_Db::TYPE_INT,
            'saleUid'        => Hk_Service_Db::TYPE_INT,
            'intention'      => Hk_Service_Db::TYPE_INT,
            'status'         => Hk_Service_Db::TYPE_INT,
            'transMoney'     => Hk_Service_Db::TYPE_INT,
            'transOrder'     => Hk_Service_Db::TYPE_INT,
            'transStatus'    => Hk_Service_Db::TYPE_INT,
            'transLevel'     => Hk_Service_Db::TYPE_INT,
            'gradeId'        => Hk_Service_Db::TYPE_INT,
            'createTime'     => Hk_Service_Db::TYPE_INT,
            'bindTime'       => Hk_Service_Db::TYPE_INT,
            'expireTime'     => Hk_Service_Db::TYPE_INT,
            'confirmTime'    => Hk_Service_Db::TYPE_INT,
            'unbindTime'     => Hk_Service_Db::TYPE_INT,
            'firstTouchTime' => Hk_Service_Db::TYPE_INT,
            'nextTouchTime'  => Hk_Service_Db::TYPE_INT,
            'lastTouchTime'  => Hk_Service_Db::TYPE_INT,
            'bit'            => Hk_Service_Db::TYPE_INT,
            'ext'            => Hk_Service_Db::TYPE_JSON,
            'flowId'         => Hk_Service_Db::TYPE_INT,
            'cvtValue'       => Hk_Service_Db::TYPE_INT,
            'cvtTime'        => Hk_Service_Db::TYPE_INT,
            'classAttend'    => Hk_Service_Db::TYPE_INT,
            'cvtQuota'       => Hk_Service_Db::TYPE_STR,
        ];
    }

    public function getAllFields() {
        return array_keys($this->arrFieldsMap);
    }
}
