<?php
/**************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @filename:      StuConsultTaskClue
 * @author:        <EMAIL>
 * @desc:          拉新-销售-绑定关系
 * @create:        2017-09-07 14:22:22
 * @last modified: 2017-09-07 14:22:22
 */
class Hkzb_Dao_Gnmis_StuConsultTaskClue extends Hk_Common_BaseDao {
    
    public static $allFields = array(
        'id',
        'stuPhone',
        'stuTouchPhone',
        'stuUid',
        'stuUtype',
        'stuChannel',
        'courseId',
        'saleUid',
        'intention',
        'status',
        'transTime',
        'transMoney',
        'transOrder',
        'transStatus',
        'gradeId',
        'disGradeId',
        'sex',
        'province',
        'city',
        'area',
        'school',
        'bookVersion',
        'stuName',
        'qq',
        'createTime',
        'bindTime',
        'unbindTime',
        'firstTouchTime',
        'nextTouchTime',
        'lastTouchTime',
        'bit',
        'ext',
    );

    public function __construct() {
        $this->_dbName      = 'gnmis/gnmis';
        $this->_db          = null;
        $this->_table       = 'tblStuConsultTaskClue';
        $this->_tableName   = 'tblStuConsultTaskClue';
        $this->_logFile     = 'gnmis-sql/gnmis_sql.log';
        $this->arrFieldsMap = [
            'id'              => 'id',
            'stuPhone'        => 'stu_phone',
            'stuTouchPhone'   => 'stu_touch_phone',
            'stuUid'          => 'stu_uid',
            'stuUtype'        => 'stu_utype',
            'stuChannel'      => 'stu_channel',
            'courseId'        => 'course_id',
            'saleUid'         => 'sale_uid',
            'intention'       => 'intention',
            'status'          => 'status',
            'transTime'       => 'trans_time',
            'transMoney'      => 'trans_money',
            'transOrder'      => 'trans_order',
            'transStatus'     => 'trans_status',
            'gradeId'         => 'grade_id',
            'disGradeId'      => 'dis_grade_id',
            'sex'             => 'sex',
            'province'        => 'province',
            'city'            => 'city',
            'area'            => 'area',
            'school'          => 'school',
            'bookVersion'     => 'book_version',
            'stuName'         => 'stu_name',
            'qq'              => 'qq',
            'createTime'      => 'create_time',
            'bindTime'        => 'bind_time',
            'unbindTime'      => 'unbind_time',
            'firstTouchTime'  => 'first_touch_time',
            'nextTouchTime'   => 'next_touch_time',
            'lastTouchTime'   => 'last_touch_time',
            'bit'             => 'bit',
            'ext'             => 'ext',
            'flowId'          => 'flow_id',
            'cvtLevel0'       => 'cvt_level0',
            'cvtLevel1'       => 'cvt_level1',
            'cvtLevel2'       => 'cvt_level2',
            'cvtLevel3'       => 'cvt_level3',
            'cvtTime'         => 'cvt_time',
            'classAttend'     => 'class_attend',
            'cvtQuota'        => 'cvt_quota',
        ];

        $this->arrTypesMap = [
            'id'             => Hk_Service_Db::TYPE_INT,
            'stuPhone'       => Hk_Service_Db::TYPE_INT,
            'stuTouchPhone'  => Hk_Service_Db::TYPE_INT,
            'stuUid'         => Hk_Service_Db::TYPE_INT,
            'stuUtype'       => Hk_Service_Db::TYPE_INT,
            'stuChannel'     => Hk_Service_Db::TYPE_STR,
            'courseId'       => Hk_Service_Db::TYPE_INT,
            'saleUid'        => Hk_Service_Db::TYPE_INT,
            'intention'      => Hk_Service_Db::TYPE_INT,
            'status'         => Hk_Service_Db::TYPE_INT,
            'transTime'      => Hk_Service_Db::TYPE_INT,
            'transMoney'     => Hk_Service_Db::TYPE_INT,
            'transOrder'     => Hk_Service_Db::TYPE_INT,
            'transStatus'    => Hk_Service_Db::TYPE_INT,
            'gradeId'        => Hk_Service_Db::TYPE_INT,
            'disGradeId'     => Hk_Service_Db::TYPE_INT,
            'sex'            => Hk_Service_Db::TYPE_INT,
            'province'       => Hk_Service_Db::TYPE_STR,
            'city'           => Hk_Service_Db::TYPE_STR,
            'area'           => Hk_Service_Db::TYPE_STR,
            'school'         => Hk_Service_Db::TYPE_STR,
            'bookVersion'    => Hk_Service_Db::TYPE_STR,
            'stuName'        => Hk_Service_Db::TYPE_STR,
            'qq'             => Hk_Service_Db::TYPE_STR,
            'createTime'     => Hk_Service_Db::TYPE_INT,
            'bindTime'       => Hk_Service_Db::TYPE_INT,
            'unbindTime'     => Hk_Service_Db::TYPE_INT,
            'firstTouchTime' => Hk_Service_Db::TYPE_INT,
            'nextTouchTime'  => Hk_Service_Db::TYPE_INT,
            'lastTouchTime'  => Hk_Service_Db::TYPE_INT,
            'bit'            => Hk_Service_Db::TYPE_INT,
            'ext'            => Hk_Service_Db::TYPE_JSON,
            'cvtLevel0'      => Hk_Service_Db::TYPE_INT,
            'cvtLevel1'      => Hk_Service_Db::TYPE_INT,
            'cvtLevel2'      => Hk_Service_Db::TYPE_INT,
            'cvtLevel3'      => Hk_Service_Db::TYPE_INT,
            'cvtTime'        => Hk_Service_Db::TYPE_INT,
            'classAttend'    => Hk_Service_Db::TYPE_INT,
            'cvtQuota'       => Hk_Service_Db::TYPE_STR,
        ];
    }
}
