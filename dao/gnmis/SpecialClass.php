<?php
/**
 * SpecialClass.php
 * lian<PERSON><PERSON><PERSON>@zuoyebang.com
 * 2017-08-17
 * 满班报名
 */
class Hkzb_Dao_Gnmis_SpecialClass extends Hk_Common_BaseDao
{
    public function __construct(){
        $this->_dbName      = "gnmis/gnmis";
        $this->_db          = null;
        $this->_table       = "tblSpecialClass";
        $this->arrFieldsMap = array(
            'id'                => 'id',
            'uid'               => 'uid',
            'phone'             => 'phone',
            'courseId'          => 'course_id',
            'courseName'        => 'course_name',
            'grade'             => 'grade',
            'subject'           => 'subject',
            'teacherUid'        => 'teacher_uid',
            'teacherName'       => 'teacher_name',
            'status'            => 'status',
            'createTime'        => 'create_time',
            'updateTime'        => 'update_time',
            'operatorUid'       => 'operator_uid',
            'operator'          => 'operator',
            'extData'           => 'ext_data',
            'registerUrl'       => 'register_url',
        );
        
        $this->arrTypesMap = array(
            'id'                 => Hk_Service_Db::TYPE_INT,
            'uid'                => Hk_Service_Db::TYPE_INT,
            'phone'              => Hk_Service_Db::TYPE_INT,
            'courseId'           => Hk_Service_Db::TYPE_INT,
            'courseName'         => Hk_Service_Db::TYPE_STR,
            'grade'              => Hk_Service_Db::TYPE_INT,
            'subject'            => Hk_Service_Db::TYPE_INT,
            'teacherUid'         => Hk_Service_Db::TYPE_INT,
            'teacherName'        => Hk_Service_Db::TYPE_STR,
            'status'             => Hk_Service_Db::TYPE_INT,
            'createTime'         => Hk_Service_Db::TYPE_INT,
            'updateTime'         => Hk_Service_Db::TYPE_INT,
            'operatorUid'        => Hk_Service_Db::TYPE_INT,
            'operator'           => Hk_Service_Db::TYPE_STR,
            'extData'            => Hk_Service_Db::TYPE_JSON,
            'registerUrl'        => Hk_Service_Db::TYPE_STR,
        );
    }
} 