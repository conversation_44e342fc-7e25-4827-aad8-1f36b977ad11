<?php
/**
 * @file Storage.php.
 * <AUTHOR>
 * @date: 2018/5/16
 */
class Hkzb_Dao_TikuTar_Storage extends Hk_Common_BaseDao
{
    public $arrFields;
    public function __construct() {
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_dbName = 'resource/fudao_teachresource';
        $this->_db     = Hk_Service_Db::getDB($this->_dbName);
        $this->_table  = "tblStorage";
        $this->arrFieldsMap = array(
            'id'          => 'id',
            'teacherUid'  => 'teacher_uid',
            'tid'         => 'tid',
            'zbTid'       => 'zb_tid',
            'gradeId'     => 'grade_id',
            'subjectId'   => 'subject_id',
            'category'    => 'category',
            'status'      => 'status',
            'deleted'     => 'deleted',
            'ext'         => 'ext',
            'createTime'  => 'create_time',
            'updateTime'  => 'update_time',
        );

        $this->arrTypesMap = array(
            'id'         => Hk_Service_Db::TYPE_INT,
            'teacherUid' => Hk_Service_Db::TYPE_INT,
            'tid'        => Hk_Service_Db::TYPE_INT,
            'zbTid'      => Hk_Service_Db::TYPE_INT,
            'gradeId'    => Hk_Service_Db::TYPE_INT,
            'subjectId'  => Hk_Service_Db::TYPE_INT,
            'category'   => Hk_Service_Db::TYPE_INT,
            'status'     => Hk_Service_Db::TYPE_INT,
            'deleted'    => Hk_Service_Db::TYPE_INT,
            'ext'        => Hk_Service_Db::TYPE_JSON,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
        );

        $this->arrFields = array_keys($this->arrFieldsMap);
    }
}