<?php
/**
 * @brief   课程表
 * @file    dao/bjx/CourseStage.php
 * @auther  renjie01<<EMAIL>>
 * @date    2021-06-09
 */
class Lxjxlib_Dao_Bjx_CourseStage extends Lxjxlib_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = Lxjxlib_Const_Db::DB_LXJX_SCHOOL;
        $this->_logFile = Lxjxlib_Const_Db::DBLOG_LXJX;
        $this->_db = null;
        $this->_table = 'tblCourseStage';

        $this->arrFieldsMap = [
            'id'            => 'id',
            'businessStage' => 'business_stage',
            'applyStage'    => 'apply_stage',
            'applySource'   => 'apply_source',
            'status'        => 'status',
            'season'        => 'season',
            'courseUrl'     => 'course_url',
            'startTime'     => 'start_time',
            'endTime'       => 'end_time',
            'imageUrl'      => 'image_url',
            'extInfo'       => 'ext_info',
            'opName'        => 'op_name',
            'createTime'    => 'create_time',
            'updateTime'    => 'update_time',
        ];

        $this->arrTypesMap = [
            'id'            => Hk_Service_Db::TYPE_INT,
            'businessStage' => Hk_Service_Db::TYPE_STR,
            'applyStage'    => Hk_Service_Db::TYPE_STR,
            'applySource'   => Hk_Service_Db::TYPE_STR,
            'season'        => Hk_Service_Db::TYPE_STR,
            'status'        => Hk_Service_Db::TYPE_INT,
            'courseUrl'     => Hk_Service_Db::TYPE_STR,
            'startTime'     => Hk_Service_Db::TYPE_INT,
            'endTime'       => Hk_Service_Db::TYPE_INT,
            'imageUrl'      => Hk_Service_Db::TYPE_STR,
            'extInfo'       => Hk_Service_Db::TYPE_JSON,
            'opName'        => Hk_Service_Db::TYPE_STR,
            'createTime'    => Hk_Service_Db::TYPE_INT,
            'updateTime'    => Hk_Service_Db::TYPE_INT,
        ];
    }
}