<?php

class Hkzb_Dao_Inclass_ExamAnswer extends Hk_Common_BaseMultiDao {

    public $arrFields;

    public $intPartionNum;

    public function __construct() {
        $this->_dbName      = 'resource/fudao_teachresource';
        $this->_db          = null;
        $this->_table       = "tblExamAnswer";
        $this->_tableName   = "tblExamAnswer";
        $this->_partionKey  = "exam_id";
        $this->_partionType = self::TYPE_TABLE_PARTION_MUL;
        $this->_partionNum  = 500000;
        $this->arrFieldsMap = array(
            'answerId'   => 'answer_id',
            'examId'     => 'exam_id',
            'uid'        => 'uid',
            'isFinish'   => 'is_finish',
            'score'      => 'score',
            'answerList' => 'answer_list',
            'extData'    => 'ext_data',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'deleted'    => 'deleted',
        );

        $this->arrTypesMap = array(
            'answerId'   => Hk_Service_Db::TYPE_INT,
            'examId'     => Hk_Service_Db::TYPE_INT,
            'uid'        => Hk_Service_Db::TYPE_INT,
            'isFinish'   => Hk_Service_Db::TYPE_INT,
            'score'      => Hk_Service_Db::TYPE_INT,
            'answerList' => Hk_Service_Db::TYPE_JSON,
            'extData'    => Hk_Service_Db::TYPE_JSON,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'deleted'    => Hk_Service_Db::TYPE_INT,
        );

        $this->arrFields     = array_keys($this->arrFieldsMap);
        $this->intPartionNum = $this->_partionNum;
    }

}