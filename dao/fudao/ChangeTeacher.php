<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file ChangeTeacher.php
 * <AUTHOR>
 * @date 2016/08/4 13:26:46
 * @brief 换老师
 *  
 **/

class Hkzb_Dao_Fudao_ChangeTeacher extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = 'fudao/zyb_fudao';
        //$this->_db     = Hk_Service_Db::getDB($this->_dbName);
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblChangeTeacher";
        $this->arrFieldsMap = array(
            'id'         => 'id',
            'courseId'   => 'course_id',
            'duty'       => 'duty',
            'exTeacher'  => 'ex_teacher',
            'nowTeacher' => 'now_teacher',
            'num'        => 'num',
            'reason'     => 'reason',
            'deleted'    => 'deleted',
            'operator'   => 'operator',
            'createTime' => 'create_time',
            'extData'    => 'ext_data',
        );

        $this->arrTypesMap = array(
            'id'         => Hk_Service_Db::TYPE_INT,
            'courseId'   => Hk_Service_Db::TYPE_INT,
            'duty'       => Hk_Service_Db::TYPE_INT,
            'exTeacher'  => Hk_Service_Db::TYPE_INT,
            'nowTeacher' => Hk_Service_Db::TYPE_INT,
            'num'        => Hk_Service_Db::TYPE_INT,
            'reason'     => Hk_Service_Db::TYPE_STR,
            'deleted'    => Hk_Service_Db::TYPE_INT,
            'operator'   => Hk_Service_Db::TYPE_STR,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'extData'    => Hk_Service_Db::TYPE_JSON,
        );
    }
}