<?php

/**
 * @file   Class StudentNetwork.php
 * <AUTHOR>
 * @date   2017/10/12 13:57:53
 * @brief
 **/
class Hkzb_Dao_Fudao_StudentNetwork extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName      = 'zhibo/zhibo_notice';
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblStudentLongConnection";
        $this->arrFieldsMap = array(
            'id'        => 'id',
            'lessonId'  => 'lesson_id',
            'studentId' => 'student_id',
            'extData'   => 'ext_data',
            'ctime'     => 'ctime',
        );

        $this->arrTypesMap = array(
            'id'        => Hk_Service_Db::TYPE_INT,
            'studentId' => Hk_Service_Db::TYPE_INT,
            'lessonId'  => Hk_Service_Db::TYPE_INT,
            'extData'   => Hk_Service_Db::TYPE_STR,
            'ctime'     => Hk_Service_Db::TYPE_INT,
        );
    }
}