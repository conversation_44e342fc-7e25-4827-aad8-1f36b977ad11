<?php
/**
 * Copyright (c) 2016 zuoyebang.com, Inc. All Rights Reserved
 * @file:OperationalActivity.php
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @date: 2017/2/14
 * @time: 16:20
 * @desc: 运营活动表
 */

class Hkzb_Dao_Fudao_OperationalActivity extends Hk_Common_BaseDao
{
    public static $arrFields = array(
        'actId',
        'actName',
        'assortType',
        'gradeList',
        'subjectList',
        'status',
        'deleted',
        'createTime',
        'updateTime',
        'extData',
    );

    public function __construct()
    {
        $this->_dbName      = 'fudao/zyb_fudao';
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = 'tblOperationalActivity';
        $this->arrFieldsMap = array(
            'actId'       => 'act_id',
            'actName'     => 'act_name',
            'assortType'  => 'assort_type',
            'gradeList'   => 'grade_list',
            'subjectList' => 'subject_list',
            'status'      => 'status',
            'deleted'     => 'deleted',
            'createTime'  => 'create_time',
            'updateTime'  => 'update_time',
            'extData'     => 'ext_data'
        );

        $this->arrTypesMap = array(
            'actId'       => Hk_Service_Db::TYPE_INT,
            'actName'     => Hk_Service_Db::TYPE_STR,
            'assortType'  => Hk_Service_Db::TYPE_INT,
            'gradeList'   => Hk_Service_Db::TYPE_STR,
            'subjectList' => Hk_Service_Db::TYPE_STR,
            'status'      => Hk_Service_Db::TYPE_INT,
            'deleted'     => Hk_Service_Db::TYPE_INT,
            'createTime'  => Hk_Service_Db::TYPE_INT,
            'updateTime'  => Hk_Service_Db::TYPE_INT,
            'extData'     => Hk_Service_Db::TYPE_JSON,
        );
    }
}