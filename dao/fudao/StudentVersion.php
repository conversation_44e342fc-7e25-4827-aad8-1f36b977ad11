<?php
/**************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @filename:      StudentVersion.php
 * @author:        <EMAIL>
 * @desc:          学生端版本 
 * @create:        2017-08-28 16:40:53
 * @last modified: 2017-08-28 16:40:53
 */
class Hkzb_Dao_Fudao_StudentVersion extends Hk_Common_BaseDao
{

    public static $arrFields = array(
        'id',
        'version',
        'versionCode',
        'official',
        'url',
        'msg',
        'userList',
        'deleted',
        'createTime',
        'updateTime',
        'operatorUid',
        'extData',
    );

    public function __construct()
    {
        $this->_dbName      = 'fudao/zyb_fudao';
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblStudentVersion";
        $this->arrFieldsMap = array(
            'id'          => 'id',
            'version'     => 'version',
            'versionCode' => 'version_code',
            'official'    => 'official',
            'url'         => 'url',
            'msg'         => 'msg',
            'userList'    => 'user_list',
            'deleted'     => 'deleted',
            'createTime'  => 'create_time',
            'updateTime'  => 'update_time',
            'operatorUid' => 'operator_uid',
            'extData'     => 'ext_data',
        );

        $this->arrTypesMap = array(
            'id'          => Hk_Service_Db::TYPE_INT,
            'version'     => Hk_Service_Db::TYPE_STR,
            'versionCode' => Hk_Service_Db::TYPE_INT,
            'official'    => Hk_Service_Db::TYPE_INT,
            'url'         => Hk_Service_Db::TYPE_STR,
            'msg'         => Hk_Service_Db::TYPE_STR,
            'userList'    => Hk_Service_Db::TYPE_JSON,
            'deleted'     => Hk_Service_Db::TYPE_INT,
            'createTime'  => Hk_Service_Db::TYPE_INT,
            'updateTime'  => Hk_Service_Db::TYPE_INT,
            'operatorUid' => Hk_Service_Db::TYPE_INT,
            'extData'     => Hk_Service_Db::TYPE_JSON,
        );
    }
}