<?php

/**
 * @file ExamPointStatis.php
 * <AUTHOR>
 * @date 2018-05-28
 * @brief 试卷-知识点维度的统计数据管理(堂堂测迁移，来自Hk_Dao_Practice_ExamPointStatis)
 *
 **/

class Hkzb_Dao_Fudao_Exam_ExamPointStatis extends Hk_Common_BaseDao {
    public function __construct() {
        $this->_dbName      = 'resource/fudao_teachresource';
        $this->_table       = "tblExamPointStatis";
        $this->_db          = null;
        $this->arrFieldsMap = array(
            'id'      => 'id',
            'examId'  => 'examId',
            'examType'=> 'examType',
            'pointId' => 'pointId',
            'total'   => 'total',
            'score'   => 'score',
            'num'     => 'num',
            'ext'     => 'ext',
        );

        $this->arrTypesMap = array(
            'id'      => Hk_Service_Db::TYPE_INT,    
            'examId'  => Hk_Service_Db::TYPE_INT,
            'examType'=> Hk_Service_Db::TYPE_INT,
            'pointId' => Hk_Service_Db::TYPE_INT, 
            'total'   => Hk_Service_Db::TYPE_INT, 
            'score'   => Hk_Service_Db::TYPE_INT, 
            'num'     => Hk_Service_Db::TYPE_INT, 
            'ext'     => Hk_Service_Db::TYPE_JSON,    
        );
    }


    /**
     * 用时连接db
     * @param 
     * @return true/false
     **/
    private function _aotuReConn() {
        if (empty($this->_db)) {
            $this->_db = Hk_Service_Db::getDB($this->_dbName);
        }
        return empty($this->_db)? false : true;
    }


    /**
     * 获取试卷-知识点维度的数据
     * @param integer $examId 
     * @param array   $pids     array(26,23,65[,...])
     * @return mix
     */
    public function getByExamPoints($examId, $pids) {
        if (!$this->_aotuReConn()) {
            return false;
        }
        if ( (0 >= $examId) || !is_array($pids) || empty($pids) ) {
            return false;
        }

        $arrFields = array('examId', 'total', 'pointId', 'score', 'num');

        //查询字段的转换
        $arrFields = Hk_Service_Db::mapField($arrFields, $this->arrFieldsMap, true);
        $strFields = implode(",", $arrFields);

        $strPids = array();
        foreach ($pids as $key => $val) {
            $strPids[] = intval($val);
        }
        $strPids = implode(",", $strPids);

        $sql = sprintf("SELECT %s FROM %s WHERE examId=%d AND pointId IN (%s) LIMIT 200", 
            $strFields,
            $this->_table,
            intval($examId),
            $strPids
        );

        $data = $this->query($sql);
        if (false === $data) {
            Bd_Log::warning("Error[dbError] Detail[db select failed, sql:{$sql}");
            return false;
        }
        return $data;
    }


    /**
     * 增加试卷-知识点维度的总分
     * @param array $paras  array(array('examId','pointId','score',['total']), array(...) )  total非必需
     * @return mix
     */
    public function incrForScore($paras) {
        if (!$this->_aotuReConn()) {
            return false;
        }
        if ( !is_array($paras) || empty($paras) ) {
            return false;
        }

        $strValues      = array();
        $strValuesTotal = array();
        foreach ($paras as $key => $val) {
            if ( (0 >= $val['examId']) || (0 > $val['pointId']) || !isset($val['score']) ) {
                return false;
            }
            if (isset($val['total'])) {
                $strValuesTotal[] = sprintf("%d,%d,%d,%d,%d", intval($val['examId']), intval($val['pointId']), intval($val['total']), intval($val['score']), 1);
            } else {
                $strValues[] = sprintf("%d,%d,%d,%d", intval($val['examId']), intval($val['pointId']), intval($val['score']), 1);
            }
        }

        if (!empty($strValuesTotal)) {
            $sql = sprintf("INSERT INTO %s(examId,pointId,total,score,num) VALUES(%s) ON DUPLICATE KEY UPDATE total=VALUES(total),score=score+VALUES(score),num=num+VALUES(num)", 
                $this->_table,
                implode("),(", $strValuesTotal)
            );
            $data = $this->query($sql);
            if (false === $data) {
                Bd_Log::warning("Error[dbError] Detail[db select failed, sql:{$sql}");
                return false;
            }
        } 
        if (!empty($strValues)) {
            $sql = sprintf("INSERT INTO %s(examId,pointId,score,num) VALUES(%s) ON DUPLICATE KEY UPDATE score=score+VALUES(score),num=num+VALUES(num)", 
                $this->_table,
                implode("),(", $strValues)
            );
            $data = $this->query($sql);
            if (false === $data) {
                Bd_Log::warning("Error[dbError] Detail[db select failed, sql:{$sql}");
                return false;
            }
        }

        return true;
    }
}