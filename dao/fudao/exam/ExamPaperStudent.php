<?php


/**
 * @file    ExamInfo.php
 * <AUTHOR>
 * @data    2017-09-06
 * @desc    考试系统
 *
 */
class Hkzb_Dao_Fudao_Exam_ExamPaperStudent extends Hk_Common_BaseMultiDao {

    public function __construct() {
        $this->_dbName      = 'resource/fudao_teachresource';
        $this->_db          = null;
        $this->_table       = "tblExamPaperStudent";
        $this->_tableName   = "tblExamPaperStudent";
        $this->_partionKey  = "exam_id";
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_partionType = self::TYPE_TABLE_PARTION_MOD;
        $this->_partionNum  = 20;
        $this->arrFieldsMap = array(
            'id'           => 'id',
            'examId'       => 'exam_id',
            'courseId'     => 'course_id',
            'lessonId'     => 'lesson_id',
            'studentUid'   => 'student_uid',
            'relationType' => 'relation_type',
            'status'       => 'status',
            'answerInfo'   => 'answer_info',
            'extData'      => 'ext_data',
            'createTime'   => 'create_time',
            'updateTime'   => 'update_time',
            'bestEnterTime'=> 'best_enter_time',
            'bestScore'    => 'best_score',
            'bestDuration' => 'best_duration'
        );

        $this->arrTypesMap = array(
            'id'           => Hk_Service_Db::TYPE_INT,
            'examId'       => Hk_Service_Db::TYPE_INT,
            'courseId'     => Hk_Service_Db::TYPE_INT,
            'lessonId'     => Hk_Service_Db::TYPE_INT,
            'studentUid'   => Hk_Service_Db::TYPE_INT,
            'relationType' => Hk_Service_Db::TYPE_INT,
            'status'       => Hk_Service_Db::TYPE_INT,
            'answerInfo'   => Hk_Service_Db::TYPE_JSON,
            'extData'      => Hk_Service_Db::TYPE_JSON,
            'createTime'   => Hk_Service_Db::TYPE_INT,
            'updateTime'   => Hk_Service_Db::TYPE_INT,
            'bestEnterTime'=> Hk_Service_Db::TYPE_INT,
            'bestScore'    => Hk_Service_Db::TYPE_INT,
            'bestDuration' => Hk_Service_Db::TYPE_INT
        );
    }

}