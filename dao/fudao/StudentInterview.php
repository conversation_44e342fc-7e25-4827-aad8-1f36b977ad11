<?php
/***************************************************************************
 *
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   StudentInterview.php
 * <AUTHOR>
 * @date   2015-11-26
 * @brief  学生访谈信息(辅导员维护)
 *
 **/
class Hkzb_Dao_Fudao_StudentInterview extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = 'fudao/zyb_fudao';
        //$this->_db          = Hk_Service_Db::getDB($this->_dbName);
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblStudentInterview";
        $this->arrFieldsMap = array(
            'id'            => 'id',
            'studentUid'    => 'student_uid',
            'interviewTime' => 'interview_time',
            'content'       => 'content',
            'deleted'       => 'deleted',
            'createTime'    => 'create_time',
            'updateTime'    => 'update_time',
            'operatorUid'   => 'operator_uid',
            'operator'      => 'operator',
            'extData'       => 'ext_data',
            'courseId'      => 'course_id',
            'channelType'   => 'channel_type',
        );

        $this->arrTypesMap = array(
            'id'            => Hk_Service_Db::TYPE_INT,
            'studentUid'    => Hk_Service_Db::TYPE_INT,
            'interviewTime' => Hk_Service_Db::TYPE_INT,
            'content'       => Hk_Service_Db::TYPE_STR,
            'deleted'       => Hk_Service_Db::TYPE_INT,
            'createTime'    => Hk_Service_Db::TYPE_INT,
            'updateTime'    => Hk_Service_Db::TYPE_INT,
            'operatorUid'   => Hk_Service_Db::TYPE_INT,
            'operator'      => Hk_Service_Db::TYPE_STR,
            'extData'       => Hk_Service_Db::TYPE_JSON,
            'courseId'      => Hk_Service_Db::TYPE_INT,
            'channelType'   => Hk_Service_Db::TYPE_INT,
        );
    }
}