<?php

/**
 * Created by PhpStorm.
 * User: x<PERSON><PERSON>qiang
 * Date: 20/03/2017
 * Time: 2:56 PM
 */
class Hkzb_Dao_Fudao_GuoyuanRtcpReceiverReport extends Hk_Common_BaseDao
{
    public static $allFields = array(
        'id', 'uid', 'roomId', 'streamName', 'streamType', 'ssrc', 'packetsLost', 'jitter',
        'createTime', 'bitPack', 'extPack'
    );

    public function __construct()
    {
        $this->_dbName      = 'gnmis/gnmis';
        $this->_db          = Hk_Service_Db::getDB($this->_dbName);
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblReceiverReport";
        $this->arrFieldsMap = array(
            'id'          => 'id',
            'uid'         => 'uid',
            'roomId'      => 'roomid',
            'streamName'  => 'stream_name',
            'streamType'  => 'stream_type',
            'ssrc'        => 'ssrc',
            'packetsLost' => 'packets_lost',
            'jitter'      => 'jitter',
            'createTime'  => 'create_time',
            'bitPack'     => 'bit_pack',
            'extPack'     => 'ext_pack',
        );

        $this->arrTypesMap = array(
            'id'          => Hk_Service_Db::TYPE_INT,
            'uid'         => Hk_Service_Db::TYPE_INT,
            'roomId'      => Hk_Service_Db::TYPE_STR,
            'streamName'  => Hk_Service_Db::TYPE_STR,
            'streamType'  => Hk_Service_Db::TYPE_INT,
            'ssrc'        => Hk_Service_Db::TYPE_INT,
            'packetsLost' => Hk_Service_Db::TYPE_INT,
            'jitter'      => Hk_Service_Db::TYPE_INT,
            'createTime'  => Hk_Service_Db::TYPE_INT,
            'bitPack'     => Hk_Service_Db::TYPE_INT,
            'extPack'     => Hk_Service_Db::TYPE_JSON,
        );
    }
}