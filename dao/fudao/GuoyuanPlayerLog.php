<?php

/**
 * @file   GuoyuanPlayerLog.php
 * <AUTHOR>
 * @date   2017-02-17
 * @brief  果园播放器日志
 *
 **/
class Hkzb_Dao_Fudao_GuoyuanPlayerLog extends Hk_Common_BaseDao
{
    public static $allFields = array(
        'id', 'uid', 'lessonId', 'ctime', 'action', 'os', 'extBit', 'extData'
    );

    public function __construct()
    {
        $this->_dbName = 'gnmis/gnmis';
        //$this->_db          = Hk_Service_Db::getDB($this->_dbName);
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblGuoyuanPlayerLog";
        $this->arrFieldsMap = array(
            'id'       => 'id',
            'uid'      => 'uid',
            'lessonId' => 'lesson_id',
            'ctime'    => 'ctime',
            'action'   => 'action',
            'os'       => 'os',
            'extBit'   => 'ext_bit',
            'extData'  => 'ext_data',
        );

        $this->arrTypesMap = array(
            'id'       => Hk_Service_Db::TYPE_INT,
            'uid'      => Hk_Service_Db::TYPE_INT,
            'lessonId' => Hk_Service_Db::TYPE_INT,
            'ctime'    => Hk_Service_Db::TYPE_INT,
            'action'   => Hk_Service_Db::TYPE_STR,
            'os'       => Hk_Service_Db::TYPE_STR,
            'extBit'   => Hk_Service_Db::TYPE_INT,
            'extData'  => Hk_Service_Db::TYPE_JSON,
        );
    }
}