<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file TeacherReview.php
 * <AUTHOR>
 * @date 2015/11/17 13:50:15
 * @brief 老师评价
 *  
 **/

class Hkzb_Dao_Fudao_TeacherReview extends Hk_Common_BaseDao
{
    public function __construct()
    {
//        $this->_dbName = 'fudao/zyb_fudao';
        $this->_dbName = 'jxzt_teacher/jxzt_teacher';
        //$this->_db     = Hk_Service_Db::getDB($this->_dbName);
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblTeacherReview";
        $this->arrFieldsMap = array(
            'id'         => 'id',
            'studentUid' => 'student_uid',
            'courseId'   => 'course_id',
            'lessonId'   => 'lesson_id',
            'teacherUid' => 'teacher_uid',
            'star'       => 'star',
            'tag'        => 'tag',
            'textTags'   => 'text_tags',
            'content'    => 'content',
            'status'     => 'status',
            'rank'       => 'rank',
            'reId'       => 're_id',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'contentCnt' => 'content_cnt',
            'extData'    => 'ext_data',
            'isGood'     => 'is_good',
            'contentMD5' => 'content_md5',
        );

        $this->arrTypesMap = array(
            'id'         => Hk_Service_Db::TYPE_INT,
            'studentUid' => Hk_Service_Db::TYPE_INT,
            'courseId'   => Hk_Service_Db::TYPE_INT,
            'lessonId'   => Hk_Service_Db::TYPE_INT,
            'teacherUid' => Hk_Service_Db::TYPE_INT,
            'star'       => Hk_Service_Db::TYPE_INT,
            'tag'        => Hk_Service_Db::TYPE_STR,
            'textTags'   => Hk_Service_Db::TYPE_JSON,
            'content'    => Hk_Service_Db::TYPE_STR,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'status'     => Hk_Service_Db::TYPE_INT,
            'rank'       => Hk_Service_Db::TYPE_INT,
            'reId'       => Hk_Service_Db::TYPE_INT,
            'contentCnt' => Hk_Service_Db::TYPE_INT,
            'extData'    => Hk_Service_Db::TYPE_JSON,
            'isGood'     => Hk_Service_Db::TYPE_INT,
            'contentMD5' => Hk_Service_Db::TYPE_STR,
        );
    }
}