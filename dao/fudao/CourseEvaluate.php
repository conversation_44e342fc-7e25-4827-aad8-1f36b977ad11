<?php
/**
 * Created by PhpStorm.
 * User: <EMAIL>
 * Date: 2018/02/26
 * Time: 11:37
 * 课程评价接口
 */
class Hkzb_Dao_Fudao_CourseEvaluate extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName      = 'fudao/zyb_fudao';
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblCourseEvaluate";
        $this->arrFieldsMap = array(
            'id'           => 'id',
            'uid'          => 'uid',
            'courseId'     => 'course_id',
            'content'      => 'content',
            'star'         => 'star',
            'itemId'       => 'item_id',
            'pullTime'     => 'pull_time',
            'deleted'      => 'deleted',
            'createTime'   => 'create_time',
            'updateTime'   => 'update_time',
            'extData'      => 'ext_data',
        );

        $this->arrTypesMap = array(
            'id'           => Hk_Service_Db::TYPE_INT,
            'uid'          => Hk_Service_Db::TYPE_INT,
            'star'         => Hk_Service_Db::TYPE_INT,
            'itemId'       => Hk_Service_Db::TYPE_INT,
            'pullTime'     => Hk_Service_Db::TYPE_INT,
            'courseId'     => Hk_Service_Db::TYPE_INT,
            'deleted'      => Hk_Service_Db::TYPE_INT,
            'content'      => Hk_Service_Db::TYPE_STR,
            'createTime'   => Hk_Service_Db::TYPE_INT,
            'updateTime'   => Hk_Service_Db::TYPE_INT,
            'extData'      => Hk_Service_Db::TYPE_JSON,
        );
    }
}