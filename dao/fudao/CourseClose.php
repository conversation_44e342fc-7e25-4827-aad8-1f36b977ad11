<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 zybang.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file CourseClose.php
 * <AUTHOR>
 * @date 2015/12/14 19:50:15
 * @brief 关课记录
 *  
 **/

class Hkzb_Dao_Fudao_CourseClose extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = 'fudao/zyb_fudao';
        //$this->_db     = Hk_Service_Db::getDB($this->_dbName);
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblCourseClose";
        $this->arrFieldsMap = array(
            'courseId'    => 'course_id',
            'grade'       => 'grade',
            'subject'     => 'subject',
            'inner'       => '`inner`',
            'reason'      => 'reason',
            'operatorUid' => 'operator_uid',
            'operator'    => 'operator',
            'createTime'  => 'create_time',
            'extData'     => 'ext_data',
        );

        $this->arrTypesMap = array(
            'courseId'    => Hk_Service_Db::TYPE_INT,
            'grade'       => Hk_Service_Db::TYPE_INT,
            'subject'     => Hk_Service_Db::TYPE_INT,
            'inner'       => Hk_Service_Db::TYPE_INT,
            'reason'      => Hk_Service_Db::TYPE_STR,
            'operatorUid' => Hk_Service_Db::TYPE_INT,
            'operator'    => Hk_Service_Db::TYPE_STR,
            'createTime'  => Hk_Service_Db::TYPE_INT,
            'extData'     => Hk_Service_Db::TYPE_JSON,
        );
    }
}