<?php
/***************************************************************************
 *
 * Copyright (c) 2015 zybang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file AssistantConfig.php
 * <AUTHOR>
 * @date 2017/08/10 18:59:39
 * @brief 辅导端配置
 *
 **/
class Hkzb_Dao_Fudao_AssistantConfig extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName      = 'fudao/zyb_fudao';
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = 'tblAssistantConfig';
        $this->arrFieldsMap = array(
            'id'          => 'id',
            'name'        => 'name',
            'data'        => 'data',
            'createTime'  => 'create_time',
            'updateTime'  => 'update_time',
            'operatorUid' => 'operator_uid',
            'operator'    => 'operator',
            'extData'     => 'ext_data',
        );

        $this->arrTypesMap = array(
            'id'          => Hk_Service_Db::TYPE_INT,
            'name'        => Hk_Service_Db::TYPE_STR,
            'data'        => Hk_Service_Db::TYPE_JSON,
            'createTime'  => Hk_Service_Db::TYPE_INT,
            'updateTime'  => Hk_Service_Db::TYPE_INT,
            'operatorUid' => Hk_Service_Db::TYPE_INT,
            'operator'    => Hk_Service_Db::TYPE_STR,
            'extData'     => Hk_Service_Db::TYPE_JSON,
        );
    }
}