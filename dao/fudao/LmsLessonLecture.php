<?php
/**
 * Created by PhpStorm.
 * User: <EMAIL>
 * Time: 2017/11/1 10:06
 */

class Hkzb_Dao_Fudao_LmsLessonLecture extends Hk_Common_BaseDao
{

    public function __construct()
    {
        $this->_dbName      = 'fudao/zyb_fudao';
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblLmsLessonLecture";
        $this->arrFieldsMap = array(
            'id'             => 'id',
            'lectureId'      => 'lecture_id',
            'status'         => 'status',
            'typeId'         => 'type_id',
            'type'           => 'type',
            'deleted'        => 'deleted',
            'lectureType'    => 'lecture_type',
            'researchStatus' => 'research_status',
            'scheduleStatus' => 'schedule_status',
            'extData'        => 'ext_data', 
            'subject'        => 'subject',
            'grade'          => 'grade',
            'lectureName'    => 'lecture_name',
            'courseType'     =>'course_type' ,
            'operatorType'   => 'operator_type',
            'operatorId'     => 'operator_id',
        );

        $this->arrTypesMap = array(
            'id'               => Hk_Service_Db::TYPE_INT,
            'lectureId'        => Hk_Service_Db::TYPE_INT,
            'typeId'           => Hk_Service_Db::TYPE_INT,
            'type'             => Hk_Service_Db::TYPE_INT,
            'deleted'          => Hk_Service_Db::TYPE_INT,
            'lectureType'      => Hk_Service_Db::TYPE_INT,
            'researchStatus'   => Hk_Service_Db::TYPE_INT,
            'scheduleStatus'   => Hk_Service_Db::TYPE_INT,
            'extData'          => Hk_Service_Db::TYPE_JSON,
            'subject'          => Hk_Service_Db::TYPE_INT,
            'grade'            => Hk_Service_Db::TYPE_INT,
            'lectureName'      => Hk_Service_Db::TYPE_STR,
            'courseType'       => Hk_Service_Db::TYPE_INT,
            'operatorType'     => Hk_Service_Db::TYPE_INT,
            'opreatorId'       => Hk_Service_Db::TYPE_INT,
        );
    }
}