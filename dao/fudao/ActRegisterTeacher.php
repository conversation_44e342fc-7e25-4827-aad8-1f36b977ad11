<?php
/**
 * Created by PhpStorm.
 * User: ya<PERSON><PERSON><PERSON>@zuoyebang.com
 * Date: 2017/8/30
 * Time: 14:30
 * desc：活动注册老师表
 */

class Hkzb_Dao_Fudao_ActRegisterTeacher extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName      = 'fudao/zyb_fudao';
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblActRegisterTeacher";
        $this->arrFieldsMap = array(
            'id'         => 'id',
            'userId'     => 'user_id',
            'userName'   => 'user_name',
            'userAvatar' => 'user_avatar',
            'age'        => 'age',
            'phone'      => 'phone',
            'email'      => 'email',
            'grade'      => 'grade',
            'province'   => 'province',
            'city'       => 'city',
            'school'     => 'school',
            'degree'     => 'degree',
            'subject'    => 'subject',
            'abstract'   => 'abstract',
            'deleted'    => 'deleted',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'extData'    => 'ext_data',

        );

        $this->arrTypesMap = array(
            'id'         => Hk_Service_Db::TYPE_INT,
            'userId'     => Hk_Service_Db::TYPE_INT,
            'userName'   => Hk_Service_Db::TYPE_STR,
            'userAvatar' => Hk_Service_Db::TYPE_STR,
            'age'        => Hk_Service_Db::TYPE_INT,
            'phone'      => Hk_Service_Db::TYPE_STR,
            'email'      => Hk_Service_Db::TYPE_STR,
            'grade'      => Hk_Service_Db::TYPE_STR,
            'province'   => Hk_Service_Db::TYPE_STR,
            'city'       => Hk_Service_Db::TYPE_STR,
            'school'     => Hk_Service_Db::TYPE_STR,
            'degree'     => Hk_Service_Db::TYPE_STR,
            'subject'    => Hk_Service_Db::TYPE_STR,
            'abstract'   => Hk_Service_Db::TYPE_STR,
            'deleted'    => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'extData'    => Hk_Service_Db::TYPE_JSON,
        );
    }
}
