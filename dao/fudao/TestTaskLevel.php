<?php
/**
 * Created by PhpStorm.
 * User: <EMAIL>
 * Date: 2018/12/10
 * Time: 16:03
 */

class Hkzb_Dao_Fudao_TestTaskLevel extends Hk_Common_BaseDao {

    public function __construct()
    {
        $this->_dbName      = 'fudao/zyb_fudao';
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblUserTaskLevel";
        $this->arrFieldsMap = array(
            'id'         => 'id',
            'uid'        => 'uid',
            'courseId'   => 'courseId',
            'level'      => 'level',
            'extData'    => 'ext_data',
            'createTime' => 'create_time',
            'updateTime' => 'update_time'
        );

        $this->arrTypesMap = array(
            'id'         => Hk_Service_Db::TYPE_INT,
            'uid'        => Hk_Service_Db::TYPE_INT,
            'courseId'   => Hk_Service_Db::TYPE_INT,
            'level'      => Hk_Service_Db::TYPE_INT,
            'extData'    => Hk_Service_Db::TYPE_JSON,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT
        );
    }

}