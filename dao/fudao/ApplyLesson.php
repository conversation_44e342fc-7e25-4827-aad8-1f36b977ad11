<?php
/***************************************************************************
 *
 * Copyright (c) 2015 zybang.com, Inc. All Rights Reserved
 *
 **************************************************************************/



/**
 * @file ApplyLesson.php
 * <AUTHOR>
 * @date 2015/12/14 19:50:15
 * @brief 申请章节
 *
 **/

class Hkzb_Dao_Fudao_ApplyLesson extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = 'fudao/zyb_fudao';
        //$this->_db     = Hk_Service_Db::getDB($this->_dbName);
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblApplyLesson";
        $this->arrFieldsMap = array(
            'id'            => 'id',
            'applyCourseId' => 'apply_course_id',
            'lessonId'      => 'lesson_id',
            'lessonName'    => 'lesson_name',
            'startTime'     => 'start_time',
            'stopTime'      => 'stop_time',
            'status'        => 'status',
            'deleted'       => 'deleted',
            'submitTime'    => 'submit_time',
            'createTime'    => 'create_time',
            'updateTime'    => 'update_time',
            'operatorUid'   => 'operator_uid',
            'operator'      => 'operator',
            'remark'        => 'remark',
            'extData'       => 'ext_data',
        );

        $this->arrTypesMap = array(
            'id'            => Hk_Service_Db::TYPE_INT,
            'applyCourseId' => Hk_Service_Db::TYPE_INT,
            'lessonName'    => Hk_Service_Db::TYPE_STR,
            'lessonId'      => Hk_Service_Db::TYPE_INT,
            'startTime'     => Hk_Service_Db::TYPE_INT,
            'stopTime'      => Hk_Service_Db::TYPE_INT,
            'status'        => Hk_Service_Db::TYPE_INT,
            'deleted'       => Hk_Service_Db::TYPE_INT,
            'submitTime'    => Hk_Service_Db::TYPE_INT,
            'createTime'    => Hk_Service_Db::TYPE_INT,
            'updateTime'    => Hk_Service_Db::TYPE_INT,
            'operatorUid'   => Hk_Service_Db::TYPE_INT,
            'operator'      => Hk_Service_Db::TYPE_STR,
            'remark'        => Hk_Service_Db::TYPE_JSON,
            'extData'       => Hk_Service_Db::TYPE_JSON,
        );
    }
}