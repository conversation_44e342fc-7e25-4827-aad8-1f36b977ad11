<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file ChangeCourse.php
 * <AUTHOR>
 * @date 2016/08/3 13:26:46
 * @brief 调课
 *  
 **/

class Hkzb_Dao_Fudao_ChangeCourse extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = 'fudao/zyb_fudao';
        //$this->_db     = Hk_Service_Db::getDB($this->_dbName);
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblChangeCourse";
        $this->arrFieldsMap = array(
            'id'         => 'id',
            'studentUid' => 'student_uid',
            'exCourse'   => 'ex_course',
            'nowCourse'  => 'now_course',
            'deleted'    => 'deleted',
            'operator'   => 'operator',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'extData'    => 'ext_data',
        );

        $this->arrTypesMap = array(
            'id'         => Hk_Service_Db::TYPE_INT,
            'studentUid' => Hk_Service_Db::TYPE_INT,
            'exCourse'   => Hk_Service_Db::TYPE_INT,
            'nowCourse'  => Hk_Service_Db::TYPE_INT,
            'deleted'    => Hk_Service_Db::TYPE_INT,
            'operator'   => Hk_Service_Db::TYPE_STR,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'extData'    => Hk_Service_Db::TYPE_JSON,
        );
    }
}