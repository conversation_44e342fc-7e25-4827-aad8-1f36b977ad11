<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file TeacherPersonalInfoList.php
 * <AUTHOR>
 * @date 2017-2-21
 *  
 **/

class Hkzb_Dao_Fudao_TeacherPersonalInfo extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName      = 'jxzt_teacher/jxzt_teacher';
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblTeacherPersonalInfo";
        $this->arrFieldsMap = array(
            'teacherUid'      => 'teacher_uid',
            'teacherRealName' => 'teacher_real_name',
            'phone'           => 'phone',
            'idNumber'        => 'id_number',
            'payChannel'      => 'pay_channel',
            'socialSecurity'  => 'social_security',
            'clsProStartTime' => 'cls_pro_start_time',
            'clsProStopTime'  => 'cls_pro_stop_time',
            'sbjProStartTime' => 'sbj_pro_start_time',
            'sbjProStopTime'  => 'sbj_pro_stop_time',
            'sbjProSalary'    => 'sbj_pro_salary',
            'clsProSalary'    => 'cls_pro_salary',
            'extData'         => 'ext_data',
        );

        $this->arrTypesMap = array(
            'teacherUid'      => Hk_Service_Db::TYPE_INT,
            'teacherRealName' => Hk_Service_Db::TYPE_STR,
            'phone'           => Hk_Service_Db::TYPE_STR,
            'idNumber'        => Hk_Service_Db::TYPE_STR,
            'sbjProSalary'    => Hk_Service_Db::TYPE_STR,
            'clsProSalary'    => Hk_Service_Db::TYPE_STR,
            'payChannel'      => Hk_Service_Db::TYPE_INT,
            'socialSecurity'  => Hk_Service_Db::TYPE_INT,
            'clsProStartTime' => Hk_Service_Db::TYPE_INT,
            'clsProStopTime'  => Hk_Service_Db::TYPE_INT,
            'sbjProStartTime' => Hk_Service_Db::TYPE_INT,
            'sbjProStopTime'  => Hk_Service_Db::TYPE_INT,
            'extData'         => Hk_Service_Db::TYPE_JSON,
        );
    }
}