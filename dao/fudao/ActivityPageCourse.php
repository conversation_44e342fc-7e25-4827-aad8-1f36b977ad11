<?php
/**
 * Created by PhpStorm.
 * User: ni<PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * Date: 2018/5/8
 * Time: 17:10
 * 新活动模块课程
 */

class Hkzb_Dao_Fudao_ActivityPageCourse extends Hk_Common_BaseDao
{

    public static $arrFields = array(
        'id',
        'actId',
        'courseId',
        'deleted',
        'createTime',
        'updateTime',
    );

    public function __construct()
    {
        $this->_dbName      = 'fudao/zyb_fudao';
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = 'tblActivityPageCourse';
        $this->arrFieldsMap = array(
            'id'         => 'id',
            'actId'      => 'act_id',
            'courseId'   => 'course_id',
            'deleted'    => 'deleted',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
        );

        $this->arrTypesMap = array(
            'id'         => Hk_Service_Db::TYPE_INT,
            'actId'      => Hk_Service_Db::TYPE_INT,
            'courseId'   => Hk_Service_Db::TYPE_INT,
            'deleted'    => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
        );
    }
}
