<?php
/**
 * File: StudentFeedback.php
 * User: lihongjie
 * Date: 2017/12/4
 * Time: 下午7:03
 * Desc: 学生反馈表
 */
class Hkzb_Dao_Fudao_StudentFeedback extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName      = 'fudao/zyb_fudao';
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = 'tblStudentFeedback';
        $this->arrFieldsMap = array(
            'id'            => 'id',
            'studentUid'    => 'student_uid',
            'content'       => 'content',
            'pictures'      => 'pictures',
            'type'          => 'type',
            'status'        => 'status',
            'source'        => 'source',
            'vc'            => 'vc',
            'vcname'        => 'vcname',
            'createTime'    => 'create_time',
            'updateTime'    => 'update_time',
            'extData'       => 'ext_data',
        );

        $this->arrTypesMap = array(
            'id'            => Hk_Service_Db::TYPE_INT,
            'studentUid'    => Hk_Service_Db::TYPE_INT,
            'content'       => Hk_Service_Db::TYPE_STR,
            'pictures'      => Hk_Service_Db::TYPE_JSON,
            'type'          => Hk_Service_Db::TYPE_INT,
            'status'        => Hk_Service_Db::TYPE_INT,
            'source'        => Hk_Service_Db::TYPE_STR,
            'vc'            => Hk_Service_Db::TYPE_INT,
            'vcname'        => Hk_Service_Db::TYPE_STR,
            'createTime'    => Hk_Service_Db::TYPE_INT,
            'updateTime'    => Hk_Service_Db::TYPE_INT,
            'extData'       => Hk_Service_Db::TYPE_JSON,
        );
    }
}