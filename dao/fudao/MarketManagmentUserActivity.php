<?php
/**
 * Created by PhpStorm.
 * User: zuoyebang
 * Date: 2017/12/8
 * Time: 20:30
 * 用户参与活动表
 */

class Hkzb_Dao_Fudao_MarketManagmentUserActivity extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName      = 'fudao/zyb_fudao';
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblMarketManagmentUserActivity";
        $this->arrFieldsMap = array(
            'id'           => 'id',
            'activityId'   => 'activity_id',
            'uid'          => 'uid',
            'status'       => 'status',
            'createTime'   => 'create_time',
            'updateTime'   => 'update_time',
            'goodsId'      => 'goods_id',
            'freeId'       => 'free_id',
            'ruleType'     => 'rule_type',
            'extData'      => 'ext_data',
        );

        $this->arrTypesMap = array(
            'id'           => Hk_Service_Db::TYPE_INT,
            'activityId'   => Hk_Service_Db::TYPE_INT,
            'uid'          => Hk_Service_Db::TYPE_INT,
            'status'       => Hk_Service_Db::TYPE_INT,
            'createTime'   => Hk_Service_Db::TYPE_INT,
            'updateTime'   => Hk_Service_Db::TYPE_INT,
            'goodsId'      => Hk_Service_Db::TYPE_STR,
            'freeId'       => Hk_Service_Db::TYPE_INT,
            'ruleType'     => Hk_Service_Db::TYPE_INT,
            'extData'      => Hk_Service_Db::TYPE_JSON,
        );
    }
}