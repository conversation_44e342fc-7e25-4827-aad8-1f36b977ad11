<?php


/**
 * @file    MissionRelation.php
 * <AUTHOR>
 * @data    2017-12-12
 * @desc    任务绑定关系
 *
 */
class Hkzb_Dao_Fudao_Mission_MissionInfo extends Hk_Common_BaseDao {

    public function __construct() {
        $this->_dbName      = 'resource/fudao_teachresource';
        $this->_db          = null;
        $this->_table       = "tblMissionInfo";
        $this->arrFieldsMap = array(
            'missionId'     => 'mission_id',
            'missionType'   => 'mission_type',
            'relationId'    => 'relation_id',
            'optionId'      => 'option_id',
            'missionStart'  => 'mission_start',
            'missionEnd'    => 'mission_end',
            'relationStatus'=> 'relation_status',
            'createTime'    => 'create_time',
            'updateTime'    => 'update_time',
            'operatorUid'   => 'operator_uid',
            'operator'      => 'operator',
            'deleted'       => 'deleted',

        );

        $this->arrTypesMap  = array(
            'missionId'     => Hk_Service_Db::TYPE_INT,
            'missionType'   => Hk_Service_Db::TYPE_INT,
            'relationId'    => Hk_Service_Db::TYPE_INT,
            'optionId'      => Hk_Service_Db::TYPE_INT,
            'missionStart'  => Hk_Service_Db::TYPE_INT,
            'missionEnd'    => Hk_Service_Db::TYPE_INT,
            'relationStatus'=> Hk_Service_Db::TYPE_INT,
            'createTime'    => Hk_Service_Db::TYPE_INT,
            'updateTime'    => Hk_Service_Db::TYPE_INT,
            'operatorUid'   => Hk_Service_Db::TYPE_INT,
            'operator'      => Hk_Service_Db::TYPE_STR,
            'deleted'       => Hk_Service_Db::TYPE_INT,
        );
    }

}