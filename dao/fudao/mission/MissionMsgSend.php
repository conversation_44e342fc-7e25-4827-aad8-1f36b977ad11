<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

/**
 * @file MissionMsgSend
 * <AUTHOR>
 * @date 2017-12-19 18:02:52
 * @brief 
 */
class Hkzb_Dao_Fudao_Mission_MissionMsgSend extends Hk_Common_BaseDao {

	public function __construct() {
		$this->_dbName = 'resource/fudao_teachresource';
		$this->_db = null;
		$this->_table = "tblMissionMsgSend";
		$this->arrFieldsMap = array(
			'msgId' => 'msg_id',
			'msgType' => 'msg_type',
			'studentUid' => 'student_uid',
			'courseId' => 'course_id',
			'lessonId' => 'lesson_id',
			'missionId' => 'mission_id',
			'msgSendStatus' => 'msg_send_status',
			'msgSendTime' => 'msg_send_time',
			'msgContent' => 'msg_content',
			'createTime' => 'create_time',
			'updateTime' => 'update_time',
			'operatorUid' => 'operator_uid',
			'operator' => 'operator',
			'deleted' => 'deleted',
		);

		$this->arrTypesMap = array(
			'msgId' => Hk_Service_Db::TYPE_INT,
			'msgType' => Hk_Service_Db::TYPE_INT,
			'studentUid' => Hk_Service_Db::TYPE_INT,
			'courseId' => Hk_Service_Db::TYPE_INT,
			'lessonId' => Hk_Service_Db::TYPE_INT,
			'missionId' => Hk_Service_Db::TYPE_INT,
			'msgSendStatus' => Hk_Service_Db::TYPE_INT,
			'msgSendTime' => Hk_Service_Db::TYPE_INT,
			'msgContent' => Hk_Service_Db::TYPE_STR,
			'createTime' => Hk_Service_Db::TYPE_INT,
			'updateTime' => Hk_Service_Db::TYPE_INT,
			'operatorUid' => Hk_Service_Db::TYPE_INT,
			'operator' => Hk_Service_Db::TYPE_STR,
			'deleted' => Hk_Service_Db::TYPE_INT,
		);
	}

}
