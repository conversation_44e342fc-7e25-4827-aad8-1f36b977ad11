<?php
/**************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @filename:      StudentWindowsLog.php
 * @author:        <EMAIL>
 * @desc:          学生端日志 
 * @create:        2017-08-31 14:40:53
 * @last modified: 2017-08-31 14:40:53
 */
class Hkzb_Dao_Fudao_StudentWindowsLog extends Hk_Common_BaseDao
{

    public static $arrFields = array(
        'id',
        'studentUid',
        'fileName',
        'clientIp',
        'createTime',
        'createDate',
        'extData',
    );

    public function __construct()
    {
        $this->_dbName      = 'fudao/zyb_fudao';
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblStudentWindowsLog";
        $this->arrFieldsMap = array(
            'id'         => 'id',
            'studentUid' => 'student_uid',
            'fileName'   => 'file_name',
            'clientIp'   => 'client_ip',
            'createTime' => 'create_time',
            'createDate' => 'create_date',
            'extData'    => 'ext_data',
        );

        $this->arrTypesMap = array(
            'id'         => Hk_Service_Db::TYPE_INT,
            'studentUid' => Hk_Service_Db::TYPE_INT,
            'fileName'   => Hk_Service_Db::TYPE_STR,
            'clientIp'   => Hk_Service_Db::TYPE_STR,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'createDate' => Hk_Service_Db::TYPE_INT,
            'extData'    => Hk_Service_Db::TYPE_JSON,
        );
    }
}