<?php
/***************************************************************************
 * 
 * Copyright (c) 2016 Zuoyebang.com, Inc. All Rights Reserved
 * 
 **************************************************************************/

/**
 * @file StudentStatistics.php
 * <AUTHOR>
 * @date 2017/03/14
 * @brief 学生统计数据
 *  
 **/

class Hkzb_Dao_Fudao_StudentStatistics extends Hk_Common_BaseMultiDao
{
    public function __construct()
    {
        $this->_dbName      = 'fudao/zyb_fudao';
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblStudentStatistics";
        $this->_tableName   = "tblStudentStatistics";
        $this->_partionKey  = "student_uid";
        $this->_partionType = self::TYPE_TABLE_PARTION_MOD;
        $this->_partionNum  = 20;
        $this->arrFieldsMap = array(
            'id'                 => 'id',
            'studentUid'         => 'student_uid',
            'phone'              => 'phone',
            'orderCnt'           => 'order_cnt',
            'orderPayCnt'        => 'order_pay_cnt',
            'totalPay'           => 'total_pay',
            'avgPay'             => 'avg_pay',
            'courseCnt'          => 'course_cnt',
            'lessonCnt'          => 'lesson_cnt',
            'courseTimeCnt'      => 'course_time_cnt',
            'courseStatistics'   => 'course_statistics',
            'exerciseStatistics' => 'exercise_statistics',
            'serviceStatistics'  => 'service_statistics',
            'version'            => 'version',
            'createTime'         => 'create_time',
            'updateTime'         => 'update_time',
            'extData'            => 'ext_data',
        );

        $this->arrTypesMap = array(
            'id'                 => Hk_Service_Db::TYPE_INT,
            'studentUid'         => Hk_Service_Db::TYPE_INT,
            'phone'              => Hk_Service_Db::TYPE_STR,
            'orderCnt'           => Hk_Service_Db::TYPE_INT,
            'orderPayCnt'        => Hk_Service_Db::TYPE_INT,
            'totalPay'           => Hk_Service_Db::TYPE_INT,
            'avgPay'             => Hk_Service_Db::TYPE_INT,
            'courseCnt'          => Hk_Service_Db::TYPE_INT,
            'lessonCnt'          => Hk_Service_Db::TYPE_INT,
            'courseTimeCnt'      => Hk_Service_Db::TYPE_INT,
            'courseStatistics'   => Hk_Service_Db::TYPE_JSON,
            'exerciseStatistics' => Hk_Service_Db::TYPE_JSON,
            'serviceStatistics'  => Hk_Service_Db::TYPE_JSON,
            'version'            => Hk_Service_Db::TYPE_INT,
            'createTime'         => Hk_Service_Db::TYPE_INT,
            'updateTime'         => Hk_Service_Db::TYPE_INT,
            'extData'            => Hk_Service_Db::TYPE_JSON,
        );
    }
}