<?php
/***************************************************************************
 *
 * Copyright (c) 2015 zybang.com, Inc. All Rights Reserved
 *
 **************************************************************************/



/**
 * @file ApplyLog.php
 * <AUTHOR>
 * @date 2015/12/14 19:50:15
 * @brief 申请日志
 *
 **/

class Hkzb_Dao_Fudao_ApplyExamineLog extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = 'fudao/zyb_fudao';
        //$this->_db     = Hk_Service_Db::getDB($this->_dbName);
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblApplyExamineLog";
        $this->arrFieldsMap = array(
            'id'          => 'id',
            'lessonId'    => 'lesson_id',
            'logType'     => 'log_type',
            'content'     => 'content',
            'createTime'  => 'create_time',
            'operatorUid' => 'operator_uid',
            'operator'    => 'operator',
            'extData'     => 'ext_data',
        );

        $this->arrTypesMap = array(
            'id'          => Hk_Service_Db::TYPE_INT,
            'lessonId'    => Hk_Service_Db::TYPE_INT,
            'content'     => Hk_Service_Db::TYPE_STR,
            'logType'     => Hk_Service_Db::TYPE_STR,
            'createTime'  => Hk_Service_Db::TYPE_INT,
            'operatorUid' => Hk_Service_Db::TYPE_INT,
            'operator'    => Hk_Service_Db::TYPE_STR,
            'extData'     => Hk_Service_Db::TYPE_JSON,
        );
    }
}