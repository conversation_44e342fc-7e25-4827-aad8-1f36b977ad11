<?php
/**
 * @file   GroupProduct.php
 * <AUTHOR>
 * @date   2017/12/25 15:22
 * @brief  简介
 */

class Hkzb_Dao_Fudao_Product_GroupProduct extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName  = 'fudao/zyb_fudao';
        $this->_db      = null;
        $this->_logFile = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table   = 'tblGroupProduct';
        $this->arrFieldsMap = array(
            'skuId'             => 'sku_id',
            'skuName'           => 'sku_name',
            'registerStartTime' => 'register_start_time',
            'registerStopTime'  => 'register_stop_time',
            'onlineStart'       => 'online_start',
            'onlineStop'        => 'online_stop',
            'price'             => 'price',
            'originPrice'       => 'origin_price',
            'studentCnt'        => 'student_cnt',
            'studentMaxCnt'     => 'student_max_cnt',
            'productStatus'     => 'product_status',
            'productInner'      => 'product_inner',
            'isShow'            => 'is_show',
            'createTime'        => 'create_time',
            'updateTime'        => 'update_time',
            'tags'              => 'tags',
            'operatorUid'       => 'operator_uid',
            'operator'          => 'operator',
            'deleted'           => 'deleted',
            'content'           => 'content',
            'resource'          => 'resource',
            'extData'           => 'ext_data',
        );

        $this->arrTypesMap = array(
            'skuId'             => Hk_Service_Db::TYPE_INT,
            'skuName'           => Hk_Service_Db::TYPE_STR,
            'registerStartTime' => Hk_Service_Db::TYPE_INT,
            'registerStopTime'  => Hk_Service_Db::TYPE_INT,
            'onlineStart'       => Hk_Service_Db::TYPE_INT,
            'onlineStop'        => Hk_Service_Db::TYPE_INT,
            'price'             => Hk_Service_Db::TYPE_INT,
            'originPrice'       => Hk_Service_Db::TYPE_INT,
            'studentCnt'        => Hk_Service_Db::TYPE_INT,
            'productStatus'     => Hk_Service_Db::TYPE_INT,
            'productInner'      => Hk_Service_Db::TYPE_INT,
            'isShow'            => Hk_Service_Db::TYPE_INT,
            'createTime'        => Hk_Service_Db::TYPE_INT,
            'updateTime'        => Hk_Service_Db::TYPE_INT,
            'tags'              => Hk_Service_Db::TYPE_JSON,
            'operatorUid'       => Hk_Service_Db::TYPE_INT,
            'operator'          => Hk_Service_Db::TYPE_STR,
            'deleted'           => Hk_Service_Db::TYPE_INT,
            'content'           => Hk_Service_Db::TYPE_JSON,
            'resource'          => Hk_Service_Db::TYPE_JSON,
            'extData'           => Hk_Service_Db::TYPE_JSON,
        );
    }
}
