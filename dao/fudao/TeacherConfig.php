<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 zybang.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file TeacherConfig.php
 * <AUTHOR>
 * @date 2015/12/14 19:50:15
 * @brief 老师配置
 *  
 **/

class Hkzb_Dao_Fudao_TeacherConfig extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName      = 'jxzt_teacher/jxzt_teacher';
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblTeacherConfig";
        $this->arrFieldsMap = array(
            'id'          => 'id',
            'title'       => 'title',
            'name'        => 'name',
            'data'        => 'data',
            'createTime'  => 'create_time',
            'updateTime'  => 'update_time',
            'operatorUid' => 'operator_uid',
            'operator'    => 'operator',
            'extData'     => 'ext_data',
        );

        $this->arrTypesMap = array(
            'id'          => Hk_Service_Db::TYPE_INT,
            'title'       => Hk_Service_Db::TYPE_STR,
            'name'        => Hk_Service_Db::TYPE_STR,
            'data'        => Hk_Service_Db::TYPE_JSON,
            'createTime'  => Hk_Service_Db::TYPE_INT,
            'updateTime'  => Hk_Service_Db::TYPE_INT,
            'operatorUid' => Hk_Service_Db::TYPE_INT,
            'operator'    => Hk_Service_Db::TYPE_STR,
            'extData'     => Hk_Service_Db::TYPE_JSON,
        );
    }
}