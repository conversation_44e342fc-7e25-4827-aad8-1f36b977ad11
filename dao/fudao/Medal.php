<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file Medal.php
 * <AUTHOR>
 * @date 2015/11/17 13:26:46
 * @brief 勋章
 *  
 **/

class Hkzb_Dao_Fudao_Medal extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = 'fudao/zyb_fudao';
        //$this->_db     = Hk_Service_Db::getDB($this->_dbName);
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblMedal";
        $this->arrFieldsMap = array(
            'medalId'     => 'medal_id',
            'medalName'   => 'medal_name',
            'medalUrl'    => 'medal_url',
            'medalNum'    => 'medal_num',
            'status'      => 'status',
            'createTime'  => 'create_time',
            'updateTime'  => 'update_time',
            'operatorUid' => 'operator_uid',
            'operator'    => 'operator',
            'extData'     => 'ext_data',
        );

        $this->arrTypesMap = array(
            'medalId'     => Hk_Service_Db::TYPE_INT,
            'medalName'   => Hk_Service_Db::TYPE_STR,
            'medalUrl'    => Hk_Service_Db::TYPE_STR,
            'medalNum'    => Hk_Service_Db::TYPE_INT,
            'status'      => Hk_Service_Db::TYPE_INT,
            'createTime'  => Hk_Service_Db::TYPE_INT,
            'updateTime'  => Hk_Service_Db::TYPE_INT,
            'operatorUid' => Hk_Service_Db::TYPE_INT,
            'operator'    => Hk_Service_Db::TYPE_STR,
            'extData'     => Hk_Service_Db::TYPE_JSON,
        );
    }
}