<?php
/**
 * @file    IMUserEmotion.php
 * <AUTHOR>
 * @date    2017-12-08
 * @brief   用户表情包关系
 */
class Hkzb_Dao_Fudao_IMUserEmotion extends Hk_Common_BaseDao {
    public function __construct()
    {
        $this->_dbName = 'fudao/zyb_fudao';
        $this->_db = null;
        $this->_table = 'tblIMUserEmotion';
        $this->arrFieldsMap = array(
            'id'          => 'id',
            'uid'         => 'uid',
            'emotionId'  => 'emotion_id',
            'deleted'    => 'deleted',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'extData'    => 'ext_data',
        );
        $this->arrTypesMap = array(
            'id'          => Hk_Service_Db::TYPE_INT,
            'uid'         => Hk_Service_Db::TYPE_INT,
            'emotionId'  => Hk_Service_Db::TYPE_INT,
            'deleted'    => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'extData'    => Hk_Service_Db::TYPE_JSON,
        );
    }
}