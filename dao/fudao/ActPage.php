<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file ActPage.php
 * <AUTHOR>
 * @date 2016/07/27 13:26:46
 * @brief 讲义
 *  
 **/

class Hkzb_Dao_Fudao_ActPage extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = 'fudao/zyb_fudao';
        //$this->_db     = Hk_Service_Db::getDB($this->_dbName);
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblActPage";
        $this->arrFieldsMap = array(
            'id'         => 'id',
            'name'       => 'name',
            'actLog'     => 'act_log',
            'content'    => 'content',
            'deleted'    => 'deleted',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'extData'    => 'ext_data',
        );

        $this->arrTypesMap = array(
            'id'         => Hk_Service_Db::TYPE_INT,
            'name'       => Hk_Service_Db::TYPE_STR,
            'actLog'     => Hk_Service_Db::TYPE_STR,
            'content'    => Hk_Service_Db::TYPE_STR,
            'deleted'    => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'extData'    => Hk_Service_Db::TYPE_JSON,
        );
    }
}