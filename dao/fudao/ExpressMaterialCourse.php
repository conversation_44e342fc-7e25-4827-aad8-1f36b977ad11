<?php
/**
 * @file ExpressMaterialCourse.php
 * <AUTHOR>
 * @date 2018-01-16
 * @brief 快递赠品教材映射表
 *
 **/

class Hkzb_Dao_Fudao_ExpressMaterialCourse extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName      = 'fudao/zyb_fudao';
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblExpressMaterialCourse";
        
        $this->arrFieldsMap = array(
            'id'                   => 'id',
            'courseId'             => 'course_id',
            'materialId'           => 'material_id',
            'deleted'              => 'deleted',
            'presentName'          => 'present_name',
            'createTime'           => 'create_time',
            'updateTime'           => 'update_time',
            'extData'              => 'ext_data',
        );
        
        $this->arrTypesMap = array(
            'id'                    => Hk_Service_Db::TYPE_INT,
            'courseId'              => Hk_Service_Db::TYPE_INT,
            'materialId'            => Hk_Service_Db::TYPE_INT,
            'deleted'               => Hk_Service_Db::TYPE_INT,
            'presentName'           => Hk_Service_Db::TYPE_STR,
            'createTime'            => Hk_Service_Db::TYPE_INT,
            'updateTime'            => Hk_Service_Db::TYPE_INT,
            'extData'               => Hk_Service_Db::TYPE_JSON,
        );
    }
}