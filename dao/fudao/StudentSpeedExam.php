<?php
/**
 * Created by PhpStorm.
 * User: zhangxiao
 * Date: 17/4/21
 * Time: 下午7:50
 */
class Hkzb_Dao_Fudao_StudentSpeedExam extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName      = 'fudao/zyb_fudao';
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblStudentSpeedExam";
        $this->arrFieldsMap = array(
            'id'         => 'id',
            'lessonId'   => 'lesson_id',
            'examId'     => 'exam_id',
            'studentUid' => 'student_uid',
            'rightCnt'   => 'right_cnt',
            'allCnt'     => 'all_cnt',
            'answerList' => 'answer_list',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'extData'    => 'ext_data',
        );

        $this->arrTypesMap = array(
            'examId'     => Hk_Service_Db::TYPE_INT,
            'lessonId'   => Hk_Service_Db::TYPE_INT,
            'studentUid' => Hk_Service_Db::TYPE_INT,
            'rightCnt'   => Hk_Service_Db::TYPE_INT,
            'allCnt'     => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'answerList' => Hk_Service_Db::TYPE_JSON,
            'extData'    => Hk_Service_Db::TYPE_JSON,
        );
    }
}