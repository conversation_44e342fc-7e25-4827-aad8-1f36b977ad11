<?php

/**
 * @desc    学校客户
 * @file    BdlSchoolCustomer.php
 * <AUTHOR>
 * @date    2021-04-23 11:18
 */
class Lxjxlib_Dao_Crm_BdlSchoolCustomer extends Lxjxlib_Dao_EsBaseDao
{
    public function __construct()
    {
        parent::__construct();

        $this->index        = "zyb_lxjx_crm_schoolcustomer";
        $this->arrFieldsMap = [
            'id'               => 'id',
            'code'             => 'code',
            'schoolId'         => 'school_id',
            'schoolName'       => 'school_name',
            'simpleName'       => 'simple_name',
            'namePya'          => 'name_pya',
            'schoolNature'     => 'school_nature',
            'schoolLevel'      => 'school_level',
            'eduPhase'         => 'educate_phase',
            'eduStage'         => 'educate_stage',
            'schoolScale'      => 'school_scale',
            'provinceId'       => 'province_id',
            'provinceName'     => 'province_name',
            'cityId'           => 'city_id',
            'cityName'         => 'city_name',
            'countyId'         => 'county_id',
            'countyName'       => 'county_name',
            'address'          => 'address',
            'longitude'        => 'longitude',
            'latitude'         => 'latitude',
            'dormitoryAmount'  => 'dormitory_amount',
            'dormitoryRatio'   => 'dormitory_ratio',
            'courseStatus'     => 'course_status',
            'courseStandard'   => 'course_standard',
            'isIntroduce'      => 'is_introduce',
            'claimantUid'      => 'claimant_uid',
            'claimantName'     => 'claimant_name',
            'claimantDepartId' => 'claimant_depart_id',
            'claimStatus'      => 'claim_status',
            'creatorUid'       => 'creator_uid',
            'creatorName'      => 'creator_name',
            'examDates'        => 'exam_dates',
            'status'           => 'status',
            'createTime'       => 'create_time',
            'updateTime'       => 'update_time',
            'correct'          => 'correct',
        ];
        $this->arrTypesMap  = [
            'id'               => Lxjxlib_Dao_EsBaseDao::TYPE_INT,
            'code'             => Lxjxlib_Dao_EsBaseDao::TYPE_STR,
            'schoolId'         => Lxjxlib_Dao_EsBaseDao::TYPE_INT,
            'schoolName'       => Lxjxlib_Dao_EsBaseDao::TYPE_STR,
            'simpleName'       => Lxjxlib_Dao_EsBaseDao::TYPE_STR,
            'namePya'          => Lxjxlib_Dao_EsBaseDao::TYPE_STR,
            'schoolNature'     => Lxjxlib_Dao_EsBaseDao::TYPE_INT,
            'schoolLevel'      => Lxjxlib_Dao_EsBaseDao::TYPE_INT,
            'eduPhase'         => Lxjxlib_Dao_EsBaseDao::TYPE_INT,
            'eduStage'         => Lxjxlib_Dao_EsBaseDao::TYPE_INT,
            'schoolScale'      => Lxjxlib_Dao_EsBaseDao::TYPE_INT,
            'provinceId'       => Lxjxlib_Dao_EsBaseDao::TYPE_INT,
            'provinceName'     => Lxjxlib_Dao_EsBaseDao::TYPE_STR,
            'cityId'           => Lxjxlib_Dao_EsBaseDao::TYPE_INT,
            'cityName'         => Lxjxlib_Dao_EsBaseDao::TYPE_STR,
            'countyId'         => Lxjxlib_Dao_EsBaseDao::TYPE_INT,
            'countyName'       => Lxjxlib_Dao_EsBaseDao::TYPE_STR,
            'address'          => Lxjxlib_Dao_EsBaseDao::TYPE_STR,
            'dormitoryAmount'  => Lxjxlib_Dao_EsBaseDao::TYPE_INT,
            'dormitoryRatio'   => Lxjxlib_Dao_EsBaseDao::TYPE_INT,
            'courseStatus'     => Lxjxlib_Dao_EsBaseDao::TYPE_INT,
            'courseStandard'   => Lxjxlib_Dao_EsBaseDao::TYPE_INT,
            'isIntroduce'      => Lxjxlib_Dao_EsBaseDao::TYPE_INT,
            'claimantUid'      => Lxjxlib_Dao_EsBaseDao::TYPE_INT,
            'claimantName'     => Lxjxlib_Dao_EsBaseDao::TYPE_STR,
            'claimantDepartId' => Lxjxlib_Dao_EsBaseDao::TYPE_INT,
            'claimStatus'      => Lxjxlib_Dao_EsBaseDao::TYPE_INT,
            'creatorUid'       => Lxjxlib_Dao_EsBaseDao::TYPE_INT,
            'creatorName'      => Lxjxlib_Dao_EsBaseDao::TYPE_STR,
            'examDates'        => Lxjxlib_Dao_EsBaseDao::TYPE_JSON,
            'status'           => Lxjxlib_Dao_EsBaseDao::TYPE_INT,
            'createTime'       => Lxjxlib_Dao_EsBaseDao::TYPE_INT,
            'updateTime'       => Lxjxlib_Dao_EsBaseDao::TYPE_INT,
            'correct'          => Lxjxlib_Dao_EsBaseDao::TYPE_JSON,
        ];
    }

    public function getFieldsMap()
    {
        return $this->arrFieldsMap;
    }
}