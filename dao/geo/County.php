<?php

/**
 * @befie   区
 * @file    dao/geo/CrmGeoCounty.php
 * <AUTHOR>
 * @version 1.0
 * @date    2019-10-18
 */
class Lxjxlib_Dao_Geo_County extends Lxjxlib_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName  = Lxjxlib_Const_Db::DB_LXJX_LAXINJINXIAO;
        $this->_logFile = Lxjxlib_Const_Db::DBLOG_LXJX;
        $this->_db      = null;
        $this->_table   = 'tblCrmGeoCounty';

        $this->arrFieldsMap = [
            'id'                   => 'id',
            'countyId'             => 'county_id',
            'countyName'           => 'county_name',
            'countyNamePya'        => 'county_name_pya',
            'cityId'               => 'city_id',
            'cityName'             => 'city_name',
            'cityLevel'            => 'city_level',
            'cityLevelName'        => 'city_level_name',
            'provinceId'           => 'province_id',
            'provinceName'         => 'province_name',
            'simplifyProvinceName' => 'simplify_province_name',
            'regionId'             => 'region_id',
            'regionName'           => 'region_name',
            'sourceType'           => 'source_type',
            'opUid'                => 'op_uid',
            'opName'               => 'op_name',
            'status'               => 'status',
            'updateTime'           => 'update_time',
        ];

        $this->arrTypesMap = [
            'id'                   => Hk_Service_Db::TYPE_INT,
            'countyId'             => Hk_Service_Db::TYPE_INT,
            'countyName'           => Hk_Service_Db::TYPE_STR,
            'countyNamePya'        => Hk_Service_Db::TYPE_STR,
            'cityId'               => Hk_Service_Db::TYPE_INT,
            'cityName'             => Hk_Service_Db::TYPE_STR,
            'cityLevel'            => Hk_Service_Db::TYPE_INT,
            'cityLevelName'        => Hk_Service_Db::TYPE_STR,
            'provinceId'           => Hk_Service_Db::TYPE_INT,
            'provinceName'         => Hk_Service_Db::TYPE_STR,
            'simplifyProvinceName' => Hk_Service_Db::TYPE_STR,
            'regionId'             => Hk_Service_Db::TYPE_INT,
            'regionName'           => Hk_Service_Db::TYPE_STR,
            'sourceType'           => Hk_Service_Db::TYPE_INT,
            'opUid'                => Hk_Service_Db::TYPE_INT,
            'opName'               => Hk_Service_Db::TYPE_STR,
            'status'               => Hk_Service_Db::TYPE_INT,
            'updateTime'           => Hk_Service_Db::TYPE_INT,
        ];

        $this->_fields = array_keys($this->arrFieldsMap);
    }

    /***
     * 获取城市下的区县
     * @param $provinceId
     * @return array|false
     */
    public function getCountyList($cityId)
    {
        $arrFields = [
            'provinceId',
            'provinceName',
            'cityId',
            'cityName',
            'countyId',
            'countyName',
            'countyNamePya',
        ];

        if (in_array($cityId, Lxjxlib_Const_Geo::$DIRECT_CITY_ID)) {
            $arrConds = [
                'provinceId' => $cityId,
            ];
        } else {
            $arrConds = [
                'cityId' => $cityId,
            ];
        }

        $arrConds['status'] = Lxjxlib_Const_Geo::COUNTY_STATUS_ENABLED;

        return $this->getListByConds($arrConds, $arrFields);
    }

    /***
     * 获取省份下的区县（批量）
     * @param array $provinceIds
     * @return array|false
     */
    public function getCountyListByProvinceIds($provinceIds)
    {
        $arrFields = [
            'provinceId',
            'provinceName',
            'cityId',
            'cityName',
            'countyId',
            'countyName',
        ];

        $arrConds = [
            'province_id in (' . join(',', $provinceIds) . ')',
            'status' => Lxjxlib_Const_Geo::COUNTY_STATUS_ENABLED,
        ];

        return $this->getListByConds($arrConds, $arrFields);
    }

    public function getCounty($countyId, $status)
    {
        $arrConds = [
            'countyId' => $countyId,
        ];

        if ($status) {
            $arrConds['status'] = $status;
        }

        return $this->getRecordByConds($arrConds, $this->getAllFields());
    }

    public function getSampleCountyByCity($cityId, $status)
    {
        $arrConds = [
            'cityId' => $cityId,
        ];

        if ($status) {
            $arrConds['status'] = $status;
        }

        $appends = [
            'limit 1',
        ];

        return $this->getRecordByConds($arrConds, $this->getAllFields(), null, $appends);
    }

    public function getCountyByIds($countyIds)
    {
        $arrConds = [
            'county_id in(' . implode(',', $countyIds) . ')',
        ];

        return $this->getListByConds($arrConds, $this->_fields);
    }

    public function getFields()
    {
        return $this->_fields;
    }
}