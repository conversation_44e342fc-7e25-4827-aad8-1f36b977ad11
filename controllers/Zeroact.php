<?php

/**
 * 0元课推广活动相关
 * @file    controllers/Zeroact.php
 **/
class Controller_Zeroact extends Ap_Controller_Abstract
{
    public $actions = [
        // 通用，后续不应有"通用"接口，后续再优化

        // B端
        'mylist'             => 'actions/zeroact/MyList.php',           // 获取BD的已绑定的0元课活动列表
        'myoptionallist'     => 'actions/zeroact/MyOptionalList.php',   // 获取BD的可绑定的0元课活动列表，后续版本废弃
        'bind'               => 'actions/zeroact/Bind.php',             // BD绑定0元课活动
        'unbind'             => 'actions/zeroact/UnBind.php',           // BD解绑0元课活动
        'searchsuggestion'   => 'actions/zeroact/SearchSuggestion.php', // “0元课名称”输入框-获取0元课活动可选列表
        // 直接从MyList就能获取到链接了，故不再需要使用
        // 'getlink'            => 'actions/zeroact/GetLink.php',          // 获取0元课活动的推广和海报链接

        // 后台

        // 内部svc

    ];
}
