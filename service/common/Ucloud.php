<?php
/**
 * @file Ucloud.php
 * <AUTHOR>
 * @version 1.0
 * @date 2021/5/26
 */

class Lxjxlib_Service_Common_Ucloud
{
    protected $serUcloud;

    public function __construct()
    {
        $this->serUcloud = new Hk_Service_Ucloud();
    }

    /**
     * 通过手机号获取passport的uid
     * @param $phoneArr
     * @return array|false|mixed
     * 注：单个手机号查询，直接返回uid；多个手机号查询 返回 "手机号 => uid"形式的数组
     */
    public function getUidByPhone($phoneArr)
    {
        if (!is_array($phoneArr) || empty($phoneArr)) {
            return [];
        }
        if (count($phoneArr) > 10) {
            return $this->getUserUidMulti($phoneArr);
        } else {
            return $this->getUserUid($phoneArr);
        }
    }

    /**
     * 通过手机号批量获取passport的uid  (手机号 > 10)
     * @param $phoneArr
     * @return array
     */
    private function getUserUidMulti($phoneArr)
    {
        $data       = [];
        $phoneChunk = array_chunk($phoneArr, 10);
        $params     = [];
        foreach ($phoneChunk as $v) {
            $params[] = [
                "val"  => implode(',', $v),
                "type" => "phone",
            ];
        }
        //批量获取uid
        $paramsChunk = array_chunk($params, 30);  //ral并发上限30
        foreach ($paramsChunk as $v) {
            $ret = $this->serUcloud->getUserUidMulti($v);
            if (empty($ret)) {
                continue;
            }
            foreach ($ret as $r) {
                if ($r['errno'] == Lxjxlib_Const_ExceptionCodes::SUCCESS && !empty($r['data'])) {
                    $data += $r['data'];
                }
            }
        }

        return $data;
    }

    /*
     * 通过手机号批量获取passport的uid  (手机号 <= 10)
     * @param $phoneArr
     * @return array
     */
    private function getUserUid($phoneArr)
    {
        $data = [];
        $val  = implode(',', $phoneArr);
        $ret  = $this->serUcloud->getUserUid($val);
        if (empty($ret)) {
            return $data;
        }

        return $ret;
    }
}
