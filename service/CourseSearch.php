<?php
/**************************************************************************
 * Copyright (c) 2019 Zuoyebang.com, Inc. All Rights Reserved
 * @file    CourseSearch.php
 * @date    2020-08-13
 **************************************************************************/

class Lxjxlib_Service_CourseSearch
{
    // 数据类型--课程
    const DOC_TYPE_COURSE = 2;

    // 数据类型--章节
    const DOC_TYPE_LESSON = 4;

    const SERVER     = 'moat';
    const URI        = '/coursesearch/api/query';
    const APP_KEY    = 'zyb-ol';
    const APP_SECRET = 'd06b6adaa9f5c2a72f65247b4042b91ccee667de';


    /**
     * 获取参数
     * @param $strAppkey
     * @param $strAppSecret
     * @param $arrParams
     * @return mixed
     */
    public static function getMoatOpt($strAppkey, $strAppSecret, $arrParams)
    {
        $arrParams["appkey"] = $strAppkey;
        ksort($arrParams);
        $arrSignParams = [];

        foreach ($arrParams as $strKey => $mixValue) {
            if (is_string($mixValue)) {
                $arrSignParams[] = $strKey . $mixValue;
            } else if (is_array($mixValue)) {
                $arrSignParams[] = $strKey . json_encode($mixValue, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
            } else {
                $arrSignParams[] = $strKey . (string)$mixValue;
            }
        }

        $strEnc            = $strAppkey . implode('', $arrSignParams) . $strAppSecret;
        $strSign           = strtolower(md5($strEnc));
        $arrParams["sign"] = $strSign;
        return $arrParams;
    }

    /**
     * @function        searchCourse
     * @access          public
     * @date            2020-08-13
     * @desc            查询课程信息
     * @param           $courseId
     * @return          array
     * @throws          Hk_Util_Exception
     */
    public static function searchCourse($courseId)
    {
        $ret = [];

        // 获取课程信息
        $aggs[]   = [
            'type'  => 0,
            'conds' => [
                'key'   => 'courseId',
                'value' => $courseId,
                'exps'  => 'eq',
            ],
        ];
        $arrConds = ['op' => 'and', 'aggs' => $aggs];

        $arrParams = [
            'arrFields' => ['*'],
            'arrConds'  => $arrConds,
            'docType'   => self::DOC_TYPE_COURSE,
            'orderBy'   => ['courseId' => 'desc'],
            'pn'        => 0,
            'rn'        => 1,
        ];

        $arrParams = self::getMoatOpt(self::APP_KEY, self::APP_SECRET, $arrParams);

        $ret = self::doRequest(self::SERVER, self::URI, $arrParams);

        if ($ret !== false) {
            return $ret['list'][0];
        } else {
            return false;
        }

    }

    /**
     * @function        searchLessons
     * @access          public
     * @date            2020-08-13
     * @desc            查询章节信息
     * @param           $lessonIds
     * @return          array
     * @throws          Hk_Util_Exception
     */
    public static function searchLessons($lessonIds)
    {
        // 获取章节信息
        $aggs[] = [
            'type'  => 0,
            'conds' => [
                'key'   => 'lessonId',
                'value' => $lessonIds,
                'exps'  => 'in',
            ],
        ];

        $arrConds = ['op' => 'and', 'aggs' => $aggs];

        $arrParams = [
            'arrFields' => ['*'],
            'arrConds'  => $arrConds,
            'docType'   => self::DOC_TYPE_LESSON,
            'orderBy'   => ['lessonId' => 'desc'],
            'pn'        => 0,
            'rn'        => count($lessonIds),
        ];

        $arrParams = self::getMoatOpt(self::APP_KEY, self::APP_SECRET, $arrParams);

        $ret = self::doRequest(self::SERVER, self::URI, $arrParams);

        if ($ret !== false) {
            return $ret['list'];
        } else {
            return false;
        }

    }

    private static function doRequest($server, $pathInfo, $arrParams)
    {
        $ret = Lxjxlib_Util_RalClient::ralPost($server, $pathInfo, $arrParams);

        if (!$ret) {
            return false;
        }

        if ($ret['errNo'] != 0) {
            Bd_Log::warning('moat server ' . $pathInfo . 'error' . json_encode($arrParams) . 'errstr = ' . $ret['errstr']);
            return false;
        }

        return $ret['data'];
    }


}