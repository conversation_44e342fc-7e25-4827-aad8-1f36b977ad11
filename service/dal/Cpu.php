<?php

/**
 * @file   Cpu.php
 * <AUTHOR>
 * @date   2019/1/4
 * @brief
 */

class Zb_Service_Dal_Cpu {

    private static $service     = 'zbcore_dal';
    private static $serviceUri  = '/zbcore/api/api';
    private static $module      = 'dal';
    private static $entity      = 'cpu';


    /**
     * 获取cpu信息
     * @param array $cpuIds eg. array(201801, 201802, 201803)
     * @param array $cpuFields eg. array('cpuId', 'cpuName', 'courseIds')
     * @param array $outlineFields eg. array('outlineName')
     * @param array $options eg. array()
     * @return array
     */
    public static function getKVByCpuId(array $cpuIds, array $cpuFields, array $outlineFields = array(), array $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getKV');
        $arrParams = array(
            'cpuIds' => $cpuIds,
            'cpuFields' => $cpuFields,
            'outlineFields' => $outlineFields,
        );

        if (!empty($options['allFields'])) {
            $arrParams['allFields'] = 1;
        }

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }


}
