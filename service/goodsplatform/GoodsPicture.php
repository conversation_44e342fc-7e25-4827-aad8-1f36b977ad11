<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   GoodsField.php
 * <AUTHOR> (<EMAIL>)
 * @date   2019年2月11日18:57:56
 * @brief
 **/


class Zb_Service_Goodsplatform_GoodsPicture{
    private static $service = 'newgoodsplatform';
    public static function getModelInfoByModelId($modelId = 0) {
        $serviceUri = '/newgoodsplatform/goodspicture/getmodelinfobymodelid';
        $arrHeader = Zb_Util_ZbServiceTools::getHeaders($serviceUri);
        $arrParams = array(
            'modelId' => $modelId,
        );
        if (!empty($options['isRalMulti'])) {
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    public static function getGoodsPictureInfoBySkuId($skuId = 0, $useCache = 1) {
        $serviceUri = '/newgoodsplatform/goodspicture/getgoodspictureinfobyskuid';
        $arrHeader = Zb_Util_ZbServiceTools::getHeaders($serviceUri);
        $arrParams = array(
            'skuId' => $skuId,
            'useCache' => $useCache,
        );
        if (!empty($options['isRalMulti'])) {
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }
    public static function getGoodsPictureInfoBySpuId($spuId = 0)
    {
        $serviceUri = '/newgoodsplatform/goodspicture/getgoodspictureinfobyspuid';
        $arrHeader = Zb_Util_ZbServiceTools::getHeaders($serviceUri);
        $arrParams = array(
            'spuId' => $spuId,
        );
        if (!empty($options['isRalMulti'])) {
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }
    public static function bindGoodsPicture($modelId = 0, $skuIds = [], $spuIds = [], $bindType = 0){
        $serviceUri = '/newgoodsplatform/goodspicture/bindgoodspicture';
        $arrHeader = Zb_Util_ZbServiceTools::getHeaders($serviceUri);
        $arrParams = array(
            'modelId'   => $modelId,
            'skuIds'    => $skuIds,
            'spuIds'    => $spuIds,
            'bindType'  => $bindType,
        );
        if (!empty($options['isRalMulti'])) {
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }
        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * @param int $modelId
     * @param int $itemType
     * @return array
     */
    public static function getItemByModelIdAndType($modelId = 0, $itemType = 0, $itemIds = [], $offset = 0, $limit = 20) {
        $serviceUri = '/newgoodsplatform/goodspicture/getitembymodelidandtype';
        $arrHeader = Zb_Util_ZbServiceTools::getHeaders($serviceUri);
        $arrParams = array(
            'modelId'         => $modelId,
            'itemType'        => $itemType,
            'itemIds'          => $itemIds,
            'offset'          => $offset,
            'limit'           => $limit,
        );
        if (!empty($options['isRalMulti'])) {
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }
        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * @param int $modelId
     * @param int $itemType
     * @return array
     */
    public static function getBindListByItem($itemIds = [], $itemType = 0) {
        $serviceUri = '/newgoodsplatform/goodspicture/getbindlistbyitem';
        $arrHeader = Zb_Util_ZbServiceTools::getHeaders($serviceUri);
        $arrParams = array(
            'itemType'        => $itemType,
            'itemIds'          => $itemIds,
        );
        if (!empty($options['isRalMulti'])) {
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }
        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }
}
