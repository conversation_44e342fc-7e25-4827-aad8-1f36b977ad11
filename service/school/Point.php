<?php
/**
 * 积分相关服务
 * @file Point.php
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/22
 */

class Lxjxlib_Service_School_Point
{
    protected $_dsUserPoint;
    protected $_dsUserPointRecord;

    public function __construct()
    {
        $this->_dsUserPoint       = new Lxjxlib_Ds_School_UserPoint();
        $this->_dsUserPointRecord = new Lxjxlib_Ds_School_UserPointRecord();
    }

    /**
     * 增/减用户积分
     * @param $uid
     * @param $changeType
     * @param $changeMode
     * @param $changePoint
     * @param null $operatorId  操作人id
     * @param false $useTransaction  是否开启事物
     * @return array  [
                        'errNo'  => 0,  //错误码（0为成功）
                        'errMsg' => '',  //错误信息
                        'data'   => [
                            'recordId' => 1345678,  //积分操作记录ID
                        ],
                    ];
     * @throws Lxjxlib_Const_Exception
     */
    public function changePoint($uid, $changeType, $changeMode, $changePoint, $operatorId = null, $useTransaction = false)
    {
        //校验参数
        $this->_checkParam($changeType, $changeMode, $changePoint, $operatorId);

        //积分账户信息是否存在
        $conds         = [
            'uid' => $uid,
        ];
        $userPointInfo = $this->_dsUserPoint->getRecordByConds($conds);
        if (empty($userPointInfo)) {
            throw new Lxjxlib_Const_Exception(Lxjxschool_Const_Point::ERROR_CODE_INVALID_USER);
        }

        //扣积分，积分是否充足
        if ($changeType == Lxjxschool_Const_Point::POINT_CHANGE_TYPE_MINUS && $userPointInfo['remainPoint'] <
            $changePoint) {
            throw new Lxjxlib_Const_Exception(Lxjxschool_Const_Point::ERROR_CODE_POINT_LACK);
        }

        $remainPoint = intval($userPointInfo['remainPoint']);
        if (Lxjxschool_Const_Point::POINT_CHANGE_TYPE_PLUS == $changeType) {
            $remainPoint += $changePoint;
        } else {
            $remainPoint -= $changePoint;
        }

        //返回结果
        $return = [
            'errNo'  => 0,
            'errMsg' => '',
            'data'   => [
                'recordId' => 0,
            ],
        ];
        if ($useTransaction) {
            $this->_dsUserPoint->startTransaction();
        }
        try {
            //添加积分变动记录
            $recordId = $this->_dsUserPointRecord->addPointRecord($uid, $changeType, $changeMode, $changePoint,
                $remainPoint);
            if ($recordId === false) {
                throw new Lxjxlib_Const_Exception(Lxjxlib_Const_ExceptionCodes::DB_INSERT_ERR, '积分记录');
            }
            //变更积分
            $ret = $this->_dsUserPoint->_updateUserPoint($changeType, $changePoint, $userPointInfo);
            if ($ret === false) {
                throw new Lxjxlib_Const_Exception(Lxjxlib_Const_ExceptionCodes::DB_UPDATE_ERR, '更新积分');
            }

            //BD充值、运营充值 需要记充值记录
            $arrChangeMod = [
                Lxjxschool_Const_Point::POINT_CHANGE_MODE_OP_PRESENT,
                Lxjxschool_Const_Point::POINT_CHANGE_MODE_BD_PRESENT,
            ];
            if (in_array($changeMode, $arrChangeMod)) {
                $operatorRole = 0;
                if (Lxjxschool_Const_Point::POINT_CHANGE_MODE_OP_PRESENT == $changeMode) {
                    $operatorRole = Lxjxlib_Const_Lxjxuser::USER_ROLE_ID_YY;
                } elseif (Lxjxschool_Const_Point::POINT_CHANGE_MODE_BD_PRESENT == $changeMode) {
                    $operatorRole = Lxjxlib_Const_Lxjxuser::USER_ROLE_ID_BD;
                }
                $ds  = new Lxjxlib_Ds_School_PointRechargeRecord();
                $ret = $ds->addRecord($uid, $recordId, $changePoint, $operatorRole, $operatorId);
                if ($ret === false) {
                    throw new Lxjxlib_Const_Exception(Lxjxlib_Const_ExceptionCodes::DB_INSERT_ERR, '操作记录');
                }
            }
            if ($useTransaction) {
                $this->_dsUserPoint->commit();
            }
            $return['data']['recordId'] = $recordId;
        } catch (Lxjxlib_Const_Exception $ex) {
            if ($useTransaction) {
                $this->_dsUserPoint->rollback();
            }
            $return['errNo']  = $ex->getErrNo();
            $return['errMsg'] = $ex->getErrMsg();
        }

        return $return;
    }

    /**
     * 检查参数
     * @param $changeType
     * @param $changeMode
     * @param $changePoint
     * @param $operatorId
     * @return bool
     * @throws Lxjxlib_Const_Exception
     */
    private function _checkParam($changeType, $changeMode, $changePoint, $operatorId)
    {
        //changeMode必须在默认配置中
        if (empty(Lxjxschool_Const_Point::getChangeDesc($changeMode))) {
            throw new Lxjxlib_Const_Exception(Lxjxlib_Const_ExceptionCodes::PARAM_ERROR, 'changeMode');
        }
        //changeType必须在默认配置中
        if (!in_array($changeType, Lxjxschool_Const_Point::$POINT_CHANGE_TYPE_MAP)) {
            throw new Lxjxlib_Const_Exception(Lxjxlib_Const_ExceptionCodes::PARAM_ERROR, 'changeType');
        }
        //changePoint整数且大于0
        if ($changePoint <= 0 || !is_numeric($changePoint)) {
            throw new Lxjxlib_Const_Exception(Lxjxlib_Const_ExceptionCodes::PARAM_ERROR, 'changePoint');
        }

        //BD充值、运营充值 需要有操作人
        $arrChangeMod = [
            Lxjxschool_Const_Point::POINT_CHANGE_MODE_OP_PRESENT,
            Lxjxschool_Const_Point::POINT_CHANGE_MODE_BD_PRESENT,
        ];
        if (in_array($changeMode, $arrChangeMod) && empty($operatorId)) {
            throw new Lxjxlib_Const_Exception(Lxjxlib_Const_ExceptionCodes::PARAM_ERROR, 'operatorId必须');
        }

        return true;
    }

    /**
     * 增加积分 - 后续处理
     * @param $uid
     * @param $recordId
     * @return bool
     */
    public function afterAddPoint($uid, $recordId)
    {
        // 用户积分增加，发送nmq，判断是否需要推送可兑换提醒
        $service = new Lxjxlib_Service_Nmq();
        $data    = [
            'uid'      => $uid,
            'recordId' => $recordId,
        ];
        $service->sendNmq(
            Lxjxlib_Const_Command::NMQ_COMMAND_LXJXSCH_USER_POINT_INCREASE,
            $data,
            Lxjxlib_Const_Common::NMQ_TOPIC_LXJX_LXJXSCH,
            '[userPointIncrease]'
        );

        return true;
    }
}