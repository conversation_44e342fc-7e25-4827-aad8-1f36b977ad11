<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file Achilles.php
 * <AUTHOR>
 * @date 2019/06/15
 * @brief 请求Achilles接口
 *
 **/
class Hkzb_Service_Achilles
{
    public static function call($url, $params)
    {
        $isCluster = empty(Bd_Conf::getConf('achilles-server')['local']);
        return self::ralCall($url, $params, $isCluster);
    }

    public static function v3call($v3Path, $v2Path, array $v3Data, array $v2Data, $outKey)
    {
        return self::ralV3Call($v3Path, $v2Path, $v3Data, $v2Data, $outKey, false);
    }

    private static function ralCall($url, $params, $isCluster = false)
    {
        $header = [
            'pathinfo' => $url,
        ];
        $serviceName = $isCluster ? 'achilles-server' : 'achilles-server_local';
        $ret = ral($serviceName, "POST", $params, 123, $header);
        if ($ret === false) {
            $errno = ral_get_errno();
            $errmsg = ral_get_error();
            $protocol_status = ral_get_protocol_code();
            Bd_Log::warning("Error:[service $serviceName connect error], Detail:[errno:$errno errmsg:$errmsg protocol_status:$protocol_status]");
            return !$isCluster ? self::ralCall($url, $params, true) : false;
        }
        if ($ret['errNo']) {
            Bd_Log::warning("Error:[service $serviceName response error], Detail:[" . json_encode($ret) . "]");
            return [];
        }
        return $ret['data'];
    }

    private static function ralV3Call($v3Path, $v2Path, $v3Data = [], $v2Data = [], $outKey = '', $cluster = false)
    {
        $header = [
            'pathinfo' => $v3Path,
        ];
        $serviceName = 'achilles-v3-server';
        if(empty($v3Data)) $v3Data = (object)array();
        $ret = ral($serviceName, "POST", $v3Data, 123, $header);
        if ($ret === false) {
            $errno = ral_get_errno();
            $errmsg = ral_get_error();
            $protocol_status = ral_get_protocol_code();
            Bd_Log::warning("Error:[service $serviceName connect error], Detail:[errno:$errno errmsg:$errmsg protocol_status:$protocol_status]");
            return !empty($v2Path) && !empty($v2Data) ? self::call($v2Path, $v2Data) : false;
        }
        if ($ret['errNo']) {
            Bd_Log::warning("Error:[service $serviceName response error], Detail:[" . json_encode($ret) . "]");
            return [];
        }
        return $ret['data'];
    }
}
