<?php

/**
 * @befie   经纬度计算
 * 地球上同一个地理位置的经纬度，在不同的坐标系中，会有少于偏移，国内目前常见的坐标系主要分为三种：
 * 地球坐标系——WGS84：常见于 GPS 设备，Google 地图等国际标准的坐标体系。
 * 火星坐标系——GCJ-02：中国国内使用的被强制加密后的坐标体系，高德坐标就属于该种坐标体系。
 * 百度坐标系——BD-09：百度地图所使用的坐标体系，是在火星坐标系的基础上又进行了一次加密处理。
 * @file    service/lbs/Coords.php
 * <AUTHOR>
 * @version 1.0
 * @since
 * @date    2019-12-09
 */
class Lxjxlib_Service_Lbs_Coords
{
    const x_PI  = 52.35987755982988;
    const PI  = 3.1415926535897932384626;
    const a = 6378245.0;
    const ee = 0.00669342162296594323;

    /**
     * 根据两点间的经纬度计算距离
     * @param $lng1
     * @param $lat1
     * @param $lng2
     * @param $lat2
     * @return float 单位km
     */
    public static function dist($lng1, $lat1, $lng2, $lat2)
    {
        // 将角度转为狐度
        $radLat1 = deg2rad($lat1); //deg2rad()函数将角度转换为弧度
        $radLat2 = deg2rad($lat2);
        $radLng1 = deg2rad($lng1);
        $radLng2 = deg2rad($lng2);
        $a = $radLat1 - $radLat2;
        $b = $radLng1 - $radLng2;
        $s = 2 * asin(sqrt(pow(sin($a / 2), 2) + cos($radLat1) * cos($radLat2) * pow(sin($b / 2), 2))) * 6378.137;
        return round($s, 2);
    }

    /**
     * WGS84转GCj02(北斗转高德)
     * @param $lng
     * @param $lat
     * @return array
     */
    public static function wgs84togcj02($lng, $lat)
    {
        if (self::isOutOfChina($lng, $lat)) {
            return ['longitude' => $lng, 'latitude' => $lat];
        } else {
            $dlat = self::transformlat($lng - 105.0, $lat - 35.0);
            $dlng = self::transformlng($lng - 105.0, $lat - 35.0);
            $radlat = $lat / 180.0 * self::PI;
            $magic = sin($radlat);
            $magic = 1 - self::ee * $magic * $magic;
            $sqrtmagic = sqrt($magic);
            $dlat = ($dlat * 180.0) / ((self::a * (1 - self::ee)) / ($magic * $sqrtmagic) * self::PI);
            $dlng = ($dlng * 180.0) / (self::a / $sqrtmagic * cos($radlat) * self::PI);
            $mglat = $lat + $dlat;
            $mglng = $lng + $dlng;
            return ['longitude' => $mglng, 'latitude' => $mglat];
        }
    }

    /**
     * GCJ02转换为WGS84(高德转北斗)
     * @param lng
     * @param lat
     * @return array(lng, lat);
     */
    public static function gcj02towgs84($lng, $lat)
    {
        if (self::isOutOfChina($lng, $lat)) {
            return ['longitude' => $lng, 'latitude' => $lat];
        } else {
            $dlat = self::transformlat($lng - 105.0, $lat - 35.0);
            $dlng = self::transformlng($lng - 105.0, $lat - 35.0);
            $radlat = $lat / 180.0 * self::PI;
            $magic = sin($radlat);
            $magic = 1 - self::ee * $magic * $magic;
            $sqrtmagic = sqrt($magic);
            $dlat = ($dlat * 180.0) / ((self::a * (1 - self::ee)) / ($magic * $sqrtmagic) * self::PI);
            $dlng = ($dlng * 180.0) / (self::a / $sqrtmagic * cos($radlat) * self::PI);
            $mglat = $lat + $dlat;
            $mglng = $lng + $dlng;
            return ['longitude' => $lng * 2 - $mglng, 'latitude' => $lat * 2 - $mglat];
        }
    }


    /**
     * 百度坐标系 (BD-09) 与 火星坐标系 (GCJ-02)的转换
     * @param $lng
     * @param $lat
     * @return array
     */
    public static function bd09togcj02($lng, $lat)
    {
        $x = $lng - 0.0065;
        $y = $lat - 0.006;
        $z = sqrt($x * $x + $y * $y) - 0.00002 * sin($y * self::x_PI);
        $theta = atan2($y, $x) - 0.000003 * cos($x * self::x_PI);
        return [
            'longitude' => round($z * cos($theta), 6),
            'latitude' => round($z * sin($theta), 6)
        ];
    }

    /**
     * GCJ-02转换为BD-09（火星坐标系转百度，即谷歌、高德转百度）
     * @param $lng
     * @param $lat
     * @return array
     */
    public static function gcj02tobd09($lng, $lat)
    {
        $z = sqrt($lng * $lng + $lat * $lat) + 0.00002 * sin($lat * self::x_PI);
        $theta = atan2($lat, $lng) + 0.000003 * cos($lng * self::x_PI);
        return [
            'longitude' => round($z * cos($theta) + 0.0065, 6),
            'latitude' => round($z * sin($theta) + 0.006, 6)
        ];
    }

    private static function transformlat($lng, $lat)
    {
        $ret = -100.0 + 2.0 * $lng + 3.0 * $lat + 0.2 * $lat * $lat + 0.1 * $lng * $lat + 0.2 * sqrt(abs($lng));
        $ret += (20.0 * sin(6.0 * $lng * self::PI) + 20.0 * sin(2.0 * $lng * self::PI)) * 2.0 / 3.0;
        $ret += (20.0 * sin($lat * self::PI) + 40.0 * sin($lat / 3.0 * self::PI)) * 2.0 / 3.0;
        $ret += (160.0 * sin($lat / 12.0 * self::PI) + 320 * sin($lat * self::PI / 30.0)) * 2.0 / 3.0;
        return $ret;
    }

    private static function transformlng($lng, $lat)
    {
        $ret = 300.0 + $lng + 2.0 * $lat + 0.1 * $lng * $lng + 0.1 * $lng * $lat + 0.1 * sqrt(abs($lng));
        $ret += (20.0 * sin(6.0 * $lng * self::PI) + 20.0 * sin(2.0 * $lng * self::PI)) * 2.0 / 3.0;
        $ret += (20.0 * sin($lng * self::PI) + 40.0 * sin($lng / 3.0 * self::PI)) * 2.0 / 3.0;
        $ret += (150.0 * sin($lng / 12.0 * self::PI) + 300.0 * sin($lng / 30.0 * self::PI)) * 2.0 / 3.0;
        return $ret;
    }

    private static function rad($param)
    {
        return  $param * self::PI / 180.0;
    }

    /**
     * 判断是否在国内，不在国内则不做偏移
     * @param $lng
     * @param $lat
     * @return bool
     */
    private static function isOutOfChina($lng, $lat)
    {
        return ($lng < 72.004 || $lng > 137.8347) || (($lat < 0.8293 || $lat > 55.8271) || false);
    }

}