<?php

/**
 * @desc    调用黑沙接口
 * @file    Client.php
 * <AUTHOR>
 * @date    2021-01-09 15:53
 */
class Lxjxlib_Service_Heisha_Client
{
    protected $request;
    protected $corpId;
    const SUCCESS_CODE = 0;

    public function __construct($corpId = null)
    {
        $this->request = new Lxjxlib_Service_Heisha_Request();
        # 企微 企业id
        if ($corpId) {
            $this->corpId = $corpId;
        } else {
            $config = Lxjxlib_Util_Conf::getEnvConf('wxconfig');
            if (!empty($config['hkb_qw']['appid'])) {
                $this->corpId = $config['hkb_qw']['appid'];
            }
        }
    }

    /**
     * 获取BD和老师在企微的好友关系
     * @param $bdQwUserId
     * @param $tkUnionId
     * @return bool
     * @throws Hk_Util_Exception
     */
    public function getBdTeacherFriendship($bdQwUserId, $tkUnionId)
    {
        $method = '/api/zyb/Member/getFriendShip';
        $params = [
            'corpId'  => $this->corpId,
            'userId'  => $bdQwUserId,
            'unionId' => $tkUnionId,
        ];

        $ret = $this->request->doRequest($method, $params);
        return $ret;
    }

    /**
     * 发送应用消息
     * @param $msgData
     * @return bool
     * @throws Hk_Util_Exception
     */
    public function sendMessage($msgData)
    {
        $method = '/api/zyb/Message/send';
        $params = $msgData;

        $params['corpId'] = $this->corpId;

        $ret = $this->request->doRequest($method, $params);
        return $ret;
    }

    /**
     * 给客户设置标签
     * @param $bdQwUserId
     * @param $externalUserId
     * @param $addTags
     * @param $groupId
     * @return bool
     * @throws Hk_Util_Exception
     */
    public function editCustomerTags($bdQwUserId, $externalUserId, $addTags, $groupId)
    {
        $method = '/api/zyb/Tag/editCustomerTags';
        $params = [
            'corpId'         => $this->corpId,
            'userId'         => $bdQwUserId,
            'externalUserId' => $externalUserId,
            'addTag'         => $addTags,
            'groupId'        => $groupId,
        ];

        $ret = $this->request->doRequest($method, $params);
        return $ret;
    }

    /**
     * 给客户设置标签
     * @param        $groupName
     * @param string $tagName
     * @return bool
     * @throws Hk_Util_Exception
     */
    public function createTag($groupName, $tagName = '')
    {
        $method = '/api/zyb/Tag/createTag';
        $params = [
            'corpId'    => $this->corpId,
            'groupName' => $groupName,
            //            'groupId' => $groupId,   //和黑沙约定不需要传id了
            'tagName'   => $tagName,
        ];

        $ret = $this->request->doRequest($method, $params);
        return $ret;
    }

    /**
     * 获取标签信息
     * @param string $tagId
     * @return bool
     * @throws Hk_Util_Exception
     */
    public function getCompanyTags($tagId = '')
    {
        $method = '/api/zyb/Tag/getCompanyTags';
        $params = [
            'corpId' => $this->corpId,
            'tagId'  => $tagId,
        ];

        $ret = $this->request->doRequest($method, $params);
        return $ret;
    }

    /**
     * 获取老师在企微的BD列表
     * @param $externalUserId
     * @return bool
     * @throws Hk_Util_Exception
     */
    public function getTeacherBdList($externalUserId)
    {
        $method = '/api/zyb/Member/repeatCustomersInfo';
        $params = [
            'corpId'         => $this->corpId,
            'externalUserid' => $externalUserId,
        ];

        $ret = $this->request->doRequest($method, $params);
        return $ret;
    }

    /**
     * 根据昵称获取企微中的BD列表
     * @param $nickName
     * @return array
     * @throws Hk_Util_Exception
     */
    public function getUserListByNickName($nickName)
    {
        $method = '/api/zyb/Member/serch';
        $params = [
            'corpId'   => $this->corpId,
            'nickName' => $nickName,
        ];

        $ret = $this->request->doRequest($method, $params);
        if ($ret['errNo'] == self::SUCCESS_CODE) {
            return $ret['data'];
        }
        return [];
    }

    /**
     * 获取老师在企微的信息
     * @param null $externalUserid
     * @param null $tkUnionId
     * @return bool
     * @throws Hk_Util_Exception
     * @throws Lxjxlib_Const_Exception
     */
    public function getTeacherInfo($externalUserid = null, $tkUnionId = null)
    {
        $method = '/api/zyb/Member/getExternalUser';
        # externalUserId和unionId只传一个
        if (empty($externalUserid) && empty($tkUnionId)) {
            throw new Lxjxlib_Const_Exception(Lxjxlib_Const_ExceptionCodes::PARAM_ERROR);
        }
        $params = [
            'corpId' => $this->corpId,
        ];
        if (!empty($externalUserid)) {
            $params['externalUserid'] = $externalUserid;
        } else {
            $params['unionId'] = $tkUnionId;
        }

        $ret = $this->request->doRequest($method, $params);
        return $ret;
    }

    /**
     * 小程序素材点击打点
     * @param $materialId
     * @param $unionid
     * @param $sendUserId
     * @return bool
     * @throws Hk_Util_Exception
     */
    public function materialClick($materialId, $unionid, $sendUserId)
    {
        $method = '/home/<USER>/clickLog';
        $params = [
            'corpId'     => $this->corpId,
            'materialId' => $materialId,
            'unionid'    => $unionid,
            'sendUserId' => $sendUserId,
        ];

        $ret = $this->request->doRequest($method, $params);
        return $ret;
    }

    /**
     * 创建自定义标签
     * @param $bdQwUserId
     * @param $tagId
     * @param $tagName
     * @return bool
     * @throws Hk_Util_Exception
     */
    public function createCustomerTag($bdQwUserId, $tagId, $tagName)
    {
        $method = '/api/zyb/CustomTag/createTag';
        $params = [
            'corpId'  => $this->corpId,
            'userId'  => $bdQwUserId,
            'tagId'   => $tagId,
            'tagName' => $tagName,
        ];

        $ret = $this->request->doRequest($method, $params);
        return $ret;
    }

    /**
     * 删除自定义标签
     * @param $tagId
     * @return bool
     * @throws Hk_Util_Exception
     */
    public function deleteCustomerTag($tagId)
    {
        $method = '/api/zyb/CustomTag/deleteTag';
        $params = [
            'corpId' => $this->corpId,
            'tagId'  => $tagId,
        ];

        $ret = $this->request->doRequest($method, $params);
        return $ret;
    }

    /**
     * 给客户添加/删除自定义标签
     * @param $bdQwUserId
     * @param $externalUserid
     * @param $addTag
     * @param $removeTag
     * @return bool
     * @throws Hk_Util_Exception
     */
    public function markCustomerTag($bdQwUserId, $externalUserid, $addTag, $removeTag)
    {
        $method = '/api/zyb/CustomTag/markTag';
        $params = [
            'corpId'         => $this->corpId,
            'userId'         => $bdQwUserId,
            'externalUserid' => $externalUserid,
            'addTag'         => $addTag,
            'removeTag'      => $removeTag,
        ];

        $ret = $this->request->doRequest($method, $params);
        return $ret;
    }

    /**
     * 通过userId查询企微用户信息（批量）
     * 注：黑沙查自己的备库
     * @param $userIds ["WangHuaBin","XingXing"]
     * @return bool
     * @throws Hk_Util_Exception
     */
    public function serchForUserId($userIds)
    {
        $method = '/api/zyb/Member/serchForUserId';
        $params = [
            'corpId' => $this->corpId,
            'userId' => $userIds,
        ];
        $ret    = $this->request->doRequest($method, $params);

        return $ret;
    }

    /**
     * 获取企微用户信息（只支持单个）
     * 注：黑沙直接查询企微
     * @param $userId
     * @return array|bool|mixed
     * @throws Hk_Util_Exception
     * 官方文档：https://work.weixin.qq.com/api/doc/90000/90135/90196
     * 黑沙Yapi：http://yapi.zuoyebang.cc/project/5873/interface/api/193018
     */
    public function getUserByUserId($userId)
    {
        $method = '/api/zyb/Member/serchForUserId';
        $params = [
            'corpId' => $this->corpId,
            'userId' => $userId,
            'type'   => 1,
        ];
        $ret    = $this->request->doRequest($method, $params);

        return $ret;
    }

    /**
     * 批量获取用户的打卡记录
     * @param $userIds
     * @param $startTime
     * @param $endTime
     * @return array|bool|mixed
     * @throws Hk_Util_Exception
     */
    public function getMemberCheckin($userIds, $startTime, $endTime)
    {
        $method = '/api/zyb/Member/getMemberCheckin';
        $params = [
            'corpId'    => $this->corpId,
            'userList'  => $userIds,
            'startTime' => $startTime,
            'endTime'   => $endTime,
        ];
        $ret    = $this->request->doRequest($method, $params);

        return $ret;
    }

    /**
     * 获取指定日期在职的人员（当前在职 & 离职日期 > $startTime）
     * @param $startTime
     * @return array|bool|mixed
     * @throws Hk_Util_Exception
     */
    public function getPartMember($startTime)
    {
        $method = '/api/zyb/Member/getPartMember';
        $params = [
            'corpId'    => $this->corpId,
            'startTime' => $startTime,
        ];
        $ret    = $this->request->doRequest($method, $params);

        return $ret;
    }

    /**
     * 获取部门成员详情（Yapi：http://yapi.zuoyebang.cc/project/5873/interface/api/194730）
     * @param $departId
     * @param int $fetchChild
     * @return array|bool|mixed
     * @throws Hk_Util_Exception
     */
    public function getDepartMemberList($departId, $fetchChild = 0)
    {
        $method = '/api/zyb/Member/getDepartMemberList';
        $params = [
            'corpId'       => $this->corpId,
            'departmentId' => $departId,
            'fetchChild'   => $fetchChild,
        ];
        $ret    = $this->request->doRequest($method, $params);

        return $ret;
    }

    /**
     * 获取用户的部门（包含子部门。。。。）
     * @param $userId Array
     * @return array|bool|mixed
     * @throws Hk_Util_Exception
     */
    public function getMemberSubDepart($userId)
    {
        $method = '/api/zyb/Member/getMemberSubDepart';
        $params = [
            'corpId' => $this->corpId,
            'userId' => $userId,
        ];
        $ret    = $this->request->doRequest($method, $params);

        return $ret;
    }

    /**
     * 获取打卡数据总览
     * @param $corpId
     * @param $departmentId
     * @param $startTime
     * @param $endTime
     * @param array $checkInDataType
     * @param array $userId
     * @return array|bool|mixed
     */
    public function getCheckinOverview($departmentId, $startTime, $endTime, $checkInDataType = [], $userId = [])
    {
        $method = '/api/zyb/Member/checkinOverview';
        $params = [
            'corpId'          => $this->corpId,
            'departmentId'    => $departmentId,
            'startTime'       => $startTime,
            'endTime'         => $endTime,
            'checkinDataType' => $checkInDataType,
        ];
        if($userId){
            $params['userId'] = $userId;
        }
        $ret    = $this->request->doRequest($method, $params);

        return $ret;
    }

    /**
     * 获取打卡数据(按日)
     * @param $corpId
     * @param $departmentId
     * @param $startTime
     * @param $endTime
     * @param array $checkInDataType
     * @param array $userId
     * @return array|bool|mixed
     */
    public function getCheckinData($departmentId, $startTime, $endTime, $page = 1, $pageSize = 20, $checkInDataType =
    [], $userId = [])
    {
        $method = '/api/zyb/Member/checkinData';
        $params = [
            'corpId'          => $this->corpId,
            'departmentId'    => $departmentId,
            'startTime'       => $startTime,
            'endTime'         => $endTime,
            'checkinDataType' => $checkInDataType,
            'page'            => $page,
            'pageSize'        => $pageSize,
        ];
        if($userId){
            $params['userId'] = $userId;
        }
        $ret    = $this->request->doRequest($method, $params);

        return $ret;
    }

    /**
     * 多个部门ID获取部门信息
     * @param $ids
     * @return array|bool|mixed
     * @throws Hk_Util_Exception
     */
    public function getDepartmentForId($ids)
    {
        $method = '/api/zyb/Department/getDepartmentForId';
        $params = [
            'corpId' => $this->corpId,
            'ids'    => $ids,
        ];
        $ret    = $this->request->doRequest($method, $params);

        return $ret;
    }

    /**
     * 获取部门的下级
     * @param $departmentId
     * @param int $fetchChild 0只取下级，1获取所有子部门的下级
     * @return array|bool|mixed
     * @throws Hk_Util_Exception
     */
    public function getDepartmentChain($departmentId, $fetchChild = 0)
    {
        $method = '/api/zyb/Department/getDepartmentChain';
        $params = [
            'corpId'       => $this->corpId,
            'departmentId' => $departmentId,
            'fetchChild'   => $fetchChild,
        ];
        $ret    = $this->request->doRequest($method, $params);

        return $ret;
    }

    /**
     * 获取部门下成员
     * @param $departmentId
     * @param int $fetchChild 0只取本部门，1获取所有子部门的成员
     * @return array|bool|mixed
     * @throws Hk_Util_Exception
     */
    public function getMemberChain($departmentId, $fetchChild = 0)
    {
        $method = '/api/zyb/Member/getMemberChain';
        $params = [
            'corpId'       => $this->corpId,
            'departmentId' => $departmentId,
            'fetchChild'   => $fetchChild,
        ];
        $ret    = $this->request->doRequest($method, $params);

        return $ret;
    }
}
