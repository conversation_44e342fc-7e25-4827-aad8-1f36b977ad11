<?php
/**
 * Copyright (c) 2019 zuoyebang.com, Inc. All Rights Reserved
 * @author: liu<PERSON><PERSON>@zuoyebang.com
 * @file: phplib/service/dar/Commit.php
 * @date: 2019/07/23
 * @file: 3:53 PM
 * @desc: 提供简易逻辑的db数据获取方法，排查dar 库外部系统直接使用时创建
 */
class Zb_Service_Dar_DataCollection {
    private static $service     = 'zbcore_dar';
    private static $serviceUri  = '/zbcore/api/api';
    private static $module      = 'dar';
    private static $entity      = 'dataCollection';

    /**
     * userPersonas系统获取SKUID维度预约表数据信息
     * @param $userId
     * @param $skuIds
     * @return array
     */
    public static function getPreSkuStatics($userId, $skuIds) {
        $params = [
            "userId" => $userId,
            "skuIds" => $skuIds,
        ];
        return self::helper($params, "getPreSkuStatics");
    }

    /**
     * userPersonas系统获取SKUID维度子订单表数据信息
     * @param $userId
     * @param $skuIds
     * @param $businessType
     * @return array
     */
    public static function getSubSkuStatics($userId, $skuIds,$businessType = null) {
        $params = [
            "userId" => $userId,
            "skuIds" => $skuIds,
        ];

        if ($businessType) {
            $params['businessType'] = $businessType;
        }

        return self::helper($params, "getSubSkuStatics");
    }

    /**
     * userPersonas系统获取SKUID维度纯实物子订单数据信息
     * @param $userId
     * @param $skuIds
     * @return array
     */
    public static function getPureEntitySkuStatics($userId, $skuIds) {
        $params = [
            "userId" => $userId,
            "skuIds" => $skuIds,
        ];
        return self::helper($params, "getPureEntitySkuStatics");
    }

    /**
     * userPersonas系统修复工具获取用户维度SKU子订单聚合信息
     * @param $userId
     * @return array
     */
    public static function getUserSubSkuStatics($userId) {
        $params = [
            "userId" => $userId,
        ];
        return self::helper($params, "getUserSubSkuStatics");
    }

    /**
     * userPersonas系统修复工具获取用户维度SKU子订单聚合信息
     * @param $userId
     * @return array
     */
    public static function getUserPreSkuStatics($userId) {
        $params = [
            "userId" => $userId,
        ];
        return self::helper($params, "getUserPreSkuStatics");
    }

    /**
     * Listing 系统获取表维度主订单信息
     * @param $userId
     * @param $sTime
     * @param $eTime
     * @return array
     */
    public static function getUserTradeList($userId, $sTime, $eTime) {
        $params = [
            'userId' => $userId,
            'sTime' => $sTime,
            'eTime' => $eTime,
        ];
        return self::helper($params, "getUserTradeList");
    }

    /**
     * Listing 系统获取用户维度主订单信息
     * @param $table
     * @param $sTime
     * @param $eTime
     * @return array
     */
    public static function getTableTradeList($table, $sTime, $eTime) {
        $params = [
            'table' => $table,
            'sTime' => $sTime,
            'eTime' => $eTime,
        ];
        return self::helper($params, "getTableTradeList");
    }

    /**
     * billing系统获取客服过滤接口用courseId列表
     * @param $userId
     * @param int $orderId
     * @param int $courseId
     * @param int $startTime
     * @param int $endTime
     * @param array $orderChannel
     * @return array
     */
    public static function getUserToCheckCourseIds($userId, $orderId=-1, $courseId=-1, $startTime=946684800,
                                                   $endTime=4102444800, $orderChannel=[]) {
        $params = [
            'userId' => $userId,
            'orderId' => $orderId,
            'courseId' => $courseId,
            'startTime' => $startTime,
            'endTime' => $endTime,
            'orderChannel' => $orderChannel,
        ];
        return self::helper($params, "getUserToCheckCourseIds");
    }

    /**
     * billing系统获取tradeId客服过滤接口用tradeId列表
     * @param $userId
     * @param array $courseIds
     * @param int $orderId
     * @param int $courseId
     * @param int $startTime
     * @param int $endTime
     * @param array $orderChannel
     * @return array
     */
    public static function getTradeIdsByUserIdAndCourseArr($userId, $courseIds=[], $orderId=-1, $courseId=-1,
                                                      $startTime=946684800, $endTime=4102444800, $orderChannel=[]) {
        $params = [
            'userId' => $userId,
            'courseIds' => $courseIds,
            'orderId' => $orderId,
            'courseId' => $courseId,
            'startTime' => $startTime,
            'endTime' => $endTime,
            'orderChannel' => $orderChannel,
        ];
        return self::helper($params, "getTradeIdsByUserIdAndCourseArr");
    }

    /**
     * billing系统获取tradeId客服过滤接口用tradeId列表，包含纯实物订单
     * @param $userId
     * @param int $orderId
     * @param int $startTime
     * @param int $endTime
     * @param array $orderChannel
     * @return array
     */
    public static function getAllCssTradeIds($userId, $orderId=-1, $startTime=946684800, $endTime=4102444800,
                                             $orderChannel=[]) {
        $params = [
            'userId' => $userId,
            'orderId' => $orderId,
            'startTime' => $startTime,
            'endTime' => $endTime,
            'orderChannel' => $orderChannel,
        ];
        return self::helper($params, "getAllCssTradeIds");
    }

    /**
     * observer系统获取待处理列表
     * @param $table
     * @param $startTime
     * @param $endTime
     * @param $limit
     * @param $offset
     * @return array
     */
    public static function getProcessingList($table, $startTime, $endTime, $limit, $offset) {
        $params = [
            "table" => $table,
            "startTime" => $startTime,
            "endTime" => $endTime,
            "limit" => $limit,
            "offset" => $offset,
        ];
        return self::helper($params, "getProcessingList");
    }

    /**
     * trade系统地址后置订单发送短信脚本获取数据
     * @param $table
     * @param $timeGroups
     * @return array
     */
    public static function getAddressPositionData($table, $timeGroups) {
        $params = [
            "table" => $table,
            "timeGroups" => $timeGroups,
        ];
        return self::helper($params, "getAddressPositionData");
    }

    private static function helper($arrParams, $api) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, $api);
        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }
        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }
}
