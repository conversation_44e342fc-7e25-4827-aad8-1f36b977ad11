<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   RefundSummary.php
 * <AUTHOR>
 * @date   2018/8/18 下午7:27
 * @brief
 **/


class Zb_Service_Dar_RefundSummary {

    private static $service     = 'zbcore_dar';
    private static $serviceUri  = '/zbcore/api/api';
    private static $module      = 'dar';
    private static $entity      = 'refundSummary';


    /**
     * 3.4  用户退款流水列表接口
     *
     * @param  int      $userId
     * @param  array    $refundSummaryFields
     * @param  array    $refundDetailFields
     * @param  int      $page
     * @param  int      $pageSize
     * @param  array    $options
     * @return array
     */
    public static function getListByUserId($userId, $refundSummaryFields=array(), $refundDetailFields=array(), $page=1, $pageSize=10, $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getListByUserId');
        $arrParams = array(
            'userId'                => $userId,
            'refundSummaryFields'   => $refundSummaryFields,
            'refundDetailFields'    => $refundDetailFields,
            'page'                  => $page,
            'pageSize'              => $pageSize,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

}