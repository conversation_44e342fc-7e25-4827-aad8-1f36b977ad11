<?php
/**
 * Redis封装
 *
 * @file Redis.php
 * <AUTHOR>
 * @data 2019/2/18 11:22
 * @version 1.0
 * @brief
 *      $redisName : /home/<USER>/conf/hk/redis.conf中配置项名称
 */

class Hkzb_Service_Redis extends Hk_Service_Redis
{
    public function __construct($redisName)
    {
        Bd_Log::notice('Redis_Instantiation_Start.RedisName:' . $redisName);
        $conf = '/hk/redis/' . $redisName;
        $redisConf = Bd_conf::getConf($conf);
        if ($redisConf === false) {
            Bd_Log::warning('redis conf error.conf:' . $conf);
        }
        $objRedis = parent::__construct($redisConf['service']);

        return $objRedis;
    }
}