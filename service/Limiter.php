<?php

class Zb_Service_Limiter
{
    const CLUSTER = "limiter";

    const PATH    = "/limiter/api/limit";

    const MODULE = "limiter";

    const GROUP  = "public";

    const ERR_NO_LIMITED = 596;

    public static function drive(): bool
    {
        $cluster = $_SERVER['CLUSTER_NAME'];
        $module  = $_SERVER['APP_NAME'];
        $path    = '/'. $module . $_SERVER['PATH_INFO'];
        if (empty($cluster) || empty($module) || empty($path)) {
            return false;
        }
        $cluster = self::genCluster($cluster, $module);

        $config  = Zb_Service_NCM::Get(Zb_Service_NCM::APP_YIKE, self::MODULE, self::GROUP, $cluster);
        $aConfig = empty($config) ? [] : json_decode($config,true);
        if (!isset($aConfig[$path])) {
            return false;
        }

        $params = array(
            "cluster"   => $cluster,
            "module"    => $module,
            "path"      => $path,
            "timestamp" => round(microtime(true) * 1000),
        );
        $header = [
            'pathinfo'     => self::PATH,
        ];
        try {
            Zb_Service_RalRequest::rpc(self::CLUSTER, $params, $header);
        } catch (Zb_Util_Exception $e) {
            if ($e->getCode() == self::ERR_NO_LIMITED) {
                return true;
            }
        }
        return false;
    }

    private static function genCluster($cluster, $module): string {
        $arr = explode(".", $cluster);
        return $module . '_' . $arr[1] . '_' . $arr[2];
    }
}