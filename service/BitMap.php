<?php

/**
 * @desc    位映射
 * @file    BitMap.php
 * <AUTHOR>
 * @date    2020-12-12 13:39
 */
class Lxjxlib_Service_BitMap
{
    protected $all = 0;
    protected $bitMap = [];

    /**
     * Bit constructor.
     * @param array $bitMap
     */
    public function __construct(array $bitMap)
    {
        # 每位相加都不产生进位，可以用加法代替按位或
        $this->all    = array_sum(array_keys($bitMap));
        $this->bitMap = $bitMap;
    }

    /**
     * 找出复合数值包含的值
     * 如 输入5返回[1=>'a',4=>'b'];输入7返回[1=>'a',2=>'b',4=>'c'];
     * @param $storage :复合值
     * @return array
     */
    public function bitMap($storage)
    {
        $map    = [];
        $bitMap = $this->bitMap;
        foreach ($bitMap as $bit => $bitName) {
            if (($storage & $bit) == $bit) {
                $map[$bit] = $bitName;
            }
        }
        return $map;
    }
}