<?php
/**
 * Copyright (c) 1998-2019 作业帮. (http://www.zybang.com)
 * o开头的变量是对象,a开头的变量是数组
 * Author: MaRongcai
 * Date: 22/4/6
 * Class Zb_Service_RalRequest
 * RPC 请求封装
 */
class Zb_Service_RalRequest
{

    /**
     * @param $serviceName
     * @param $aParameter
     * @param $aHttpHeader
     * @param string $method
     * @return mixed
     * @throws Zb_Util_Exception
     */
    public static function rpc($serviceName,$aParameter,$aHttpHeader, $method = 'GET') {
        $serviceURI         = $aHttpHeader['pathinfo'];
        //防止header丢失
        ral_set_pathinfo($serviceURI);

        $aResult        = ral($serviceName, $method, $aParameter, $_SERVER['HTTP_X_BD_LOGID'],$aHttpHeader);

        if ($aResult === false || empty($aResult)) {
            // ral调用失败，服务异常，需要重试
            $errorCode      = ral_get_errno();
            $errorMessage   = ral_get_error();
            $errMsg         = sprintf('[%s]Ral服务连接失败;[URI:%s errCode:%d errMsg:%s]',$serviceName,$serviceURI,$errorCode,$errorMessage);
            throw new Zb_Util_Exception(Zb_Util_ExceptionCodes::NETWORK_ERROR,$errMsg);
        }

        if(is_string($aResult)) {
            $aResult        = json_decode($aResult, true);
        }

        if(empty($aResult) || !isset($aResult['errNo'])) {
            $errMsg         = sprintf('[%s]Ral服务返回协议非法,URI=%s',$serviceName,$serviceURI);
            throw new Zb_Util_Exception(Zb_Util_ExceptionCodes::NETWORK_ERROR,$errMsg);
        }

        $errNo              = $aResult['errNo'];
        //goweb框架是errMsg 兼容
        $errStr             = $aResult['errStr'] ?? $aResult['errMsg'];
        //同步异常返回给上游
        if ($errNo != 0) {
            throw new Zb_Util_Exception($errNo,$errStr);
        }
        return $aResult['data'];
    }
}