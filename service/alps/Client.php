<?php

/**
 * @desc    调用alps接口
 * @file    Client.php
 * <AUTHOR>
 * @date    2021-09-09 15:53
 */
class Lxjxlib_Service_Alps_Client
{

    //是否在职
    const STATUS_YES = "A";
    const STATUS_NO  = "I";

    protected $request;
    protected $appId;
    protected $appSecret;
    protected $domain;

    public function __construct($corpId = null)
    {
        $this->request = new Lxjxlib_Service_Alps_Request();

        $alpsConfig      = Lxjxlib_Util_Conf::getEnvConf('common')['alps'];
        $this->appId     = $alpsConfig['appId'];
        $this->appSecret = $alpsConfig['appSecret'];
        $this->domain    = $alpsConfig['domain'];
    }

    /**
     * 通过邮箱查询人员信息 （工号、姓名、邮箱、是否在职）
     * @param $email
     * @return mixed
     * @throws Lxjxlib_Const_Exception
     */
    public function selectByEmail($email)
    {
        if (Lxjxlib_Util_Tools::getEnv() != 'prod') {
            return [
                "emplId"    => $email,
                "emplName"  => $email,
                "busnEmail" => $email,
                "hrStatus"  => "A",
            ];
        }

        $path = '/api/v2/customized/selectByEmail';
        $url  = $this->domain . $path;

        $params = [
            "email" => $email,
        ];

        $params = $this->buildParams($params);

        $ret = $this->request->curlGet($url, $params);
        if (isset($ret["code"]) && $ret["code"] == 0) {
            return $ret["data"];
        }
        if (isset($ret["code"]) && $ret["code"] == 1000) {
            return [];
        }
        throw new Lxjxlib_Const_Exception(Lxjxlib_Const_ExceptionCodes::ALPS_SERVER_ERROR, json_encode($ret));
    }


    /**
     * 获取指定时间范围内（默认一天，最多5天）离职人员信息（工号、姓名、邮箱、是否在职）
     * @param int $days
     * @return mixed
     * @throws Lxjxlib_Const_Exception
     */
    public function selectByLeaveDate($days = 10)
    {
        $path = '/api/v2/customized/selectByLeaveDate';
        $url  = $this->domain . $path;

        $params = [
            "days" => $days,
        ];

        $params = $this->buildParams($params);

        $ret = $this->request->curlGet($url, $params);
        if (isset($ret["code"]) && $ret["code"] == 0) {
            return $ret["data"];
        }
        throw new Lxjxlib_Const_Exception(Lxjxlib_Const_ExceptionCodes::ALPS_SERVER_ERROR, json_encode($ret));
    }

    private function buildParams(&$params)
    {
        $time = time();

        $commonParams = [
            'appId'     => $this->appId,
            'timestamp' => $time,
            'requestId' => md5(microtime(1)),
            'nonceStr'  => md5(microtime(1)),
        ];
        $params       = array_merge($commonParams, $params);

        ksort($params);

        $signStr = '';
        foreach ($params as $k => $v) {
            $signStr .= $k . '=' . $v . '&';
        }

        $signStr .= 'secret=' . $this->appSecret;
        $signStr = strtoupper(md5($signStr));

        $params['sign'] = $signStr;

        return $params;
    }
}
