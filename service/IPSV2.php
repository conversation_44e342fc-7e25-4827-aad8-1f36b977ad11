<?php

/**
 * <AUTHOR>
 * @version 2.0
 * @date    2021-07-07
 */

class Saaslib_Service_IPSV2
{
    const LOGIN_URI            = "/static/cas-fe/?version=%s&sdk=%s";
    const LOGOUT_FE_URI        = "/static/cas-fe/?logout=1&version=%s&sdk=%s";
    const LOGOUT_URI           = "/ips/home/<USER>";
    const SECURELEVEL_AUTH_URI = "/static/cas-fe/?version=%s&sdk=%s&sid=%s&service=%s&extendAuth=true";
    const CHECKLOGIN_URI       = "/ips/home/<USER>";
    const ACCTOKEN_URI         = "/ips/oauth/accessToken";
    const COOKIE_KEY           = "ZYBIPSCAS";
    const REQ_METHOD_POST      = "POST";
    const REQ_METHOD_GET       = "GET";
    const ENV_PRODUCT          = "production";
    const ENV_TEST             = "test";
    const VERSION              = "2.0";
    const SDK                  = "odp";

    const ERR_CODE_ACCESS_DENY      = 1010; // 暂无系统权限，拒绝访问
    const ERR_CODE_SECURELEVEL_DENY = 1011; // 登录态安全等级过低，拒绝访问

    private static $conf = [
        self::ENV_TEST    => [
            "host" => "https://ips.suanshubang.cc",
            "svc"  => "ips",
        ],
        self::ENV_PRODUCT => [
            "host" => "https://ips.zuoyebang.cc",
            "svc"  => "ips",
        ],
    ];

    private $_host;
    private $_svc;
    private $_appId;
    private $_appSecret;

    public function __construct($appId, $appSecret, $path = "/", $isRootDomain = false)
    {
        $confKey = "";
        $env     = Hk_Util_Env::getRunEnv();
        switch ($env) {
            case "prod":
                $confKey = self::ENV_PRODUCT;
                break;
            case "tips":
                $confKey = self::ENV_PRODUCT;
                break;
            case "test":
                $confKey = self::ENV_TEST;
                break;
            default:
                $confKey = self::ENV_TEST;
        }

        $conf = $this->getConf($confKey);

        $this->_appId     = $appId;
        $this->_appSecret = $appSecret;
        $this->_host      = $conf["host"];
        $this->_svc       = $conf["svc"];

        if (false !== strpos($_SERVER["HTTP_HOST"], ".com")) {
            $this->_host = str_replace(".cc", ".com", $this->_host);
        }
    }

    private function getConf($confKey)
    {
        if (!isset(self::$conf[$confKey])) {
            $conf = self::$conf[self::ENV_TEST];
            Bd_Log::addNotice("ipsEnv", self::ENV_TEST);
            return $conf;
        }
        Bd_Log::addNotice("ipsEnv", $confKey);
        return self::$conf[$confKey];
    }

    private function getSecureLevelAuthUrl($url)
    {
        $authUrl = sprintf(self::SECURELEVEL_AUTH_URI, self::VERSION, self::SDK, $this->_appId, urlencode($url));
        return $this->_host . $authUrl;
    }

    public function logout($url)
    {
        header("Location:" . $this->getLogoutUrl($url));
    }

    public function getLogoutUrl($url)
    {
        $url         = !empty($url) ? $url : "";
        $logoutFeUrl = sprintf(self::LOGOUT_FE_URI, self::VERSION, self::SDK);
        $location    = urlencode(sprintf("%s&sid=%s&service=%s", $this->_host . $logoutFeUrl, $this->_appId, urlencode($url)));
        return $this->_host . self::LOGOUT_URI . "?location=" . $location;
    }

    public function getLoginUrl($url)
    {
        $loginUrl = sprintf(self::LOGIN_URI, self::VERSION, self::SDK);
        return sprintf("%s&sid=%s&service=%s", $this->_host . $loginUrl, $this->_appId, urlencode($url));
    }

    public function login($url)
    {
        header("Location:" . $this->getLoginUrl($url));
    }

    /**
     * 后续会废弃，请优先使用 getSession
     */
    public function validateAndSession($ticket = "", $url = "")
    {
        return $this->getSession($url);
    }

    public function getSession($url = "")
    {

        if (empty($_COOKIE) || empty($_COOKIE[self::COOKIE_KEY])) {
            Bd_Log::addNotice("getIpsErr", "cookie empty");
            return false;
        }

        $accessToken = $this->getAccessToken();
        if (empty($accessToken)) {
            Bd_Log::addNotice("getIpsErr", "get accesstoken err");
            return false;
        }

        $data       = array(
            "appId"       => $this->_appId,
            "sessionId"   => $_COOKIE[self::COOKIE_KEY],
            "accessToken" => $accessToken
        );
        $sessionUrl = sprintf(self::CHECKLOGIN_URI, self::VERSION, self::SDK);

        $resp = $this->ral($sessionUrl, self::REQ_METHOD_POST, $data);
        if ($resp !== false) {
            $ret = json_decode($resp, true);
            if (isset($ret["errNo"]) && $ret["errNo"] === 0) {
                return $ret["data"];
            } elseif ($ret["errNo"] == self::ERR_CODE_SECURELEVEL_DENY) {
                $response["authUrl"] = $this->getSecureLevelAuthUrl($url);
                Bd_Log::addNotice("getIpsErr", "need secure auth");
                return $response;
            } elseif ($ret["errNo"] == self::ERR_CODE_ACCESS_DENY) {
                $response["accessDeny"] = 1;
                Bd_Log::addNotice("getIpsErr", "need open access permission");
                return $response;
            } else {
                Bd_Log::addNotice("getIpsErr", "other err");
                Bd_Log::warning("ips get session error, resp=" . $resp);
                return false;
            }
        }
        Bd_Log::warning("ips getSession error, request failed");
        return false;
    }

    private function getAccessToken()
    {
        $data = [
            "appId"     => $this->_appId,
            "appSecret" => $this->_appSecret,
        ];
        $resp = $this->ral(self::ACCTOKEN_URI, self::REQ_METHOD_POST, $data);
        if ($resp !== false) {
            $ret = json_decode($resp, true);
            if (isset($ret["errNo"]) && $ret["errNo"] === 0 && !empty($ret["data"]["accessToken"])) {
                return strval($ret["data"]["accessToken"]);
            } else {
                Bd_Log::warning("ips get accessToken error, resp=" . $resp);
            }
        }
        Bd_Log::warning("ips getAccessToken error, request failed");
        return false;
    }

    private function ral($uri, $method, $params = [])
    {
        $header = [
            "pathinfo" => $uri,
        ];

        $ret = ral($this->_svc, $method, $params, "", $header);

        if ($ret === false) {
            Bd_Log::warning("ips request failed, result is false, uri=" . $uri . ", params=" . json_encode($params));
            return false;
        }
        if ($ret === "") {
            Bd_Log::warning("ips request failed, result is nil, uri=" . $uri . ", params=" . json_encode($params));
            return false;
        }

        return $ret;
    }
}
