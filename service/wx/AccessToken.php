<?php
/**
 * @befie   微信获取AccessToken
 * @file    service/wx/AccessToken.php
 * <AUTHOR>
 * @version 1.0
 * @date    2019-09-19
 */
class Lxjxlib_Service_Wx_AccessToken
{
    private $_redis;
    private $_app;
    private $_appid;
    private $_appsecret;

    const EXPIRE_TIME = Lxjxlib_Const_Common::EXPIRE_HOUR;

    public function __construct($app)
    {
        $this->_redis = Hk_Service_RedisClient::getInstance(Lxjxlib_Const_Cache::REDIS_NAME_LXJX);
        $conf = Lxjxlib_Service_Wx_Conf::getAppConf($app);
        $this->_appid = $conf['appid'];
        $this->_appsecret = $conf['appsecret'];
        $this->_app = $app;
    }

    private function _getCacheKey()
    {
        return sprintf(Lxjxlib_Const_Cache::RK_WX_ACCESSTOKEN, $this->_appid, $this->_appsecret);
    }

    private function _getLockKey()
    {
        return sprintf(Lxjxlib_Const_Cache::WX_ACCESSTOKEN_LOCK, $this->_appid, $this->_appsecret);
    }

    /**
     * 从微信服务器获取微信ACCESS_TOKEN
     * @return arraym|bool|mixed|null|string
     */
    private function _getAccessToken()
    {
        $url = 'https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential'
            .'&appid='.$this->_appid
            .'&secret='.$this->_appsecret;
        $resp = Lxjxlib_Util_Curl::callRequest($url);
        if (!isset($resp['access_token']) || empty($resp['access_token'])
            || !isset($resp['expires_in']) || empty($resp['expires_in'])) {
            Bd_Log::warning('getWxAccessTokenFail');
            return '';
        }

        $this->_redis->setEx($this->_getCacheKey(), $resp['expires_in'] - 300, $resp['access_token']);
        return $resp['access_token'];
    }

    /**
     * 从微信服务器获取企业微信ACCESS_TOKEN
     * @return string
     */
    private function _getQwAccessToken()
    {
        $url = 'https://qyapi.weixin.qq.com/cgi-bin/gettoken'
            .'?corpid='.$this->_appid
            .'&corpsecret='.$this->_appsecret;
        $resp = Lxjxlib_Util_Curl::callRequest($url);
        if (!isset($resp['access_token']) || empty($resp['access_token'])
            || !isset($resp['expires_in']) || empty($resp['expires_in'])) {
            Bd_Log::warning('getWxAccessTokenFail');
            return '';
        }

        $this->_redis->setEx($this->_getCacheKey(), $resp['expires_in'] - 300, $resp['access_token']);
        return $resp['access_token'];
    }

    /**
     * 检测微信ACCESS_TOKEN是否过期
     * @return bool|mixed|NULL
     */
    private function _checkAccessToken()
    {
        // 获取access_token。是上面的获取方法获取到后存起来的。
        $accessToken = $this->_redis->get($this->_getCacheKey());
        if (empty($accessToken)) {
            return null;
        }

        return $accessToken;
    }

    /**
     * 获取微信小程序Access_Token
     * @param bool $refresh
     * @return mixed
     */
    public function getWxaAccessToken($refresh = false)
    {
        $accessToken = null;

        if (!in_array($this->_app, Lxjxlib_Service_Wx_Const::$WX_XCX_NAME_LIST)) {
            return $accessToken;
        }

        if (Lxjxlib_Util_Tools::isTestEnv()) {
            $url = 'https://ol.zybang.com/olactivity/wx/accesstoken?app='.$this->_app;
            $result = Lxjxlib_Util_Curl::callRequest($url);
            $accessToken = isset($result['data']['accessToken']) ? $result['data']['accessToken'] : null;
        } else {
            $accessToken = $this->_checkAccessToken();
            if ($refresh || empty($accessToken)) {
                $accessToken = $this->_getAccessTokenByLock();
            }
        }

        return $accessToken;
    }

    /**
     * 获取微信服务号Access_Token
     * @param bool $refresh
     * @return mixed
     */
    public function getWxAccessToken($refresh = false)
    {
        $accessToken = '';

        if (!in_array($this->_app, Lxjxlib_Service_Wx_Const::$WX_FWH_NAME_LIST)) {
            return $accessToken;
        }

        $accessToken = $this->_checkAccessToken();
        if ($refresh || empty($accessToken)) {
            $accessToken = $this->_getAccessTokenByLock();
        }

        return $accessToken;
    }

    /**
     * 获取企微Access_Token
     * @param bool $refresh
     * @return mixed
     */
    public function getQwAccessToken($refresh = false)
    {
        $accessToken = null;

        if (!in_array($this->_app, Lxjxlib_Service_Wx_Const::$WX_QW_NAME_LIST)) {
            return $accessToken;
        }

        $accessToken = $this->_checkAccessToken();
        if ($refresh || empty($accessToken)) {
            $accessToken = $this->_getQwAccessToken();
        }

        return $accessToken;
    }

    /**
     * 使用锁 获取access_token,防止重复获取相互覆盖
     */
    private function _getAccessTokenByLock()
    {
        $accessToken = null;

        $i = 5;
        while ($i > 0) {
            //获取锁
            $redisLock = new Lxjxlib_Service_RedisLock();
            $lock      = $redisLock->getLock($this->_getLockKey(), 2000, 0, 200);
            if ($lock) {
                //获取到锁后，请求微信
                $accessToken = $this->_getAccessToken();
            } else {
                //没有获取到锁，查找缓存
                $accessToken = $this->_checkAccessToken();
            }
            if (!empty($accessToken)) {
                break;
            }
            $i--;
        }
        Bd_Log::notice('_getAccessTokenByLock-get-accesstoken-'.$accessToken);
        return $accessToken;
    }
}
