<?php

/**
 * @befie   微信服务config
 * @file    service/wx/Conf.php
 * <AUTHOR>
 * @version 1.0
 * @date    2019-09-19
 */
class Lxjxlib_Service_Wx_Conf
{
    private static $wxappConf = [];

    public static function getAppConf($app)
    {
        if (isset(self::$wxappConf[$app])) {
            return self::$wxappConf[$app];
        }

        self::$wxappConf[$app] = Lxjxlib_Util_Conf::getEnvConf('wxconfig/'.$app);

        return self::$wxappConf[$app];
    }

    public static function getAppId($app)
    {
        self::getAppConf($app);

        return self::$wxappConf[$app]['appid'];
    }

    public static function getAppSecret($app)
    {
        self::getAppConf($app);

        return self::$wxappConf[$app]['appsecret'];
    }
}
