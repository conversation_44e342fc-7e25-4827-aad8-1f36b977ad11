<?php

/**
 * @befie   微信小程序用户认证相关api
 * @file    service/wx/xcx/ApiAuth.php
 * <AUTHOR>
 * @version 1.0
 * @date    2019-09-19
 */
class Lxjxlib_Service_Wx_Xcx_ApiAuth extends Lxjxlib_Service_Wx_Common_ApiBase
{
    public function __construct($app)
    {
        parent::__construct($app);
    }

    /**
     * 登录凭证校验
     * @man https://developers.weixin.qq.com/miniprogram/dev/api-backend/open-api/login/auth.code2Session.html
     * @param $jsCode
     * @return array|null
     */
    public function code2Session($jsCode)
    {
        $url    = 'https://api.weixin.qq.com/sns/jscode2session';
        $params = [
            'appid'      => $this->_appid,
            'secret'     => $this->_appsecret,
            'js_code'    => $jsCode,
            'grant_type' => 'authorization_code',
        ];
        $resp   = $this->callApi($url, $params);
        if (empty($resp)) {
            return null;
        }

        $result = [
            'appId'       => $this->_appid,
            'openid'      => $resp['openid'],
            'session_key' => $resp['session_key'],
            'unionid'     => isset($resp['unionid']) ? $resp['unionid'] : '',
        ];
        return $result;
    }

    /**
     * 解密用户信息
     * @param $sessionKey
     * @param $encryptedData
     * @param $iv
     * @return array|null
     */
    public function decryptedData($sessionKey, $encryptedData, $iv)
    {
        if (strlen($sessionKey) != 24 || strlen($iv) != 24) {
            return null;
        }

        $aesKey    = base64_decode($sessionKey);
        $aesIV     = base64_decode($iv);
        $aesCipher = base64_decode($encryptedData);
        $result    = openssl_decrypt($aesCipher, 'AES-128-CBC', $aesKey, 1, $aesIV);
        $decData   = json_decode($result, true);
        if (empty($decData) || !isset($decData['watermark'])
            || $decData['watermark']['appid'] != $this->_appid) {
            return null;
        }

        return $decData;
    }
}