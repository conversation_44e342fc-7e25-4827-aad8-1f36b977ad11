<?php

/**
 * @befie   微信小程序客服api
 * @file    service/wx/xcx/ApiCustom.php
 * <AUTHOR>
 * @version 1.0
 * @date    2019-09-19
 */
class Lxjxlib_Service_Wx_Xcx_ApiCustom extends Lxjxlib_Service_Wx_Common_ApiBase
{
    public function __construct($app)
    {
        parent::__construct($app);
        $this->getAccessToken();
    }

    /**
     * 发送客服消息给用户
     * @see https://developers.weixin.qq.com/miniprogram/dev/api-backend/open-api/customer-message/customerServiceMessage.send.html
     * @param $params
     * @return bool
     */
    public function sendCustomerMessage($params)
    {
        if (null === $this->_accessToken) {
            return false;
        }

        $url  = 'https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token=' . $this->_accessToken;
        $resp = $this->callApi($url, $params, 'JSON');
        return false === $resp ? false : true;
    }
}