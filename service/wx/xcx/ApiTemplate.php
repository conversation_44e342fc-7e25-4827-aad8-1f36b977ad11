<?php

/**
 * @befie   微信小程序模板消息api
 * @file    service/wx/xcx/ApiTemplate.php
 * <AUTHOR>
 * @version 1.0
 * @date    2019-09-19
 */
class Lxjxlib_Service_Wx_Xcx_ApiTemplate extends Lxjxlib_Service_Wx_Common_ApiBase
{
    public function __construct($app)
    {
        parent::__construct($app);
        $this->getAccessToken();
    }

    /**
     * 向用户推送模板消息
     * @see https://developers.weixin.qq.com/miniprogram/dev/api-backend/templateMessage.send.html
     * @param $params
     * @return bool
     */
    public function sendTemplateMessage($params)
    {
        if (null === $this->_accessToken) {
            return false;
        }

        if (isset($params['isLog'])) {
            $isLog = $params['isLog'];
            unset($params['isLog']);
        } else {
            $isLog = true;
        }

        $url  = 'https://api.weixin.qq.com/cgi-bin/message/wxopen/template/send?access_token=' . $this->_accessToken;
        $resp = $this->callApi($url, $params, 'JSON', 2, $isLog);
        return false === $resp ? false : true;
    }
}