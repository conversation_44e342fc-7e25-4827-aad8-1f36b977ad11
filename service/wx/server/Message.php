<?php

/**
 * 消息管理
 * Created by Phpstorm
 * User: ha<PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * Date: 2021/4/27
 * Time: 5:16 下午
 */
class Lxjxlib_Service_Wx_Server_Message
{
    //客服消息类型
    const CUSTOM_TYPE_MAP = [
        1 => 'text',
        2 => 'image',
        3 => 'voice',
        4 => 'video',
        5 => 'music',
        6 => 'news',
        7 => 'mpnews',
        8 => 'msgmenu',
        9 => 'miniprogrampage',
    ];

    const EXPIRE_TIME = 300; //缓存5分钟

    //获取模版列表
    const GET_TEMPLATE_URL = 'https://api.weixin.qq.com/cgi-bin/template/get_all_private_template?access_token=ACCESS_TOKEN';

    //发送模版消息
    const SEND_TEMPLATE_URL = 'https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=ACCESS_TOKEN';

    //发送客服消息
    const SEND_CUSTOM_URL = 'https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token=ACCESS_TOKEN';

    //获取订阅模版列表
    const GET_SUBSCRIBE_URL = 'https://api.weixin.qq.com/wxaapi/newtmpl/gettemplate?access_token=ACCESS_TOKEN';

    //获取订阅模版中关键词
    const GET_SUBSCRIBE_KEYWORD_URL = 'https://api.weixin.qq.com/wxaapi/newtmpl/getpubtemplatekeywords?access_token=ACCESS_TOKEN';

    //发送订阅消息
    const SEND_SUBSCRIBE_URL = 'https://api.weixin.qq.com/cgi-bin/message/subscribe/bizsend?access_token=ACCESS_TOKEN';

    /**
     * 获取模版列表
     * @return array
     * @throws Exception
     */
    private function getAllTemplate()
    {
        $key = $this->getTemplateListCacheKey();

        $redisObj = Hk_Service_RedisClient::getInstance(Lxjxlib_Const_Cache::REDIS_NAME_LXJX);
        $rst = $redisObj->get($key);

        if ($rst) {
            $rst = json_decode($rst, true);
        } else {
            $rst = Lxjxlib_Service_Wx_Server::callApi(self::GET_TEMPLATE_URL);
            $cacheRst = $redisObj->set($key, json_encode($rst), self::EXPIRE_TIME);
            if (!$cacheRst) {
                Bd_Log::warning('微信服务缓存模版列表失败');
            }
        }

        $data = $rst['template_list'];

        foreach ($data as &$item) {
            $item['example'] = explode("\r\n", $item['example']);

            preg_match_all("/\{\{(.*?)\./", $item['content'], $matches);

            $item['content'] = $matches[1];
        }

        return $data;
    }

    //获取模版列表缓存key
    private function getTemplateListCacheKey()
    {
        return sprintf(Lxjxlib_Const_Cache::WX_TEMPLATE_LIST, Lxjxlib_Service_Wx_Server::getAppId());
    }

    //获取订阅模版列表缓存key
    private function getSubscribeListCacheKey()
    {
        return sprintf(Lxjxlib_Const_Cache::WX_SUBSCRIBE_LIST, Lxjxlib_Service_Wx_Server::getAppId());
    }

    /**
     * 根据模版id获取此模版样例
     * @param string $templateId
     * @return array
     * @throws Lxjxlib_Const_Exception
     */
    private function getOneTemplateExample(string $templateId)
    {
        $list = $this->getAllTemplate();
        if (empty($list)) {
            return [];
        }

        $list = array_column($list, null, 'template_id');
        if (empty($list[$templateId])) {
            throw new Lxjxlib_Const_Exception(Lxjxlib_Const_ExceptionCodes::WX_SERVER_TEMPLATE_NO_EXISTS);
        }

        return $list[$templateId];
    }

    /**
     * 发送模版消息
     * @param string $templateId 模版id
     * @param string $toUser 发送给哪个用户，用户的openid
     * @param array $data 发送模版消息的内容
     * @param string $url 非必传，模版跳转的h5链接
     * @param string $pagePath 非必传，模版跳转的小程序页面路径
     * @param string $appId 非必传，模版跳转的小程序appid，不填默认好课帮老师xcx
     * @return array
     * @throws Exception
     */
    private function sendTemplateMsg(string $templateId, string $toUser, array $data, string $url = '', string $pagePath = '', string $appId = Lxjxlib_Service_Wx_Server::APPID)
    {
        $postData = [
            'touser' => $toUser,
            'template_id' => $templateId,
            'data' => $data,
        ];

        if (!empty($url)) {
            $postData['url'] = $url;
        }

        if (!empty($pagePath)) {
            $postData['miniprogram'] = [
                'appid' => $appId,
                'pagepath' => $pagePath
            ];
        }

        return Lxjxlib_Service_Wx_Server::callApi(self::SEND_TEMPLATE_URL, $postData, 'POST', 'JSON');
    }

    /**
     * 发送客服消息，在触发特定动作（用户发送消息、点击自定义菜单推事件、关注公众号、扫描二维码、支付成功）时，24小时内，可推送最多20条
     * @param string $toUser
     * @param int $type
     * @param $data
     * @return array
     * @throws Lxjxlib_Const_Exception
     */
    private function sendCustomMsg(string $toUser, int $type, $data)
    {
        if (!isset(self::CUSTOM_TYPE_MAP[$type])) {
            throw new Lxjxlib_Const_Exception(Lxjxlib_Const_ExceptionCodes::WX_SERVER_TYPE_NO_EXISTS);
        }

        $type = self::CUSTOM_TYPE_MAP[$type];

        $postData = [
            'touser' => $toUser,
            'msgtype' => $type,
        ];

        if ($type == 'text') {
            $postData[$type] = [
                'content' => $data
            ];
        } else if ($type == 'image' || $type == 'voice' || $type == 'mpnews') {
            $postData[$type] = [
                'media_id' => $data
            ];
        } else if ($type == 'card_id') {
            $postData[$type] = [
                'card_id' => $data
            ];
        } else if ($type == 'miniprogrampage') {
            $postData[$type] = $data;
            if (empty($postData[$type]['appid'])) {
                $postData[$type]['appid'] = Lxjxlib_Service_Wx_Server::APPID;
            }
        } else {
            $postData[$type] = $data;
        }

        return Lxjxlib_Service_Wx_Server::callApi(self::SEND_CUSTOM_URL, $postData, 'POST', 'JSON');
    }

    /**
     * 获取订阅模版列表
     * @return array
     * @throws Exception
     */
    private function getSubscribeList()
    {
        $key = $this->getSubscribeListCacheKey();

        $redisObj = Hk_Service_RedisClient::getInstance(Lxjxlib_Const_Cache::REDIS_NAME_LXJX);
        $rst = $redisObj->get($key);

        if ($rst) {
            $rst = json_decode($rst, true);
        } else {
            $rst = Lxjxlib_Service_Wx_Server::callApi(self::GET_SUBSCRIBE_URL);
            $cacheRst = $redisObj->set($key, json_encode($rst), self::EXPIRE_TIME);
            if (!$cacheRst) {
                Bd_Log::warning('微信服务缓存订阅列表失败');
            }
        }

        $data = $rst['data'];

        foreach ($data as &$item) {
            $item['example'] = explode("\n", trim($item['example'], "\n"));

            preg_match_all("/\{\{(.*?)\./", $item['content'], $matches);

            $item['content'] = $matches[1];
        }

        return $data;
    }

    /**
     * 获取订阅模版关键词
     * @param string $tid
     * @return array
     * @throws Exception
     */
    private function getTemplateKeyWords(string $tid)
    {
        $data = [
            'tid' => $tid
        ];
        return Lxjxlib_Service_Wx_Server::callApi(self::GET_SUBSCRIBE_KEYWORD_URL, $data);
    }

    /**
     * 发送订阅消息
     * @param string $templateId
     * @param string $toUser
     * @param array $data
     * @param string $url
     * @param string $pagePath
     * @param string $appId
     * @return array
     * @throws Exception
     */
    private function sendSubscribeMsg(string $templateId, string $toUser, array $data, string $url = '', string $pagePath = '', string $appId = Lxjxlib_Service_Wx_Server::APPID)
    {
        $postData = [
            'touser' => $toUser,
            'template_id' => $templateId,
            'data' => [],
        ];

        foreach ($data as $key => $val) {
            $postData['data'][$key] = [
                'value' => $val
            ];
        }

        if (!empty($url)) {
            $postData['page'] = $url;
        }

        if (!empty($pagePath)) {
            $postData['miniprogram'] = [
                'appid' => $appId,
                'pagepath' => $pagePath
            ];
        }

        return Lxjxlib_Service_Wx_Server::callApi(self::SEND_SUBSCRIBE_URL, $postData, 'POST', 'JSON');
    }

}
