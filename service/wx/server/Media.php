<?php

/**
 * 素材管理
 * Created by Phpstorm
 * User: ha<PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * Date: 2021/4/25
 * Time: 7:33 下午
 */
class Lxjxlib_Service_Wx_Server_Media
{
    //类型映射
    const TYPE_MAP = [
        1 => 'image',
        2 => 'voice',
        3 => 'video',
        4 => 'thumb',
    ];

    //上传临时素材
    const TEMP_MEDIA_URL = 'https://api.weixin.qq.com/cgi-bin/media/upload?access_token=ACCESS_TOKEN&type=%s';

    //获取临时素材
    const GET_TEMP_MEDIA_URL = 'https://api.weixin.qq.com/cgi-bin/media/get?access_token=ACCESS_TOKEN&media_id=%s';

    //上传永久素材
    const FOREVER_MEDIA_URL = 'https://api.weixin.qq.com/cgi-bin/material/add_material?access_token=ACCESS_TOKEN&type=%s';

    //获取永久素材
    const GET_FOREVER_MEDIA_URL = 'https://api.weixin.qq.com/cgi-bin/material/get_material?access_token=ACCESS_TOKEN';

    //删除永久素材
    const DEL_FOREVER_MEDIA_URL = 'https://api.weixin.qq.com/cgi-bin/material/del_material?access_token=ACCESS_TOKEN';

    /**
     * 上传临时素材
     * 临时素材media_id三天失效
     * image->10m,支持PNG\JPEG\JPG\GIF;
     * voice->2m,不超过60s，支持AMR\MP3
     * video->10m,支持MP4
     * thumb->64k,支持JPG
     * @param string $path 上传文件的绝对路径
     * @param int $type 上传类型
     * @return array ['type', 'media_id', 'created_at', 'expire_time']
     * @throws Lxjxlib_Const_Exception
     */
    private function addTempMedia(string $path, int $type)
    {
        if (empty($path) || !isset(self::TYPE_MAP[$type])) {
            throw new Lxjxlib_Const_Exception(Lxjxlib_Const_ExceptionCodes::WX_SERVER_TYPE_NO_EXISTS);
        }

        $url = sprintf(self::TEMP_MEDIA_URL, self::TYPE_MAP[$type]);
        $data = [
            'media' => new \CURLFile(realpath($path))
        ];
        $rst = Lxjxlib_Service_Wx_Server::callApi($url, $data, 'POST');
        $rst['expire_time'] = $rst['created_at'] + 3*24*7200;
        return $rst;
    }

    /**
     * 获取临时素材
     * @param $mediaId
     * @return array|string 视频返回['video_url'],其他返回base64编码
     * @throws Exception
     */
    private function getTempMedia(string $mediaId)
    {
        $url = sprintf(self::GET_TEMP_MEDIA_URL, $mediaId);
        $rst = Lxjxlib_Service_Wx_Server::callApi($url, '', 'GET', '', false);

        if (empty(json_decode($rst, true))) {
            return base64_encode($rst);
        } else {
            return json_decode($rst, true);
        }
    }

    /**
     * 上传永久素材
     * @param string $path
     * @param int $type
     * @param string $title type=3时必传，视频标题
     * @param string $description type=3时必传，视频描述
     * @return array ['media_id', 'url']
     * @throws Lxjxlib_Const_Exception
     */
    private function addForeverMedia(string $path, int $type, string $title = '', string $description = '')
    {
        if (empty($path) || !isset(self::TYPE_MAP[$type]) || ($type === 3 && empty($title))) {
            throw new Lxjxlib_Const_Exception(Lxjxlib_Const_ExceptionCodes::PARAM_ERROR);
        }

        $url = sprintf(self::FOREVER_MEDIA_URL, self::TYPE_MAP[$type]);

        $data = [
            'media' => new \CURLFile(realpath($path))
        ];
        if ($type === 3) {
            $data['description'] = json_encode([
                'title' => $title,
                'introduction' => $description
            ]);
        }
        return Lxjxlib_Service_Wx_Server::callApi($url, $data, 'POST');
    }

    /**
     * 获取永久素材
     * @param string $mediaId
     * @return array|string 视频['title','description','down_url']，图文素材array，其他类型为base64
     * @throws Exception
     */
    private function getForeverMedia(string $mediaId)
    {
        $data = [
            'media_id' => $mediaId
        ];
        $rst = Lxjxlib_Service_Wx_Server::callApi(self::GET_FOREVER_MEDIA_URL, $data, 'POST', 'JSON', false);

        if (empty(json_decode($rst, true))) {
            return base64_encode($rst);
        } else {
            return json_decode($rst, true);
        }
    }

    /**
     * 删除永久素材
     * @param $mediaId
     * @return array ['errcode','errmsg']
     * @throws Exception
     */
    private function delForeverMedia(string $mediaId)
    {
        $data = [
            'media_id' => $mediaId
        ];
        return Lxjxlib_Service_Wx_Server::callApi(self::DEL_FOREVER_MEDIA_URL, $data, 'POST', 'JSON');
    }

}
