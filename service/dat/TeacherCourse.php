<?php
/**
 * Created by PhpStorm.
 * User: wangfeng<PERSON>@zuoyebang.com
 * Date: 2018/9/4
 * Time: 上午11:16
 */

class Zb_Service_Dat_TeacherCourse
{
    private static $service     = 'zbcore_dat';
    private static $serviceUri  = '/zbcore/api/api';
    private static $module      = 'dat';
    private static $entity      = 'teacherCourse';



    /**
     * 根据查询条件获取教师课程数据
     * @param array $arrConds
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @param array $options
     * @return array
     */

    public static function getTCListByConds($arrConds, $arrFields, $offset = 0, $limit = 20, array $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getTCListByConds');
        $arrParams = array(
            'arrConds'     => $arrConds,
            'arrFields'      => $arrFields,
            'offset'         => $offset,
            'limit'          => $limit,

        );
        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }


    /**
     * 根据主讲老师ID获取数据
     * @param $teacherUid
     * @param $arrFields
     * @param int $offset
     * @param int $limit
     * @param array $timeRange
     * @param int $courseType
     * @param int $status
     * @param array $options
     * @return array
     */
    public static function getTCListByTeacherUid($teacherUid, $arrFields, $offset = 0, $limit = 20, $timeRange = [],  $courseType = -1 , $status = -1 , array $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getTCListByTeacherUid');
        $arrParams = array(
            'teacherUid'     => $teacherUid,
            'arrFields'      => $arrFields,
            'offset'         => $offset,
            'limit'          => $limit,
            'timeRange'      => $timeRange,
            'courseType'     => $courseType,
            'status'         => $status,
        );
        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 根据课程Id获取数据
     * @param $courseId
     * @param $arrFields
     * @param array $options
     * @return array
     */
    public static function getTCListByCourseId($courseId, $arrFields, array $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getTCListByCourseId');
        $arrParams = array(
            'courseId'     => $courseId,
            'arrFields'      => $arrFields,
        );
        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 根据课程Id获取数据
     * @param $courseIds
     * @param $arrFields
     * @param array $options
     * @return array
     */
    public static function getTCListByCourseIds($courseIds, $arrFields, array $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getTCListByCourseIds');
        $arrParams = array(
            'courseIds'      => $courseIds,
            'arrFields'      => $arrFields,
        );
        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }
}