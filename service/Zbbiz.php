<?php

class Zb_Service_Zbbiz {
    /**
     * 获取推荐信息
     * @param $skuIdGroup
     * @param $studentUid
     * @param $nowTimeStamp
     * @param $appId
     * @param $os
     * @param $pageType
     * @return bool
     */
    public static function getRecommendInfo($skuIdGroup, $studentUid, $nowTimeStamp, $appId = '', $os = '', $pageType = ''){
        $arrHeader = array(
            'pathinfo' => '/zbbiz/api/getrecommendbiz',
        );
        $arrParams = array(
            'uid'  => $studentUid,
            'skuIdList' => $skuIdGroup,
            'nowTime'   => $nowTimeStamp,
            'appId'     => $appId,
            'os'        => $os,
            'pageType'  => $pageType,
        );
        return self::requestZbbiz($arrParams, $arrHeader);
    }
    /**
     *  获取商品single策略（主要面向列表页、详情页）
     * @param $skuIdList
     * @param $studentUid
     * @param $nowTimeStamp
     * @param $appId
     * @param $os
     * @param $pageType
     * @return bool
     */
    public static function getSignalHitBiz($skuIdList, $studentUid,  $nowTimeStamp, $appId = '', $os = '', $pageType = '', $service = 'zyb-http'){
        $arrHeader = array(
            'pathinfo' => '/zbbiz/api/getsignalbizlist',
        );
        $arrParams = array(
            'uid'  => $studentUid,
            'skuIdList' => $skuIdList,
            'nowTime'   => $nowTimeStamp,
            'appId'     => $appId,
            'os'        => $os,
            'pageType'  => $pageType,
        );
        return self::requestZbbiz($arrParams, $arrHeader, $service);
    }

    /**
     * 获取商品group策略（主要面向选课单、详情页-推荐组合）
     * @param $skuIdList
     * @param $studentUid
     * @param $nowTimeStamp
     * @param $appId
     * @param $os
     * @param $pageType
     * @param $needLog
     * @return bool
     */
    public static function getHitBizList($skuIdList, $studentUid,  $nowTimeStamp, $appId = '', $os = '', $pageType = '', $needLog = 0){
        if(empty($skuIdList)) {
            Bd_Log::warning('notice empty skuidlist use hitbizlist');
        }
        $arrHeader = array(
            'pathinfo' => '/zbbiz/api/gethitbizlist',
        );
        $arrParams = array(
            'uid'  => $studentUid,
            'skuIdList' => $skuIdList,
            'nowTime'   => $nowTimeStamp,
            'appId'     => $appId,
            'os'        => $os,
            'pageType'  => $pageType,
        );
        return self::requestZbbiz($arrParams, $arrHeader, 'zyb-http', $needLog);
    }

        /**
     * @param $arrParams
     * @param $arrHeader
     * @return bool
     */
    private static function requestZbbiz($arrParams, $arrHeader, $service = 'zyb-http', $needLog = 0){
        return Zb_Util_ZbServiceTools::requestApi('zbbiz', 'post', $arrParams, $arrHeader, $needLog);
    }
    /**
     *每日报名人数纪录
     * */
    public function incrSpecialOneDaySaleCnt($nowTime, $courseId){
        $key = $this->getSpecialOneDayCacheKey($nowTime, $courseId);
        $redisConf = Bd_Conf::getConf("/hk/redis/zhiboke");
        $objCache  = new Hk_Service_Redis($redisConf['service']);
        $studentFlag = $objCache->incr($key);
    }
    /**
     *获取每日报名人数
     * */
    public function getSpecialOneDaySaleCnt($nowTime, $courseId) {
        $key = $this->getSpecialOneDayCacheKey($nowTime, $courseId);
        $redisConf = Bd_Conf::getConf("/hk/redis/zhiboke");
        $objCache  = new Hk_Service_Redis($redisConf['service']);
        $cnt = $objCache->get($key);
        return $cnt;
    }
    public function getSpecialOneDayCacheKey($nowTime, $courseId){
        $key = 'startTime_%s_endTime_%s_courseId_%s';
        $hTime = date('H', $nowTime);
        $dayTime = date('Y-m-d', $nowTime);
        $oriTime = $dayTime. ' 10:00:00';
        $oriTime = strtotime($oriTime);
        $startTime = 0;
        $endTime = 0;
        if($hTime >= 10) {
            $startTime = $oriTime;
            $endTime   = $oriTime + 24*3600;
        } else {
            $startTime = $oriTime - 24*3600;
            $endTime = $oriTime;
        }
        $key = sprintf($key, $startTime, $endTime, $groupId);
        return md5($key);
    }


    /**
     * 根据spuid及用户id 推荐skuid
     * @param $spuId
     * @param $uid
     * @return array('skuId' => 12321)
     */
    public static function getRecommendSkuIdBySpuId($spuId, $uid){
        $arrHeader = array(
            'pathinfo' => '/zbbiz/api/getrecommendskuidbyspuid',
        );
        $arrParams = array(
            'uid'  => $uid,
            'spuId' => $spuId,
        );
        return self::requestZbbiz($arrParams, $arrHeader);
    }

    /**
     * 获取推荐信息
     * @param $skuIdGroup
     * @param $studentUid
     * @param $nowTimeStamp
     * @param $appId
     * @param $os
     * @param $pageType
     * @return bool
     */
    public static function getRecommendInfoBatch($skuIdGroup, $studentUid, $nowTimeStamp, $appId = '', $os = '', $pageType = ''){
        $arrHeader = array(
            'pathinfo' => '/zbbiz/api/getrecommendbizbatch',
        );
        $arrParams = array(
            'uid'  => $studentUid,
            'skuIdList' => $skuIdGroup,
            'nowTime'   => $nowTimeStamp,
            'appId'     => $appId,
            'os'        => $os,
            'pageType'  => $pageType,
        );
        return self::requestZbbiz($arrParams, $arrHeader);
    }

}
