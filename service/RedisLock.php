<?php

/**
 * redis锁
 * @file RedisLock.php
 * <AUTHOR>
 * @version 1.0
 * @date 2020/10/28
 */
class Lxjxlib_Service_RedisLock
{
    const LOCK_PREFIX = 'zyblxjx_redislock_';  //缓存前缀

    const TIME_MILLISECOND = 1000;  //毫秒和微妙的进制

    protected $redisObj;  //redis实例

    public function __construct()
    {
        $this->redisObj = Hk_Service_RedisClient::getInstance(Lxjxlib_Const_Cache::REDIS_NAME_LXJX);
    }

    protected function _getLockCacheKey($lockKey)
    {
        return self::LOCK_PREFIX . $lockKey;
    }

    /**
     * 获取锁
     * @param string $lockKey 唯一key
     * @param int $lockTime 锁住时间（单位：ms）
     * @param int $timeOut 获取锁的超时时间（单位：ms）
     * @param int $intervalTime 获取锁的间隔时间（单位：ms）
     * @return bool
     */
    public function getLock($lockKey, $lockTime, $timeOut, $intervalTime)
    {
        $spendtime = 0;
        $cacheKey  = $this->_getLockCacheKey($lockKey);
        $lock      = false;
        while (true) {
            //获取锁超时
            if ($spendtime > $timeOut) {
                Bd_Log::addNotice('lxjx_redislock_getlock_timeout', $lockKey);
                break;
            }

            // stoneD不支持毫秒
            $secondTime = 1;
            if ($lockTime > 1000) {
                $secondTime = $lockTime / 1000;
            }
            $ret = $this->redisObj->set($cacheKey, 1, ['EX' => $secondTime, 'NX']);

            //成功加锁
            if ($ret) {
                $lock = true;
                break;
            } elseif (empty($timeOut)) {  //未设置超时时间，只获取一次锁
                break;
            }
            //等待。。。
            usleep(self::TIME_MILLISECOND * $intervalTime);
            $spendtime += $intervalTime;
        }
        Bd_Log::addNotice('lxjx_redislock_getlock[' . $lockKey . ']', $ret);

        return $lock;
    }

    /**
     * 释放锁
     * @param $lockKey
     * @return bool
     */
    public function releaseLock($lockKey)
    {
        $cacheKey = $this->_getLockCacheKey($lockKey);
        $ret      = $this->redisObj->del($cacheKey);
        Bd_Log::addNotice('lxjx_redislock_releaselock[' . $lockKey . ']', $ret);

        return true;
    }
}