<?php

/**
 * @desc    调用ip2地址
 * @file    Client.php
 * <AUTHOR>
 * @date    2021-07-29 15:53
 */
class Lxjxlib_Service_Location_Client
{
    protected $request;
    protected $corpId;
    const SUCCESS_CODE = 0;

    public function __construct($corpId = null)
    {
        $this->request = new Lxjxlib_Service_Location_Request();

    }


    /**
     * 通过ip获取省市区名字
     * @param $ip
     * @return array|bool|mixed
     * @throws Hk_Util_Exception
     */
    public function ip2Location($ip)
    {
        $method = '/location/api/v2/ip';
        $params = [
            'ip' => $ip,
        ];

        $ret = $this->request->doRequest($method, $params);
        return $ret;
    }
}
