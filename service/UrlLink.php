<?php
/**
 * url链接转换
 * User: wang<PERSON><PERSON>@zuoyebang.com
 * Date: 2019/4/13
 * Time: 19:40
 */

class Hkzb_Service_UrlLink {
    /**
     * 获取http|https协议的url链接
     * @param $protocolUri 协议uri
     * @param $https 是否需要https
     * @param $logTrace 是否需要打出日志
     * @return string|bool
     */
    public static function getHttpUrl($protocolUri, $logTrace = true)
    {

        if (empty($protocolUri)) {
            Bd_Log::warning("empty protocolUri");
            return false;
        }

        $arrInput = array(
            'protocolUri' => $protocolUri,
            'context' => $_SERVER,
        );
        $uri = self::limitStream($arrInput);
        if ($uri) {
            bd_log::addNotice("fromHkzb",$uri);
            return $uri;
        }
        
        return self::getUri($arrInput, $logTrace);
    }

    private static function getUri($arrInput, $logTrace) {
        $ret = Hk_Service_Rpc::call('sparta', 'getUri', $arrInput,array(),$logTrace);
        if($ret == false || $ret['errno'] != 0) {
            Bd_Log::warning("Error:[Hk_Service_Rpc call sparta getUri error], arrInput=>".json_encode($arrInput).",ret=>".json_encode($ret));
            $uri = self::limitStream($arrInput,true);
            return $uri?$uri:'';
        }

        return strval($ret['data']['uri']);
    }

    /**
     * 限流模块，有些模块不走sparta
     * zyb:\/\/primary-homepage\/page\/homepage?hideStatus=1&tempBack=1&landscapeType=1&hideNav=1&loadingType=1&versionSwitch=1&courseId=341903#\/
     *interactive_class/index-54c54d5d-hycache.html?
     *  @param [array] $arrInput
     * @return void
     */
    private static function limitStream($arrInput,$passBLackList = false) {
       // $blackHouse = ["excitation","live-plugin-v2"];
        $blackHouse = [];
        $protocolUri = $arrInput['protocolUri'];
        $context = $arrInput['context'];
        $r = preg_match('/^[\w]+:\/\/(.*?)\/[\w]+\/([\w_-]+)(\?.*)?/', $protocolUri, $matches);
        if (!$r || count($matches) < 3) {
            Bd_log::warning("invalid protocolUri[$protocolUri]");
            return NULL;
        }
        $module = $matches[1];

        if (!in_array($module,$blackHouse) && !$passBLackList) {
            return false;
        }

        $page = $matches[2];
        $query = isset($matches[3])? $matches[3] : '';
        $html = "";
        $protocol = 'https://';
        $host = $context['HTTP_HOST'];
        $modelName = explode("/",$arrInput['protocolUri'])[2];
        $html = self::getPageValueFromFile($module, $page);

        if (!$html) {
            //没有部署sparta到调用集群情况
            $arrProtocal = explode("/",$protocolUri);
            $html = explode("?",$arrProtocal[4])[0].".html";
        }

        if (!in_array($modelName,$blackHouse)) {
            return false;
        }
        $realUrl = $protocol . $host . "/static/hy/${module}/" . $html . $query;
        return $realUrl;
    }

    private static function getPageValueFromFile($module, $pageKey) {
        return false;
        $html = "";
        $dir = "/home/<USER>/conf/app/sparta/modules";
        $file = $dir . '/' . $module . '.json';
        if (!file_exists($file)) {
           return false;
        }
        $json = json_decode(file_get_contents($file), true);
        $pages = $json['pages'];
        foreach ($pages as $p) {
            if ($p['key'] == $pageKey) {
                $html = $p['html'];
                break;
            }
        }
        return $html;

    }

}
