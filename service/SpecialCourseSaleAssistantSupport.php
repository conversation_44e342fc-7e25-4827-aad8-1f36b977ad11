<?php
/***************************************************************************
 *
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file SpecialCourseSaleAssistantSupport.php
 * <AUTHOR>
 * @date 2018/03/13 21:07:18
 * @brief 18暑秋售卖特惠课(一元课)班主任方向支持
 *
 **/
class Hkzb_Service_SpecialCourseSaleAssistantSupport
{

    // 每个课程的限额
    const REDIS_PREFIX_COURSE_QUOTA = 'SPECIAL_COURSE_SALE_ASSISTANT_SUPPORT_QUOTA';
    // 每个课程对应的组
    const REDIS_PREFIX_COURSE_GROUP_MAP = 'SPECIAL_COURSE_SALE_ASSISTANT_SUPPORT_GROUP_MAP';
    // 每个组下的课程list
    const REDIS_PREFIX_COURSE_LIST = 'SPECIAL_COURSE_SALE_ASSISTANT_SUPPORT_COURSE_LIST';

    private $_objSpecialCourseSupport;
    private $_objFudaoRedis = null;

    public function __construct()
    {
        $this->_objSpecialCourseSupport = new Hkzb_Ds_Fudao_SpecialCourseSupport();
        $redisConf = Bd_Conf::getConf("/hk/redis/fudao");
        $this->_objFudaoRedis = new Hk_Service_Redis($redisConf['service']);
    }

    /**
     * 获取当天新生购买课程限额
     * 通过courseId去redis拿限额
     *
     */
    public function getTodayNewStudentQuota($arrCourseId)
    {

        $seniorCourseId = $this->getSeniorHighCourseId();

        $curHour = date("H", time());
        $dates = date("Y-m-d", time());
        if ($curHour < 10) {
            $dates = date("Y-m-d", strtotime("-1 day"));
        }
        if (empty($arrCourseId)) {
            return array();
        }
        $list = array();
        foreach ($arrCourseId as $courseId) {
            // 高中课程不做限流
            if(in_array($courseId,$seniorCourseId)){
                $list[$courseId] = 10000;
                continue;
            }

            $cacheKey = self::REDIS_PREFIX_COURSE_QUOTA . "_{$courseId}_{$dates}";
            $quotaNum = $this->_objFudaoRedis->get($cacheKey);
            if (empty($quotaNum)) {
                $list[$courseId] = 0;
                continue;
            }
            $list[$courseId] = (int)$quotaNum;
        }

        return $list;
    }


    /**
     * 没有购买过春季课程、不是暑秋连报的用户，支付成功后回调这个函数
     * 通组下每个课程都要减去1
     * 班主任方向记录下课程和studentUid，分班需要
     */
    public function reduceQuota($courseId, $studentUid)
    {
        if (empty($courseId) || empty($studentUid)) {
            return false;
        }

        $seniorCourseId = $this->getSeniorHighCourseId();
        // 高中课程不做限流
        if(in_array($courseId,$seniorCourseId)){
            return true;
        }

        // 入口记录表
        $cacheKey = self::REDIS_PREFIX_COURSE_GROUP_MAP . "_{$courseId}";
        $groupId = $this->_objFudaoRedis->get($cacheKey);
        if (empty($groupId)) {
            $groupId = 0;
        }
        $arrParam['studentUid'] = $studentUid;
        $arrParam['courseId'] = $courseId;
        $arrParam['groupId'] = $groupId;
        $ret = $this->_objSpecialCourseSupport->addRecord($arrParam);
        if (empty($ret)) {
            Bd_Log::warning("Error:[reduceQuota], arrConds:[" . json_encode($arrParam) . "]" . "]");
        }

        $curHour = date("H", time());
        $dates = date("Y-m-d", time());
        if ($curHour < 10) {
            $dates = date("Y-m-d", strtotime("-1 day"));
        }
        // 获取该课程下所有的组
        $arrCourseId = $this->getGroupCourseList($courseId);
        if (empty($arrCourseId) || !is_array($arrCourseId)) {
            return false;
        }
        foreach ($arrCourseId as $item) {
            $cacheKey = self::REDIS_PREFIX_COURSE_QUOTA . "_{$item}_{$dates}";
            $quotaNum = $this->_objFudaoRedis->get($cacheKey);
            if (empty($quotaNum)) {
                continue;
            }
            $this->_objFudaoRedis->decr($cacheKey);
        }

        return true;
    }

    /**
     * 通过课程id，获取改组下 当天 所有课程id
     */
    public function getGroupCourseList($courseId)
    {

        $list = array();
        // 获取组
        $cacheKey = self::REDIS_PREFIX_COURSE_GROUP_MAP . "_{$courseId}";
        $groupId = $this->_objFudaoRedis->get($cacheKey);
        if (empty($groupId)) {
            return array();
        }
        // 获取组下的课程list
        $courseListKey = self::REDIS_PREFIX_COURSE_LIST . "_{$groupId}";
        $courseList = $this->_objFudaoRedis->get($courseListKey);
        if (empty($courseList)) {
            return array();
        }
        $list = json_decode($courseList, 1);
        return $list;
    }

    /**
     * 高一数学拉新课不限流
     */
    public function getSeniorHighCourseId()
    {
        $list = array(56899,56898,56451,56415,56414,56413,56412,56411,56410,56409,56408,56407,56406,56405,56404,56403,56402,56401,56400,56399,56398,56397,56396,56395,56394,56393,56392,56391,56390,56389,70229,70281,74101,95144,95145);
        return $list;
    }


}
