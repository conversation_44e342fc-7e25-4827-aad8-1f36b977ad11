<?php
/**************************************************************************
 *
 * Copyright (c) 2019 Zuoyebang.com, Inc. All Rights Reserved
 *
 * @file    GeneralConfig.php
 * @date    2020-08-17
 *
 **************************************************************************/

/**
 * Class        Lxjxlib_Service_GeneralConfig
 * @date        2020-08-17
 * @desc        通用配置服务
 */
class Lxjxlib_Service_GeneralConfig
{
    protected $_daoConfig;

    protected $_redis;

    /**
     * Lxjxlib_Service_GeneralConfig constructor.
     */
    public function __construct()
    {
        $this->_daoConfig   = new Lxjxlib_Dao_General_Config();
        $this->_redis       = Hk_Service_RedisClient::getInstance(Lxjxlib_Const_Cache::REDIS_NAME_LXJX);
    }

    /**
     * @function        makeConfigCacheKey
     *
     * @access          public
     * @date            2020-08-17
     * @desc            生成缓存key
     *
     * @param           $project
     * @param           $module
     * @param           $configKey
     *
     * @return          string
     */
    public static function makeConfigCacheKey($project, $module, $configKey)
    {
        return sprintf('lxjxlib_general_config_project_%s_module_%s_key_%s', $project, $module, $configKey);
    }

    /**
     * @function        _getCachedConfigs
     *
     * @access          protected
     * @date            2020-08-17
     * @desc            查询缓存数据
     *
     * @param           $project
     * @param           $module
     * @param           $configKeys
     *
     * @return          array
     */
    protected function _getCachedConfigs($project, $module, $configKeys)
    {
        $ret = array();

        foreach ($configKeys as $configKey) {
            $cacheKeys[] = self::makeConfigCacheKey($project, $module, $configKey);
        }
        $cachedValues = $this->_redis->get($cacheKeys);

        if ($cachedValues) {
            foreach ($cachedValues as $cachedValue) {
                $cachedValue = json_decode($cachedValue, true);
                $configKey = $cachedValue['configKey'];
                $ret[ $configKey ] = $cachedValue;
            }
        }

        return $ret;
    }

    /**
     * @function        addConfigs
     *
     * @access          public
     * @date            2020-08-17
     * @desc            批量添加配置
     *
     * @param           $project
     * @param           $module
     * @param           $keyValues
     * @param           $opUser
     *
     * @return          array
     */
    public function addConfigs($project, $module, $keyValues, $opUser)
    {
        // 检查待添加的配置键是否存在
        $existConfigs = $this->getConfigsByKey($project, $module, array_keys($keyValues));
        if (!empty($existConfigs)) {
            $existKeys = array_column($existConfigs, 'configKey');
            return array(
                'res' => -1,
                'msg' => '键冲突(' . implode(',', $existKeys) . ')',
            );
        }

        $curTime = time();

        // 启动事务
        $this->_daoConfig->startTransaction();

        try {
            foreach ($keyValues as $configKey => $configValue) {
                $insRes = $this->_daoConfig->addConfig($project, $module, $configKey, $configValue, $opUser, $curTime);
                if (false === $insRes) {
                    throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR);
                }
            }

            // 提交事务
            $this->_daoConfig->commit();
        } catch (Hk_Util_Exception $exception) {
            $this->_daoConfig->rollback();
            return array(
                'res' => -2,
                'msg' => '数据库异常',
            );
        }

        return array(
            'res' => 0,
            'msg' => 'success',
        );
    }

    /**
     * @function        getConfigsByKey
     *
     * @access          public
     * @date            2020-08-17
     * @desc            查询配置--按键值
     *
     * @param           $project
     * @param           $module
     * @param           $configKeys
     *
     * @return          array
     */
    public function getConfigsByKey($project, $module, $configKeys)
    {
        // 获取缓存的数据
        $ret = $this->_getCachedConfigs($project, $module, $configKeys);

        if (count($ret) != count($configKeys)) {
            foreach ($configKeys as $configKey) {
                if (!isset($cachedConfigs[ $configKey ])) {
                    $configData = $this->_daoConfig->getConfigByKey($project, $module, $configKey);
                    if (!empty($configData)) {
                        $ret[ $configKey ] = $configData;

                        // 添加缓存
                        $cacheKey = self::makeConfigCacheKey($project, $module, $configKey);
                        $this->_redis->set($cacheKey, json_encode($configData), Lxjxlib_Const_Common::EXPIRE_HOUR);
                    }
                }
            }
        }

        return $ret;
    }

    /**
     * @function        getConfigsByModule
     *
     * @access          public
     * @date            2020-08-18
     * @desc            查询配置--按模块
     *
     * @param           $project
     * @param           $module
     *
     * @return          array
     */
    public function getConfigsByModule($project, $module)
    {
        $configList = $this->_daoConfig->getConfigsByModule($project, $module);
        return is_array($configList) ? $configList : array();
    }

    /**
     * @function        updateConfigs
     *
     * @access          public
     * @date            2020-08-17
     * @desc            更新配置
     *
     * @param           $project
     * @param           $module
     * @param           $keyValues
     * @param           $opUser
     *
     * @return          array
     */
    public function updateConfigs($project, $module, $keyValues, $opUser)
    {
        // 检查待更新的配置键是否存在
        $existConfigs = $this->getConfigsByKey($project, $module, array_keys($keyValues));
        if (count($existConfigs) != count($keyValues)) {
            return array(
                'res' => -1,
                'msg' => '部分配置键无效',
            );
        }

        $this->_daoConfig->startTransaction();

        try {
            foreach ($keyValues as $configKey => $configValue) {
                $existConfig = $existConfigs[ $configKey ];
                if ($existConfig['configValue'] != $configValue) {
                    $upRes = $this->_daoConfig->updateConfig($project, $module, $configKey, $configValue, $opUser);
                    if (!$upRes) {
                        throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::DB_ERROR);
                    }

                    // 清除缓存
                    $cacheKey = self::makeConfigCacheKey($project, $module, $configKey);
                    $this->_redis->delete($cacheKey);
                }
            }

            $this->_daoConfig->commit();
        } catch (Hk_Util_Exception $exception) {
            $this->_daoConfig->rollback();
            return array(
                'res'   => -2,
                'msg'   => '数据库异常',
            );
        }

        return array(
            'res'   => 0,
            'msg'   => 'success',
        );
    }

}
