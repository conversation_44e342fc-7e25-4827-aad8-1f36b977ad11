<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   Personas.php
 * <AUTHOR>
 * @date   2019-02-18 20:37
 * @brief
 **/
 
class Zb_Service_UserPersonas_Personas {
    private static $service     = 'zbuserpersonas';
    private static $serviceUri  = '/userpersonas/api/personas';
    private static $batchUri    = '/userpersonas/api/batch';
    private static $skuUri      = '/userpersonas/api/sku';

    private static $rKeyTpl = "UP_%d_%d"; // 第一个%d 是UID，第二个 %d 是 Index, UP => user personas


    const UP_PRIVATE            = 100; //专题课 用户, 专题课类型是0，json_encode 会自动转list
    const UP_PUBLIC             = 101; //公开课 用户
    const UP_PRIVATE_LONG       = 102; //班课 用户
    const UP_PARENT_COURSE      = 103; //家长课 用户
    const UP_PRE_LONG           = 104; //预备班 用户
    const UP_NEW                = 105; //全新用户
    const UP_2019_1_2           = 106; //2019 春季 班课 用户
    const UP_2019_2_2           = 107; //2019 暑期 班课 用户
    const UP_2019_3_2           = 108; //2019 秋季 班课 用户
    const UP_2019_4_2           = 109; //2019 寒季 班课 用户
    const UP_2019_2_PUB_COURSE  = 110; //2019 暑秋发布会课程用户
    const UP_SUBJECT_YUWEN      = 111; //语文课用户
    const UP_SUBJECT_SHUXUE     = 112; //数学课用户
    const UP_SUBJECT_YINGYU     = 113; //英语课用户
    const UP_SUBJECT_WULI       = 114; //物理课用户
    const UP_SUBJECT_HUAXUE     = 115; //化学课用户
    const UP_SUBJECT_SHENGWU    = 116; //生物课用户
    const UP_SUBJECT_ZHENGZHI   = 117; //政治课用户
    const UP_SUBJECT_LISHI      = 118; //历史课用户
    const UP_SUBJECT_DILI       = 119; //地理课用户
    const UP_SUBJECT_XINGQU     = 120; //兴趣课用户
    const UP_SUBJECT_SIXIANG    = 121; //思想品德课用户
    const UP_SUBJECT_JIANGZUO   = 122; //讲座课用户
    const UP_SUBJECT_LIZONG     = 123; //理综课用户
    const UP_SUBJECT_WENZONG    = 124; //文综课用户
    const UP_SUBJECT_AOSHU      = 125; //奥数课用户
    const UP_SUBJECT_KEXUE      = 126; //科学课用户
    const UP_HOLDING_TRADE      = 127; //预约用户
    const UP_2020_1_2           = 128; //2020 春季 班课 用户
    const UP_2020_2_2           = 129; //2020 暑期 班课 用户
    const UP_2020_3_2           = 130; //2020 秋季 班课 用户
    const UP_2020_4_2           = 131; //2020 寒季 班课 用户
    const UP_NEW_DAY_7          = 132; //7天内的纯新用户...
    const UP_BB_PRIVATE_LONG    = 133; //帮帮班课用户
    const UP_XIAO_HUAN_XIONG    = 134; //小浣熊用户
    const UP_OLD_HUAN_XIONG     = 135; //老浣熊用户
    const UP_2019_3_BB_PRIVATE_LONG = 136; //2019秋帮帮班课用户
    const UP_2020_4_BB_PRIVATE_LONG = 137; //2020寒帮帮班课用户
    const UP_2020_1_12_2        = 145;//20年小学春二期班课用户
    const UP_2020_11_2          = 146;//20年春一班课用户

    public static $personasMap = array(
        // 课程类型
        self::UP_PRIVATE            => "专题课用户",
        self::UP_PUBLIC             => "公开课用户",
        self::UP_PRIVATE_LONG       => "班课用户",
        self::UP_PARENT_COURSE      => "家长课用户",
        self::UP_PRE_LONG           => "预备班用户",
        // 基础类型
        self::UP_NEW                => "全新用户", // 1 新用户  0 老用户
        self::UP_HOLDING_TRADE      => "预约用户",
        // 复合类型
        self::UP_2019_1_2           => "2019春季班课",
        self::UP_2019_2_2           => "2019暑季班课",
        self::UP_2019_3_2           => "2019秋季班课",
        self::UP_2019_4_2           => "2019寒季班课",
        self::UP_2019_2_PUB_COURSE  => "2019暑秋发布会课程用户",
        self::UP_2020_1_2           => "2020春季班课",
        self::UP_2020_2_2           => "2020暑季班课",
        self::UP_2020_3_2           => "2020秋季班课",
        self::UP_2020_4_2           => "2020寒季班课",
        // 科目类型
        self::UP_SUBJECT_YUWEN      => "语文课用户",
        self::UP_SUBJECT_SHUXUE     => "数学课用户",
        self::UP_SUBJECT_YINGYU     => "英语课用户",
        self::UP_SUBJECT_WULI       => "物理课用户",
        self::UP_SUBJECT_HUAXUE     => "化学课用户",
        self::UP_SUBJECT_SHENGWU    => "生物课用户",
        self::UP_SUBJECT_ZHENGZHI   => "政治课用户",
        self::UP_SUBJECT_LISHI      => "历史课用户",
        self::UP_SUBJECT_DILI       => "地理课用户",
        self::UP_SUBJECT_XINGQU     => "兴趣课用户",
        self::UP_SUBJECT_SIXIANG    => "思想品德课用户",
        self::UP_SUBJECT_JIANGZUO   => "讲座课用户",
        self::UP_SUBJECT_LIZONG     => "理综课用户",
        self::UP_SUBJECT_WENZONG    => "文综课用户",
        self::UP_SUBJECT_AOSHU      => "奥数课用户",
        self::UP_SUBJECT_KEXUE      => "科学课用户",
        self::UP_NEW_DAY_7          => "注册7天内的纯新用户",
        self::UP_BB_PRIVATE_LONG    => "帮帮班课用户",
        self::UP_XIAO_HUAN_XIONG    => "小浣熊",
        self::UP_OLD_HUAN_XIONG     => "老浣熊用户",
        self::UP_2019_3_BB_PRIVATE_LONG => "2019秋帮帮班课用户",
        self::UP_2020_4_BB_PRIVATE_LONG => "2020寒帮帮班课用户",
    );

    //上架策略用户展示对应的角色
    public  static $onlineChannelLessionUserTypeRole = array(
        self::UP_NEW                        => "纯新用户",
        self::UP_PRE_LONG                   => "试听课用户",
        self::UP_PRIVATE                    => "专题课用户",
        self::UP_PRIVATE_LONG               => "班课",
        self::UP_2019_2_2                   => "2019年暑季班课用户",
        self::UP_2019_3_2                   => "2019年秋季班课用户",
        self::UP_2020_4_2                   => "2020年寒季班课用户",
        self::UP_2020_1_2                   => "2020年春季班课用户",
        self::UP_2020_2_2                   => "2020年暑季班课用户",
        self::UP_2020_3_2                   => "2020年秋季班课用户",
        self::UP_BB_PRIVATE_LONG            => "帮帮班课用户",
        self::UP_2019_3_BB_PRIVATE_LONG     => "2019秋帮帮班课用户",
        self::UP_2020_4_BB_PRIVATE_LONG     => "2020寒帮帮班课用户",
        self::UP_2020_1_12_2                => "2020年小学春二期班课用户",
        self::UP_2020_11_2                  => "2020年春一班课用户",
    );
    // 定义 bitmap 位顺序，只可以追加，不可以插入 ！！！！！！
    private static $redisBitMapIdx        = array(
        0 => array(
            // Bit 位，顺序不可以变，只可以追加，一个分片 最大 256 位
            // 101010101010100101001010
            0 => self::UP_PRIVATE          ,
            1 => self::UP_PUBLIC           ,
            2 => self::UP_PRIVATE_LONG     ,
            3 => self::UP_PARENT_COURSE    ,
            4 => self::UP_PRE_LONG         ,
            5 => self::UP_NEW              ,
            6 => self::UP_2019_1_2         ,
            7 => self::UP_2019_2_2         ,
            8 => self::UP_2019_3_2         ,
            9 => self::UP_2019_4_2         ,
            10=> self::UP_2019_2_PUB_COURSE,
            11=> self::UP_SUBJECT_YUWEN    ,
            12=> self::UP_SUBJECT_SHUXUE   ,
            13=> self::UP_SUBJECT_YINGYU   ,
            14=> self::UP_SUBJECT_WULI     ,
            15=> self::UP_SUBJECT_HUAXUE   ,
            16=> self::UP_SUBJECT_SHENGWU  ,
            17=> self::UP_SUBJECT_ZHENGZHI ,
            18=> self::UP_SUBJECT_LISHI    ,
            19=> self::UP_SUBJECT_DILI     ,
            20=> self::UP_SUBJECT_XINGQU   ,
            21=> self::UP_SUBJECT_SIXIANG  ,
            22=> self::UP_SUBJECT_JIANGZUO ,
            23=> self::UP_SUBJECT_LIZONG   ,
            24=> self::UP_SUBJECT_WENZONG  ,
            25=> self::UP_SUBJECT_AOSHU    ,
            26=> self::UP_SUBJECT_KEXUE    ,
            27=> self::UP_HOLDING_TRADE    ,
            28=> self::UP_2020_1_2         ,
            29=> self::UP_2020_2_2         ,
            30=> self::UP_2020_3_2         ,
            31=> self::UP_2020_4_2         ,
            32=> self::UP_NEW_DAY_7        ,
            33=> self::UP_BB_PRIVATE_LONG  ,
            34=> self::UP_XIAO_HUAN_XIONG  ,
            35=> self::UP_OLD_HUAN_XIONG   ,
        ),
    );

    public static $learnSeasonCourseMap = array(
        '2019_1_2'  => self::UP_2019_1_2,
        '2019_2_2'  => self::UP_2019_2_2,
        '2019_3_2'  => self::UP_2019_3_2,
        '2019_4_2'  => self::UP_2019_4_2,
        '2020_1_2'  => self::UP_2020_1_2,
        '2020_2_2'  => self::UP_2020_2_2,
        '2020_3_2'  => self::UP_2020_3_2,
        '2020_4_2'  => self::UP_2020_4_2,
    );


    /**
     * 用户身份接口
     * @param array     $userId     用户ID
     * @param array     $personas   用户角色数组
     * @param array     $options    额外选项 [isRalMulti]
     * @return array
     */
    public static function getUserPersonas($userId, $personas, $options = array()) {
        $quickFlag = false;

        if($quickFlag && empty($options['isRalMulti'])){
            return self::getUserPersonasV2($userId,$personas);
        }else {
            $arrHeader = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri);
            $arrParams = array(
                'uid' => $userId,
                'ups' => $personas,
            );

            if (!empty($options['isRalMulti'])) {
                return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
            }

            return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
        }
    }

    /**
     * 多用户身份接口
     * @param $userIds
     * @param $personas
     * @param array $options
     * @return array
     */
    public static function getBatch($userIds, $personas, $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$batchUri);
        $arrParams = array(
            'uids'  => $userIds,
            'ups'   => $personas,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 是否购买指定商品接口
     * @param $userIds
     * @param $skuIds
     * @param array $options
     * @return array
     */
    public static function getSkuStatus($userIds, $skuIds=[], $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$skuUri);
        $arrParams = array(
            'uids'      => $userIds,
            'skuIds'    => $skuIds,
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }

    /**
     * 是否购买指定商品接口
     * @param $userIds
     * @param $skuIds
     * @return array
     * @throws Hk_Util_Exception
     */
    public static function getSkuStatusV2($userIds, $skuIds) {
        if (empty($userIds) || empty($skuIds)) {
            return false;
        }
        sort($userIds);
        sort($skuIds);
        $stKey = "ST_US_" . join(':', $userIds) . "_" . join(":", $skuIds);

        Hk_Util_Log::start("zb_userpersonas_skustatus_redis");
        if (Zb_Service_Navigator::isPressure()) {
            $redis = Hk_Service_RedisClient::getInstance('userpersonas',array('prefix' => "navigator:shadow"));
        } else {
            $redis = Hk_Service_RedisClient::getInstance('userpersonas');
        }

        $keys = [];

        foreach($userIds as $uid) {
            foreach($skuIds as $skuId) {
                $keys[] = "US_{$uid}_{$skuId}";
            }
        }

        $res = $redis->mget($keys);
        Bd_Log::addNotice('zb_userpersonas_skustatus_redis_res', json_encode($res,true));

        $out = [];
        foreach($res as $k => $v ) {
            list (,$uid,$skuId) = explode('_', $keys[$k]);
            list($status, $tradeId, $businessType) = explode(',', $v);
            $out[$uid][$skuId]['status'] = (is_null($status)||trim($status)==='') ? -1 : $status;
            $out[$uid][$skuId]['tradeId'] = is_null($tradeId) ? 0 : $tradeId;
            $out[$uid][$skuId]['businessType'] = is_null($businessType) ? -1 : $businessType;
        }

        Hk_Util_Log::stop("zb_userpersonas_skustatus_redis");
        return $out;
    }

    /**
     * User: <EMAIL>
     * Desc: 直接走redis 用户角色
     * Date: 2019-09-20 12:31
     */
    private static function getUserPersonasV2($userId,$personas){
        $uid = intval(trim($userId));//用户ID
        $ups = $personas;//用户身份
        if ($uid <= 0 || !is_numeric($uid) || empty($ups) || !is_array($ups)) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR,'参数错误',['uid'=>$uid,'ups'=>$ups]);
        }

        $diff = array_diff($ups, array_keys(self::$personasMap));
        if (!empty($diff)) {
            $str = join(',',$diff);
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR,"invalid param ups=$str",['uid'=>$uid,'ups'=>$ups]);
        }

        //如果是7日内纯新用户，加入新用户
        $newUserRole = self::UP_NEW;
        $sevenUserRole = self::UP_NEW_DAY_7;
        if(in_array($sevenUserRole,$ups) && !in_array($newUserRole,$ups)){
            $ups[] = $newUserRole;
        }

        //如果是压测流量过来获取真实的uid
        $uidReal = self::getRealUid($uid);
        $data = self::getOneUserPersonas($uidReal, $ups);

        if(isset($data[$uidReal]) && $uidReal != $uid){
            $person["$uid"] = $data["$uidReal"];
        }else{
            $person = $data;
        }

        Bd_Log::notice("uid:$uid-uidReal:$uidReal--ret:".@json_encode($person));

        return self::successRet($person);
    }

    private static function getRealUid($uid){
        if(empty($uid)){
            return $uid;
        }

        if(Zb_Service_Navigator::isPressure()){
            $uid = Zb_Service_Navigator::regressUid($uid);
        }

        return $uid;
    }

    public static function getOffset($tag, $idx, $logMsg='') {
        foreach (self::$redisBitMapIdx[$idx] as $offset=>$upk) {
            if ($upk == $tag) {
                return $offset;
            }
        }
        Bd_Log::warning("GET_OFFSET_ERR_" . $logMsg, "tag=$tag; idx=$idx");
        return false;
    }

    /**
     * User: <EMAIL>
     * Desc: 获取单用户用户角色
     * Date: 2019-09-20 13:33
     */
    public static function getOneUserPersonas($uid, $ups) {
        $redisKey = sprintf(self::$rKeyTpl, $uid, 0);
        $persons  = $arrIdx = array();


        $tempUps = array_unique(array_merge($ups,[self::UP_PRIVATE,self::UP_PRIVATE_LONG,self::UP_PRE_LONG]));//购买过这三种课的用户才不是新用户
        foreach($tempUps as $up) {
            $offset = self::getOffset($up, 0);
            $arrIdx[$offset] = $up;
        }
        $ret = self::getBit($redisKey, array_keys($arrIdx));

        $tmp = [];
        foreach ($ret as $idx=>$val) {
            $tmp[$arrIdx[$idx]] = $val;
        }

        if(isset($tmp[self::UP_NEW]) && empty($tmp[self::UP_NEW]) && empty($tmp[self::UP_PRIVATE]) && empty($tmp[self::UP_PRIVATE_LONG]) && empty($tmp[self::UP_PRE_LONG])){
            $tmp[self::UP_NEW] = 1;
        }

        $roleRet = [];
        foreach ($ups as $up){
            if($up == self::UP_XIAO_HUAN_XIONG){   //纯小浣熊用户（小浣熊用户&&非班课用户）
                if($tmp[$up] == 1 && $tmp[self::UP_PRIVATE_LONG] == 0) {
                    $roleRet[$up] = 1;
                }else{
                    $roleRet[$up] = 0;
                }
                continue;
            }
            $roleRet[$up] = $tmp[$up];
        }

        $persons[$uid] = $roleRet;

        //计算是否7天内纯新用户
        $persons = self::addSevenUserRoleType($uid,$persons,$ups);

        return $persons;
    }

    /**
     * User: <EMAIL>
     * Desc: 七天新用户
     * Date: 2019-09-20 13:34
     */
    private static function addSevenUserRoleType($uid,$persons,$ups){
        $sevenUserRole = self::UP_NEW_DAY_7;
        $newUserRole = self::UP_NEW;
        if(!in_array($sevenUserRole,$ups)){
            return $persons;
        }

        if(empty($uid)){
            Bd_Log::warning("uid is empty");
            return $persons;
        }

        $persons[$uid][$sevenUserRole] = 0;//默认不是
        $userRoleList = isset($persons[$uid]) ? $persons[$uid] : array();

        if(empty($userRoleList)){
            Bd_Log::warning("userRoleList is empty uid:{$uid}");
            return $persons;
        }

        if(empty($userRoleList[$newUserRole])){
            return $persons;
        }

        $uidList = array($uid);
        $userInfoList = self::getUcloudUserInfoList($uidList);
        $userInfo = isset($userInfoList[$uid]) ? $userInfoList[$uid] : array();
        if(empty($userInfo)){
            Bd_Log::warning("ucloud userInfo is empty uid:{$uid}");
            return $persons;
        }

        if(!isset($userInfo['regTime'])){
            Bd_Log::addNotice("addSevenUserRoleType","注册时间:regTime is empty uid:{$uid} userInfo:" . json_encode($userInfo));
            return $persons;
        }

        $regTime = isset($userInfo['regTime']) ? $userInfo['regTime'] : 0;
        if(empty($regTime)){
            Bd_Log::warning("注册时间:regTime is empty uid:{$uid} userInfo:" . json_encode($userInfo));
            return $persons;
        }

        $nowTime = time();
        $beforeSevenDayTime = $nowTime - 7*24*3600;
        if($regTime >= $beforeSevenDayTime){
            $persons[$uid][$sevenUserRole] = 1;
        }

        return $persons;
    }

    /**
     * User: <EMAIL>
     * Desc: 获取用户信息
     * Date: 2019-09-20 13:34
     */
    private static function getUcloudUserInfoList($uidArr = array()){
        if(empty($uidArr)){
            return array();
        }

        $dsUserUcloud = new Hk_Ds_User_Ucloud();

        $userInfoList = $dsUserUcloud->getUserInfo($uidArr);

        if(empty($userInfoList)){
            Bd_Log::warning(" userinfo is empty uidArr:".json_encode($uidArr));
            return array();
        }

        return $userInfoList;
    }

    private static function getBit($redisKey, $arrIdx) {
        $ret = [];
        if (Zb_Service_Navigator::isPressure()) {
            $objRedis   = Hk_Service_RedisClient::getInstance('userpersonas',array('prefix' => "navigator:shadow"));
        } else {
            $objRedis   = Hk_Service_RedisClient::getInstance('userpersonas');
        }

        $rs = $objRedis->get($redisKey);
        
        //null表示redis链接失败
        if (null === $rs) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::NETWORK_ERROR,"redis connect failed redisKey：{$redisKey}");
        }

        //false表示key不存在
        if (false === $rs) {
            foreach ($arrIdx as $idx) {
                $ret[$idx] = 0;
                if ($idx == 5) { // New User 105
                    $ret[$idx] = 1;
                }
            }
            return $ret;
        }

        $hex_str = bin2hex($rs);
        $arr = [
            '0'=>0,
            '1'=>8,
            '2'=>4,
            '3'=>12,
            '4'=>2,
            '5'=>10,
            '6'=>6,
            '7'=>14,
            '8'=>1,
            '9'=>9,
            'a'=>5,
            'b'=>13,
            'c'=>3,
            'd'=>11,
            'e'=>7,
            'f'=>15,
        ];

        foreach($arrIdx as $idx) {
            $grp  = intval($idx/4);
            $offset = $idx%4;
            if ($grp > strlen($hex_str)) {
                $ret[$idx] = 0;
                continue;
            }

            $exist = ($arr[$hex_str[$grp]] & (1<<$offset)) != 0 ;
            $ret[$idx] = intval($exist);
        }

        Bd_Log::notice('userpersonas getbit index ret'.@json_encode($ret));

        return $ret;
    }

    private static function successRet($data){
        $success = [
            'errNo' => 0,
            'errstr' => 'success',
            'data' => $data,
        ];

        return $success;
    }
}
