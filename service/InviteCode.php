<?php
///**
// * 邀请码服务
// * User: <EMAIL>
// * Date: 2021-01-22
// */
//class Lxjxlib_Service_InviteCode {
//
//    /**
//     * 过期时间(默认时间72小时)
//     */
//    const EXPITE_TIME = 259200;
//
//    /**
//     * 邀请码长度
//     */
//    const INVITE_CODE_LENGTH = 5;
//
//
//    /**
//     * 邀请码状态
//     */
//    const INVITE_CODE_STATUS_NORMAL = 1;        //正常
//    const INVITE_CODE_STATUS_EXPIRE = 2;        //已过期
//
//    /**
//     * 校验邀请码
//     * @param $inviteCode    邀请码
//     * @param $genChannel    渠道
//     * @param $businessUuid  业务线唯一ID
//     * @return array|bool
//     */
////    public static function checkInviteCode($inviteCode, $genChannel, $businessUuid)
////    {
////        $daoUserInviteCode = new Lxjxlib_Dao_User_UserInviteCode();
////
////        self::filterInviteCode($inviteCode);
////
////        $result = [
////            'errNo'  => 0,
////            'errMsg' => '',
////        ];
////
////        //校验渠道
////        if (!in_array($genChannel, array_keys(Lxjxlib_Const_InviteCode::$inviteCodeConf))) {
////            $result['errNo']  = Lxjxlib_Const_InviteCode::INVITE_CODE_CHANNEL_ERROR;
////            $result['errMsg'] = Lxjxlib_Const_InviteCode::$errorCodeDesc[Lxjxlib_Const_InviteCode::INVITE_CODE_CHANNEL_ERROR];
////            return $result;
////        }
////
////        if (empty($inviteCode) || strlen($inviteCode) != self::INVITE_CODE_LENGTH) {
////            $result['errNo']  = Lxjxlib_Const_InviteCode::INVITE_CODE_ERR;
////            $result['errMsg'] = Lxjxlib_Const_InviteCode::$errorCodeDesc[Lxjxlib_Const_InviteCode::INVITE_CODE_ERR];
////            return $result;
////        }
////
////        $inviteCode = strtoupper($inviteCode);
////
////        $inviteCodeInfo = $daoUserInviteCode->getInviteCode($inviteCode, $genChannel, $businessUuid);
////        if (empty($inviteCodeInfo)) {
////            $result['errNo']  = Lxjxlib_Const_InviteCode::INVITE_CODE_ERR;
////            $result['errMsg'] = Lxjxlib_Const_InviteCode::$errorCodeDesc[Lxjxlib_Const_InviteCode::INVITE_CODE_ERR];
////            return $result;
////        }
////
////        $nowTime = time();
////        if ($inviteCodeInfo['expireTime'] <= $nowTime) {
////            $result['errNo']  = Lxjxlib_Const_InviteCode::INVITE_CODE_EXPIRE;
////            $result['errMsg'] = Lxjxlib_Const_InviteCode::$errorCodeDesc[Lxjxlib_Const_InviteCode::INVITE_CODE_EXPIRE];
////            return $result;
////        }
////
////        $result['info'] = $inviteCodeInfo;
////
////        return $result;
////    }
//
//
//    /**
//     * 生成可用的邀请码
//     * @param $genFrom
//     * @param $genUid
//     * @param $genChannel
//     * @param $validTime
//     * @param $businessUuid
//     * @return array|bool
//     */
////    public static function genValidInviteCode($genFrom, $genUid, $genChannel = 0, $validTime = 0, $businessUuid = 0)
////    {
////        $daoUserInviteCode = new Lxjxlib_Dao_User_UserInviteCode();
////
////        $errNo  = 0;
////        $errMsg = '';
////
////        //校验渠道
////        if (!in_array($genChannel, array_keys(Lxjxlib_Const_InviteCode::$inviteCodeConf))) {
////            $result['errNo']  = Lxjxlib_Const_InviteCode::INVITE_CODE_CHANNEL_ERROR;
////            $result['errMsg'] = Lxjxlib_Const_InviteCode::$errorCodeDesc[Lxjxlib_Const_InviteCode::INVITE_CODE_CHANNEL_ERROR];
////            return $result;
////        }
////
////        $validTime = $validTime > 0 ? $validTime : self::EXPITE_TIME;
////
////        $nowTime = time();
////        // 先判断当前是否有未过期的邀请码
////        $nowInviteCode = $daoUserInviteCode->getValidInviteCode($genFrom, $genUid, $nowTime, $genChannel, $businessUuid);
////        if (!empty($nowInviteCode)) {
////            // 已经有可使用的，直接返回
////            $inviteCode = $nowInviteCode['inviteCode'];
////            $expireTime = $nowInviteCode['expireTime'];
////        } else {
////            $expireTime = $nowTime + $validTime;
////            $inviteCode = '';
////            $retry = 5;
////            while ($retry > 0) {
////                $code = self::_genInviteCode();
////                if (in_array($code, Lxjxlib_Const_InviteCode::$arrFilterCode, true)) {
////                    continue;
////                }
////                $isExist = $daoUserInviteCode->isExist($code, $nowTime);
////                if (false === $isExist) {
////                    // 重新生成新的邀请码
////                    $newInviteCodeData = [
////                        'inviteCode'   => $code,
////                        'genFrom'      => $genFrom,
////                        'genUid'       => $genUid,
////                        'genChannel'   => $genChannel,
////                        'businessUuid' => $businessUuid,
////                        'status'       => self::INVITE_CODE_STATUS_NORMAL,
////                        'expireTime'   => $expireTime,
////                        'createTime'   => $nowTime,
////                        'updateTime'   => $nowTime,
////                    ];
////
////                    $ret = $daoUserInviteCode->insertRecords($newInviteCodeData);
////                    if (true === $ret) {
////                        $inviteCode = $code;
////                        break;
////                    }
////                }
////
////                $retry--;
////            }
////        }
////
////        if (empty($inviteCode)) {
////            $errNo  = Lxjxlib_Const_InviteCode::INVITE_CODE_GEN_FAIL;
////            $errMsg = Lxjxlib_Const_InviteCode::$errorCodeDesc[Lxjxlib_Const_InviteCode::INVITE_CODE_GEN_FAIL];
////            Bd_Log::warning('[Error]gen_invitecode_fail');
////        }
////
////        $info = [
////            'inviteCode' => $inviteCode,
////            'expireTime' => $expireTime,
////        ];
////
////        $result = [
////            'errNo'  => $errNo,
////            'errMsg' => $errMsg,
////            'info'   => $info,
////        ];
////
////        return $result;
////    }
////
////    private static function _genInviteCode()
////    {
////        $len = self::INVITE_CODE_LENGTH;
////        $chars = 'Y8PQFX6HEWS29BTN3AJ7DKCUMZVG54R';
////        $randStr = '';
////
////        for ($i = 0; $i < $len; $i++) {
////            $randStr .= $chars[mt_rand(0, 30)];
////        }
////        return $randStr;
////    }
//
//    /**
//     * 过滤空格&全角转半角
//     * @param $inviteCode
//     */
////    private static function filterInviteCode(&$inviteCode)
////    {
////        //过滤空格
////        $inviteCode = str_replace(' ', '', $inviteCode);
////        //全角转半角
////        $arr = [
////            '０' => '0', '１' => '1', '２' => '2', '３' => '3', '４' => '4',
////
////            '５' => '5', '６' => '6', '７' => '7', '８' => '8', '９' => '9',
////
////            'Ａ' => 'A', 'Ｂ' => 'B', 'Ｃ' => 'C', 'Ｄ' => 'D', 'Ｅ' => 'E',
////
////            'Ｆ' => 'F', 'Ｇ' => 'G', 'Ｈ' => 'H', 'Ｉ' => 'I', 'Ｊ' => 'J',
////
////            'Ｋ' => 'K', 'Ｌ' => 'L', 'Ｍ' => 'M', 'Ｎ' => 'N', 'Ｏ' => 'O',
////
////            'Ｐ' => 'P', 'Ｑ' => 'Q', 'Ｒ' => 'R', 'Ｓ' => 'S', 'Ｔ' => 'T',
////
////            'Ｕ' => 'U', 'Ｖ' => 'V', 'Ｗ' => 'W', 'Ｘ' => 'X', 'Ｙ' => 'Y',
////
////            'Ｚ' => 'Z', 'ａ' => 'a', 'ｂ' => 'b', 'ｃ' => 'c', 'ｄ' => 'd',
////
////            'ｅ' => 'e', 'ｆ' => 'f', 'ｇ' => 'g', 'ｈ' => 'h', 'ｉ' => 'i',
////
////            'ｊ' => 'j', 'ｋ' => 'k', 'ｌ' => 'l', 'ｍ' => 'm', 'ｎ' => 'n',
////
////            'ｏ' => 'o', 'ｐ' => 'p', 'ｑ' => 'q', 'ｒ' => 'r', 'ｓ' => 's',
////
////            'ｔ' => 't', 'ｕ' => 'u', 'ｖ' => 'v', 'ｗ' => 'w', 'ｘ' => 'x',
////
////            'ｙ' => 'y', 'ｚ' => 'z',
////
////            '（' => '(', '）' => ')', '〔' => '[', '〕' => ']', '【' => '[',
////
////            '】' => ']', '〖' => '[', '〗' => ']', '“' => '[', '”' => ']',
////
////            '‘' => '[', '’' => ']', '｛' => '{', '｝' => '}', '《' => '<',
////
////            '》' => '>',
////
////            '％' => '%', '＋' => '+', '—' => '-', '－' => '-', '～' => '-',
////
////            '：' => ':', '。' => '.', '、' => ',', '，' => '.', '、' => '.',
////
////            '；' => ',', '？' => '?', '！' => '!', '…' => '-', '‖' => '|',
////
////            '”' => '"', '’' => '`', '‘' => '`', '｜' => '|', '〃' => '"',
////
////            '　' => ' '];
////
////        $inviteCode = strtr($inviteCode, $arr);
////    }
//
//
//
//}
