<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   GoodsField.php
 * <AUTHOR> (<EMAIL>)
 * @date   2019年2月11日18:57:56
 * @brief
 **/


class Zb_Service_Dak_GoodsField
{

    private static $service = 'zbcore';
    private static $serviceUri = '/zbcore/api/api';
    private static $module = 'dak';
    private static $entity = 'GoodsField';


    /**
     *  通过 field_id 或 field_key  (两字段只能存在一种)
     *  批量获取field+tags KV 数据
     *
     * @param $searchParam
     *                    array(
     *                            'field_id' => array(1,2,3,4,5,6,7,8,9),
     *                            'field_key' => array('grade','subject'),
     *                          );
     * @param $options
     *
     * @return array
     */
    public static function getFieldTagsKVByOnly($searchParam, $options = array())
    {
        $arrHeader = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getFieldTagsKVByOnly');

        if (!empty($options['isRalMulti'])) {
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $searchParam, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $searchParam, $arrHeader);
    }

}
