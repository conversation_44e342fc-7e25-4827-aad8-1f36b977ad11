<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   Spu.php
 * <AUTHOR>
 * @date   2018/7/2 下午10:06
 * @brief
 **/
 
 
class Zb_Service_Dak_Spu {

    private static $service     = 'zbcore';
    private static $serviceUri  = '/zbcore/api/api';
    private static $module      = 'dak';
    private static $entity      = 'spu';


    /**
     * 根据spuIds获取spu列表
     * @param $spuIds array 
     * @param $options
     * @return array
     */
    public static function getKVBySpuIds($spuIds, $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getKVBySpuIds');
        $arrParams = array(
            'spuIds'  => $spuIds, 
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }
    
    /**
     * 根据条件获取spu列表
     * @param array $searchConds
     * @param string $sortField
     * @param int $sortType
     * @param int $offset
     * @param int $limit
     * @param type $bCache
     * @param type $options
     * @return array
     */
    public static function searchSpu(
        $searchConds = [], $sortField='spu_id', $sortType=Zb_Const_Spu::SPU_SORT_TYPE_DESC, 
        $offset=0, $limit=10, $bCache = true,$options=array()
    ){
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'searchSpu');
        $arrParams = array(
            'searchConds'   => $searchConds,
            'sortField'     => $sortField,
            'sortType'      => $sortType,
            'offset'        => $offset,
            'limit'         => $limit, 
            'bCache'        => $bCache, 
        );

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
    }
    
    /**
     * 根据条件获取spu 记录数
     * @param type $searchConds
     * @param type $bCache
     * @param type $options
     * @return type
     */
    public static function searchSpuCount($searchConds, $bCache = true, $options = array())
    {
        $arrHeader = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'searchSpuCount');
        $arrParams = array(
            'searchConds' => $searchConds,
            'bCache' => $bCache,
        );

        if( ! empty($options['isRalMulti']))
        {
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        $result = Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
        if(false !== $result)
        {
            return $result['cnt'];
        }
        return $result;
    }

    /* 根据 SpuId 获取基础SkuIds
	 *
     * @param array $spuid 
     * @param array $options
	 *
     * @return array
     */
	public static function getSkuIdsBySquIds($spuIds, $options=array()) {
        $arrHeader = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'getSkuIdsBySquIds');

        $arrParams = array(
            'spuIds' => $spuIds,
        );

        if (!empty($options['isRalMulti'])) {
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $arrParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $arrParams, $arrHeader);
	}

    /**
     * 编辑spuInfo信息
     */
    public static function editSpuInfo($inParams,$options=array())
    {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'editSpuInfo');
       
        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $inParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $inParams, $arrHeader);
    }
    
    /**
     * 一课cpu信息同步spu接口
     * @param $inParams
     * @param $options
     * @return array
     */
    public static function syncSpu($inParams, $options=array()) {
        $arrHeader  = Zb_Util_ZbServiceTools::getHeaders(self::$serviceUri, self::$module, self::$entity, 'syncSpu'); 

        if (!empty($options['isRalMulti'])){
            return Zb_Util_ZbServiceTools::multiPost(self::$service, $inParams, $arrHeader);
        }

        return Zb_Util_ZbServiceTools::post(self::$service, $inParams, $arrHeader);
    }

}
