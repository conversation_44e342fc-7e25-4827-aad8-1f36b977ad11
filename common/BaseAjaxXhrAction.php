<?php
/**
 * @befie   ajax跨域支持基类，此基类用于前端返回json数据接口，需要校验conf文件
 * @file    common/BaseAjaxAction.php
 * <AUTHOR>
 * @version 1.0
 * @since
 * @date    2019-08-15 17:18
 */
abstract class Lxjxlib_Common_BaseAjaxXhrAction extends Lxjxlib_Common_BaseAjaxAction
{
    private static $ALLOW_ORIGIN = [
    ];

    protected function _display()
    {
        if (Lxjxlib_Util_Tools::isTestEnv()) {
            header("Access-Control-Allow-Origin: *");
        } else {
            $origin = isset($_SERVER['HTTP_ORIGIN']) ? $_SERVER['HTTP_ORIGIN'] : '';
            if (in_array($origin, self::$ALLOW_ORIGIN)) {
                header('Access-Control-Allow-Origin:' . $origin);
            }
        }

        $json = Lxjxlib_Util_Json::encode((object)$this->_tplData);
        if ($json === false) {
            Bd_Log::warning("json_encode failed");
        }
        header('Content-type:application/json; charset=UTF-8');     // 设置json头

        $etag = '"' . md5($json) . ':' . dechex(strlen($json)) . '"';
        // 检测etag缓存，开启etag检测才会执行
        if (isset($_SERVER['HTTP_IF_NONE_MATCH']) && strlen($_SERVER['HTTP_IF_NONE_MATCH']) > 0 && $this->_openEtag) {
            if ($etag == $_SERVER['HTTP_IF_NONE_MATCH'] || "W/" . $etag == $_SERVER['HTTP_IF_NONE_MATCH']) {
                Hk_Util_Log::setLog('http_etag', 1);
                header('ETag: ' . $etag, true, 304);
                return true;
            }
        }
        header('ETag: ' . $etag);
        echo $json;
        return true;
    }
}
