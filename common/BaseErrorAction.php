<?php

/**
 * @befie   错误控制类
 * @file    common/BaseErrorAction.php
 * <AUTHOR>
 * @version 1.0
 * @since
 * @date    2019-11-01
 */
class Lxjxlib_Common_BaseErrorAction extends Ap_Controller_Abstract
{
    public function errorAction($exception)
    {
        if ($exception instanceof Hk_Util_Exception) {
            // hk异常
            $errno = $exception->getErrNo();
            $errmsg = $exception->getErrStr();
        } elseif ($exception instanceof Ap_Exception_LoadFailed_Action) {
            // 路由不存在
            $errno = Hk_Util_ExceptionCodes::SYSTEM_CRAZY;
            $errmsg = '接口不存在';
        } else {
            // 其他的类型为内部异常
            $errno = Hk_Util_ExceptionCodes::SYSTEM_CRAZY;
            $errmsg = '系统内部错误';
        }

        $output = [
            'errno' => $errno,
            'errmsg' => $exception->getMessage(),
            'data' => [],
        ];

        $json = json_encode($output);

        header('Content-type:application/json; charset=UTF-8');

        print $json;
    }
}
