<?php

/**
 * @befie   测评action基类
 * @file    common/action/Base.php
 * <AUTHOR>
 * @version 1.0
 * @date    2020-10-22
 */
abstract class Lxjxlib_Common_Action_TestBase extends Ap_Action_Abstract
{
    // 日志变量
    protected $logVars = array(
        'app_version' => '',
        'app_terminal' => '',
        'api_version' => '',
        'api_module' => '',
        'api_action' => '',
        'pro_errno' => '',
        'pro_un' => '',
        'pro_uid' => '',
        'pv' => '',
    );

    // 用户信息
    protected $_userInfo = array(
        'isLogin' => false,
        'uid' => 0,
        'uname' => '',
    );

    // 是否校验登录
    protected $_isCheckLogin = false;

    // 使用Filter过滤过的参数列表
    protected $_requestParam;

    # 公共参数列表
    protected $_publicParam;

    // 配置信息项
    protected $_actionName = null;

    // controller name
    protected $_controllor = 'action';

    // api version
    protected $_apiVersion = 'v1';

    // app version 两位版本后端接口映射版本号
    protected $_appVersion = 0;

    // app versionCode 具体客户端中的版本号
    protected $_appVersionCode = 0;

    // app terminal
    protected $_appTerminal = '';

    // app type app安装的操作系统类型
    protected $_appType = '';

    # 业务线标示
    protected $_appId = '';

    // app source
    protected $_appSource = 0;

    # 是否开启etag校验，默认都开启
    protected $_openEtag = true;

    // 输入验证配置
    protected $_input = null;

    // 输出验证配置
    protected $_output = null;

    // request
    protected $_request = null;

    // response
    protected $_tplData = array(
        'errNo' => 0,
        'errstr' => 'success',
        'data' => array(),
    );

    /*
     * 子类特有逻辑，强制子类必须实现
     */
    abstract protected function invoke();

    protected function afterDisplay()
    {
    }

    //action配置文件读取,参数初始化
    protected function _init()
    {
        // 初始化配置文件选项
        $objRequest = $this->getRequest();
        $this->_controllor = strtolower($objRequest->getControllerName());
        $this->_actionName = strtolower($objRequest->getActionName());
        $this->_apiVersion = strtolower($objRequest->getParam('apiversion', 'v1'));
        Bd_AppEnv::setCurrAction($this->_actionName);
        Bd_AppEnv::setCurrCtl($this->_controllor);

        // 获取请求参数
        $arrRequestParam = Saf_SmartMain::getCgi();
        $this->_requestParam = !is_null($arrRequestParam['request_param']) ? $arrRequestParam['request_param'] : [];
        $this->_appId = !isset($this->_requestParam['appId']) ? '' : $this->_requestParam['appId'];
        if (!isset($requestParam['vc'])) {
            $requestParam['vc'] = isset($_COOKIE['vc']) ? intval($_COOKIE['vc']) : 0;
        }
        if (!isset($requestParam['os'])) {
            $requestParam['os'] = isset($_COOKIE['os']) ? $_COOKIE['os'] : '';
        }

        $appVersion = Hk_Util_Client::getVersion();
        $this->_appType = $appVersion['type'];
        $this->_appVersion = $appVersion['version'];
        $this->_appSource = $appVersion['source'];
        $this->_appVersionCode = $appVersion['versionCode'];

        $appTerminal = Hk_Util_Client::getTerminal();
        $this->_appTerminal = $appTerminal['terminal'];
        // 初始化公共参数
        $this->_publicParam = $this->initPublicParam();

        $arrLogData = $this->_requestParam;
        foreach ($arrLogData as $logKey => $logValue) {
            if (is_string($logValue) && strlen($logValue) > 5000) {
                $logValue = 'LongString';
                $arrLogData[$logKey] = $logValue;
            }
        }
        Hk_Util_Log::setLog('request_param', @json_encode($arrLogData));
    }

    /**
     * 初始化app公共参数，其他类型的基类可以通过重载此函数进行实现<br>
     * 目前公共参数包含（不包含类似sign这种系统参数）：<br>
     * <code>
     * array(
     *     os,
     *     vc,
     *     vcname,
     *     cuid,
     *     appId,
     *     channel,
     *     device,
     * );
     * </code>
     * iOS独有：iOSVersion, bundleID
     * android独有：sdk, pkgName
     *
     * @return array
     **/
    protected function initPublicParam()
    {
        $os = strval($this->_requestParam['os']);
        $pubParam = [
            'os' => $os,
            'vc' => intval($this->_requestParam['vc']),
            'vcname' => strval($this->_requestParam['vcname']),
            'cuid' => strval($this->_requestParam['cuid']),
            'appId' => $this->_appId,
            'channel' => strval($this->_requestParam['channel']),
            'device' => strval($this->_requestParam['device']),
        ];

        // 操作系统特有参数
        if ('ios' === $os) {
            $pubParam['iOSVersion'] = strval($this->_requestParam['iOSVersion']);
            $pubParam['bundleID'] = strval($this->_requestParam['bundleID']);
        } elseif ('android' === $os) {
            $pubParam['sdk'] = intval($this->_requestParam['sdk']);
            $pubParam['pkgName'] = strval($this->_requestParam['pkgName']);
        }

        return $pubParam;
    }

    // 参数校验
    protected function _before()
    {
        if ($this->_isCheckLogin) {
            // 登录校验
            $this->_userInfo = Lxjxlib_Ds_User_TestSession::checkLogin();
        }

        return true;
    }

    //输出参数校验
    protected function _after()
    {
        return true;
    }

    protected function _display()
    {
        $json = json_encode((object)$this->_tplData);
        if ($json === false) {
            Bd_Log::warning('json_encode failed');
        }
        header('Content-type:application/json; charset=UTF-8');     // 设置json头

        /*
        $etag = '"' . md5($json) . ':' . dechex(strlen($json)) . '"';
        // 检测etag缓存，开启etag检测才会执行
        if (isset($_SERVER['HTTP_IF_NONE_MATCH']) && strlen($_SERVER['HTTP_IF_NONE_MATCH']) > 0 && $this->_openEtag) {
            if ($etag == $_SERVER['HTTP_IF_NONE_MATCH'] || 'W/' . $etag == $_SERVER['HTTP_IF_NONE_MATCH']) {
                Hk_Util_Log::setLog('http_etag', 1);
                header('ETag: ' . $etag, true, 304);
                return true;
            }
        }
        header('ETag: ' . $etag);
        */
        echo $json;
        return true;
    }

    //统计处理
    protected function _processLog()
    {
        $this->logVars['api_version'] = $this->_apiVersion;
        $this->logVars['api_module'] = $this->_controllor;
        $this->logVars['api_action'] = $this->_actionName;
        $this->logVars['pro_errno'] = $this->_tplData['errNo'];
        $this->logVars['pro_un'] = $this->_userInfo['uname'];
        $this->logVars['pro_uid'] = $this->_userInfo['uid'];
        $this->logVars['app_version'] = $this->_appVersion;
        $this->logVars['app_terminal'] = $this->_appTerminal;
        if (empty($this->logVars['pv'])) {
            $this->logVars['pv'] = sprintf('%s/%s/%s', $this->_controllor, $this->_apiVersion, $this->_actionName);
        }

        $this->logVars['mem_peak'] = round(memory_get_peak_usage(true) / 1024 / 1024, 3) . 'MB';

        foreach ($this->logVars as $key => $value) {
            Hk_Util_Log::setLog($key, $value);
        }

        $idc = Bd_Conf::getConf('idc/cur');
        Hk_Util_Log::setLog('idc', $idc);

        Hk_Util_Log::printLog();
    }

    protected function _antispam()
    {
        $uri = strtolower($this->getRequest()->getRequestUri());
        $antispam = new Hk_Service_Antispam($this->_appId);
        $antispam->appCheck($uri);
    }

    public function execute()
    {
        Hk_Util_Log::start('ts_all');
        try {
            Hk_Util_Log::start('ts_init');
            $this->_init();
            Hk_Util_Log::stop('ts_init');

            // na端需要进行签名检查
            if ($this->_appType == 'android' || $this->_appType == 'ios') {
                Hk_Util_Log::start('ts_antispam');
                $this->_antispam();
                Hk_Util_Log::stop('ts_antispam');
            }

            Hk_Util_Log::start('ts_before');
            $ret = $this->_before();
            Hk_Util_Log::stop('ts_before');

            if ($ret === true) {
                Hk_Util_Log::start('ts_invoke');
                $res = $this->invoke();
                $this->_tplData['data'] = is_array($res) ? $res : [];
                Hk_Util_Log::stop('ts_invoke');

                Hk_Util_Log::start('ts_after');
                $this->_after();
                Hk_Util_Log::stop('ts_after');
            }
        } catch (Hk_Util_Exception $e) {
            $this->_tplData['errNo'] = $e->getErrNo();
            $this->_tplData['errstr'] = $e->getErrStr();
        } catch (Exception $e) {
            $this->_tplData['errNo'] = $e->getCode();
            $this->_tplData['errstr'] = $e->getMessage();
        }

        Hk_Util_Log::start('ts_display');
        $this->_display();           // 输出
        Hk_Util_Log::stop('ts_display');

        Hk_Util_Log::stop('ts_all');

        $this->afterDisplay();
        $this->_processLog();       // 打印日志
    }
}
